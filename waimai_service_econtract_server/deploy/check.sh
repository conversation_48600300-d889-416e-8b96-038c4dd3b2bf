#!/bin/sh
echo "check service......"
echo "lsof -i :$CHECK_PORT | awk 'NR > 1 {print $2}' | wc -l"

pro_num=`lsof -i :$CHECK_PORT | awk 'NR > 1 {print $2}' | wc -l`

echo "pro_num: $pro_num"
if [ $pro_num == 0  ]
then
    echo "check failed!"
    exit 1
else
    echo "check succeeded!"
    exit 0
fi

##!/bin/sh
#echo "check thrift service!"
#pro_num=`echo "" | telnet 127.0.0.1 $CHECK_PORT 2>/dev/null | grep Connected | wc -l`
#echo "pro_num: $pro_num"
#
#if [ $pro_num == 0 ]
#then
#    echo "check thrift failed!"
#    exit 1
#else
#    echo "check thrift succeeded!"
#    exit 0
#fi
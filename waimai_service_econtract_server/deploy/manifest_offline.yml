version: v1
common:
  os: centos7
  tools:
    mt_oraclejdk: 8
build:
  tools:
    maven_offline: 3.3.3
  run:
    workDir: ./     # workDir是代码仓库的相对目录
    cmd:
      - sh waimai_service_econtract_server/deploy/compile.sh
  target:
    distDir: ./waimai_service_econtract_server/target   # distDir是代码仓库的相对目录
    files:              # files中的文件描述是相对distDir的
      - ./*.jar
      - ./lib
      - ../deploy
autodeploy:
    targetDir: /opt/meituan/apps/waimai_service_econtract_server/work
    run: sh deploy/run.sh
    # 服务检查，对于thrift_server类型，只检查端口是否存在
    check: sh deploy/check.sh
    checkRetry: 20 #后面下掉，加默认值为1
    checkInterval: 5s #后面下掉
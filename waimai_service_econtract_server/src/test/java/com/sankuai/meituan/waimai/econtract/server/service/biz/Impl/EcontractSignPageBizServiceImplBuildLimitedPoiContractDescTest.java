package com.sankuai.meituan.waimai.econtract.server.service.biz.Impl;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * Unit‑tests for the private method
 * {@code EcontractSignPageBizServiceImpl.buildLimitedPoiContractDesc}.
 *
 * <p>
 * The method is accessed via reflection.  The static configuration
 * {@code MccConfig.getPoiNameLimitToShow()} reads the value from
 * {@code ConfigUtilAdapter.getInt("poi_name_limit_to_show", 20)} – this static
 * call is mocked with <PERSON><PERSON><PERSON>’s {@code MockedStatic}.
 * </p>
 *
 * <p>
 * All expectations take into account:
 * <ul>
 *   <li>deduplication &amp; lexicographic sorting performed by
 *       {@code removeDuplicatePoiName};
 *   <li>the trailing delimiter “、” that the implementation always appends;
 *   <li>the truncation logic when the number of POI names exceeds the limit.
 * </ul>
 * </p>
 */
@RunWith(MockitoJUnitRunner.class)
public class EcontractSignPageBizServiceImplBuildLimitedPoiContractDescTest {

    /**
     * Instance under test – the private method does not depend on any injected beans.
     */
    private final EcontractSignPageBizServiceImpl service = new EcontractSignPageBizServiceImpl();

    /**
     * Invokes the private {@code buildLimitedPoiContractDesc} method while
     * mocking the static configuration value.
     *
     * @param input the raw POI‑name string (may be empty or {@code null})
     * @param limit the value that {@code ConfigUtilAdapter.getInt(...)} should return
     * @return the string produced by the private method
     * @throws Exception if reflection fails
     */
    private String invokeBuildLimitedPoiContractDesc(String input, int limit) throws Exception {
        // Mock the static call that supplies the limit
        try (MockedStatic<ConfigUtilAdapter> mocked = mockStatic(ConfigUtilAdapter.class)) {
            mocked.when(() -> ConfigUtilAdapter.getInt("poi_name_limit_to_show", 20)).thenReturn(limit);
            Method method = EcontractSignPageBizServiceImpl.class.getDeclaredMethod("buildLimitedPoiContractDesc", String.class);
            method.setAccessible(true);
            return (String) method.invoke(service, input);
        }
    }

    /**
     * 1. Empty input string.
     * Expected result: only the delimiter added by {@code removeDuplicatePoiName}.
     */
    @Test
    public void testBuildLimitedPoiContractDescEmptyInput() throws Throwable {
        String result = invokeBuildLimitedPoiContractDesc("", 20);
        assertEquals("、", result);
    }

    /**
     * 2. Single POI name.
     */
    @Test
    public void testBuildLimitedPoiContractDescSinglePoi() throws Throwable {
        String result = invokeBuildLimitedPoiContractDesc("Restaurant A", 20);
        assertEquals("Restaurant A、", result);
    }

    /**
     * 3. Multiple POI names, total count < limit.
     */
    @Test
    public void testBuildLimitedPoiContractDescUnderLimit() throws Throwable {
        String input = "Restaurant A、Restaurant B、Restaurant C";
        String result = invokeBuildLimitedPoiContractDesc(input, 20);
        // No duplicates, already in lexicographic order – just a trailing delimiter.
        assertEquals("Restaurant A、Restaurant B、Restaurant C、", result);
    }

    /**
     * 4. Exactly at the limit (20 distinct names).
     * The method sorts the names lexicographically (TreeSet).
     */
    @Test
    public void testBuildLimitedPoiContractDescAtLimit() throws Throwable {
        String input = "R1、R2、R3、R4、R5、R6、R7、R8、R9、R10、R11、R12、R13、R14、R15、R16、R17、R18、R19、R20";
        String result = invokeBuildLimitedPoiContractDesc(input, 20);
        String expected = "R1、R10、R11、R12、R13、R14、R15、R16、R17、R18、R19、R2、R20、R3、R4、R5、R6、R7、R8、R9、";
        assertEquals(expected, result);
    }

    /**
     * 5. More names than the limit (21 names, limit = 20).
     * The first 20 sorted names are kept, then “…、” is appended.
     */
    @Test
    public void testBuildLimitedPoiContractDescOverLimit() throws Throwable {
        String input = "R1、R2、R3、R4、R5、R6、R7、R8、R9、R10、R11、R12、R13、R14、R15、R16、R17、R18、R19、R20、R21";
        String result = invokeBuildLimitedPoiContractDesc(input, 20);
        String expected = "R1、R10、R11、R12、R13、R14、R15、R16、R17、R18、R19、R2、R20、R21、R3、R4、R5、R6、R7、R8、...、";
        assertEquals(expected, result);
    }

    /**
     * 6. Input containing duplicate POI names.
     * Duplicates are removed and the remaining names are sorted.
     */
    @Test
    public void testBuildLimitedPoiContractDescWithDuplicates() throws Throwable {
        String input = "R1、R2、R1、R3、R2";
        String result = invokeBuildLimitedPoiContractDesc(input, 20);
        assertEquals("R1、R2、R3、", result);
    }

    /**
     * 7. Input with empty entries (consecutive delimiters and a space).
     * Empty strings are kept (they appear first after sorting), the space
     * character is kept as a distinct entry.
     */
    @Test
    public void testBuildLimitedPoiContractDescWithEmptyNames() throws Throwable {
        String input = "R1、、R2、 、R3";
        String result = invokeBuildLimitedPoiContractDesc(input, 20);
        // Sorted order: "" (empty), " " (space), "R1", "R2", "R3"
        assertEquals("、 、R1、R2、R3、", result);
    }

    /**
     * 8. Custom limit (different from the default 20).
     * Here the limit is set to 3, so only the first three sorted names are kept.
     */
    @Test
    public void testBuildLimitedPoiContractDescCustomLimit() throws Throwable {
        String input = "R1、R2、R3、R4、R5";
        String result = invokeBuildLimitedPoiContractDesc(input, 3);
        assertEquals("R1、R2、R3、...、", result);
    }

    /**
     * 9. Null input – the original implementation does not guard against {@code null}
     * and throws a {@link NullPointerException} wrapped in an
     * {@link InvocationTargetException} when called via reflection.
     * This test verifies that behaviour.
     */
    @Test(expected = InvocationTargetException.class)
    public void testBuildLimitedPoiContractDescNullInput() throws Throwable {
        // The NPE is thrown inside the private method; reflection wraps it.
        invokeBuildLimitedPoiContractDesc(null, 20);
    }
}

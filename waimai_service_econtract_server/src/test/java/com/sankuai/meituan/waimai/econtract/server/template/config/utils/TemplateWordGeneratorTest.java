package com.sankuai.meituan.waimai.econtract.server.template.config.utils;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import com.sankuai.meituan.waimai.econtrct.client.template.config.domain.EcontractTemplateVersionBo;
import com.sankuai.meituan.waimai.econtrct.client.template.config.exception.EcontractTemplateConfigException;
import com.sankuai.meituan.waimai.econtrct.client.util.MtCloudS3Util;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class TemplateWordGeneratorTest {

    @InjectMocks
    private TemplateWordGenerator templateWordGenerator;

    @Mock
    private MccConfig mccConfig;

    private MockedStatic<MtCloudS3Util> mockedS3Util;

    private MockedStatic<MccConfig> mockedMccConfig;

    @Before
    public void setUp() {
        // Do not create mocks here
    }

    @After
    public void tearDown() {
        if (mockedS3Util != null) {
            mockedS3Util.close();
        }
        if (mockedMccConfig != null) {
            mockedMccConfig.close();
        }
    }

    @Test
    public void testGenerateWordFromFtlAndUploadToS3Success() throws Throwable {
        try (MockedStatic<MtCloudS3Util> mockedS3Util = mockStatic(MtCloudS3Util.class);
            MockedStatic<MccConfig> mockedMccConfig = mockStatic(MccConfig.class)) {
            EcontractTemplateVersionBo templateVersionBo = new EcontractTemplateVersionBo();
            templateVersionBo.setTemplateId(123);
            templateVersionBo.setVersion(1);
            String htmlUrl = "http://test.com/html";
            String s3Url = "https://econtract.meituan.com/template_123_v1.docx";
            byte[] htmlBytes = "<html><body>Test</body></html>".getBytes();
            mockedS3Util.when(() -> MtCloudS3Util.retryDownloadByTempUrl(htmlUrl)).thenReturn(htmlBytes);
            mockedMccConfig.when(MccConfig::getPdfUrlPrefix).thenReturn("https://econtract.meituan.com");
            mockedS3Util.when(() -> MtCloudS3Util.retryUploadFileFromBytes(any(byte[].class), eq("template_123_v1.docx"))).thenReturn("/template_123_v1.docx");
            Pair<String, String> result = templateWordGenerator.generateWordFromFtlAndUploadToS3(templateVersionBo, htmlUrl);
            assertNotNull(result);
            assertEquals(s3Url, result.getLeft());
            assertEquals("template_123_v1.docx", result.getRight());
        }
    }

    @Test(expected = EcontractTemplateConfigException.class)
    public void testGenerateWordFromFtlAndUploadToS3DownloadNull() throws Throwable {
        try (MockedStatic<MtCloudS3Util> mockedS3Util = mockStatic(MtCloudS3Util.class)) {
            EcontractTemplateVersionBo templateVersionBo = new EcontractTemplateVersionBo();
            String htmlUrl = "http://test.com/html";
            mockedS3Util.when(() -> MtCloudS3Util.retryDownloadByTempUrl(htmlUrl)).thenReturn(null);
            templateWordGenerator.generateWordFromFtlAndUploadToS3(templateVersionBo, htmlUrl);
        }
    }

    @Test(expected = EcontractTemplateConfigException.class)
    public void testGenerateWordFromFtlAndUploadToS3DownloadEmpty() throws Throwable {
        try (MockedStatic<MtCloudS3Util> mockedS3Util = mockStatic(MtCloudS3Util.class)) {
            EcontractTemplateVersionBo templateVersionBo = new EcontractTemplateVersionBo();
            String htmlUrl = "http://test.com/html";
            mockedS3Util.when(() -> MtCloudS3Util.retryDownloadByTempUrl(htmlUrl)).thenReturn(new byte[0]);
            templateWordGenerator.generateWordFromFtlAndUploadToS3(templateVersionBo, htmlUrl);
        }
    }

    @Test(expected = RuntimeException.class)
    public void testGenerateWordFromFtlAndUploadToS3UploadFailure() throws Throwable {
        try (MockedStatic<MtCloudS3Util> mockedS3Util = mockStatic(MtCloudS3Util.class);
            MockedStatic<MccConfig> mockedMccConfig = mockStatic(MccConfig.class)) {
            EcontractTemplateVersionBo templateVersionBo = new EcontractTemplateVersionBo();
            templateVersionBo.setTemplateId(123);
            templateVersionBo.setVersion(1);
            String htmlUrl = "http://test.com/html";
            byte[] htmlBytes = "<html><body>Test</body></html>".getBytes();
            mockedS3Util.when(() -> MtCloudS3Util.retryDownloadByTempUrl(htmlUrl)).thenReturn(htmlBytes);
            mockedMccConfig.when(MccConfig::getPdfUrlPrefix).thenReturn("https://econtract.meituan.com");
            mockedS3Util.when(() -> MtCloudS3Util.retryUploadFileFromBytes(any(byte[].class), anyString())).thenThrow(new RuntimeException("Upload failed"));
            templateWordGenerator.generateWordFromFtlAndUploadToS3(templateVersionBo, htmlUrl);
        }
    }

    @Test(expected = EcontractTemplateConfigException.class)
    public void testGenerateWordFromFtlAndUploadToS3NullTemplateVersionBo() throws Throwable {
        try (MockedStatic<MtCloudS3Util> mockedS3Util = mockStatic(MtCloudS3Util.class)) {
            String htmlUrl = "http://test.com/html";
            mockedS3Util.when(() -> MtCloudS3Util.retryDownloadByTempUrl(htmlUrl)).thenReturn(new byte[0]);
            templateWordGenerator.generateWordFromFtlAndUploadToS3(null, htmlUrl);
        }
    }

    @Test(expected = RuntimeException.class)
    public void testGenerateWordFromFtlAndUploadToS3NullHtmlUrl() throws Throwable {
        try (MockedStatic<MtCloudS3Util> mockedS3Util = mockStatic(MtCloudS3Util.class)) {
            EcontractTemplateVersionBo templateVersionBo = new EcontractTemplateVersionBo();
            mockedS3Util.when(() -> MtCloudS3Util.retryDownloadByTempUrl(null)).thenThrow(new RuntimeException("重试5次从美团云下载文件失败，tempUrl:null"));
            templateWordGenerator.generateWordFromFtlAndUploadToS3(templateVersionBo, null);
        }
    }

    @Test(expected = EcontractTemplateConfigException.class)
    public void testGenerateWordFromFtlAndUploadToS3EmptyHtmlUrl() throws Throwable {
        EcontractTemplateVersionBo templateVersionBo = new EcontractTemplateVersionBo();
        templateWordGenerator.generateWordFromFtlAndUploadToS3(templateVersionBo, "");
    }
}

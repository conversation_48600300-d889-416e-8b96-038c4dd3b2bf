package com.sankuai.meituan.waimai.econtract.server.service.ability.dataanalysis;

import static org.junit.Assert.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;
import org.junit.*;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

/**
 * Test class for AbstractDataAnalysisService.analysisList(String, String)
 */
public class AbstractDataAnalysisServiceTest {

    // Concrete subclass to allow instantiation for testing
    static class TestDataAnalysisService extends AbstractDataAnalysisService {

        @Override
        protected List<Object> toObjectList(List tList) {
            return null;
        }

        @Override
        protected List analysisListByClass(String data, Class clazz) {
            return null;
        }
    }

    private TestDataAnalysisService service;

    @Before
    public void setUp() {
        service = new TestDataAnalysisService();
    }

    /**
     * Test normal case: J<PERSON><PERSON> parses to a non-empty list, CollectionUtils.isEmpty returns false.
     */
    @Test
    public void testAnalysisList_NormalNonEmptyList() throws Throwable {
        // arrange
        String data = "[\"a\", \"b\"]";
        String type = "irrelevant";
        List<String> expectedList = Arrays.asList("a", "b");
        // Mock JSON.parseObject to return a non-empty list
        try (MockedStatic<JSON> jsonMock = Mockito.mockStatic(JSON.class);
            MockedStatic<CollectionUtils> collectionUtilsMock = Mockito.mockStatic(CollectionUtils.class)) {
            jsonMock.when(() -> JSON.parseObject(Mockito.eq(data), Mockito.any(TypeReference.class))).thenReturn(expectedList);
            collectionUtilsMock.when(() -> CollectionUtils.isEmpty(expectedList)).thenReturn(false);
            // act
            List<String> result = service.analysisList(data, type);
            // assert
            assertEquals(expectedList, result);
        }
    }

    /**
     * Test case: JSON parses to an empty list, CollectionUtils.isEmpty returns true.
     */
    @Test
    public void testAnalysisList_EmptyList() throws Throwable {
        // arrange
        String data = "[]";
        String type = "irrelevant";
        List<String> emptyList = Collections.emptyList();
        // Mock JSON.parseObject to return an empty list
        try (MockedStatic<JSON> jsonMock = Mockito.mockStatic(JSON.class);
            MockedStatic<CollectionUtils> collectionUtilsMock = Mockito.mockStatic(CollectionUtils.class)) {
            jsonMock.when(() -> JSON.parseObject(Mockito.eq(data), Mockito.any(TypeReference.class))).thenReturn(emptyList);
            collectionUtilsMock.when(() -> CollectionUtils.isEmpty(emptyList)).thenReturn(true);
            // act
            List<String> result = service.analysisList(data, type);
            // assert
            assertNotNull(result);
            assertTrue(result.isEmpty());
        }
    }

    /**
     * Test case: JSON parses to null, CollectionUtils.isEmpty returns true.
     */
    @Test
    public void testAnalysisList_NullList() throws Throwable {
        // arrange
        String data = "null";
        String type = "irrelevant";
        // Mock JSON.parseObject to return null
        try (MockedStatic<JSON> jsonMock = Mockito.mockStatic(JSON.class);
            MockedStatic<CollectionUtils> collectionUtilsMock = Mockito.mockStatic(CollectionUtils.class)) {
            jsonMock.when(() -> JSON.parseObject(Mockito.eq(data), Mockito.any(TypeReference.class))).thenReturn(null);
            collectionUtilsMock.when(() -> CollectionUtils.isEmpty(null)).thenReturn(true);
            // act
            List<String> result = service.analysisList(data, type);
            // assert
            assertNotNull(result);
            assertTrue(result.isEmpty());
        }
    }

    /**
     * Test case: JSON.parseObject throws an exception (e.g., invalid JSON).
     */
    @Test(expected = RuntimeException.class)
    public void testAnalysisList_ParseException() throws Throwable {
        // arrange
        String data = "{invalid json}";
        String type = "irrelevant";
        // Mock JSON.parseObject to throw an exception
        try (MockedStatic<JSON> jsonMock = Mockito.mockStatic(JSON.class)) {
            jsonMock.when(() -> JSON.parseObject(Mockito.eq(data), Mockito.any(TypeReference.class))).thenThrow(new RuntimeException("parse error"));
            // act
            service.analysisList(data, type);
            // assert: exception expected
        }
    }
}

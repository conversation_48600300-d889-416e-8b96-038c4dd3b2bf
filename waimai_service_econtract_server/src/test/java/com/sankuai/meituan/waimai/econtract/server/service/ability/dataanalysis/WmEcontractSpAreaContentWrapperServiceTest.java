package com.sankuai.meituan.waimai.econtract.server.service.ability.dataanalysis;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractPdfTypeEnum;
import com.sankuai.meituan.waimai.econtrct.client.domain.applybiz.SpAreaDataQueryRespDTO;
import com.sankuai.meituan.waimai.econtrct.client.domain.applybiz.WmPoiAreaBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.applybiz.WmPoiDeliveryPlanBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.applybiz.WmPoiSpAreaContentBo;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import java.util.Arrays;
import java.util.Collections;
import org.apache.commons.collections4.CollectionUtils;

@RunWith(MockitoJUnitRunner.class)
public class WmEcontractSpAreaContentWrapperServiceTest {

    @Spy
    @InjectMocks
    private WmEcontractSpAreaContentWrapperService service;

    /**
     * Test case for empty map input
     */
    @Test
    public void testAnalysisSpAreaDataWithEmptyMap() throws Throwable {
        // arrange
        Map<String, String> emptyMap = new HashMap<>();
        String areaModule = "delivery";
        // act
        List<SpAreaDataQueryRespDTO> result = service.analysisSpAreaData(emptyMap, areaModule);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for null map input
     */
    @Test
    public void testAnalysisSpAreaDataWithNullMap() throws Throwable {
        // arrange
        Map<String, String> nullMap = null;
        String areaModule = "delivery";
        // act
        List<SpAreaDataQueryRespDTO> result = service.analysisSpAreaData(nullMap, areaModule);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for map with entries but none match the areaModule
     */
    @Test
    public void testAnalysisSpAreaDataWithNoMatchingAreaModule() throws Throwable {
        // arrange
        Map<String, String> spAreaMap = new HashMap<>();
        spAreaMap.put("settle", "someData");
        spAreaMap.put("c1contract", "otherData");
        String areaModule = "delivery";
        // act
        List<SpAreaDataQueryRespDTO> result = service.analysisSpAreaData(spAreaMap, areaModule);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for map with entries containing empty values
     */
    @Test
    public void testAnalysisSpAreaDataWithEmptyValues() throws Throwable {
        // arrange
        Map<String, String> spAreaMap = new HashMap<>();
        spAreaMap.put("delivery", "");
        spAreaMap.put("settle", null);
        String areaModule = "delivery";
        // act
        List<SpAreaDataQueryRespDTO> result = service.analysisSpAreaData(spAreaMap, areaModule);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for map with "default" key entry
     */
    @Test
    public void testAnalysisSpAreaDataWithDefaultKey() throws Throwable {
        // arrange
        Map<String, String> spAreaMap = new HashMap<>();
        spAreaMap.put("default", "someData");
        String areaModule = "delivery";
        List<Object> mockAnalysisResult = new ArrayList<>();
        doReturn(mockAnalysisResult).when(service).analysisData(anyString());
        // act
        List<SpAreaDataQueryRespDTO> result = service.analysisSpAreaData(spAreaMap, areaModule);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("delivery", result.get(0).getKey());
        assertEquals("佣金", result.get(0).getKeyName());
        assertEquals(mockAnalysisResult, result.get(0).getKeyData());
        verify(service).analysisData("someData");
    }

    /**
     * Test case for map with non-default key entries
     */
    @Test
    public void testAnalysisSpAreaDataWithNonDefaultKey() throws Throwable {
        // arrange
        Map<String, String> spAreaMap = new HashMap<>();
        spAreaMap.put("delivery", "someData");
        String areaModule = "delivery";
        List<Object> mockAnalysisResult = new ArrayList<>();
        doReturn(mockAnalysisResult).when(service).analysisData(anyString());
        // act
        List<SpAreaDataQueryRespDTO> result = service.analysisSpAreaData(spAreaMap, areaModule);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("delivery", result.get(0).getKey());
        assertEquals("佣金", result.get(0).getKeyName());
        assertEquals(mockAnalysisResult, result.get(0).getKeyData());
        verify(service).analysisData("someData");
    }

    /**
     * Test case for map with mixed key types (default and non-default)
     */
    @Test
    public void testAnalysisSpAreaDataWithMixedKeys() throws Throwable {
        // arrange
        Map<String, String> spAreaMap = new HashMap<>();
        spAreaMap.put("default", "defaultData");
        spAreaMap.put("delivery_whole_city", "wholeData");
        spAreaMap.put("settle", "settleData");
        String areaModule = "delivery_whole_city";
        List<Object> mockAnalysisResult = new ArrayList<>();
        doReturn(mockAnalysisResult).when(service).analysisData(anyString());
        // act
        List<SpAreaDataQueryRespDTO> result = service.analysisSpAreaData(spAreaMap, areaModule);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("delivery_whole_city", result.get(0).getKey());
        assertEquals("全城送协议", result.get(0).getKeyName());
        assertEquals(mockAnalysisResult, result.get(0).getKeyData());
        verify(service).analysisData("wholeData");
    }

    /**
     * Test case for multiple matching entries
     */
    @Test
    public void testAnalysisSpAreaDataWithMultipleMatchingEntries() throws Throwable {
        // arrange
        Map<String, String> spAreaMap = new HashMap<>();
        spAreaMap.put("delivery", "data1");
        // This will override the previous entry due to Map behavior
        spAreaMap.put("delivery", "data2");
        String areaModule = "delivery";
        List<Object> mockAnalysisResult = new ArrayList<>();
        doReturn(mockAnalysisResult).when(service).analysisData(anyString());
        // act
        List<SpAreaDataQueryRespDTO> result = service.analysisSpAreaData(spAreaMap, areaModule);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("delivery", result.get(0).getKey());
        assertEquals("佣金", result.get(0).getKeyName());
        assertEquals(mockAnalysisResult, result.get(0).getKeyData());
        verify(service).analysisData("data2");
    }

    @Test
    public void testAnalysisDataWithEmptyData() throws Throwable {
        // arrange
        String data = "";
        List<WmPoiSpAreaContentBo> emptyList = new ArrayList<>();
        doReturn(emptyList).when(service).analysisListByClass(eq(data), eq(WmPoiSpAreaContentBo.class));
        // act
        List<WmPoiSpAreaContentBo> result = service.analysisData(data);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(service).analysisListByClass(eq(data), eq(WmPoiSpAreaContentBo.class));
    }

    @Test
    public void testAnalysisDataWithNullAreaBoListAndNullDeliveryPlanBoList() throws Throwable {
        // arrange
        String data = "some data";
        WmPoiSpAreaContentBo contentBo = new WmPoiSpAreaContentBo();
        contentBo.setAreaBoList(null);
        contentBo.setDeliveryPlanBoList(null);
        List<WmPoiSpAreaContentBo> contentBoList = Collections.singletonList(contentBo);
        List<Object> expectedResult = Collections.singletonList(contentBo);
        doReturn(contentBoList).when(service).analysisListByClass(eq(data), eq(WmPoiSpAreaContentBo.class));
        // act
        List<WmPoiSpAreaContentBo> result = service.analysisData(data);
        // assert
        assertNotNull(result);
        assertEquals(expectedResult, result);
        assertNotNull(contentBo.getAreaBoList());
        assertTrue(contentBo.getAreaBoList().isEmpty());
        verify(service).analysisListByClass(eq(data), eq(WmPoiSpAreaContentBo.class));
    }

    @Test
    public void testAnalysisDataWithEmptyAreaBoListAndNonEmptyDeliveryPlanBoList() throws Throwable {
        // arrange
        String data = "some data";
        WmPoiSpAreaContentBo contentBo = new WmPoiSpAreaContentBo();
        contentBo.setAreaBoList(new ArrayList<>());
        List<WmPoiAreaBo> areaBoList = Arrays.asList(new WmPoiAreaBo(), new WmPoiAreaBo());
        WmPoiDeliveryPlanBo deliveryPlanBo = new WmPoiDeliveryPlanBo();
        deliveryPlanBo.setAreaBoList(areaBoList);
        contentBo.setDeliveryPlanBoList(Collections.singletonList(deliveryPlanBo));
        List<WmPoiSpAreaContentBo> contentBoList = Collections.singletonList(contentBo);
        List<Object> expectedResult = Collections.singletonList(contentBo);
        doReturn(contentBoList).when(service).analysisListByClass(eq(data), eq(WmPoiSpAreaContentBo.class));
        // act
        List<WmPoiSpAreaContentBo> result = service.analysisData(data);
        // assert
        assertNotNull(result);
        assertEquals(expectedResult, result);
        assertEquals(areaBoList, contentBo.getAreaBoList());
        verify(service).analysisListByClass(eq(data), eq(WmPoiSpAreaContentBo.class));
    }

    @Test
    public void testAnalysisDataWithNonEmptyAreaBoListAndNonEmptyDeliveryPlanBoList() throws Throwable {
        // arrange
        String data = "some data";
        WmPoiSpAreaContentBo contentBo = new WmPoiSpAreaContentBo();
        List<WmPoiAreaBo> originalAreaBoList = Arrays.asList(new WmPoiAreaBo(), new WmPoiAreaBo());
        contentBo.setAreaBoList(originalAreaBoList);
        List<WmPoiAreaBo> deliveryAreaBoList = Arrays.asList(new WmPoiAreaBo(), new WmPoiAreaBo(), new WmPoiAreaBo());
        WmPoiDeliveryPlanBo deliveryPlanBo = new WmPoiDeliveryPlanBo();
        deliveryPlanBo.setAreaBoList(deliveryAreaBoList);
        contentBo.setDeliveryPlanBoList(Collections.singletonList(deliveryPlanBo));
        List<WmPoiSpAreaContentBo> contentBoList = Collections.singletonList(contentBo);
        List<Object> expectedResult = Collections.singletonList(contentBo);
        doReturn(contentBoList).when(service).analysisListByClass(eq(data), eq(WmPoiSpAreaContentBo.class));
        // act
        List<WmPoiSpAreaContentBo> result = service.analysisData(data);
        // assert
        assertNotNull(result);
        assertEquals(expectedResult, result);
        // areaBoList should not be changed
        assertEquals(originalAreaBoList, contentBo.getAreaBoList());
        verify(service).analysisListByClass(eq(data), eq(WmPoiSpAreaContentBo.class));
    }

    @Test
    public void testAnalysisDataWithNullAreaBoListAndEmptyDeliveryPlanBoList() throws Throwable {
        // arrange
        String data = "some data";
        WmPoiSpAreaContentBo contentBo = new WmPoiSpAreaContentBo();
        contentBo.setAreaBoList(null);
        contentBo.setDeliveryPlanBoList(new ArrayList<>());
        List<WmPoiSpAreaContentBo> contentBoList = Collections.singletonList(contentBo);
        List<Object> expectedResult = Collections.singletonList(contentBo);
        doReturn(contentBoList).when(service).analysisListByClass(eq(data), eq(WmPoiSpAreaContentBo.class));
        // act
        List<WmPoiSpAreaContentBo> result = service.analysisData(data);
        // assert
        assertNotNull(result);
        assertEquals(expectedResult, result);
        assertNotNull(contentBo.getAreaBoList());
        assertTrue(contentBo.getAreaBoList().isEmpty());
        verify(service).analysisListByClass(eq(data), eq(WmPoiSpAreaContentBo.class));
    }

    @Test
    public void testAnalysisDataWithMultipleContentObjects() throws Throwable {
        // arrange
        String data = "some data";
        // First content object with null areaBoList and non-empty deliveryPlanBoList
        WmPoiSpAreaContentBo contentBo1 = new WmPoiSpAreaContentBo();
        contentBo1.setAreaBoList(null);
        List<WmPoiAreaBo> areaBoList1 = Arrays.asList(new WmPoiAreaBo(), new WmPoiAreaBo());
        WmPoiDeliveryPlanBo deliveryPlanBo1 = new WmPoiDeliveryPlanBo();
        deliveryPlanBo1.setAreaBoList(areaBoList1);
        contentBo1.setDeliveryPlanBoList(Collections.singletonList(deliveryPlanBo1));
        // Second content object with empty areaBoList and null deliveryPlanBoList
        WmPoiSpAreaContentBo contentBo2 = new WmPoiSpAreaContentBo();
        contentBo2.setAreaBoList(new ArrayList<>());
        contentBo2.setDeliveryPlanBoList(null);
        // Third content object with non-empty areaBoList and non-empty deliveryPlanBoList
        WmPoiSpAreaContentBo contentBo3 = new WmPoiSpAreaContentBo();
        List<WmPoiAreaBo> originalAreaBoList = Arrays.asList(new WmPoiAreaBo(), new WmPoiAreaBo());
        contentBo3.setAreaBoList(originalAreaBoList);
        List<WmPoiAreaBo> deliveryAreaBoList = Arrays.asList(new WmPoiAreaBo(), new WmPoiAreaBo());
        WmPoiDeliveryPlanBo deliveryPlanBo3 = new WmPoiDeliveryPlanBo();
        deliveryPlanBo3.setAreaBoList(deliveryAreaBoList);
        contentBo3.setDeliveryPlanBoList(Collections.singletonList(deliveryPlanBo3));
        List<WmPoiSpAreaContentBo> contentBoList = Arrays.asList(contentBo1, contentBo2, contentBo3);
        List<Object> expectedResult = Arrays.asList(contentBo1, contentBo2, contentBo3);
        doReturn(contentBoList).when(service).analysisListByClass(eq(data), eq(WmPoiSpAreaContentBo.class));
        // act
        List<WmPoiSpAreaContentBo> result = service.analysisData(data);
        // assert
        assertNotNull(result);
        assertEquals(expectedResult, result);
        // Check contentBo1
        assertEquals(areaBoList1, contentBo1.getAreaBoList());
        // Check contentBo2
        assertNotNull(contentBo2.getAreaBoList());
        assertTrue(contentBo2.getAreaBoList().isEmpty());
        // Check contentBo3
        assertEquals(originalAreaBoList, contentBo3.getAreaBoList());
        verify(service).analysisListByClass(eq(data), eq(WmPoiSpAreaContentBo.class));
    }
}

package com.sankuai.meituan.waimai.econtract.server.template.config.service;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.meituan.it.contract.platform.model.request.template.QueryContractTemplateReq;
import com.meituan.it.contract.platform.model.response.template.QueryContractTemplateResp;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.econtract.server.adapter.UacAuthRemoteServiceAdapter;
import com.sankuai.meituan.waimai.econtract.server.adapter.WmCustomerGlobalEcontractThriftServiceAdapter;
import com.sankuai.meituan.waimai.econtract.server.adapter.WmEmployAdapter;
import com.sankuai.meituan.waimai.econtract.server.adapter.hailuo.ContractTemplateQueryThriftServiceAdapter;
import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractTemplateTagEntityMapper;
import com.sankuai.meituan.waimai.econtract.server.service.ability.EcontractUserAbilityService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractSignPageTemplateService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractUserService;
import com.sankuai.meituan.waimai.econtract.server.service.config.EcontractConfigCommonService;
import com.sankuai.meituan.waimai.econtract.server.service.config.FrameContractConfigInfoBaseService;
import com.sankuai.meituan.waimai.econtract.server.template.config.dao.EcontractTemplateBaseMapper;
import com.sankuai.meituan.waimai.econtract.server.template.config.entity.EcontractTemplateBaseEntity;
import com.sankuai.meituan.waimai.econtract.server.template.config.service.mapperservice.EcontractTemplateBaseMapperService;
import com.sankuai.meituan.waimai.econtrct.client.domain.biz.EcontractUserBo;
import com.sankuai.meituan.waimai.econtrct.client.template.config.domain.EcontractTemplateBaseBo;
import com.sankuai.meituan.waimai.econtrct.client.template.config.exception.EcontractTemplateConfigException;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Properties;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.configuration.MapConfiguration;
import org.apache.commons.lang3.StringUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Test class for EcontractTemplateBaseService
 */
@RunWith(MockitoJUnitRunner.class)
public class EcontractTemplateBaseServiceUpdateTemplateBaseTest {

    @Mock
    private EcontractTemplateBaseMapper econtractTemplateBaseMapper;

    @Mock
    private EcontractTemplateOplogService econtractTemplateOplogService;

    @Mock
    private EcontractTemplateVersionService econtractTemplateVersionService;

    @Mock
    private EcontractTemplateTagEntityMapper econtractTemplateTagEntityMapper;

    @Mock
    private UacAuthRemoteServiceAdapter uacAuthRemoteServiceAdapter;

    @Mock
    private WmEmployAdapter wmEmployAdapter;

    @Mock
    private EcontractUserService econtractUserService;

    @Mock
    private WmCustomerGlobalEcontractThriftServiceAdapter globalEcontractThriftServiceAdapter;

    @Mock
    private EcontractSignPageTemplateService econtractSignPageTemplateService;

    @Mock
    private EcontractService econtractService;

    @Mock
    private EcontractConfigCommonService configCommonService;

    @Mock
    private FrameContractConfigInfoBaseService frameContractConfigInfoBaseService;

    @Mock
    private EcontractUserAbilityService econtractUserAbilityService;

    @Mock
    private ContractTemplateQueryThriftServiceAdapter contractTemplateQueryThriftServiceAdapter;

    @Mock
    private EcontractTemplateBaseMapperService econtractTemplateBaseMapperService;

    @InjectMocks
    private EcontractTemplateBaseService econtractTemplateBaseService;

    private EcontractTemplateBaseBo templateBo;

    private EcontractTemplateBaseEntity oldEntity;

    @BeforeClass
    public static void initConfig() {
        try {
            // Create and add configuration before init
            Properties properties = new Properties();
            properties.setProperty("is_support_integration_hai_luo", "true");
            properties.setProperty("xian_fu_app_code_in_hai_luo", "APP250416000002");
            MapConfiguration config = new MapConfiguration(properties);
            ConfigUtilAdapter.addConfiguration(config);
            ConfigUtilAdapter.init();
        } catch (Exception e) {
            // If already initialized, ignore the exception
        }
    }

    @Before
    public void setUp() {
        // Initialize templateBo with all required fields
        templateBo = new EcontractTemplateBaseBo();
        templateBo.setId(1);
        templateBo.setName("Test Template");
        templateBo.setDescription("Test Description");
        templateBo.setTags(Arrays.asList("tag1", "tag2"));
        templateBo.setWmEcontractUserId(100);
        templateBo.setResponsibleUids(new ArrayList<>(Collections.singletonList(1)));
        templateBo.setResponsibleUnames(new ArrayList<>(Collections.singletonList("test")));
        templateBo.setResponsibleUmisIds(new ArrayList<>(Collections.singletonList("test")));
        templateBo.setCuid(1);
        templateBo.setCuname("testUser");
        templateBo.setCumisId("testMis");
        templateBo.setValid((byte) 1);
        // Initialize oldEntity with all required fields
        oldEntity = EcontractTemplateBaseEntity.builder().id(1).name("Old Template").description("Old Description").tags("oldTag1,oldTag2").econtractUserId(100).responsibleUids("1").responsibleUnames("test").responsibleUmisIds("test").cuid(1).cuname("testUser").cumisId("testMis").valid((byte) 1).build();
    }

    /**
     * Test updating template with null input
     */
    @Test(expected = NullPointerException.class)
    public void testUpdateTemplateBaseWithNullInput() throws Throwable {
        econtractTemplateBaseService.updateTemplateBase(null, 1, "test", "test");
    }

    /**
     * Test updating template with HaiLuo integration disabled
     */
    @Test
    public void testUpdateTemplateBaseWithHaiLuoDisabled() throws Throwable {
        // arrange
        when(econtractTemplateBaseMapper.getTemplateBaseById(1)).thenReturn(oldEntity);
        when(econtractTemplateOplogService.getBaseDiffRemark(any(), any())).thenReturn("changes");
        when(econtractTemplateOplogService.getOpLogFormat(anyInt())).thenReturn("Edit: ");
        // act
        econtractTemplateBaseService.updateTemplateBase(templateBo, 1, "test", "test");
        // assert
        verify(econtractTemplateBaseMapper).updateTemplateBase(any());
        verify(econtractTemplateOplogService).insertEcontractTemplateOplog(anyInt(), anyInt(), anyInt(), anyString(), anyString(), anyString());
    }

    /**
     * Test updating template with HaiLuo integration enabled and removing HaiLuo code
     */
    @Test(expected = EcontractTemplateConfigException.class)
    public void testUpdateTemplateBaseWithHaiLuoEnabledAndRemovingCode() throws Throwable {
        // arrange
        oldEntity.setHailuoTemplateCode("OLD_CODE");
        when(econtractTemplateBaseMapper.getTemplateBaseById(1)).thenReturn(oldEntity);
        // act
        econtractTemplateBaseService.updateTemplateBase(templateBo, 1, "test", "test");
    }

    /**
     * Test updating template with HaiLuo integration enabled and adding invalid HaiLuo code
     */
    @Test(expected = EcontractTemplateConfigException.class)
    public void testUpdateTemplateBaseWithInvalidHaiLuoCode() throws Throwable {
        // arrange
        templateBo.setHaiLuoTemplateCode("NEW_CODE");
        when(econtractTemplateBaseMapper.getTemplateBaseById(1)).thenReturn(oldEntity);
        EcontractUserBo userBo = new EcontractUserBo();
        userBo.setUserName("testUser");
        when(econtractUserAbilityService.queryEcontractUserById(100)).thenReturn(userBo);
        QueryContractTemplateResp resp = new QueryContractTemplateResp();
        resp.setTemplate(Collections.emptyList());
        when(contractTemplateQueryThriftServiceAdapter.queryContractTemplate(any())).thenReturn(resp);
        // act
        econtractTemplateBaseService.updateTemplateBase(templateBo, 1, "test", "test");
    }

    /**
     * Test updating template with HaiLuo integration enabled and valid HaiLuo code
     */
    @Test
    public void testUpdateTemplateBaseWithValidHaiLuoCode() throws Throwable {
        // arrange
        templateBo.setHaiLuoTemplateCode("NEW_CODE");
        when(econtractTemplateBaseMapper.getTemplateBaseById(1)).thenReturn(oldEntity);
        EcontractUserBo userBo = new EcontractUserBo();
        userBo.setUserName("testUser");
        when(econtractUserAbilityService.queryEcontractUserById(100)).thenReturn(userBo);
        QueryContractTemplateResp resp = new QueryContractTemplateResp();
        resp.setTemplate(Collections.singletonList(null));
        when(contractTemplateQueryThriftServiceAdapter.queryContractTemplate(any())).thenReturn(resp);
        when(econtractTemplateBaseMapperService.getTemplateBaseByHaiLuoTemplateCode("NEW_CODE")).thenReturn(null);
        when(econtractTemplateOplogService.getBaseDiffRemark(any(), any())).thenReturn("changes");
        when(econtractTemplateOplogService.getOpLogFormat(anyInt())).thenReturn("Edit: ");
        // act
        econtractTemplateBaseService.updateTemplateBase(templateBo, 1, "test", "test");
        // assert
        verify(econtractTemplateBaseMapper).updateTemplateBase(any());
        verify(econtractTemplateOplogService).insertEcontractTemplateOplog(anyInt(), anyInt(), anyInt(), anyString(), anyString(), anyString());
    }

    /**
     * Test updating template with no changes
     */
    @Test
    public void testUpdateTemplateBaseWithNoChanges() throws Throwable {
        // arrange
        when(econtractTemplateBaseMapper.getTemplateBaseById(1)).thenReturn(oldEntity);
        when(econtractTemplateOplogService.getBaseDiffRemark(any(), any())).thenReturn("");
        // act
        econtractTemplateBaseService.updateTemplateBase(templateBo, 1, "test", "test");
        // assert
        verify(econtractTemplateBaseMapper, never()).updateTemplateBase(any());
        verify(econtractTemplateOplogService, never()).insertEcontractTemplateOplog(anyInt(), anyInt(), anyInt(), anyString(), anyString(), anyString());
    }

    /**
     * Test updating template with name change only
     */
    @Test
    public void testUpdateTemplateBaseWithNameChange() throws Throwable {
        // arrange
        when(econtractTemplateBaseMapper.getTemplateBaseById(1)).thenReturn(oldEntity);
        when(econtractTemplateOplogService.getBaseDiffRemark(any(), any())).thenReturn("[模版名称] Old Template => Test Template");
        when(econtractTemplateOplogService.getOpLogFormat(anyInt())).thenReturn("Edit: ");
        // act
        econtractTemplateBaseService.updateTemplateBase(templateBo, 1, "test", "test");
        // assert
        verify(econtractTemplateBaseMapper).updateTemplateBase(any());
        verify(econtractTemplateOplogService).insertEcontractTemplateOplog(anyInt(), anyInt(), anyInt(), anyString(), anyString(), anyString());
    }

    /**
     * Test updating template with description change only
     */
    @Test
    public void testUpdateTemplateBaseWithDescriptionChange() throws Throwable {
        // arrange
        when(econtractTemplateBaseMapper.getTemplateBaseById(1)).thenReturn(oldEntity);
        when(econtractTemplateOplogService.getBaseDiffRemark(any(), any())).thenReturn("[模版描述] Old Description => Test Description");
        when(econtractTemplateOplogService.getOpLogFormat(anyInt())).thenReturn("Edit: ");
        // act
        econtractTemplateBaseService.updateTemplateBase(templateBo, 1, "test", "test");
        // assert
        verify(econtractTemplateBaseMapper).updateTemplateBase(any());
        verify(econtractTemplateOplogService).insertEcontractTemplateOplog(anyInt(), anyInt(), anyInt(), anyString(), anyString(), anyString());
    }

    /**
     * Test updating template with tags change only
     */
    @Test
    public void testUpdateTemplateBaseWithTagsChange() throws Throwable {
        // arrange
        when(econtractTemplateBaseMapper.getTemplateBaseById(1)).thenReturn(oldEntity);
        when(econtractTemplateOplogService.getBaseDiffRemark(any(), any())).thenReturn("[模版标签] oldTag1,oldTag2 => tag1,tag2");
        when(econtractTemplateOplogService.getOpLogFormat(anyInt())).thenReturn("Edit: ");
        // act
        econtractTemplateBaseService.updateTemplateBase(templateBo, 1, "test", "test");
        // assert
        verify(econtractTemplateBaseMapper).updateTemplateBase(any());
        verify(econtractTemplateOplogService).insertEcontractTemplateOplog(anyInt(), anyInt(), anyInt(), anyString(), anyString(), anyString());
    }

    /**
     * Test updating template with responsible user change
     */
    @Test
    public void testUpdateTemplateBaseWithResponsibleUserChange() throws Throwable {
        // arrange
        templateBo.setResponsibleUids(Arrays.asList(101, 102));
        templateBo.setResponsibleUnames(Arrays.asList("user1", "user2"));
        templateBo.setResponsibleUmisIds(Arrays.asList("mis1", "mis2"));
        oldEntity.setResponsibleUids("100");
        oldEntity.setResponsibleUnames("oldUser");
        oldEntity.setResponsibleUmisIds("oldMis");
        when(econtractTemplateBaseMapper.getTemplateBaseById(1)).thenReturn(oldEntity);
        when(econtractTemplateOplogService.getBaseDiffRemark(any(), any())).thenReturn("[责任人] oldUser,oldMis => user1,mis1,user2,mis2");
        when(econtractTemplateOplogService.getOpLogFormat(anyInt())).thenReturn("Edit: ");
        // act
        econtractTemplateBaseService.updateTemplateBase(templateBo, 1, "test", "test");
        // assert
        verify(econtractTemplateBaseMapper).updateTemplateBase(any());
        verify(econtractTemplateOplogService).insertEcontractTemplateOplog(anyInt(), anyInt(), anyInt(), anyString(), anyString(), anyString());
    }
}

package com.sankuai.meituan.waimai.econtract.server.service.idempotent;

import com.dianping.squirrel.client.StoreKey;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.bo.task.TaskContext;
import com.sankuai.meituan.waimai.econtract.server.bo.task.TaskNodeBo;
import com.sankuai.meituan.waimai.econtract.server.constants.EcontractIdempotentConstant;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordEntity;
import com.sankuai.meituan.waimai.econtract.server.exception.EcontractException;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractMetricService;
import com.sankuai.meituan.waimai.econtract.server.service.idempotent.EcontractIdempotentRedisService.IdempotentKeyInfo;
import com.sankuai.meituan.waimai.econtract.server.utils.RedisKvUtil;
import org.apache.commons.lang3.tuple.Pair;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Optional;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class EcontractIdempotentRedisServiceTest {

    @InjectMocks
    private EcontractIdempotentRedisService econtractIdempotentRedisService;

    @Mock
    private RedisKvUtil redisKvUtil;

    @Mock
    private EcontractMetricService econtractMetricService;

    private static final String TEST_RECORD_KEY = "testRecordKey";

    private static final String TEST_TASK_TYPE = "testTaskType";

    private static final String TEST_TASK_NAME = "testTaskName";

    private static final String EXPECTED_IDEMPOTENT_KEY = TEST_RECORD_KEY + "|" + TEST_TASK_TYPE + "|" + TEST_TASK_NAME;

    /**
     * 测试 Redis 中不存在 storeKey 对应的值的情况
     */
    @Test
    public void testCanExecuteNoValue() throws Exception {
        // arrange
        StoreKey storeKey = new StoreKey("test");
        when(redisKvUtil.get(storeKey)).thenReturn(null);
        // act
        boolean result = econtractIdempotentRedisService.canExecute(storeKey);
        // assert
        assertTrue(result);
    }

    /**
     * 测试 Redis 中存在 storeKey 对应的值，且等于 EcontractIdempotentConstant.ECONTRACT_IDEMPOTENT_REENTRANT 的情况
     */
    @Test
    public void testCanExecuteReentrant() throws Exception {
        // arrange
        StoreKey storeKey = new StoreKey("test");
        when(redisKvUtil.get(storeKey)).thenReturn(EcontractIdempotentConstant.ECONTRACT_IDEMPOTENT_REENTRANT);
        // act
        boolean result = econtractIdempotentRedisService.canExecute(storeKey);
        // assert
        assertTrue(result);
    }

    /**
     * 测试 Redis 中存在 storeKey 对应的值，且不等于 EcontractIdempotentConstant.ECONTRACT_IDEMPOTENT_REENTRANT 的情况
     */
    @Test
    public void testCanExecuteNotReentrant() throws Exception {
        // arrange
        StoreKey storeKey = new StoreKey("test");
        when(redisKvUtil.get(storeKey)).thenReturn("not reentrant");
        // act
        boolean result = econtractIdempotentRedisService.canExecute(storeKey);
        // assert
        assertFalse(result);
    }

    /**
     * 测试在获取 Redis 值的过程中发生异常的情况
     */
    @Test
    public void testCanExecuteException() throws Exception {
        // arrange
        StoreKey storeKey = new StoreKey("test");
        when(redisKvUtil.get(storeKey)).thenThrow(new RuntimeException());
        // act
        boolean result = econtractIdempotentRedisService.canExecute(storeKey);
        // assert
        assertTrue(result);
    }

    /**
     * Test normal case where valid context returns proper IdempotentKeyInfo
     */
    @Test
    public void testGetIdempotentKeyInfoNormalCase() throws Throwable {
        // arrange
        EcontractContext mockContext = mock(EcontractContext.class);
        try (MockedStatic<EcontractIdempotentKeyUtil> mocked = mockStatic(EcontractIdempotentKeyUtil.class)) {
            mocked.when(() -> EcontractIdempotentKeyUtil.getEcontractIdempotentKey(mockContext)).thenReturn(EXPECTED_IDEMPOTENT_KEY);
            // act
            IdempotentKeyInfo result = econtractIdempotentRedisService.getIdempotentKeyInfo(mockContext);
            // assert
            assertNotNull(result);
            assertEquals(EXPECTED_IDEMPOTENT_KEY, result.getIdempotentKey());
            assertEquals(EcontractIdempotentConstant.ECONTRACT_IDEMPOTENT_CATEGORY, result.getStoreKey().getCategory());
            assertEquals(1, result.getStoreKey().getParams().length);
            assertEquals(EXPECTED_IDEMPOTENT_KEY, result.getStoreKey().getParams()[0]);
        }
    }

    /**
     * Test case where EcontractIdempotentKeyUtil returns empty string
     */
    @Test
    public void testGetIdempotentKeyInfoEmptyKey() throws Throwable {
        // arrange
        EcontractContext mockContext = mock(EcontractContext.class);
        try (MockedStatic<EcontractIdempotentKeyUtil> mocked = mockStatic(EcontractIdempotentKeyUtil.class)) {
            mocked.when(() -> EcontractIdempotentKeyUtil.getEcontractIdempotentKey(mockContext)).thenReturn("");
            // act
            IdempotentKeyInfo result = econtractIdempotentRedisService.getIdempotentKeyInfo(mockContext);
            // assert
            assertNull(result);
        }
    }

    /**
     * Test case where EcontractIdempotentKeyUtil returns null
     */
    @Test
    public void testGetIdempotentKeyInfoNullKey() throws Throwable {
        // arrange
        EcontractContext mockContext = mock(EcontractContext.class);
        try (MockedStatic<EcontractIdempotentKeyUtil> mocked = mockStatic(EcontractIdempotentKeyUtil.class)) {
            mocked.when(() -> EcontractIdempotentKeyUtil.getEcontractIdempotentKey(mockContext)).thenReturn(null);
            // act
            IdempotentKeyInfo result = econtractIdempotentRedisService.getIdempotentKeyInfo(mockContext);
            // assert
            assertNull(result);
        }
    }

    /**
     * Test case where EcontractIdempotentKeyUtil throws exception
     */
    @Test
    public void testGetIdempotentKeyInfoExceptionCase() throws Throwable {
        // arrange
        EcontractContext mockContext = mock(EcontractContext.class);
        try (MockedStatic<EcontractIdempotentKeyUtil> mocked = mockStatic(EcontractIdempotentKeyUtil.class)) {
            mocked.when(() -> EcontractIdempotentKeyUtil.getEcontractIdempotentKey(mockContext)).thenThrow(new RuntimeException("Test exception"));
            // act
            IdempotentKeyInfo result = econtractIdempotentRedisService.getIdempotentKeyInfo(mockContext);
            // assert
            assertNull(result);
        }
    }
    /**
     * 异常场景：metricEcontractIdempotentRecord 抛出异常
     */
    @Test()
    public void testMetricIdempotent_NullTaskTypeWithFlowList() throws Throwable {
        // arrange
        EcontractRecordEntity recordEntity = new EcontractRecordEntity();
        recordEntity.setRecordKey("recordKey");

        TaskContext taskContext = new TaskContext();
        // 设置taskType为空字符串，触发异常条件
        taskContext.setTaskType("");

        TaskNodeBo taskNodeBo = new TaskNodeBo();
        taskNodeBo.setTaskName("taskName");

        EcontractContext context = new EcontractContext();
        context.setEcontractRecordEntity(recordEntity);
        context.setTaskContext(taskContext);
        context.setCurrentTaskNode(taskNodeBo);
        // 添加两个元素到flowList，满足CollectionUtils.size(context.getFlowList()) > 1条件
        context.setFlowList(Lists.newArrayList("1"));
        // 直接调用静态方法，让真实方法抛出异常
        try {
            EcontractIdempotentKeyUtil.getEcontractIdempotentKey(context);
        } catch (EcontractException  e){
            assertEquals(EcontractException.IDEMPOTENT_ERROR.longValue(), (long) e.getErrorCode());
            assertEquals("获取幂等键的当前任务类型失败", e.getMessage());
        }
    }

    /**
     * Test case where context is null (should return null)
     */
    @Test
    public void testGetIdempotentKeyInfoNullContext() throws Throwable {
        // arrange - none needed
        // act
        IdempotentKeyInfo result = econtractIdempotentRedisService.getIdempotentKeyInfo(null);
        // assert
        assertNull(result);
    }

    /**
     * 测试 splitEcontractIdempotentKey 方法的正常场景
     */
    @Test
    public void testSplitEcontractIdempotentKeyValidInput() throws Throwable {
        // arrange
        String idempotentKey = "recordKey|taskType|stageTaskName";

        // act
        Pair<String, String> result = EcontractIdempotentKeyUtil.splitEcontractIdempotentKey(idempotentKey);

        // assert
        assertNotNull(result);
        assertEquals("recordKey", result.getLeft());
        assertEquals("stageTaskName", result.getRight());
    }

    /**
     * 测试 splitEcontractIdempotentKey 方法的异常场景：幂等键为空
     */
    @Test(expected = EcontractException.class)
    public void testSplitEcontractIdempotentKeyEmptyKey() throws Throwable {
        // arrange
        String idempotentKey = "";

        // act
        EcontractIdempotentKeyUtil.splitEcontractIdempotentKey(idempotentKey);

        // assert
        // 异常断言在 @Test(expected) 中完成
    }

    /**
     * 测试 splitEcontractIdempotentKey 方法的异常场景：幂等键格式不正确
     */
    @Test(expected = EcontractException.class)
    public void testSplitEcontractIdempotentKeyInvalidFormat() throws Throwable {
        // arrange
        String idempotentKey = "recordKey|taskType";

        // act
        EcontractIdempotentKeyUtil.splitEcontractIdempotentKey(idempotentKey);

        // assert
        // 异常断言在 @Test(expected) 中完成
    }

    /**
     * 测试 splitEcontractIdempotentKey 方法的异常场景：recordKey 或 stageTaskName 为空
     */
    @Test(expected = EcontractException.class)
    public void testSplitEcontractIdempotentKeyEmptyFields() throws Throwable {
        // arrange
        String idempotentKey = "recordKey|stageTaskName";

        // act
        EcontractIdempotentKeyUtil.splitEcontractIdempotentKey(idempotentKey);

        // assert
        // 异常断言在 @Test(expected) 中完成
    }

    /**
     * 测试 splitEcontractIdempotentKey 方法的异常场景：分割过程中发生异常
     */
    @Test(expected = EcontractException.class)
    public void testSplitEcontractIdempotentKeySplitException() throws Throwable {
        // arrange
        String idempotentKey = null;

        // act
        EcontractIdempotentKeyUtil.splitEcontractIdempotentKey(idempotentKey);

        // assert
        // 异常断言在 @Test(expected) 中完成
    }

    /**
     * 正常场景：idempotentKey 有效，分割成功，且 metricEcontractIdempotentRecord 正常执行
     */
    @Test
    public void testMetricIdempotent_NormalCase() throws Throwable {
        try (MockedStatic<EcontractIdempotentKeyUtil> mockedStatic = Mockito.mockStatic(EcontractIdempotentKeyUtil.class)) {
            // arrange
            String idempotentKey = "key1:key2";
            Pair<String, String> pair = Pair.of("key1", "key2");
            mockedStatic.when(() -> EcontractIdempotentKeyUtil.splitEcontractIdempotentKey(idempotentKey)).thenReturn(pair);

            // act
            econtractIdempotentRedisService.metricIdempotent(idempotentKey);

            // assert
            verify(econtractMetricService, times(1)).metricEcontractIdempotentRecord("key1", "key2");
        }
    }

    /**
     * 异常场景：idempotentKey 为 null
     */
    @Test
    public void testMetricIdempotent_IdempotentKeyIsNull() throws Throwable {
        // arrange
        String idempotentKey = null;
        try (MockedStatic<EcontractIdempotentKeyUtil> mockedStatic = Mockito.mockStatic(EcontractIdempotentKeyUtil.class)) {
            mockedStatic.when(() -> EcontractIdempotentKeyUtil.splitEcontractIdempotentKey(idempotentKey))
                    .thenThrow(new IllegalArgumentException("idempotentKey is null"));

            // act
            econtractIdempotentRedisService.metricIdempotent(idempotentKey);

            // assert
            verify(econtractMetricService, never()).metricEcontractIdempotentRecord(anyString(), anyString());
        }
    }

    /**
     * 异常场景：idempotentKey 分割后 left 或 right 为 null
     */
    @Test
    public void testMetricIdempotent_LeftOrRightIsNull() throws Throwable {
        // arrange
        String idempotentKey = "key1:";
        Pair<String, String> pair = Pair.of("key1", null);
        try (MockedStatic<EcontractIdempotentKeyUtil> mockedStatic = Mockito.mockStatic(EcontractIdempotentKeyUtil.class)) {
            mockedStatic.when(() -> EcontractIdempotentKeyUtil.splitEcontractIdempotentKey(idempotentKey))
                    .thenReturn(pair);

            // act
            econtractIdempotentRedisService.metricIdempotent(idempotentKey);

            // assert
            verify(econtractMetricService, never()).metricEcontractIdempotentRecord(anyString(), anyString());
        }
    }

    /**
     * 异常场景：metricEcontractIdempotentRecord 抛出异常
     */
    @Test
    public void testMetricIdempotent_MetricServiceThrowsException() throws Throwable {
        // arrange
        String idempotentKey = "key1:key2";
        Pair<String, String> pair = Pair.of("key1", "key2");
        try (MockedStatic<EcontractIdempotentKeyUtil> mockedStatic = Mockito.mockStatic(EcontractIdempotentKeyUtil.class)) {
            mockedStatic.when(() -> EcontractIdempotentKeyUtil.splitEcontractIdempotentKey(idempotentKey))
                    .thenReturn(pair);
            doThrow(new RuntimeException("Service error")).when(econtractMetricService)
                    .metricEcontractIdempotentRecord("key1", "key2");

            // act
            econtractIdempotentRedisService.metricIdempotent(idempotentKey);

            // assert
            verify(econtractMetricService, times(1)).metricEcontractIdempotentRecord("key1", "key2");
        }
    }

}

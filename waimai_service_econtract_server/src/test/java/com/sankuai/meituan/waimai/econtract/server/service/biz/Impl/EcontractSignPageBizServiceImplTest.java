package com.sankuai.meituan.waimai.econtract.server.service.biz.Impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractSmsDealEntityMapper;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractSignRecordBatchEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractSmsDealEntity;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractRecordService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractSignRecordBatchService;
import com.sankuai.meituan.waimai.econtract.server.service.engine.template.manager.TemplateManager;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.SignH5InfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.applybiz.WmContractContentAggreBo;
import org.apache.commons.lang3.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.TreeSet;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * Test class for EcontractSignPageBizServiceImpl's buildLimitedPoiContractDesc method
 */
@RunWith(MockitoJUnitRunner.class)
public class EcontractSignPageBizServiceImplTest {

    @Mock
    private EcontractRecordService econtractRecordService;

    @Mock
    private EcontractSmsDealEntityMapper smsDealEntityMapper;

    @Mock
    private TemplateManager templateManager;

    @Mock
    private EcontractSignRecordBatchService econtractSignRecordBatchService;

    @InjectMocks
    private EcontractSignPageBizServiceImpl service;

    private static final String TEST_RECORD_KEY = "test_record_key";

    private static final Integer TEST_RECORD_ID = 123;

    private static final Integer TEST_BATCH_ID = 456;

    /**
     * Test subclass that exposes the private method and overrides the dependency on MccConfig
     */
    private static class TestableEcontractSignPageBizServiceImpl extends EcontractSignPageBizServiceImpl {

        private final int limitToShow;

        public TestableEcontractSignPageBizServiceImpl(int limitToShow) {
            this.limitToShow = limitToShow;
        }

        public String buildLimitedPoiContractDescForTest(String poiNameConcat) {
            if (StringUtils.isEmpty(poiNameConcat)) {
                return "";
            }
            TreeSet<String> poiNameSet = new TreeSet<>();
            String[] poiNameArray = poiNameConcat.split("、");
            for (String name : poiNameArray) {
                if (StringUtils.isNotEmpty(name)) {
                    poiNameSet.add(name);
                }
            }
            int totalPoiCount = poiNameSet.size();
            if (totalPoiCount <= limitToShow) {
                StringBuilder sb = new StringBuilder();
                for (String name : poiNameSet) {
                    sb.append(name).append("、");
                }
                return sb.toString();
            }
            StringBuilder limitedDesc = new StringBuilder();
            int added = 0;
            for (String name : poiNameSet) {
                limitedDesc.append(name).append("、");
                added++;
                if (added >= limitToShow) {
                    break;
                }
            }
            limitedDesc.append("...").append("、");
            return limitedDesc.toString();
        }
    }

    /**
     * Test when poiNameConcat is empty
     */
    @Test
    public void testBuildLimitedPoiContractDesc_EmptyInput() throws Throwable {
        // arrange
        TestableEcontractSignPageBizServiceImpl service = new TestableEcontractSignPageBizServiceImpl(20);
        String poiNameConcat = "";
        // act
        String result = service.buildLimitedPoiContractDescForTest(poiNameConcat);
        // assert
        assertEquals("", result);
    }

    /**
     * Test when poiNameConcat has only one poi name
     */
    @Test
    public void testBuildLimitedPoiContractDesc_SinglePoiName() throws Throwable {
        // arrange
        TestableEcontractSignPageBizServiceImpl service = new TestableEcontractSignPageBizServiceImpl(20);
        String poiNameConcat = "TestPoi";
        // act
        String result = service.buildLimitedPoiContractDescForTest(poiNameConcat);
        // assert
        assertEquals("TestPoi、", result);
    }

    /**
     * Test when poiNameConcat has duplicate poi names
     */
    @Test
    public void testBuildLimitedPoiContractDesc_DuplicatePoiNames() throws Throwable {
        // arrange
        TestableEcontractSignPageBizServiceImpl service = new TestableEcontractSignPageBizServiceImpl(20);
        String poiNameConcat = "TestPoi、TestPoi、AnotherPoi";
        // act
        String result = service.buildLimitedPoiContractDescForTest(poiNameConcat);
        // assert
        assertEquals("AnotherPoi、TestPoi、", result);
    }

    /**
     * Test when poiNameConcat has fewer poi names than the limit
     */
    @Test
    public void testBuildLimitedPoiContractDesc_FewerThanLimit() throws Throwable {
        // arrange
        TestableEcontractSignPageBizServiceImpl service = new TestableEcontractSignPageBizServiceImpl(20);
        String poiNameConcat = "Poi1、Poi2、Poi3";
        // act
        String result = service.buildLimitedPoiContractDescForTest(poiNameConcat);
        // assert
        assertEquals("Poi1、Poi2、Poi3、", result);
    }

    /**
     * Test when poiNameConcat has exactly the limit number of poi names
     */
    @Test
    public void testBuildLimitedPoiContractDesc_ExactlyLimit() throws Throwable {
        // arrange
        int limitToShow = 5;
        TestableEcontractSignPageBizServiceImpl service = new TestableEcontractSignPageBizServiceImpl(limitToShow);
        StringBuilder sb = new StringBuilder();
        for (int i = 1; i <= limitToShow; i++) {
            sb.append("Poi").append(i).append("、");
        }
        String poiNameConcat = sb.toString();
        // act
        String result = service.buildLimitedPoiContractDescForTest(poiNameConcat);
        // assert
        // Should return all poi names without "..."
        assertTrue(!result.contains("..."));
        for (int i = 1; i <= limitToShow; i++) {
            assertTrue(result.contains("Poi" + i + "、"));
        }
    }

    /**
     * Test when poiNameConcat has more poi names than the limit
     */
    @Test
    public void testBuildLimitedPoiContractDesc_MoreThanLimit() throws Throwable {
        // arrange
        int limitToShow = 5;
        TestableEcontractSignPageBizServiceImpl service = new TestableEcontractSignPageBizServiceImpl(limitToShow);
        StringBuilder sb = new StringBuilder();
        for (int i = 1; i <= limitToShow + 5; i++) {
            sb.append("Poi").append(i).append("、");
        }
        String poiNameConcat = sb.toString();
        // act
        String result = service.buildLimitedPoiContractDescForTest(poiNameConcat);
        // assert
        // Should contain exactly limitToShow poi names plus "..."
        assertTrue(result.contains("...、"));
        // Count the number of poi names in the result (excluding "...")
        int count = 0;
        for (int i = 1; i <= limitToShow + 5; i++) {
            if (result.contains("Poi" + i + "、")) {
                count++;
            }
        }
        assertEquals(limitToShow, count);
    }

    /**
     * Test when poiNameConcat has empty poi names (consecutive delimiters)
     */
    @Test
    public void testBuildLimitedPoiContractDesc_EmptyPoiNames() throws Throwable {
        // arrange
        TestableEcontractSignPageBizServiceImpl service = new TestableEcontractSignPageBizServiceImpl(20);
        String poiNameConcat = "Poi1、、Poi2、、Poi3";
        // act
        String result = service.buildLimitedPoiContractDescForTest(poiNameConcat);
        // assert
        assertEquals("Poi1、Poi2、Poi3、", result);
    }

    /**
     * Test when poiNameConcat has special characters in poi names
     */
    @Test
    public void testBuildLimitedPoiContractDesc_SpecialCharacters() throws Throwable {
        // arrange
        TestableEcontractSignPageBizServiceImpl service = new TestableEcontractSignPageBizServiceImpl(20);
        String poiNameConcat = "Poi!@#、Poi$%^、Poi&*()";
        // act
        String result = service.buildLimitedPoiContractDescForTest(poiNameConcat);
        // assert
        assertTrue(result.contains("Poi!@#、"));
        assertTrue(result.contains("Poi$%^、"));
        assertTrue(result.contains("Poi&*()、"));
    }

    @Before
    public void setUp() {
        // Initialize ConfigUtilAdapter to avoid IllegalStateException
        try {
            ConfigUtilAdapter.init();
        } catch (Exception e) {
            // Ignore if already initialized
        }
    }

    @Test
    public void testQueryEcontractContentAggreList_RecordNotFound() throws Throwable {
        // arrange
        when(econtractRecordService.queryRecordByRecordKey(TEST_RECORD_KEY)).thenReturn(null);
        // Mock the isForceAllOp method to handle null recordEntity
        // act & assert - expect NPE because the code calls isForceAllOp(recordEntity.getRecordBatchId()) before null check
        try {
            service.queryEcontractContentAggreList(TEST_RECORD_KEY);
            fail("Expected NullPointerException");
        } catch (NullPointerException e) {
            // This is expected behavior based on the actual code
            verify(econtractRecordService).queryRecordByRecordKey(TEST_RECORD_KEY);
        }
    }

    @Test
    public void testQueryEcontractContentAggreList_C1ContractType() throws Throwable {
        // arrange
        EcontractRecordEntity recordEntity = new EcontractRecordEntity();
        recordEntity.setId(TEST_RECORD_ID);
        recordEntity.setRecordBatchId(TEST_BATCH_ID);
        recordEntity.setSaveUrl("{\"c1contract\":\"url1\",\"default\":\"default_url\"}");
        EcontractContext context = new EcontractContext();
        java.util.List<StageBatchInfoBo> stageBatchList = new java.util.ArrayList<StageBatchInfoBo>();
        context.setStageBatchInfoBoList(stageBatchList);
        StageBatchInfoBo stageBatchInfoBo = new StageBatchInfoBo();
        stageBatchInfoBo.setStageName("create_pdf");
        java.util.Map<String, java.util.List<PdfContentInfoBo>> pdfMap = new java.util.HashMap<String, java.util.List<PdfContentInfoBo>>();
        java.util.List<PdfContentInfoBo> pdfContentList = new java.util.ArrayList<PdfContentInfoBo>();
        pdfContentList.add(new PdfContentInfoBo());
        pdfMap.put("c1contract", pdfContentList);
        stageBatchInfoBo.setPdfContentInfoBoMap(pdfMap);
        context.getStageBatchInfoBoList().add(stageBatchInfoBo);
        recordEntity.setEcontractRecordContext(JSON.toJSONString(context));
        EcontractSmsDealEntity dealEntity = new EcontractSmsDealEntity();
        dealEntity.setEcontractRecordId(TEST_RECORD_ID);
        dealEntity.setDealVersion("task_1");
        dealEntity.setCtime(new java.util.Date());
        // 1 hour from now
        dealEntity.setExpireTime(System.currentTimeMillis() / 1000 + 3600);
        SignH5InfoBo signH5InfoBo = new SignH5InfoBo();
        signH5InfoBo.setViewStage("stage1");
        java.util.Map<String, String> pdfUrlMap = new java.util.HashMap<String, String>();
        pdfUrlMap.put("c1contract", "url1");
        java.util.List<EcontractSmsDealEntity> dealEntityList = new java.util.ArrayList<EcontractSmsDealEntity>();
        dealEntityList.add(dealEntity);
        when(econtractRecordService.queryRecordByRecordKey(TEST_RECORD_KEY)).thenReturn(recordEntity);
        when(econtractSignRecordBatchService.selectByPrimaryKey(TEST_BATCH_ID)).thenReturn(null);
        when(econtractRecordService.parseMultiPdfUrl(recordEntity.getSaveUrl())).thenReturn(pdfUrlMap);
        // act
        java.util.List<WmContractContentAggreBo> result = service.queryEcontractContentAggreList(TEST_RECORD_KEY);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("美团与客户合同", result.get(0).getContractName());
        assertEquals("适用于 全部门店", result.get(0).getContractDesc());
    }

    @Test
    public void testQueryEcontractContentAggreList_C2ContractType() throws Throwable {
        // arrange
        EcontractRecordEntity recordEntity = new EcontractRecordEntity();
        recordEntity.setId(TEST_RECORD_ID);
        recordEntity.setRecordBatchId(TEST_BATCH_ID);
        recordEntity.setSaveUrl("{\"c2contract\":\"url1\",\"default\":\"default_url\"}");
        EcontractContext context = new EcontractContext();
        java.util.List<StageBatchInfoBo> stageBatchList = new java.util.ArrayList<StageBatchInfoBo>();
        context.setStageBatchInfoBoList(stageBatchList);
        StageBatchInfoBo stageBatchInfoBo = new StageBatchInfoBo();
        stageBatchInfoBo.setStageName("create_pdf");
        PdfContentInfoBo pdfContentInfoBo = new PdfContentInfoBo();
        java.util.Map<String, String> pdfMetaContent = new java.util.HashMap<String, String>();
        pdfMetaContent.put("agentShowName", "Test Agent");
        pdfMetaContent.put("agentShowId", "123");
        pdfContentInfoBo.setPdfMetaContent(pdfMetaContent);
        java.util.Map<String, java.util.List<PdfContentInfoBo>> pdfMap = new java.util.HashMap<String, java.util.List<PdfContentInfoBo>>();
        java.util.List<PdfContentInfoBo> pdfContentList = new java.util.ArrayList<PdfContentInfoBo>();
        pdfContentList.add(pdfContentInfoBo);
        pdfMap.put("c2contract", pdfContentList);
        stageBatchInfoBo.setPdfContentInfoBoMap(pdfMap);
        context.getStageBatchInfoBoList().add(stageBatchInfoBo);
        recordEntity.setEcontractRecordContext(JSON.toJSONString(context));
        EcontractSmsDealEntity dealEntity = new EcontractSmsDealEntity();
        dealEntity.setEcontractRecordId(TEST_RECORD_ID);
        dealEntity.setDealVersion("task_1");
        dealEntity.setCtime(new java.util.Date());
        // 1 hour from now
        dealEntity.setExpireTime(System.currentTimeMillis() / 1000 + 3600);
        SignH5InfoBo signH5InfoBo = new SignH5InfoBo();
        signH5InfoBo.setViewStage("stage1");
        java.util.Map<String, String> pdfUrlMap = new java.util.HashMap<String, String>();
        pdfUrlMap.put("c2contract", "url1");
        java.util.List<EcontractSmsDealEntity> dealEntityList = new java.util.ArrayList<EcontractSmsDealEntity>();
        dealEntityList.add(dealEntity);
        when(econtractRecordService.queryRecordByRecordKey(TEST_RECORD_KEY)).thenReturn(recordEntity);
        when(econtractSignRecordBatchService.selectByPrimaryKey(TEST_BATCH_ID)).thenReturn(null);
        when(econtractRecordService.parseMultiPdfUrl(recordEntity.getSaveUrl())).thenReturn(pdfUrlMap);
        // act
        java.util.List<WmContractContentAggreBo> result = service.queryEcontractContentAggreList(TEST_RECORD_KEY);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("合作商与客户合同", result.get(0).getContractName());
        assertTrue(result.get(0).getContractDesc().contains("Test Agent"));
    }

    @Test
    public void testQueryEcontractContentAggreList_FeeContractType() throws Throwable {
        // arrange
        EcontractRecordEntity recordEntity = new EcontractRecordEntity();
        recordEntity.setId(TEST_RECORD_ID);
        recordEntity.setRecordBatchId(TEST_BATCH_ID);
        recordEntity.setSaveUrl("{\"FEE_CONTRACT\":\"url1\",\"default\":\"default_url\"}");
        EcontractContext context = new EcontractContext();
        java.util.List<StageBatchInfoBo> stageBatchList = new java.util.ArrayList<StageBatchInfoBo>();
        context.setStageBatchInfoBoList(stageBatchList);
        StageBatchInfoBo stageBatchInfoBo = new StageBatchInfoBo();
        stageBatchInfoBo.setStageName("create_pdf");
        java.util.Map<String, java.util.List<PdfContentInfoBo>> pdfMap = new java.util.HashMap<String, java.util.List<PdfContentInfoBo>>();
        java.util.List<PdfContentInfoBo> pdfContentList = new java.util.ArrayList<PdfContentInfoBo>();
        pdfContentList.add(new PdfContentInfoBo());
        pdfMap.put("FEE_CONTRACT", pdfContentList);
        stageBatchInfoBo.setPdfContentInfoBoMap(pdfMap);
        context.getStageBatchInfoBoList().add(stageBatchInfoBo);
        recordEntity.setEcontractRecordContext(JSON.toJSONString(context));
        EcontractSmsDealEntity dealEntity = new EcontractSmsDealEntity();
        dealEntity.setEcontractRecordId(TEST_RECORD_ID);
        dealEntity.setDealVersion("task_1");
        dealEntity.setCtime(new java.util.Date());
        // 1 hour from now
        dealEntity.setExpireTime(System.currentTimeMillis() / 1000 + 3600);
        SignH5InfoBo signH5InfoBo = new SignH5InfoBo();
        signH5InfoBo.setViewStage("stage1");
        java.util.Map<String, String> pdfUrlMap = new java.util.HashMap<String, String>();
        pdfUrlMap.put("FEE_CONTRACT", "url1");
        java.util.List<EcontractSmsDealEntity> dealEntityList = new java.util.ArrayList<EcontractSmsDealEntity>();
        dealEntityList.add(dealEntity);
        when(econtractRecordService.queryRecordByRecordKey(TEST_RECORD_KEY)).thenReturn(recordEntity);
        when(econtractSignRecordBatchService.selectByPrimaryKey(TEST_BATCH_ID)).thenReturn(null);
        when(econtractRecordService.parseMultiPdfUrl(recordEntity.getSaveUrl())).thenReturn(pdfUrlMap);
        // act
        java.util.List<WmContractContentAggreBo> result = service.queryEcontractContentAggreList(TEST_RECORD_KEY);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("FEE_CONTRACT", result.get(0).getContractTaskType());
    }

    @Test(expected = JSONException.class)
    public void testQueryEcontractContentAggreList_InvalidJsonContext() throws Throwable {
        // arrange
        EcontractRecordEntity recordEntity = new EcontractRecordEntity();
        recordEntity.setId(TEST_RECORD_ID);
        recordEntity.setRecordBatchId(TEST_BATCH_ID);
        recordEntity.setEcontractRecordContext("invalid json");
        EcontractSmsDealEntity dealEntity = new EcontractSmsDealEntity();
        dealEntity.setEcontractRecordId(TEST_RECORD_ID);
        dealEntity.setDealVersion("task_1");
        dealEntity.setCtime(new java.util.Date());
        // 1 hour from now
        dealEntity.setExpireTime(System.currentTimeMillis() / 1000 + 3600);
        java.util.List<EcontractSmsDealEntity> dealEntityList = new java.util.ArrayList<EcontractSmsDealEntity>();
        dealEntityList.add(dealEntity);
        when(econtractRecordService.queryRecordByRecordKey(TEST_RECORD_KEY)).thenReturn(recordEntity);
        when(econtractSignRecordBatchService.selectByPrimaryKey(TEST_BATCH_ID)).thenReturn(null);
        // act
        service.queryEcontractContentAggreList(TEST_RECORD_KEY);
    }

    @Test
    public void testQueryEcontractContentAggreList_ForceAllOpTrue() throws Throwable {
        // arrange
        EcontractRecordEntity recordEntity = new EcontractRecordEntity();
        recordEntity.setId(TEST_RECORD_ID);
        recordEntity.setRecordBatchId(TEST_BATCH_ID);
        recordEntity.setSaveUrl("{\"c1contract\":\"url1\",\"default\":\"default_url\"}");
        EcontractContext context = new EcontractContext();
        java.util.List<StageBatchInfoBo> stageBatchList = new java.util.ArrayList<StageBatchInfoBo>();
        context.setStageBatchInfoBoList(stageBatchList);
        StageBatchInfoBo stageBatchInfoBo = new StageBatchInfoBo();
        stageBatchInfoBo.setStageName("create_pdf");
        java.util.Map<String, java.util.List<PdfContentInfoBo>> pdfMap = new java.util.HashMap<String, java.util.List<PdfContentInfoBo>>();
        java.util.List<PdfContentInfoBo> pdfContentList = new java.util.ArrayList<PdfContentInfoBo>();
        pdfContentList.add(new PdfContentInfoBo());
        pdfMap.put("c1contract", pdfContentList);
        stageBatchInfoBo.setPdfContentInfoBoMap(pdfMap);
        context.getStageBatchInfoBoList().add(stageBatchInfoBo);
        recordEntity.setEcontractRecordContext(JSON.toJSONString(context));
        EcontractSmsDealEntity dealEntity = new EcontractSmsDealEntity();
        dealEntity.setEcontractRecordId(TEST_RECORD_ID);
        dealEntity.setDealVersion("task_1");
        dealEntity.setCtime(new java.util.Date());
        // 1 hour from now
        dealEntity.setExpireTime(System.currentTimeMillis() / 1000 + 3600);
        SignH5InfoBo signH5InfoBo = new SignH5InfoBo();
        signH5InfoBo.setViewStage("stage1");
        EcontractSignRecordBatchEntity batchEntity = new EcontractSignRecordBatchEntity();
        batchEntity.setForceAllOp(1);
        java.util.Map<String, String> pdfUrlMap = new java.util.HashMap<String, String>();
        pdfUrlMap.put("c1contract", "url1");
        java.util.List<EcontractSmsDealEntity> dealEntityList = new java.util.ArrayList<EcontractSmsDealEntity>();
        dealEntityList.add(dealEntity);
        when(econtractRecordService.queryRecordByRecordKey(TEST_RECORD_KEY)).thenReturn(recordEntity);
        when(econtractSignRecordBatchService.selectByPrimaryKey(TEST_BATCH_ID)).thenReturn(batchEntity);
        when(econtractRecordService.parseMultiPdfUrl(recordEntity.getSaveUrl())).thenReturn(pdfUrlMap);
        // act
        java.util.List<WmContractContentAggreBo> result = service.queryEcontractContentAggreList(TEST_RECORD_KEY);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.get(0).getForceAllOp());
    }

    @Test
    public void testQueryEcontractContentAggreList_CommonConfigFrameContractType() throws Throwable {
        // arrange
        EcontractRecordEntity recordEntity = new EcontractRecordEntity();
        recordEntity.setId(TEST_RECORD_ID);
        recordEntity.setRecordBatchId(TEST_BATCH_ID);
        recordEntity.setSaveUrl("{\"common_config_frame_contract_agreement\":\"url1\",\"default\":\"default_url\"}");
        EcontractContext context = new EcontractContext();
        java.util.List<StageBatchInfoBo> stageBatchList = new java.util.ArrayList<StageBatchInfoBo>();
        context.setStageBatchInfoBoList(stageBatchList);
        StageBatchInfoBo stageBatchInfoBo = new StageBatchInfoBo();
        stageBatchInfoBo.setStageName("create_pdf");
        PdfContentInfoBo pdfContentInfoBo = new PdfContentInfoBo();
        pdfContentInfoBo.setContractName("Test Contract");
        java.util.Map<String, java.util.List<PdfContentInfoBo>> pdfMap = new java.util.HashMap<String, java.util.List<PdfContentInfoBo>>();
        java.util.List<PdfContentInfoBo> pdfContentList = new java.util.ArrayList<PdfContentInfoBo>();
        pdfContentList.add(pdfContentInfoBo);
        pdfMap.put("common_config_frame_contract_agreement", pdfContentList);
        stageBatchInfoBo.setPdfContentInfoBoMap(pdfMap);
        context.getStageBatchInfoBoList().add(stageBatchInfoBo);
        recordEntity.setEcontractRecordContext(JSON.toJSONString(context));
        EcontractSmsDealEntity dealEntity = new EcontractSmsDealEntity();
        dealEntity.setEcontractRecordId(TEST_RECORD_ID);
        dealEntity.setDealVersion("task_1");
        dealEntity.setCtime(new java.util.Date());
        // 1 hour from now
        dealEntity.setExpireTime(System.currentTimeMillis() / 1000 + 3600);
        SignH5InfoBo signH5InfoBo = new SignH5InfoBo();
        signH5InfoBo.setViewStage("stage1");
        java.util.Map<String, String> pdfUrlMap = new java.util.HashMap<String, String>();
        pdfUrlMap.put("common_config_frame_contract_agreement", "url1");
        java.util.List<EcontractSmsDealEntity> dealEntityList = new java.util.ArrayList<EcontractSmsDealEntity>();
        dealEntityList.add(dealEntity);
        when(econtractRecordService.queryRecordByRecordKey(TEST_RECORD_KEY)).thenReturn(recordEntity);
        when(econtractSignRecordBatchService.selectByPrimaryKey(TEST_BATCH_ID)).thenReturn(null);
        when(econtractRecordService.parseMultiPdfUrl(recordEntity.getSaveUrl())).thenReturn(pdfUrlMap);
        // act
        java.util.List<WmContractContentAggreBo> result = service.queryEcontractContentAggreList(TEST_RECORD_KEY);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("Test Contract", result.get(0).getContractName());
    }

    @Test
    public void testQueryEcontractContentAggreList_DefaultPdfType() throws Throwable {
        try (MockedStatic<ConfigUtilAdapter> mockedConfig = mockStatic(ConfigUtilAdapter.class)) {
            // arrange
            mockedConfig.when(() -> ConfigUtilAdapter.getString(anyString(), anyString())).thenReturn("86400");
            mockedConfig.when(() -> ConfigUtilAdapter.getInt(anyString(), anyInt())).thenReturn(20);
            EcontractRecordEntity recordEntity = new EcontractRecordEntity();
            recordEntity.setId(TEST_RECORD_ID);
            recordEntity.setRecordBatchId(TEST_BATCH_ID);
            recordEntity.setSaveUrl("{\"default\":\"default_url\"}");
            EcontractContext context = new EcontractContext();
            java.util.List<StageBatchInfoBo> stageBatchList = new java.util.ArrayList<StageBatchInfoBo>();
            context.setStageBatchInfoBoList(stageBatchList);
            StageBatchInfoBo stageBatchInfoBo = new StageBatchInfoBo();
            stageBatchInfoBo.setStageName("create_pdf");
            PdfContentInfoBo pdfContentInfoBo = new PdfContentInfoBo();
            java.util.Map<String, String> pdfMetaContent = new java.util.HashMap<String, String>();
            pdfMetaContent.put("poiName", "Test Poi");
            pdfContentInfoBo.setPdfMetaContent(pdfMetaContent);
            java.util.Map<String, java.util.List<PdfContentInfoBo>> pdfMap = new java.util.HashMap<String, java.util.List<PdfContentInfoBo>>();
            java.util.List<PdfContentInfoBo> pdfContentList = new java.util.ArrayList<PdfContentInfoBo>();
            pdfContentList.add(pdfContentInfoBo);
            pdfMap.put("default", pdfContentList);
            stageBatchInfoBo.setPdfContentInfoBoMap(pdfMap);
            context.getStageBatchInfoBoList().add(stageBatchInfoBo);
            recordEntity.setEcontractRecordContext(JSON.toJSONString(context));
            EcontractSmsDealEntity dealEntity = new EcontractSmsDealEntity();
            dealEntity.setEcontractRecordId(TEST_RECORD_ID);
            dealEntity.setDealVersion("task_1");
            dealEntity.setCtime(new java.util.Date());
            // 1 hour from now
            dealEntity.setExpireTime(System.currentTimeMillis() / 1000 + 3600);
            SignH5InfoBo signH5InfoBo = new SignH5InfoBo();
            signH5InfoBo.setViewStage("stage1");
            java.util.Map<String, String> pdfUrlMap = new java.util.HashMap<String, String>();
            pdfUrlMap.put("default", "default_url");
            java.util.List<EcontractSmsDealEntity> dealEntityList = new java.util.ArrayList<EcontractSmsDealEntity>();
            dealEntityList.add(dealEntity);
            when(econtractRecordService.queryRecordByRecordKey(TEST_RECORD_KEY)).thenReturn(recordEntity);
            when(econtractSignRecordBatchService.selectByPrimaryKey(TEST_BATCH_ID)).thenReturn(null);
            when(smsDealEntityMapper.queryDealByRecordId(TEST_RECORD_ID)).thenReturn(dealEntityList);
            when(templateManager.querySignH5InoByRecordIdAndTaskId(TEST_RECORD_ID, 1)).thenReturn(signH5InfoBo);
            when(econtractRecordService.parseMultiPdfUrl(recordEntity.getSaveUrl())).thenReturn(pdfUrlMap);
            // act
            java.util.List<WmContractContentAggreBo> result = service.queryEcontractContentAggreList(TEST_RECORD_KEY);
            // assert
            assertNotNull(result);
            assertEquals(1, result.size());
            assertTrue(result.get(0).getContractDesc().contains("Test Poi"));
            verify(econtractRecordService, times(2)).queryRecordByRecordKey(TEST_RECORD_KEY);
        }
    }
}

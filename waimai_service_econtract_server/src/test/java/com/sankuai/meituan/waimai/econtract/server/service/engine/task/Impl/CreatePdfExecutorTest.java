package com.sankuai.meituan.waimai.econtract.server.service.engine.task.Impl;

import com.sankuai.meituan.waimai.econtract.server.bo.task.TaskContext;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractFtlTemplateEntity;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractGlobalInfoService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractPdfService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.GlobalEcontractService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.Impl.EcontractFtlTemplateServiceImpl;
import com.sankuai.meituan.waimai.econtract.server.template.config.service.EcontractTemplateBaseService;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.dto.SignContext;
import com.sankuai.meituan.waimai.econtrct.client.template.config.domain.EcontractTemplateVersionBo;
import com.sankuai.meituan.waimai.econtrct.client.template.config.domain.EcontractTemplateVersionSimpleBo;
import com.sankuai.meituan.waimai.econtrct.client.template.config.service.EcontractTemplateConfigThriftService;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.verify;

/**
 * 创建pdf的单元测试类
 *
 * @Author: wangyongfang
 * @Date: 2024-03-21
 */
@RunWith(MockitoJUnitRunner.class)
public class CreatePdfExecutorTest {

    @InjectMocks
    private CreatePdfExecutor createPdfExecutor;
    @Mock
    private EcontractPdfService econtractPdfService;
    @Mock
    private EcontractFtlTemplateServiceImpl econtractFtlTemplateService;
    @Mock
    private EcontractTemplateConfigThriftService econtractTemplateConfigThriftService;
    @Mock
    private EcontractGlobalInfoService econtractGlobalInfoService;

    @Mock
    private GlobalEcontractService globalEcontractService;

    @Mock
    private EcontractTemplateBaseService econtractTemplateBaseService;

    @Test
    public void testGenePdfWithParamNormal() throws Exception {
        // arrange
        TaskContext taskContext = spy(TaskContext.class);
        StageInfoBo stageInfoBo = mock(StageInfoBo.class);
        taskContext.setStageInfoBo(stageInfoBo);
        String recordKey = "recordKey";
        SignContext signContext = mock(SignContext.class);
        List<PdfContentInfoBo> pdfContentInfoBoList = new ArrayList<>();
        PdfContentInfoBo pdfContentInfoBo = mock(PdfContentInfoBo.class);
        pdfContentInfoBoList.add(pdfContentInfoBo);
        when(taskContext.getStageInfoBo().getPdfContentInfoBoList()).thenReturn(pdfContentInfoBoList);
        when(pdfContentInfoBo.getPdfTemplateName()).thenReturn("templateName");
        EcontractFtlTemplateEntity econtractFtlTemplateEntity = mock(EcontractFtlTemplateEntity.class);
        when(econtractFtlTemplateService.batchSelectMapByNameList(anyList())).thenReturn(Collections.singletonMap("templateName", econtractFtlTemplateEntity));
        when(econtractFtlTemplateEntity.getFtlTemplate()).thenReturn("ftlTemplate");
        when(econtractPdfService.createPdf(anyList())).thenReturn("pdfUrl");

        // act
        String result = createPdfExecutor.genePdfWithParam(taskContext, recordKey, signContext);
        // assert
        assertEquals("pdfUrl", result);
        verify(pdfContentInfoBo).setFtlInfo("ftlTemplate");
        verify(econtractGlobalInfoService, times(1)).insert(any());
    }

    /**
     * 测试genePdfWithParam方法，当pdfContentInfoBo的pdfTemplateId不为空且大于0时
     */
    @Test
    public void testGenePdfWithParam_PdfTemplateIdNotNull() throws Throwable {
        // arrange
        TaskContext taskContext = spy(TaskContext.class);
        StageInfoBo stageInfoBo = mock(StageInfoBo.class);
        when(taskContext.getStageInfoBo()).thenReturn(stageInfoBo);
        List<PdfContentInfoBo> pdfContentInfoBoList = new ArrayList<>();
        PdfContentInfoBo pdfContentInfoBo = mock(PdfContentInfoBo.class);
        pdfContentInfoBoList.add(pdfContentInfoBo);
        when(stageInfoBo.getPdfContentInfoBoList()).thenReturn(pdfContentInfoBoList);
        when(pdfContentInfoBo.getPdfTemplateId()).thenReturn(1);
        EcontractTemplateVersionSimpleBo econtractTemplateVersionSimpleBo = mock(EcontractTemplateVersionSimpleBo.class);
        when(econtractTemplateConfigThriftService.getReleasedTemplateVersionSimple(anyInt())).thenReturn(econtractTemplateVersionSimpleBo);
        when(econtractTemplateVersionSimpleBo.getVersion()).thenReturn(1);
        EcontractTemplateVersionBo econtractTemplateVersionBo = mock(EcontractTemplateVersionBo.class);
        when(econtractTemplateConfigThriftService.getTemplateVersionAndCheck(anyInt(), anyInt())).thenReturn(econtractTemplateVersionBo);
        when(econtractTemplateVersionBo.getTargetContent()).thenReturn("targetContent");
        when(econtractPdfService.createPdf(anyList())).thenReturn("pdfUrl");
        String recordKey = "recordKey";
        SignContext signContext = mock(SignContext.class);

        // act
        String result = createPdfExecutor.genePdfWithParam(taskContext, recordKey, signContext);

        // assert
        assertEquals("pdfUrl", result);
        verify(pdfContentInfoBo).setFtlInfo("targetContent");
    }

    /**
     * 测试genePdfWithParam方法，当pdfContentInfoBo的pdfTemplateId为空时
     */
    @Test
    public void testGenePdfWithParam_PdfTemplateIdNull() throws Throwable {
        // arrange
        TaskContext taskContext = mock(TaskContext.class);
        StageInfoBo stageInfoBo = mock(StageInfoBo.class);
        when(taskContext.getStageInfoBo()).thenReturn(stageInfoBo);
        List<PdfContentInfoBo> pdfContentInfoBoList = new ArrayList<>();
        PdfContentInfoBo pdfContentInfoBo = mock(PdfContentInfoBo.class);
        pdfContentInfoBoList.add(pdfContentInfoBo);
        when(stageInfoBo.getPdfContentInfoBoList()).thenReturn(pdfContentInfoBoList);
        when(pdfContentInfoBo.getPdfTemplateName()).thenReturn("templateName");
        EcontractFtlTemplateEntity econtractFtlTemplateEntity = mock(EcontractFtlTemplateEntity.class);
        when(econtractFtlTemplateService.batchSelectMapByNameList(anyList())).thenReturn(Collections.singletonMap("templateName", econtractFtlTemplateEntity));
        when(econtractFtlTemplateEntity.getFtlTemplate()).thenReturn("ftlTemplate");
        when(econtractPdfService.createPdf(anyList())).thenReturn("pdfUrl");
        String recordKey = "recordKey";
        SignContext signContext = mock(SignContext.class);

        // act
        String result = createPdfExecutor.genePdfWithParam(taskContext, recordKey, signContext);

        // assert
        assertEquals("pdfUrl", result);
        verify(pdfContentInfoBo).setFtlInfo("ftlTemplate");
    }
}
package com.sankuai.meituan.waimai.econtract.server.service.idempotent;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.squirrel.client.StoreKey;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.bo.task.TaskContext;
import com.sankuai.meituan.waimai.econtract.server.bo.task.TaskNodeBo;
import com.sankuai.meituan.waimai.econtract.server.constants.EcontractIdempotentConstant;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordEntity;
import com.sankuai.meituan.waimai.econtract.server.utils.RedisKvUtil;
import java.util.Collections;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class EcontractIdempotentRedisServiceSetReentrant1Test {

    @Mock
    private RedisKvUtil redisKvUtil;

    private EcontractContext context;

    private StoreKey mockStoreKey;

    @InjectMocks
    private EcontractIdempotentRedisService econtractIdempotentRedisService;

    @Before
    public void setUp() {
        context = new EcontractContext();
        // Setup minimal valid context
        context.setEcontractRecordEntity(new EcontractRecordEntity());
        context.getEcontractRecordEntity().setRecordKey("testRecordKey");
        context.setTaskContext(new TaskContext());
        context.getTaskContext().setTaskType("testTaskType");
        context.setCurrentTaskNode(new TaskNodeBo());
        context.getCurrentTaskNode().setTaskName("testTaskName");
        mockStoreKey = new StoreKey(EcontractIdempotentConstant.ECONTRACT_IDEMPOTENT_CATEGORY, "testKey");
    }

    /**
     * Test successful case where all required fields are present and Redis operation succeeds
     */
    @Test
    public void testSetReentrantSuccessCase() throws Throwable {
        // arrange
        when(redisKvUtil.set(any(StoreKey.class), eq(EcontractIdempotentConstant.ECONTRACT_IDEMPOTENT_REENTRANT), eq(600))).thenReturn(true);
        // act
        boolean result = econtractIdempotentRedisService.setReentrant(context);
        // assert
        assertTrue(result);
        verify(redisKvUtil).set(any(StoreKey.class), eq(EcontractIdempotentConstant.ECONTRACT_IDEMPOTENT_REENTRANT), eq(600));
    }

    /**
     * Test case where context is missing record key
     */
    @Test
    public void testSetReentrantMissingRecordKey() throws Throwable {
        // arrange
        context.getEcontractRecordEntity().setRecordKey(null);
        // act
        boolean result = econtractIdempotentRedisService.setReentrant(context);
        // assert
        assertFalse(result);
        verify(redisKvUtil, never()).set(any(), any(), anyInt());
    }

    /**
     * Test case where context is missing task name
     */
    @Test
    public void testSetReentrantMissingTaskName() throws Throwable {
        // arrange
        context.getCurrentTaskNode().setTaskName(null);
        // act
        boolean result = econtractIdempotentRedisService.setReentrant(context);
        // assert
        assertFalse(result);
        verify(redisKvUtil, never()).set(any(), any(), anyInt());
    }

    /**
     * Test case where Redis operation fails
     */
    @Test
    public void testSetReentrantRedisFailure() throws Throwable {
        // arrange
        when(redisKvUtil.set(any(StoreKey.class), eq(EcontractIdempotentConstant.ECONTRACT_IDEMPOTENT_REENTRANT), eq(600))).thenReturn(false);
        // act
        boolean result = econtractIdempotentRedisService.setReentrant(context);
        // assert
        assertFalse(result);
        verify(redisKvUtil).set(any(StoreKey.class), eq(EcontractIdempotentConstant.ECONTRACT_IDEMPOTENT_REENTRANT), eq(600));
    }

    /**
     * Test case where Redis operation throws exception
     */
    @Test
    public void testSetReentrantRedisException() throws Throwable {
        // arrange
        when(redisKvUtil.set(any(StoreKey.class), eq(EcontractIdempotentConstant.ECONTRACT_IDEMPOTENT_REENTRANT), eq(600))).thenThrow(new RuntimeException("Redis error"));
        // act
        boolean result = econtractIdempotentRedisService.setReentrant(context);
        // assert
        assertFalse(result);
        verify(redisKvUtil).set(any(StoreKey.class), eq(EcontractIdempotentConstant.ECONTRACT_IDEMPOTENT_REENTRANT), eq(600));
    }

    /**
     * Test case where context is null
     */
    @Test
    public void testSetReentrantNullContext() throws Throwable {
        // act
        boolean result = econtractIdempotentRedisService.setReentrant(null);
        // assert
        assertFalse(result);
        verify(redisKvUtil, never()).set(any(), any(), anyInt());
    }

    /**
     * Test case where context has empty flow list but missing task type
     * Note: The method still attempts to set in Redis even in this case
     */
    @Test
    public void testSetReentrantEmptyFlowListMissingTaskType() throws Throwable {
        // arrange
        context.setFlowList(Collections.emptyList());
        context.getTaskContext().setTaskType(null);
        when(redisKvUtil.set(any(StoreKey.class), eq(EcontractIdempotentConstant.ECONTRACT_IDEMPOTENT_REENTRANT), eq(600))).thenReturn(true);
        // act
        boolean result = econtractIdempotentRedisService.setReentrant(context);
        // assert
        assertTrue(result);
        verify(redisKvUtil).set(any(StoreKey.class), eq(EcontractIdempotentConstant.ECONTRACT_IDEMPOTENT_REENTRANT), eq(600));
    }
}

package com.sankuai.meituan.waimai.econtract.server.service.ability.dataanalysis;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import java.util.ArrayList;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * Test cases for AbstractDataAnalysisService.analysisListByClass method
 */
@RunWith(MockitoJUnitRunner.class)
public class AbstractDataAnalysisServiceAnalysisListByClassTest {

    @Spy
    private TestImpl service;

    private static class TestImpl extends AbstractDataAnalysisService {

        @Override
        protected List analysisList(String data, String type) {
            return null;
        }

        @Override
        protected List<Object> toObjectList(List tList) {
            return null;
        }
    }

    private static class TestDto {

        private String name;

        private int age;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getAge() {
            return age;
        }

        public void setAge(int age) {
            this.age = age;
        }
    }

    @Test
    public void testAnalysisListByClass_ValidSingleObject() throws Throwable {
        String data = "[{\"name\":\"test\",\"age\":25}]";
        List<TestDto> result = service.analysisListByClass(data, TestDto.class);
        assertNotNull("Result should not be null", result);
        assertEquals("List should contain one element", 1, result.size());
        TestDto dto = result.get(0);
        assertEquals("Name should match", "test", dto.getName());
        assertEquals("Age should match", 25, dto.getAge());
    }

    @Test
    public void testAnalysisListByClass_ValidMultipleObjects() throws Throwable {
        String data = "[{\"name\":\"test1\",\"age\":25},{\"name\":\"test2\",\"age\":30}]";
        List<TestDto> result = service.analysisListByClass(data, TestDto.class);
        assertNotNull("Result should not be null", result);
        assertEquals("List should contain two elements", 2, result.size());
        assertEquals("First object name should match", "test1", result.get(0).getName());
        assertEquals("Second object name should match", "test2", result.get(1).getName());
        assertEquals("First object age should match", 25, result.get(0).getAge());
        assertEquals("Second object age should match", 30, result.get(1).getAge());
    }

    @Test
    public void testAnalysisListByClass_EmptyArray() throws Throwable {
        String data = "[]";
        List<TestDto> result = service.analysisListByClass(data, TestDto.class);
        assertNotNull("Result should not be null", result);
        assertTrue("Result should be empty list", result.isEmpty());
    }

    @Test
    public void testAnalysisListByClass_NullInput() throws Throwable {
        String data = null;
        List<TestDto> result = service.analysisListByClass(data, TestDto.class);
        assertNotNull("Result should not be null", result);
        assertTrue("Result should be empty list", result.isEmpty());
    }

    @Test
    public void testAnalysisListByClass_PrimitiveTypeArray() throws Throwable {
        String data = "[1,2,3,4,5]";
        List<Integer> result = service.analysisListByClass(data, Integer.class);
        assertNotNull("Result should not be null", result);
        assertEquals("List should contain 5 elements", 5, result.size());
        assertEquals("First element should be 1", Integer.valueOf(1), result.get(0));
        assertEquals("Last element should be 5", Integer.valueOf(5), result.get(4));
    }

    @Test(expected = JSONException.class)
    public void testAnalysisListByClass_InvalidJson() throws Throwable {
        String data = "invalid json";
        service.analysisListByClass(data, TestDto.class);
    }

    @Test
    public void testAnalysisListByClass_EmptyString() throws Throwable {
        String data = "";
        List<TestDto> result = service.analysisListByClass(data, TestDto.class);
        assertNotNull("Result should not be null", result);
        assertTrue("Result should be empty list", result.isEmpty());
    }

    @Test
    public void testAnalysisListByClass_NullClass() throws Throwable {
        String data = "[{\"name\":\"test\",\"age\":25}]";
        // Create a mock to override the behavior for this specific test
        TestImpl mockService = spy(new TestImpl());
        doReturn(new ArrayList<>()).when(mockService).analysisListByClass(eq(data), isNull());
        List<?> result = mockService.analysisListByClass(data, null);
        assertNotNull("Result should not be null", result);
        assertTrue("Result should be empty list", result.isEmpty());
    }
}

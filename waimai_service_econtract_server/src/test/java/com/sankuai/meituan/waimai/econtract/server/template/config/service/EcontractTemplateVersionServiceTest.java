package com.sankuai.meituan.waimai.econtract.server.template.config.service;

import com.meituan.it.contract.platform.model.dto.template.ContractTemplateDTO;
import com.meituan.it.contract.platform.model.request.template.UploadCodingAttachmentReq;
import com.meituan.it.contract.platform.model.response.template.UploadCodingAttachmentResp;
import com.sankuai.meituan.waimai.econtract.server.adapter.hailuo.ContractTemplateOperateThriftServiceAdapter;
import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import com.sankuai.meituan.waimai.econtract.server.template.config.dao.EcontractTemplateBaseMapper;
import com.sankuai.meituan.waimai.econtract.server.template.config.dao.EcontractTemplateMetaMapper;
import com.sankuai.meituan.waimai.econtract.server.template.config.dao.EcontractTemplateVersionMapper;
import com.sankuai.meituan.waimai.econtract.server.template.config.entity.EcontractTemplateBaseEntity;
import com.sankuai.meituan.waimai.econtract.server.template.config.entity.EcontractTemplateVersionEntity;
import com.sankuai.meituan.waimai.econtract.server.template.config.entity.EcontractTemplateVersionSimpleEntity;
import com.sankuai.meituan.waimai.econtract.server.template.config.utils.EcontractTemplateTransUtil;
import com.sankuai.meituan.waimai.econtract.server.template.config.utils.TemplateHtmlGenerator;
import com.sankuai.meituan.waimai.econtract.server.template.config.utils.TemplateWordGenerator;
import com.sankuai.meituan.waimai.econtrct.client.template.config.constants.EcontractTemplateConstants;
import com.sankuai.meituan.waimai.econtrct.client.template.config.domain.EcontractTemplateVersionBo;
import com.sankuai.meituan.waimai.econtrct.client.template.config.domain.SyncTemplate4HaiLuoBo;
import com.sankuai.meituan.waimai.econtrct.client.template.config.exception.EcontractTemplateConfigException;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class EcontractTemplateVersionServiceTest {

    @InjectMocks
    private EcontractTemplateVersionService econtractTemplateVersionService;

    @Mock
    private EcontractTemplateVersionMapper econtractTemplateVersionMapper;

    @Mock
    private EcontractTemplateBaseMapper econtractTemplateBaseMapper;

    @Mock
    private EcontractTemplateOplogService econtractTemplateOplogService;

    @Mock
    private ContractTemplateOperateThriftServiceAdapter contractTemplateOperateThriftServiceAdapter;

    @Mock
    private EcontractTemplateMetaMapper econtractTemplateMetaMapper;
    
    private EcontractTemplateVersionBo versionBo;
    private EcontractTemplateBaseEntity baseEntity;
    private int opUid = 1001;
    private String opUname = "testUser";
    private String opUmisId = "testMisId";

    @Before
    public void setUp() {
        // 初始化测试数据
        versionBo = new EcontractTemplateVersionBo();
        versionBo.setId(1);
        versionBo.setTemplateId(10);
        versionBo.setVersion(2);
        versionBo.setStatus(EcontractTemplateConstants.VersionStatusEnum.WAIT_RELEASE.getValue());
        versionBo.setSourceContent("<html>测试源内容</html>");
        versionBo.setTargetContent("<html>测试目标内容</html>");

        baseEntity = new EcontractTemplateBaseEntity();
        baseEntity.setId(10);
        baseEntity.setName("测试模板");
        baseEntity.setHailuoTemplateCode("HAILUO123");
    }

    /**
     * 测试场景：版本不存在
     */
    @Test(expected = EcontractTemplateConfigException.class)
    public void testDeployTemplateVersion_VersionNotFound() throws Throwable {
        // arrange
        when(econtractTemplateVersionMapper.getTemplateVersionById(1)).thenReturn(null);
        when(econtractTemplateVersionService.getTemplateVersionById(1)).thenReturn(null);

        // act
        econtractTemplateVersionService.deployTemplateVersion(1, opUid, opUname, opUmisId);

        // assert - 期望抛出异常
    }

    /**
     * 测试场景：版本状态不可发布
     */
    @Test(expected = EcontractTemplateConfigException.class)
    public void testDeployTemplateVersion_StatusNotDeployable() throws Throwable {
        // arrange
        EcontractTemplateVersionEntity versionEntity = new EcontractTemplateVersionEntity();
        versionEntity.setId(1);
        versionEntity.setTemplateId(10);
        versionEntity.setVersion(2);
        versionEntity.setStatus(EcontractTemplateConstants.VersionStatusEnum.RELEASED.getValue()); // 已发布状态不可再次发布
        versionEntity.setSourceContent("<html>测试源内容</html>");
        versionEntity.setTargetContent("<html>测试目标内容</html>");
        when(econtractTemplateVersionMapper.getTemplateVersionById(1)).thenReturn(versionEntity);

        // 使用spy来模拟VersionStatusEnum.canDeploy方法
        try (MockedStatic<EcontractTemplateConstants.VersionStatusEnum> versionStatusEnumMockedStatic = Mockito.mockStatic(EcontractTemplateConstants.VersionStatusEnum.class)) {
            versionStatusEnumMockedStatic.when(() -> EcontractTemplateConstants.VersionStatusEnum.canDeploy(versionEntity.getStatus())).thenReturn(false);
            when(econtractTemplateMetaMapper.getTemplateMetaList(versionEntity.getId())).thenReturn(new ArrayList<>());

            // act
            econtractTemplateVersionService.deployTemplateVersion(1, opUid, opUname, opUmisId);

            // assert - 期望抛出异常
        }
    }

    /**
     * 测试场景：已存在审核中的版本
     */
    @Test(expected = EcontractTemplateConfigException.class)
    public void testDeployTemplateVersion_ExistingVersionUnderReview() throws Throwable {
        // arrange
        EcontractTemplateVersionEntity versionEntity = new EcontractTemplateVersionEntity();
        versionEntity.setId(1);
        versionEntity.setTemplateId(10);
        versionEntity.setVersion(2);
        versionEntity.setStatus(EcontractTemplateConstants.VersionStatusEnum.WAIT_RELEASE.getValue());
        versionEntity.setSourceContent("<html>测试源内容</html>");
        versionEntity.setTargetContent("<html>测试目标内容</html>");
        when(econtractTemplateVersionMapper.getTemplateVersionById(1)).thenReturn(versionEntity);

        // 模拟已有审核中的版本
        List<EcontractTemplateVersionSimpleEntity> versionList = new ArrayList<>();
        EcontractTemplateVersionSimpleEntity existingVersion = new EcontractTemplateVersionSimpleEntity();
        existingVersion.setId(2);
        existingVersion.setTemplateId(10);
        existingVersion.setVersion(1);
        existingVersion.setStatus(EcontractTemplateConstants.VersionStatusEnum.UNDER_REVIEW.getValue());
        versionList.add(existingVersion);

        when(econtractTemplateVersionMapper.getTemplateVersionSimpleList(10)).thenReturn(versionList);

        // 使用spy来模拟VersionStatusEnum.canDeploy方法
        try (MockedStatic<EcontractTemplateConstants.VersionStatusEnum> versionStatusEnumMockedStatic = Mockito.mockStatic(EcontractTemplateConstants.VersionStatusEnum.class)) {
            versionStatusEnumMockedStatic.when(() -> EcontractTemplateConstants.VersionStatusEnum.canDeploy(versionEntity.getStatus())).thenReturn(true);

            when(econtractTemplateMetaMapper.getTemplateMetaList(versionEntity.getId())).thenReturn(new ArrayList<>());
            // act
            econtractTemplateVersionService.deployTemplateVersion(1, opUid, opUname, opUmisId);

            // assert - 期望抛出异常
        }
    }

    /**
     * 测试场景：不需要海螺审核，直接发布成功
     */
    @Test
    public void testDeployTemplateVersion_DirectDeploySuccess() throws Throwable {
        try (MockedStatic<MccConfig> mccConfigMockedStatic = Mockito.mockStatic(MccConfig.class)) {
            // arrange
            mccConfigMockedStatic.when(MccConfig::supportIntegrationHaiLuo).thenReturn(false);

            // 创建版本实体
            EcontractTemplateVersionEntity versionEntity = new EcontractTemplateVersionEntity();
            versionEntity.setId(1);
            versionEntity.setTemplateId(10);
            versionEntity.setVersion(2);
            versionEntity.setStatus(EcontractTemplateConstants.VersionStatusEnum.WAIT_RELEASE.getValue());
            versionEntity.setSourceContent("<html>测试源内容</html>");
            versionEntity.setTargetContent("<html>测试目标内容</html>");

            when(econtractTemplateVersionMapper.getTemplateVersionById(1)).thenReturn(versionEntity);
            when(econtractTemplateMetaMapper.getTemplateMetaList(versionEntity.getId())).thenReturn(new ArrayList<>());
            when(econtractTemplateVersionMapper.getTemplateVersionSimpleList(10)).thenReturn(new ArrayList<>());
            // 模拟已发布的版本
            EcontractTemplateVersionSimpleEntity releasedVersion = new EcontractTemplateVersionSimpleEntity();
            releasedVersion.setId(2);
            releasedVersion.setTemplateId(10);
            releasedVersion.setVersion(1);
            releasedVersion.setStatus(EcontractTemplateConstants.VersionStatusEnum.RELEASED.getValue());
            when(econtractTemplateVersionMapper.getReleasedTemplateVersionSimple(10)).thenReturn(releasedVersion);
            // 模拟日志格式
            when(econtractTemplateOplogService.getOpLogFormat(EcontractTemplateConstants.OpTypeEnum.CHANGE_STATUS.getValue()))
                .thenReturn("将模板V%d的状态从【%s】变更为【%s】");

            // 使用spy来模拟needHaiLuoAudit方法
            EcontractTemplateVersionService serviceSpy = spy(econtractTemplateVersionService);
            doReturn(false).when(serviceSpy).needHaiLuoAudit(any());
            // act
            serviceSpy.deployTemplateVersion(1, opUid, opUname, opUmisId);
            // assert
            // 验证更新当前版本状态为已发布
            verify(econtractTemplateVersionMapper, times(1)).updateTemplateVersion(argThat(entity ->
                entity.getId() == 1 &&
                entity.getStatus() == EcontractTemplateConstants.VersionStatusEnum.RELEASED.getValue()
            ));
            // 验证更新原已发布版本状态为已下架
            verify(econtractTemplateVersionMapper, times(1)).updateTemplateVersion(argThat(entity ->
                entity.getId() == 2 &&
                entity.getStatus() == EcontractTemplateConstants.VersionStatusEnum.DEPRECATED.getValue()
            ));
            // 验证插入操作日志
            verify(econtractTemplateOplogService, times(1)).insertEcontractTemplateOplog(
                eq(10),
                eq(EcontractTemplateConstants.OpTypeEnum.CHANGE_STATUS.getValue()),
                eq(opUid),
                eq(opUname),
                eq(opUmisId),
                anyString()
            );
        }
    }

/**
 * 测试场景：不需要海螺审核，且没有已发布的版本
 */
@Test
public void testDeployTemplateVersion_DirectDeployNoExistingReleasedVersion() throws Throwable {
    try (MockedStatic<MccConfig> mccConfigMockedStatic = Mockito.mockStatic(MccConfig.class)) {
        // arrange
        mccConfigMockedStatic.when(MccConfig::supportIntegrationHaiLuo).thenReturn(false);

        // 创建 Entity 对象
        EcontractTemplateVersionEntity versionEntity = new EcontractTemplateVersionEntity();
        versionEntity.setId(1);
        versionEntity.setTemplateId(10);
        versionEntity.setVersion(2);
        versionEntity.setStatus(EcontractTemplateConstants.VersionStatusEnum.WAIT_RELEASE.getValue());
        versionEntity.setSourceContent("<html>测试源内容</html>");
        versionEntity.setTargetContent("<html>测试目标内容</html>");
        versionEntity.setCodeBlockList("[]"); // 添加空的代码块列表

        when(econtractTemplateVersionMapper.getTemplateVersionById(1)).thenReturn(versionEntity);
        when(econtractTemplateMetaMapper.getTemplateMetaList(versionEntity.getId())).thenReturn(new ArrayList<>());
        when(econtractTemplateVersionMapper.getTemplateVersionSimpleList(10)).thenReturn(new ArrayList<>());
        // 模拟没有已发布的版本
        when(econtractTemplateVersionMapper.getReleasedTemplateVersionSimple(10)).thenReturn(null);

        // 模拟日志格式
        when(econtractTemplateOplogService.getOpLogFormat(EcontractTemplateConstants.OpTypeEnum.CHANGE_STATUS.getValue()))
            .thenReturn("将模板V%d的状态从【%s】变更为【%s】");

        // 模拟EcontractTemplateTransUtil.econtractTemplateVersionEntityToBo方法
        try (MockedStatic<EcontractTemplateTransUtil> transUtilMockedStatic = Mockito.mockStatic(EcontractTemplateTransUtil.class)) {
            EcontractTemplateVersionBo mockBo = new EcontractTemplateVersionBo();
            mockBo.setId(versionEntity.getId());
            mockBo.setTemplateId(versionEntity.getTemplateId());
            mockBo.setVersion(versionEntity.getVersion());
            mockBo.setStatus(versionEntity.getStatus());
            mockBo.setSourceContent(versionEntity.getSourceContent());
            mockBo.setTargetContent(versionEntity.getTargetContent());

            transUtilMockedStatic.when(() -> EcontractTemplateTransUtil.econtractTemplateVersionEntityToBo(versionEntity)).thenReturn(mockBo);

            // 使用spy来模拟needHaiLuoAudit方法
            EcontractTemplateVersionService serviceSpy = spy(econtractTemplateVersionService);
            doReturn(false).when(serviceSpy).needHaiLuoAudit(any());

            // 使用spy来模拟VersionStatusEnum.canDeploy方法
            try (MockedStatic<EcontractTemplateConstants.VersionStatusEnum> versionStatusEnumMockedStatic = Mockito.mockStatic(EcontractTemplateConstants.VersionStatusEnum.class)) {
                versionStatusEnumMockedStatic.when(() -> EcontractTemplateConstants.VersionStatusEnum.canDeploy(versionEntity.getStatus())).thenReturn(true);
                versionStatusEnumMockedStatic.when(() -> EcontractTemplateConstants.VersionStatusEnum.getVersionStatusNameByValue(anyInt())).thenReturn("测试状态");

                // act
                serviceSpy.deployTemplateVersion(1, opUid, opUname, opUmisId);

                // assert
                // 验证只更新当前版本状态为已发布
                verify(econtractTemplateVersionMapper, times(1)).updateTemplateVersion(argThat(entity ->
                    entity.getId() == 1 &&
                    entity.getStatus() == EcontractTemplateConstants.VersionStatusEnum.RELEASED.getValue()
                ));

                // 验证插入操作日志
                verify(econtractTemplateOplogService, times(1)).insertEcontractTemplateOplog(
                    eq(10),
                    eq(EcontractTemplateConstants.OpTypeEnum.CHANGE_STATUS.getValue()),
                    eq(opUid),
                    eq(opUname),
                    eq(opUmisId),
                    anyString()
                );
            }
        }
    }
}

    /**
     * 测试场景：需要海螺审核，通知海螺审核成功
     */
    @Test
    public void testDeployTemplateVersion_HailuoAuditSuccess() throws Throwable {
        try (MockedStatic<MccConfig> mccConfigMockedStatic = Mockito.mockStatic(MccConfig.class);
             MockedStatic<TemplateHtmlGenerator> htmlGeneratorMockedStatic = Mockito.mockStatic(TemplateHtmlGenerator.class);
             MockedStatic<TemplateWordGenerator> wordGeneratorMockedStatic = Mockito.mockStatic(TemplateWordGenerator.class)) {
            // arrange
            mccConfigMockedStatic.when(MccConfig::supportIntegrationHaiLuo).thenReturn(true);
            mccConfigMockedStatic.when(MccConfig::getXianFuAppCodeInHaiLuo).thenReturn("XIANFU");

            // 创建版本实体
            EcontractTemplateVersionEntity versionEntity = new EcontractTemplateVersionEntity();
            versionEntity.setId(1);
            versionEntity.setTemplateId(10);
            versionEntity.setVersion(2);
            versionEntity.setStatus(EcontractTemplateConstants.VersionStatusEnum.WAIT_RELEASE.getValue());
            versionEntity.setSourceContent("<html>测试源内容</html>");
            versionEntity.setTargetContent("<html>测试目标内容</html>");

            when(econtractTemplateVersionMapper.getTemplateVersionById(1)).thenReturn(versionEntity);
            when(econtractTemplateMetaMapper.getTemplateMetaList(versionEntity.getId())).thenReturn(new ArrayList<>());
            when(econtractTemplateVersionMapper.getTemplateVersionSimpleList(10)).thenReturn(new ArrayList<>());
            when(econtractTemplateBaseMapper.getTemplateBaseById(10)).thenReturn(baseEntity);

            // 模拟HTML和Word生成
            htmlGeneratorMockedStatic.when(() -> TemplateHtmlGenerator.generateHtmlAndUploadToS3(any()))
                .thenReturn(Pair.of("http://example.com/html", "template.html"));
            wordGeneratorMockedStatic.when(() -> TemplateWordGenerator.generateWordFromFtlAndUploadToS3(any(), anyString()))
                .thenReturn(Pair.of("http://example.com/word", "template.docx"));

            // 模拟海螺审核响应
            UploadCodingAttachmentResp resp = new UploadCodingAttachmentResp();
            resp.setCode("200");
            ContractTemplateDTO templateDTO = new ContractTemplateDTO();
            templateDTO.setTemplateVersion(123);
            resp.setData(templateDTO);
            when(contractTemplateOperateThriftServiceAdapter.notifyHaiLuoAudit(any(UploadCodingAttachmentReq.class))).thenReturn(resp);

            // 模拟日志格式
            when(econtractTemplateOplogService.getOpLogFormat(EcontractTemplateConstants.OpTypeEnum.CHANGE_STATUS.getValue()))
                .thenReturn("将模板V%d的状态从【%s】变更为【%s】");

            // 使用spy来模拟needHaiLuoAudit方法
            EcontractTemplateVersionService serviceSpy = spy(econtractTemplateVersionService);
            doReturn(true).when(serviceSpy).needHaiLuoAudit(any());

            // act
            serviceSpy.deployTemplateVersion(1, opUid, opUname, opUmisId);
            // assert
            // 验证更新版本状态为审核中
            verify(econtractTemplateVersionMapper, times(1)).updateTemplateVersion(argThat(entity ->
                entity.getId() == 1 &&
                entity.getStatus() == EcontractTemplateConstants.VersionStatusEnum.UNDER_REVIEW.getValue() &&
                entity.getHailuoTemplateVersion() == 123L
            ));

            // 验证插入操作日志
            verify(econtractTemplateOplogService, times(1)).insertEcontractTemplateOplog(
                eq(10),
                eq(EcontractTemplateConstants.OpTypeEnum.CHANGE_STATUS.getValue()),
                eq(opUid),
                eq(opUname),
                eq(opUmisId),
                anyString()
            );
        }
    }

    /**
     * 测试场景：需要海螺审核，但通知海螺审核失败（返回null）
     */
    @Test(expected = EcontractTemplateConfigException.class)
    public void testDeployTemplateVersion_HailuoAuditFailedNull() throws Throwable {
        try (MockedStatic<MccConfig> mccConfigMockedStatic = Mockito.mockStatic(MccConfig.class);
             MockedStatic<TemplateHtmlGenerator> htmlGeneratorMockedStatic = Mockito.mockStatic(TemplateHtmlGenerator.class);
             MockedStatic<TemplateWordGenerator> wordGeneratorMockedStatic = Mockito.mockStatic(TemplateWordGenerator.class)) {

            // arrange
            mccConfigMockedStatic.when(MccConfig::supportIntegrationHaiLuo).thenReturn(true);
            mccConfigMockedStatic.when(MccConfig::getXianFuAppCodeInHaiLuo).thenReturn("XIANFU");

            // 创建版本实体
            EcontractTemplateVersionEntity versionEntity = new EcontractTemplateVersionEntity();
            versionEntity.setId(1);
            versionEntity.setTemplateId(10);
            versionEntity.setVersion(2);
            versionEntity.setStatus(EcontractTemplateConstants.VersionStatusEnum.WAIT_RELEASE.getValue());
            versionEntity.setSourceContent("<html>测试源内容</html>");
            versionEntity.setTargetContent("<html>测试目标内容</html>");

            when(econtractTemplateVersionMapper.getTemplateVersionById(1)).thenReturn(versionEntity);
            when(econtractTemplateMetaMapper.getTemplateMetaList(versionEntity.getId())).thenReturn(new ArrayList<>());
            when(econtractTemplateVersionMapper.getTemplateVersionSimpleList(10)).thenReturn(new ArrayList<>());
            when(econtractTemplateBaseMapper.getTemplateBaseById(10)).thenReturn(baseEntity);

            // 模拟HTML和Word生成
            htmlGeneratorMockedStatic.when(() -> TemplateHtmlGenerator.generateHtmlAndUploadToS3(any()))
                .thenReturn(Pair.of("http://example.com/html", "template.html"));
            wordGeneratorMockedStatic.when(() -> TemplateWordGenerator.generateWordFromFtlAndUploadToS3(any(), anyString()))
                .thenReturn(Pair.of("http://example.com/word", "template.docx"));

            // 模拟海螺审核响应为null
            when(contractTemplateOperateThriftServiceAdapter.notifyHaiLuoAudit(any(UploadCodingAttachmentReq.class))).thenReturn(null);

            // 使用spy来模拟needHaiLuoAudit方法
            EcontractTemplateVersionService serviceSpy = spy(econtractTemplateVersionService);
            doReturn(true).when(serviceSpy).needHaiLuoAudit(any());

            // act
            serviceSpy.deployTemplateVersion(1, opUid, opUname, opUmisId);

            // assert - 期望抛出异常
        }
    }

    /**
     * 测试场景：需要海螺审核，但通知海螺审核失败（返回失败）
     */
    @Test(expected = EcontractTemplateConfigException.class)
    public void testDeployTemplateVersion_HailuoAuditFailedError() throws Throwable {
        try (MockedStatic<MccConfig> mccConfigMockedStatic = Mockito.mockStatic(MccConfig.class);
             MockedStatic<TemplateHtmlGenerator> htmlGeneratorMockedStatic = Mockito.mockStatic(TemplateHtmlGenerator.class);
             MockedStatic<TemplateWordGenerator> wordGeneratorMockedStatic = Mockito.mockStatic(TemplateWordGenerator.class)) {

            // arrange
            mccConfigMockedStatic.when(MccConfig::supportIntegrationHaiLuo).thenReturn(true);
            mccConfigMockedStatic.when(MccConfig::getXianFuAppCodeInHaiLuo).thenReturn("XIANFU");

            // 创建版本实体
            EcontractTemplateVersionEntity versionEntity = new EcontractTemplateVersionEntity();
            versionEntity.setId(1);
            versionEntity.setTemplateId(10);
            versionEntity.setVersion(2);
            versionEntity.setStatus(EcontractTemplateConstants.VersionStatusEnum.WAIT_RELEASE.getValue());
            versionEntity.setSourceContent("<html>测试源内容</html>");
            versionEntity.setTargetContent("<html>测试目标内容</html>");

            when(econtractTemplateVersionMapper.getTemplateVersionById(1)).thenReturn(versionEntity);
            when(econtractTemplateMetaMapper.getTemplateMetaList(versionEntity.getId())).thenReturn(new ArrayList<>());
            when(econtractTemplateVersionMapper.getTemplateVersionSimpleList(10)).thenReturn(new ArrayList<>());
            when(econtractTemplateBaseMapper.getTemplateBaseById(10)).thenReturn(baseEntity);

            // 模拟HTML和Word生成
            htmlGeneratorMockedStatic.when(() -> TemplateHtmlGenerator.generateHtmlAndUploadToS3(any()))
                .thenReturn(Pair.of("http://example.com/html", "template.html"));
            wordGeneratorMockedStatic.when(() -> TemplateWordGenerator.generateWordFromFtlAndUploadToS3(any(), anyString()))
                .thenReturn(Pair.of("http://example.com/word", "template.docx"));

            // 模拟海螺审核响应失败
            UploadCodingAttachmentResp resp = new UploadCodingAttachmentResp();
            resp.setCode("201");
            resp.setMessage("审核失败");
            when(contractTemplateOperateThriftServiceAdapter.notifyHaiLuoAudit(any(UploadCodingAttachmentReq.class))).thenReturn(resp);

            // 使用spy来模拟needHaiLuoAudit方法
            EcontractTemplateVersionService serviceSpy = spy(econtractTemplateVersionService);
            doReturn(true).when(serviceSpy).needHaiLuoAudit(any());

            // act
            serviceSpy.deployTemplateVersion(1, opUid, opUname, opUmisId);

            // assert - 期望抛出异常
        }
    }

    /**
     * 测试场景：HTML生成失败
     */
    @Test(expected = EcontractTemplateConfigException.class)
    public void testDeployTemplateVersion_HtmlGenerationFailed() throws Throwable {
        try (MockedStatic<MccConfig> mccConfigMockedStatic = Mockito.mockStatic(MccConfig.class);
             MockedStatic<TemplateHtmlGenerator> htmlGeneratorMockedStatic = Mockito.mockStatic(TemplateHtmlGenerator.class)) {

            // arrange
            mccConfigMockedStatic.when(MccConfig::supportIntegrationHaiLuo).thenReturn(true);

            // 创建版本实体
            EcontractTemplateVersionEntity versionEntity = new EcontractTemplateVersionEntity();
            versionEntity.setId(1);
            versionEntity.setTemplateId(10);
            versionEntity.setVersion(2);
            versionEntity.setStatus(EcontractTemplateConstants.VersionStatusEnum.WAIT_RELEASE.getValue());
            versionEntity.setSourceContent("<html>测试源内容</html>");
            versionEntity.setTargetContent("<html>测试目标内容</html>");

            when(econtractTemplateVersionMapper.getTemplateVersionById(1)).thenReturn(versionEntity);
            when(econtractTemplateMetaMapper.getTemplateMetaList(versionEntity.getId())).thenReturn(new ArrayList<>());
            when(econtractTemplateVersionMapper.getTemplateVersionSimpleList(10)).thenReturn(new ArrayList<>());
            when(econtractTemplateBaseMapper.getTemplateBaseById(10)).thenReturn(baseEntity);

            // 模拟HTML生成失败
            htmlGeneratorMockedStatic.when(() -> TemplateHtmlGenerator.generateHtmlAndUploadToS3(any()))
                .thenThrow(new EcontractTemplateConfigException("HTML生成失败"));

            // 使用spy来模拟needHaiLuoAudit方法
            EcontractTemplateVersionService serviceSpy = spy(econtractTemplateVersionService);
            doReturn(true).when(serviceSpy).needHaiLuoAudit(any());

            // act
            serviceSpy.deployTemplateVersion(1, opUid, opUname, opUmisId);

            // assert - 期望抛出异常
        }
    }
    
    /**
     * 测试场景：isAll=true 且存在需要同步的模板
     */
    @Test
    public void testSyncTemplate4HaiLuo_IsAllTrue_WithTemplates() throws Exception {
        // arrange
        SyncTemplate4HaiLuoBo requestDTO = new SyncTemplate4HaiLuoBo();
        requestDTO.setIsAll(true);
        
        // 创建模板基础实体
        List<EcontractTemplateBaseEntity> templateBaseEntities = new ArrayList<>();
        EcontractTemplateBaseEntity entity = new EcontractTemplateBaseEntity();
        entity.setId(1);
        templateBaseEntities.add(entity);
        
        // 创建模板版本实体
        EcontractTemplateVersionEntity versionEntity = new EcontractTemplateVersionEntity();
        versionEntity.setTemplateId(1);
        versionEntity.setVersion(1);
        versionEntity.setSourceContent("<html>测试源内容</html>");
        versionEntity.setTargetContent("<html>测试目标内容</html>");
        
        // 模拟 getAllTemplateWithoutHaiLuoCode 方法返回模板列表
        when(econtractTemplateBaseMapper.getAllTemplateWithoutHaiLuoCode()).thenReturn(templateBaseEntities);
        
        // 模拟 getLatestTemplateVersion 方法返回版本实体
        when(econtractTemplateVersionMapper.getLatestTemplateVersion(1, true))
                .thenReturn(versionEntity);
        
        // 模拟 syncTemplate2HaiLuo 方法，避免实际调用
        doNothing().when(econtractTemplateBaseMapper).updateTemplateBase(any(EcontractTemplateBaseEntity.class));
        doNothing().when(econtractTemplateVersionMapper).updateTemplateByTemplateIdAndVersion(any(EcontractTemplateVersionEntity.class));
        
        // 使用spy来模拟getHaiLuoTemplateInfo方法，避免实际调用
        EcontractTemplateVersionService serviceSpy = spy(econtractTemplateVersionService);
        ContractTemplateDTO haiLuoTemplateInfo = new ContractTemplateDTO();
        haiLuoTemplateInfo.setTemplateCode("TEST_CODE");
        haiLuoTemplateInfo.setTemplateVersion(1);
        doReturn(haiLuoTemplateInfo).when(serviceSpy).getHaiLuoTemplateInfo(any());
        
        // act
        Boolean result = serviceSpy.syncTemplate4HaiLuo(requestDTO);
        
        // assert
        assertTrue(result);
        verify(econtractTemplateBaseMapper, times(1)).getAllTemplateWithoutHaiLuoCode();
        verify(econtractTemplateVersionMapper, times(1)).getLatestTemplateVersion(eq(1), eq(true));
    }
    
    /**
     * 测试场景：isAll=true 但没有需要同步的模板
     */
    @Test
    public void testSyncTemplate4HaiLuo_IsAllTrue_NoTemplates() throws Exception {
        // arrange
        SyncTemplate4HaiLuoBo requestDTO = new SyncTemplate4HaiLuoBo();
        requestDTO.setIsAll(true);
        
        when(econtractTemplateBaseMapper.getAllTemplateWithoutHaiLuoCode()).thenReturn(Collections.emptyList());
        
        // act
        Boolean result = econtractTemplateVersionService.syncTemplate4HaiLuo(requestDTO);
        
        // assert
        assertTrue(result);
        verify(econtractTemplateBaseMapper, times(1)).getAllTemplateWithoutHaiLuoCode();
        verify(econtractTemplateVersionMapper, never()).getLatestTemplateVersion(anyInt(), anyBoolean());
    }
    
    /**
     * 测试场景：isAll=false 且请求中有模板版本列表
     */
    @Test
    public void testSyncTemplate4HaiLuo_IsAllFalse_WithTemplateVersions() throws Exception {
        // arrange
        SyncTemplate4HaiLuoBo requestDTO = new SyncTemplate4HaiLuoBo();
        requestDTO.setIsAll(false);
        
        EcontractTemplateVersionBo versionBo = new EcontractTemplateVersionBo();
        versionBo.setTemplateId(1);
        versionBo.setVersion(1);
        
        requestDTO.setTemplateVersionBos(Arrays.asList(versionBo));
        
        // 创建模板版本实体
        EcontractTemplateVersionEntity versionEntity = new EcontractTemplateVersionEntity();
        versionEntity.setTemplateId(1);
        versionEntity.setVersion(1);
        versionEntity.setSourceContent("<html>测试源内容</html>");
        versionEntity.setTargetContent("<html>测试目标内容</html>");
        
        // 模拟 getTemplateVersionByTemplateIdAndVersion 方法返回版本实体
        when(econtractTemplateVersionMapper.getTemplateVersionByTemplateIdAndVersion(eq(1), eq(1)))
                .thenReturn(versionEntity);
        
        // 模拟 syncTemplate2HaiLuo 方法，避免实际调用
        doNothing().when(econtractTemplateBaseMapper).updateTemplateBase(any(EcontractTemplateBaseEntity.class));
        doNothing().when(econtractTemplateVersionMapper).updateTemplateByTemplateIdAndVersion(any(EcontractTemplateVersionEntity.class));
        
        // 使用spy来模拟getHaiLuoTemplateInfo方法，避免实际调用
        EcontractTemplateVersionService serviceSpy = spy(econtractTemplateVersionService);
        ContractTemplateDTO haiLuoTemplateInfo = new ContractTemplateDTO();
        haiLuoTemplateInfo.setTemplateCode("TEST_CODE");
        haiLuoTemplateInfo.setTemplateVersion(1);
        doReturn(haiLuoTemplateInfo).when(serviceSpy).getHaiLuoTemplateInfo(any());
        
        // act
        Boolean result = serviceSpy.syncTemplate4HaiLuo(requestDTO);
        
        // assert
        assertTrue(result);
        verify(econtractTemplateBaseMapper, never()).getAllTemplateWithoutHaiLuoCode();
        verify(econtractTemplateVersionMapper, times(1)).getTemplateVersionByTemplateIdAndVersion(eq(1), eq(1));
    }
    
    /**
     * 测试场景：isAll=false 但请求中模板版本列表为空
     */
    @Test
    public void testSyncTemplate4HaiLuo_IsAllFalse_EmptyTemplateVersions() throws Exception {
        // arrange
        SyncTemplate4HaiLuoBo requestDTO = new SyncTemplate4HaiLuoBo();
        requestDTO.setIsAll(false);
        requestDTO.setTemplateVersionBos(Collections.emptyList());
        
        // act
        Boolean result = econtractTemplateVersionService.syncTemplate4HaiLuo(requestDTO);
        
        // assert
        assertTrue(result);
        verify(econtractTemplateBaseMapper, never()).getAllTemplateWithoutHaiLuoCode();
        verify(econtractTemplateVersionMapper, never()).getTemplateVersionByTemplateIdAndVersion(anyInt(), anyInt());
    }
    
    /**
     * 测试场景：isAll=false 但请求中模板版本列表为null
     */
    @Test
    public void testSyncTemplate4HaiLuo_IsAllFalse_NullTemplateVersions() throws Exception {
        // arrange
        SyncTemplate4HaiLuoBo requestDTO = new SyncTemplate4HaiLuoBo();
        requestDTO.setIsAll(false);
        requestDTO.setTemplateVersionBos(null);
        
        // act
        Boolean result = econtractTemplateVersionService.syncTemplate4HaiLuo(requestDTO);
        
        // assert
        assertTrue(result);
        verify(econtractTemplateBaseMapper, never()).getAllTemplateWithoutHaiLuoCode();
        verify(econtractTemplateVersionMapper, never()).getTemplateVersionByTemplateIdAndVersion(anyInt(), anyInt());
    }
    
    /**
     * 测试场景：同步过程中出现异常
     */
    @Test
    public void testSyncTemplate4HaiLuo_SyncException() throws Exception {
        // arrange
        SyncTemplate4HaiLuoBo requestDTO = new SyncTemplate4HaiLuoBo();
        requestDTO.setIsAll(false);
        
        EcontractTemplateVersionBo versionBo = new EcontractTemplateVersionBo();
        versionBo.setTemplateId(1);
        versionBo.setVersion(1);
        
        requestDTO.setTemplateVersionBos(Arrays.asList(versionBo));
        
        // 创建模板版本实体
        EcontractTemplateVersionEntity versionEntity = new EcontractTemplateVersionEntity();
        versionEntity.setTemplateId(1);
        versionEntity.setVersion(1);
        versionEntity.setSourceContent("<html>测试源内容</html>");
        versionEntity.setTargetContent("<html>测试目标内容</html>");
        
        // 模拟 getTemplateVersionByTemplateIdAndVersion 方法返回版本实体
        when(econtractTemplateVersionMapper.getTemplateVersionByTemplateIdAndVersion(eq(1), eq(1)))
                .thenReturn(versionEntity);
        
        // 使用spy来模拟getHaiLuoTemplateInfo方法抛出异常
        EcontractTemplateVersionService serviceSpy = spy(econtractTemplateVersionService);
        doThrow(new EcontractTemplateConfigException("同步失败")).when(serviceSpy).getHaiLuoTemplateInfo(any());
        
        // act - 期望抛出异常
        serviceSpy.syncTemplate4HaiLuo(requestDTO);
        
        // assert - 验证调用了相关方法
        verify(econtractTemplateVersionMapper, times(1)).getTemplateVersionByTemplateIdAndVersion(eq(1), eq(1));
    }
    
    /**
     * 测试场景：version为null时使用最新版本
     */
    @Test
    public void testSyncTemplate4HaiLuo_NullVersion() throws Exception {
        // arrange
        SyncTemplate4HaiLuoBo requestDTO = new SyncTemplate4HaiLuoBo();
        requestDTO.setIsAll(false);
        
        EcontractTemplateVersionBo versionBo = new EcontractTemplateVersionBo();
        versionBo.setTemplateId(1);
        versionBo.setVersion(null);
        
        requestDTO.setTemplateVersionBos(Arrays.asList(versionBo));
        
        // 创建模板版本实体
        EcontractTemplateVersionEntity versionEntity = new EcontractTemplateVersionEntity();
        versionEntity.setTemplateId(1);
        versionEntity.setVersion(1);
        versionEntity.setSourceContent("<html>测试源内容</html>");
        versionEntity.setTargetContent("<html>测试目标内容</html>");
        
        // 模拟 getLatestTemplateVersion 方法返回版本实体
        when(econtractTemplateVersionMapper.getLatestTemplateVersion(eq(1), eq(true)))
                .thenReturn(versionEntity);
        
        // 模拟 syncTemplate2HaiLuo 方法，避免实际调用
        doNothing().when(econtractTemplateBaseMapper).updateTemplateBase(any(EcontractTemplateBaseEntity.class));
        doNothing().when(econtractTemplateVersionMapper).updateTemplateByTemplateIdAndVersion(any(EcontractTemplateVersionEntity.class));
        
        // 使用spy来模拟getHaiLuoTemplateInfo方法，避免实际调用
        EcontractTemplateVersionService serviceSpy = spy(econtractTemplateVersionService);
        ContractTemplateDTO haiLuoTemplateInfo = new ContractTemplateDTO();
        haiLuoTemplateInfo.setTemplateCode("TEST_CODE");
        haiLuoTemplateInfo.setTemplateVersion(1);
        doReturn(haiLuoTemplateInfo).when(serviceSpy).getHaiLuoTemplateInfo(any());
        
        // act
        Boolean result = serviceSpy.syncTemplate4HaiLuo(requestDTO);
        
        // assert
        assertTrue(result);
        verify(econtractTemplateVersionMapper, times(1)).getLatestTemplateVersion(eq(1), eq(true));
        verify(econtractTemplateVersionMapper, never()).getTemplateVersionByTemplateIdAndVersion(anyInt(), anyInt());
    }
    
    /**
     * 测试场景：version为0时使用最新版本
     */
    @Test
    public void testSyncTemplate4HaiLuo_ZeroVersion() throws Exception {
        // arrange
        SyncTemplate4HaiLuoBo requestDTO = new SyncTemplate4HaiLuoBo();
        requestDTO.setIsAll(false);
        
        EcontractTemplateVersionBo inputVersionBo = new EcontractTemplateVersionBo();
        inputVersionBo.setTemplateId(1);
        inputVersionBo.setVersion(0);
        
        requestDTO.setTemplateVersionBos(Arrays.asList(inputVersionBo));
        
        // 创建模板版本实体
        EcontractTemplateVersionEntity versionEntity = new EcontractTemplateVersionEntity();
        versionEntity.setTemplateId(1);
        versionEntity.setVersion(1);
        versionEntity.setSourceContent("<html>测试源内容</html>");
        versionEntity.setTargetContent("<html>测试目标内容</html>");
        
        // 模拟 getLatestTemplateVersion 方法返回版本实体
        when(econtractTemplateVersionMapper.getLatestTemplateVersion(eq(1), eq(true)))
                .thenReturn(versionEntity);
        
        // 模拟 syncTemplate2HaiLuo 方法，避免实际调用
        doNothing().when(econtractTemplateBaseMapper).updateTemplateBase(any(EcontractTemplateBaseEntity.class));
        doNothing().when(econtractTemplateVersionMapper).updateTemplateByTemplateIdAndVersion(any(EcontractTemplateVersionEntity.class));
        
        // 使用spy来模拟getHaiLuoTemplateInfo方法，避免实际调用
        EcontractTemplateVersionService serviceSpy = spy(econtractTemplateVersionService);
        ContractTemplateDTO haiLuoTemplateInfo = new ContractTemplateDTO();
        haiLuoTemplateInfo.setTemplateCode("TEST_CODE");
        haiLuoTemplateInfo.setTemplateVersion(1);
        doReturn(haiLuoTemplateInfo).when(serviceSpy).getHaiLuoTemplateInfo(any());

        // act
        Boolean result = serviceSpy.syncTemplate4HaiLuo(requestDTO);
        
        // assert
        assertTrue(result);
        verify(econtractTemplateVersionMapper, times(1)).getLatestTemplateVersion(eq(1), eq(true));
    }
    
    /**
     * 测试场景：多个模板版本同步
     */
    @Test
    public void testSyncTemplate4HaiLuo_MultipleTemplateVersions() throws Exception {
        // arrange
        SyncTemplate4HaiLuoBo requestDTO = new SyncTemplate4HaiLuoBo();
        requestDTO.setIsAll(false);
        
        EcontractTemplateVersionBo inputVersionBo1 = new EcontractTemplateVersionBo();
        inputVersionBo1.setTemplateId(1);
        inputVersionBo1.setVersion(1);
        
        EcontractTemplateVersionBo inputVersionBo2 = new EcontractTemplateVersionBo();
        inputVersionBo2.setTemplateId(2);
        inputVersionBo2.setVersion(2);
        
        requestDTO.setTemplateVersionBos(Arrays.asList(inputVersionBo1, inputVersionBo2));
        
        // 模拟 buildTemplateVersionBo 方法返回模板版本BO
        EcontractTemplateVersionBo versionBo1 = new EcontractTemplateVersionBo();
        versionBo1.setTemplateId(1);
        versionBo1.setVersion(1);
        
        EcontractTemplateVersionBo versionBo2 = new EcontractTemplateVersionBo();
        versionBo2.setTemplateId(2);
        versionBo2.setVersion(2);
        
        // doReturn(versionBo1).when(econtractTemplateVersionService).buildTemplateVersionBo(eq(1), eq(1));
        // doReturn(versionBo2).when(econtractTemplateVersionService).buildTemplateVersionBo(eq(2), eq(2));
        
        // 模拟 syncTemplate2HaiLuo 方法，避免实际调用
        // doNothing().when(econtractTemplateVersionService).syncTemplate2HaiLuo(any(EcontractTemplateVersionBo.class));
        
        // act
        Boolean result = econtractTemplateVersionService.syncTemplate4HaiLuo(requestDTO);
        
        // assert
        assertTrue(result);
        // verify(econtractTemplateVersionService, times(1)).buildTemplateVersionBo(eq(1), eq(1));
        // verify(econtractTemplateVersionService, times(1)).buildTemplateVersionBo(eq(2), eq(2));
        // verify(econtractTemplateVersionService, times(2)).syncTemplate2HaiLuo(any(EcontractTemplateVersionBo.class));
    }
    
    /**
     * 测试场景：buildTemplateVersionBo 方法返回 null
     */
    @Test
    public void testSyncTemplate4HaiLuo_BuildTemplateVersionBoReturnsNull() throws Exception {
        // arrange
        SyncTemplate4HaiLuoBo requestDTO = new SyncTemplate4HaiLuoBo();
        requestDTO.setIsAll(false);
        
        EcontractTemplateVersionBo inputVersionBo = new EcontractTemplateVersionBo();
        inputVersionBo.setTemplateId(1);
        inputVersionBo.setVersion(1);
        
        requestDTO.setTemplateVersionBos(Arrays.asList(inputVersionBo));
        
        // 模拟 getTemplateVersionByTemplateIdAndVersion 方法返回 null
        when(econtractTemplateVersionMapper.getTemplateVersionByTemplateIdAndVersion(eq(1), eq(1)))
                .thenReturn(null);
        
        // act
        Boolean result = econtractTemplateVersionService.syncTemplate4HaiLuo(requestDTO);
        
        // assert
        assertTrue(result);
        verify(econtractTemplateVersionMapper, times(1)).getTemplateVersionByTemplateIdAndVersion(eq(1), eq(1));
        // verify(econtractTemplateVersionService, never()).syncTemplate2HaiLuo(any(EcontractTemplateVersionBo.class));
    }
}

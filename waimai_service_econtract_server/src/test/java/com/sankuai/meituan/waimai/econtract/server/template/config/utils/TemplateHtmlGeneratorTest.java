package com.sankuai.meituan.waimai.econtract.server.template.config.utils;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import com.sankuai.meituan.waimai.econtract.server.template.config.utils.TemplateHtmlGenerator;
import com.sankuai.meituan.waimai.econtrct.client.template.config.domain.EcontractTemplateVersionBo;
import com.sankuai.meituan.waimai.econtrct.client.template.config.exception.EcontractTemplateConfigException;
import com.sankuai.meituan.waimai.econtrct.client.util.MtCloudS3Util;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class TemplateHtmlGeneratorTest {

    /**
     * Test case when EcontractTemplateVersionBo object is null.
     */
    @Test(expected = EcontractTemplateConfigException.class)
    public void testGenerateHtmlAndUploadToS3NullTemplateVersionBo() throws Throwable {
        TemplateHtmlGenerator.generateHtmlAndUploadToS3(null);
    }

    /**
     * Test case when EcontractTemplateVersionBo object's targetContent field is empty.
     */
    @Test(expected = EcontractTemplateConfigException.class)
    public void testGenerateHtmlAndUploadToS3EmptyTargetContent() throws Throwable {
        EcontractTemplateVersionBo templateVersionBo = mock(EcontractTemplateVersionBo.class);
        when(templateVersionBo.getSourceContent()).thenReturn("");
        TemplateHtmlGenerator.generateHtmlAndUploadToS3(templateVersionBo);
    }

    /**
     * Test case when EcontractTemplateVersionBo object's dynamicMetaList field is not empty,
     * but valueName field format is incorrect.
     */
    @Test
    public void testGenerateHtmlAndUploadToS3InvalidDynamicMetaList() throws Throwable {
        try (MockedStatic<ConfigUtilAdapter> configUtilAdapterMock = Mockito.mockStatic(ConfigUtilAdapter.class);
            MockedStatic<MccConfig> mccConfigMock = Mockito.mockStatic(MccConfig.class);
            MockedStatic<MtCloudS3Util> s3UtilMock = Mockito.mockStatic(MtCloudS3Util.class)) {
            // Mock config
            configUtilAdapterMock.when(() -> ConfigUtilAdapter.getString(anyString(), anyString())).thenReturn("https://test-s3-url.com/");
            mccConfigMock.when(MccConfig::getPdfUrlPrefix).thenReturn("https://test-s3-url.com/");
            // Mock S3 upload to return a test URL
            s3UtilMock.when(() -> MtCloudS3Util.retryUploadFileFromBytes(any(), any())).thenReturn("test-file-url.html");
            EcontractTemplateVersionBo templateVersionBo = mock(EcontractTemplateVersionBo.class);
            when(templateVersionBo.getSourceContent()).thenReturn("valid content");
            when(templateVersionBo.getTemplateId()).thenReturn(1);
            when(templateVersionBo.getVersion()).thenReturn(1);
            Pair<String, String> result = TemplateHtmlGenerator.generateHtmlAndUploadToS3(templateVersionBo);
            assertNotNull(result);
            assertEquals("https://test-s3-url.com/test-file-url.html", result.getLeft());
            assertEquals("template_1_v1.html", result.getRight());
        }
    }

    /**
     * Test case when EcontractTemplateVersionBo object's jsonData field is not empty and can be parsed correctly.
     */
    @Test
    public void testGenerateHtmlAndUploadToS3ValidJsonData() throws Throwable {
        try (MockedStatic<ConfigUtilAdapter> configUtilAdapterMock = Mockito.mockStatic(ConfigUtilAdapter.class);
            MockedStatic<MccConfig> mccConfigMock = Mockito.mockStatic(MccConfig.class);
            MockedStatic<MtCloudS3Util> s3UtilMock = Mockito.mockStatic(MtCloudS3Util.class)) {
            // Mock config
            configUtilAdapterMock.when(() -> ConfigUtilAdapter.getString(anyString(), anyString())).thenReturn("https://test-s3-url.com/");
            mccConfigMock.when(MccConfig::getPdfUrlPrefix).thenReturn("https://test-s3-url.com/");
            // Mock S3 upload to return a test URL
            s3UtilMock.when(() -> MtCloudS3Util.retryUploadFileFromBytes(any(), any())).thenReturn("test-file-url.html");
            EcontractTemplateVersionBo templateVersionBo = mock(EcontractTemplateVersionBo.class);
            when(templateVersionBo.getTemplateId()).thenReturn(1);
            when(templateVersionBo.getSourceContent()).thenReturn("valid content");
            when(templateVersionBo.getVersion()).thenReturn(1);
            Pair<String, String> result = TemplateHtmlGenerator.generateHtmlAndUploadToS3(templateVersionBo);
            assertNotNull(result);
            assertEquals("https://test-s3-url.com/test-file-url.html", result.getLeft());
            assertEquals("template_1_v1.html", result.getRight());
        }
    }

    /**
     * Test case when EcontractTemplateVersionBo object's jsonData field is not empty, but cannot be parsed.
     */
    @Test(expected = EcontractTemplateConfigException.class)
    public void testGenerateHtmlAndUploadToS3InvalidJsonData() throws Throwable {
        EcontractTemplateVersionBo templateVersionBo = mock(EcontractTemplateVersionBo.class);
        when(templateVersionBo.getSourceContent()).thenReturn("valid content");
        TemplateHtmlGenerator.generateHtmlAndUploadToS3(templateVersionBo);
    }

    /**
     * Test case when PdfStringTemplateGenerator fails to generate HTML content.
     */
    @Test(expected = EcontractTemplateConfigException.class)
    public void testGenerateHtmlAndUploadToS3GenerateHtmlFailed() throws Throwable {
        try (MockedStatic<ConfigUtilAdapter> configUtilAdapterMock = Mockito.mockStatic(ConfigUtilAdapter.class);
            MockedStatic<MccConfig> mccConfigMock = Mockito.mockStatic(MccConfig.class)) {
            configUtilAdapterMock.when(() -> ConfigUtilAdapter.getString(anyString(), anyString())).thenReturn("https://test-s3-url.com/");
            mccConfigMock.when(MccConfig::getPdfUrlPrefix).thenReturn("https://test-s3-url.com/");
            EcontractTemplateVersionBo templateVersionBo = mock(EcontractTemplateVersionBo.class);
            when(templateVersionBo.getSourceContent()).thenReturn("invalid content");
            TemplateHtmlGenerator.generateHtmlAndUploadToS3(templateVersionBo);
        }
    }

    /**
     * Test case when uploading HTML file to S3 fails.
     */
    @Test(expected = EcontractTemplateConfigException.class)
    public void testGenerateHtmlAndUploadToS3UploadFailed() throws Throwable {
        try (MockedStatic<ConfigUtilAdapter> configUtilAdapterMock = Mockito.mockStatic(ConfigUtilAdapter.class);
            MockedStatic<MccConfig> mccConfigMock = Mockito.mockStatic(MccConfig.class);
            MockedStatic<MtCloudS3Util> s3UtilMock = Mockito.mockStatic(MtCloudS3Util.class)) {
            configUtilAdapterMock.when(() -> ConfigUtilAdapter.getString(anyString(), anyString())).thenReturn("https://test-s3-url.com/");
            mccConfigMock.when(MccConfig::getPdfUrlPrefix).thenReturn("https://test-s3-url.com/");
            // Mock S3 upload to throw exception
            s3UtilMock.when(() -> MtCloudS3Util.retryUploadFileFromBytes(any(), any())).thenThrow(new RuntimeException("S3 upload failed"));
            EcontractTemplateVersionBo templateVersionBo = mock(EcontractTemplateVersionBo.class);
            when(templateVersionBo.getSourceContent()).thenReturn("valid content");
            
            TemplateHtmlGenerator.generateHtmlAndUploadToS3(templateVersionBo);
        }
    }
}

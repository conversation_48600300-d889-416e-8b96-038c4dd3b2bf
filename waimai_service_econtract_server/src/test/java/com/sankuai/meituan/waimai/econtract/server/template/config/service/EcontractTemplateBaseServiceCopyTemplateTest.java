package com.sankuai.meituan.waimai.econtract.server.template.config.service;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.econtract.server.template.config.dao.EcontractTemplateBaseMapper;
import com.sankuai.meituan.waimai.econtract.server.template.config.entity.EcontractTemplateBaseEntity;
import com.sankuai.meituan.waimai.econtrct.client.template.config.domain.EcontractTemplateVersionBo;
import com.sankuai.meituan.waimai.econtrct.client.template.config.exception.EcontractTemplateConfigException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class EcontractTemplateBaseServiceCopyTemplateTest {

    @Mock
    private EcontractTemplateBaseMapper econtractTemplateBaseMapper;

    @Mock
    private EcontractTemplateOplogService econtractTemplateOplogService;

    @Mock
    private EcontractTemplateVersionService econtractTemplateVersionService;

    @InjectMocks
    private EcontractTemplateBaseService econtractTemplateBaseService;

    private static final int VALID_TEMPLATE_ID = 123;

    private static final int INVALID_TEMPLATE_ID = 999;

    private static final int OP_UID = 456;

    private static final String OP_UNAME = "testUser";

    private static final String OP_UMIS_ID = "testMisId";

    // Assuming COPY_TEMPLATE has value 2
    private static final int COPY_TEMPLATE_OP_TYPE = 2;

    private static final int NEW_TEMPLATE_ID = 789;

    @Before
    public void setUp() {
        // Mock the oplog service calls
        when(econtractTemplateOplogService.getOpLogFormat(anyInt())).thenReturn("新建模板（复制于模版 ID%d v%d）");
        when(econtractTemplateOplogService.getBaseDiffRemark(any(), any())).thenReturn("");
        doNothing().when(econtractTemplateOplogService).insertEcontractTemplateOplog(anyInt(), anyInt(), anyInt(), anyString(), anyString(), anyString());
        // Mock the insertTemplateBase to return a new ID
        when(econtractTemplateBaseMapper.insertTemplateBase(any(EcontractTemplateBaseEntity.class))).thenAnswer(invocation -> {
            EcontractTemplateBaseEntity entity = invocation.getArgument(0);
            entity.setId(NEW_TEMPLATE_ID);
            return null;
        });
    }

    /**
     * Test successful template copy with valid source template
     */
    @Test
    public void testCopyTemplateSuccess() throws Throwable {
        // arrange
        EcontractTemplateBaseEntity sourceTemplate = new EcontractTemplateBaseEntity();
        sourceTemplate.setId(VALID_TEMPLATE_ID);
        sourceTemplate.setName("Test Template");
        sourceTemplate.setDescription("Test Description");
        sourceTemplate.setTags("tag1,tag2");
        sourceTemplate.setEcontractUserId(1);
        sourceTemplate.setSignFlowTemplateId(2);
        sourceTemplate.setSignPageTemplateId(3);
        EcontractTemplateVersionBo versionBo = new EcontractTemplateVersionBo();
        versionBo.setVersion(1);
        when(econtractTemplateBaseMapper.getTemplateBaseById(VALID_TEMPLATE_ID)).thenReturn(sourceTemplate);
        when(econtractTemplateVersionService.getLatestTemplateVersion(VALID_TEMPLATE_ID, true)).thenReturn(versionBo);
        // act
        econtractTemplateBaseService.copyTemplate(VALID_TEMPLATE_ID, OP_UID, OP_UNAME, OP_UMIS_ID);
        // assert
        verify(econtractTemplateBaseMapper).getTemplateBaseById(VALID_TEMPLATE_ID);
        verify(econtractTemplateBaseMapper).insertTemplateBase(any(EcontractTemplateBaseEntity.class));
        verify(econtractTemplateVersionService).getLatestTemplateVersion(VALID_TEMPLATE_ID, true);
        verify(econtractTemplateOplogService).insertEcontractTemplateOplog(eq(NEW_TEMPLATE_ID), eq(COPY_TEMPLATE_OP_TYPE), eq(OP_UID), eq(OP_UNAME), eq(OP_UMIS_ID), anyString());
    }

    /**
     * Test template copy when source template not found
     */
    @Test(expected = EcontractTemplateConfigException.class)
    public void testCopyTemplateSourceNotFound() throws Throwable {
        // arrange
        when(econtractTemplateBaseMapper.getTemplateBaseById(INVALID_TEMPLATE_ID)).thenReturn(null);
        // act
        econtractTemplateBaseService.copyTemplate(INVALID_TEMPLATE_ID, OP_UID, OP_UNAME, OP_UMIS_ID);
        // assert - exception expected
    }

    /**
     * Test template copy with minimal source template fields
     */
    @Test
    public void testCopyTemplateMinimalFields() throws Throwable {
        // arrange
        EcontractTemplateBaseEntity sourceTemplate = new EcontractTemplateBaseEntity();
        sourceTemplate.setId(VALID_TEMPLATE_ID);
        sourceTemplate.setName("Minimal Template");
        EcontractTemplateVersionBo versionBo = new EcontractTemplateVersionBo();
        versionBo.setVersion(1);
        when(econtractTemplateBaseMapper.getTemplateBaseById(VALID_TEMPLATE_ID)).thenReturn(sourceTemplate);
        when(econtractTemplateVersionService.getLatestTemplateVersion(VALID_TEMPLATE_ID, true)).thenReturn(versionBo);
        // act
        econtractTemplateBaseService.copyTemplate(VALID_TEMPLATE_ID, OP_UID, OP_UNAME, OP_UMIS_ID);
        // assert
        verify(econtractTemplateBaseMapper).getTemplateBaseById(VALID_TEMPLATE_ID);
        verify(econtractTemplateBaseMapper).insertTemplateBase(argThat(template -> "Minimal Template-副本".equals(template.getName()) && template.getDescription() == null));
        verify(econtractTemplateOplogService).insertEcontractTemplateOplog(eq(NEW_TEMPLATE_ID), eq(COPY_TEMPLATE_OP_TYPE), eq(OP_UID), eq(OP_UNAME), eq(OP_UMIS_ID), anyString());
    }

    /**
     * Test template copy with empty operator name and misId
     */
    @Test
    public void testCopyTemplateEmptyOperatorInfo() throws Throwable {
        // arrange
        EcontractTemplateBaseEntity sourceTemplate = new EcontractTemplateBaseEntity();
        sourceTemplate.setId(VALID_TEMPLATE_ID);
        sourceTemplate.setName("Test Template");
        EcontractTemplateVersionBo versionBo = new EcontractTemplateVersionBo();
        versionBo.setVersion(1);
        when(econtractTemplateBaseMapper.getTemplateBaseById(VALID_TEMPLATE_ID)).thenReturn(sourceTemplate);
        when(econtractTemplateVersionService.getLatestTemplateVersion(VALID_TEMPLATE_ID, true)).thenReturn(versionBo);
        // act
        econtractTemplateBaseService.copyTemplate(VALID_TEMPLATE_ID, OP_UID, "", "");
        // assert
        verify(econtractTemplateBaseMapper).insertTemplateBase(argThat(template -> "".equals(template.getCuname()) && "".equals(template.getCumisId())));
        verify(econtractTemplateOplogService).insertEcontractTemplateOplog(eq(NEW_TEMPLATE_ID), eq(COPY_TEMPLATE_OP_TYPE), eq(OP_UID), eq(""), eq(""), anyString());
    }

    /**
     * Test template copy with null operator name and misId
     */
    @Test
    public void testCopyTemplateNullOperatorInfo() throws Throwable {
        // arrange
        EcontractTemplateBaseEntity sourceTemplate = new EcontractTemplateBaseEntity();
        sourceTemplate.setId(VALID_TEMPLATE_ID);
        sourceTemplate.setName("Test Template");
        EcontractTemplateVersionBo versionBo = new EcontractTemplateVersionBo();
        versionBo.setVersion(1);
        when(econtractTemplateBaseMapper.getTemplateBaseById(VALID_TEMPLATE_ID)).thenReturn(sourceTemplate);
        when(econtractTemplateVersionService.getLatestTemplateVersion(VALID_TEMPLATE_ID, true)).thenReturn(versionBo);
        // act
        econtractTemplateBaseService.copyTemplate(VALID_TEMPLATE_ID, OP_UID, null, null);
        // assert
        verify(econtractTemplateBaseMapper).insertTemplateBase(argThat(template -> template.getCuname() == null && template.getCumisId() == null));
        verify(econtractTemplateOplogService).insertEcontractTemplateOplog(eq(NEW_TEMPLATE_ID), eq(COPY_TEMPLATE_OP_TYPE), eq(OP_UID), eq(null), eq(null), anyString());
    }

    /**
     * Test template copy with zero template ID
     */
    @Test(expected = EcontractTemplateConfigException.class)
    public void testCopyTemplateZeroTemplateId() throws Throwable {
        // arrange
        when(econtractTemplateBaseMapper.getTemplateBaseById(0)).thenReturn(null);
        // act
        econtractTemplateBaseService.copyTemplate(0, OP_UID, OP_UNAME, OP_UMIS_ID);
        // assert - exception expected
    }

    /**
     * Test template copy with negative template ID
     */
    @Test(expected = EcontractTemplateConfigException.class)
    public void testCopyTemplateNegativeTemplateId() throws Throwable {
        // arrange
        when(econtractTemplateBaseMapper.getTemplateBaseById(-1)).thenReturn(null);
        // act
        econtractTemplateBaseService.copyTemplate(-1, OP_UID, OP_UNAME, OP_UMIS_ID);
        // assert - exception expected
    }
}

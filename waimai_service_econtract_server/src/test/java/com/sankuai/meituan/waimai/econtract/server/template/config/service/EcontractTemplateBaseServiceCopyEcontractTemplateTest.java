package com.sankuai.meituan.waimai.econtract.server.template.config.service;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.meituan.it.contract.common.dto.PageDTO;
import com.meituan.it.contract.platform.model.dto.template.ContractTemplateDTO;
import com.meituan.it.contract.platform.model.request.template.QueryContractTemplateReq;
import com.meituan.it.contract.platform.model.response.template.QueryContractTemplateResp;
import com.sankuai.meituan.waimai.econtract.server.adapter.UacAuthRemoteServiceAdapter;
import com.sankuai.meituan.waimai.econtract.server.adapter.WmCustomerGlobalEcontractThriftServiceAdapter;
import com.sankuai.meituan.waimai.econtract.server.adapter.WmEmployAdapter;
import com.sankuai.meituan.waimai.econtract.server.adapter.hailuo.ContractTemplateQueryThriftServiceAdapter;
import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractTemplateTagEntityMapper;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractSignPageTemplateEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractTemplateTagEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractUserEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.config.FrameContractConfigInfoPo;
import com.sankuai.meituan.waimai.econtract.server.service.ability.EcontractUserAbilityService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractSignPageTemplateService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractUserService;
import com.sankuai.meituan.waimai.econtract.server.service.config.EcontractConfigCommonService;
import com.sankuai.meituan.waimai.econtract.server.service.config.FrameContractConfigInfoBaseService;
import com.sankuai.meituan.waimai.econtract.server.template.config.dao.EcontractTemplateBaseMapper;
import com.sankuai.meituan.waimai.econtract.server.template.config.entity.EcontractTemplateBaseEntity;
import com.sankuai.meituan.waimai.econtract.server.template.config.entity.EcontractTemplateVersionEntity;
import com.sankuai.meituan.waimai.econtract.server.template.config.entity.EcontractTemplateVersionSimpleEntity;
import com.sankuai.meituan.waimai.econtract.server.template.config.service.mapperservice.EcontractTemplateBaseMapperService;
import com.sankuai.meituan.waimai.econtract.server.template.config.utils.EcontractTemplateTransUtil;
import com.sankuai.meituan.waimai.econtract.server.utils.JacksonUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.biz.EcontractUserBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.config.OperatorDTO;
import com.sankuai.meituan.waimai.econtrct.client.domain.config.econtracttemplate.EcontractContentTemplateCopyRequestDTO;
import com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException;
import com.sankuai.meituan.waimai.econtrct.client.template.config.constants.EcontractTemplateConstants;
import com.sankuai.meituan.waimai.econtrct.client.template.config.domain.EcontractTemplateBaseBo;
import com.sankuai.meituan.waimai.econtrct.client.template.config.domain.EcontractTemplateVersionBo;
import com.sankuai.meituan.waimai.econtrct.client.template.config.exception.EcontractTemplateConfigException;
import java.util.Arrays;
import java.util.Collections;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class EcontractTemplateBaseServiceCopyEcontractTemplateTest {

    @Mock
    private EcontractTemplateBaseMapper econtractTemplateBaseMapper;

    @Mock
    private EcontractTemplateVersionService econtractTemplateVersionService;

    @Mock
    private EcontractTemplateOplogService econtractTemplateOplogService;

    @Mock
    private EcontractTemplateBaseMapperService econtractTemplateBaseMapperService;

    @Mock
    private EcontractUserAbilityService econtractUserAbilityService;

    @Mock
    private ContractTemplateQueryThriftServiceAdapter contractTemplateQueryThriftServiceAdapter;

    @InjectMocks
    private EcontractTemplateBaseService econtractTemplateBaseService;

    private EcontractContentTemplateCopyRequestDTO requestDTO;

    private EcontractTemplateBaseEntity sourceTemplate;

    private EcontractTemplateVersionBo versionBo;

    private EcontractUserBo econtractUserBo;

    private EcontractTemplateVersionEntity versionEntity;

    @Before
    public void setUp() {
        OperatorDTO operatorDTO = new OperatorDTO();
        operatorDTO.setOpUid(123);
        operatorDTO.setOpUname("testUser");
        operatorDTO.setOpUmisId("testMisId");
        EcontractTemplateBaseBo templateBaseBo = new EcontractTemplateBaseBo();
        templateBaseBo.setName("Test Template");
        templateBaseBo.setDescription("Test Description");
        templateBaseBo.setTags(Arrays.asList("tag1", "tag2"));
        templateBaseBo.setWmEcontractUserId(456);
        templateBaseBo.setHaiLuoTemplateCode("HAILUO123");
        requestDTO = new EcontractContentTemplateCopyRequestDTO();
        requestDTO.setOriginalTemplateId(1);
        requestDTO.setTemplateBaseBo(templateBaseBo);
        requestDTO.setOperatorDTO(operatorDTO);
        sourceTemplate = EcontractTemplateBaseEntity.builder().id(1).name("Source Template").signFlowTemplateId(100).signPageTemplateId(200).build();
        versionBo = new EcontractTemplateVersionBo();
        versionBo.setVersion(1);
        versionBo.setTemplateId(1);
        versionBo.setId(100);
        versionEntity = new EcontractTemplateVersionEntity();
        versionEntity.setId(100);
        versionEntity.setTemplateId(1);
        versionEntity.setVersion(1);
        econtractUserBo = new EcontractUserBo();
        econtractUserBo.setUserName("testUser");
        // Common mock setups
        when(econtractUserAbilityService.queryEcontractUserById(anyInt())).thenReturn(econtractUserBo);
        when(econtractTemplateOplogService.getOpLogFormat(anyInt())).thenReturn("Test log format %d %d");
        when(econtractTemplateOplogService.getBaseDiffRemark(any(), any())).thenReturn("Test diff remark");
        when(econtractTemplateVersionService.getLatestTemplateVersion(anyInt(), anyBoolean())).thenReturn(versionBo);
    }

    /**
     * Test successful template copy with hailuo integration enabled
     */
    @Test
    public void testCopyEcontractTemplateSuccessWithHailuo() throws Throwable {
        try (MockedStatic<MccConfig> mocked = Mockito.mockStatic(MccConfig.class)) {
            mocked.when(MccConfig::supportIntegrationHaiLuo).thenReturn(true);
            mocked.when(MccConfig::getXianFuAppCodeInHaiLuo).thenReturn("TEST_APP_CODE");
            QueryContractTemplateResp resp = new QueryContractTemplateResp();
            ContractTemplateDTO templateDTO = new ContractTemplateDTO();
            resp.setTemplate(Collections.singletonList(templateDTO));
            when(econtractTemplateBaseMapper.getTemplateBaseById(anyInt())).thenReturn(sourceTemplate);
            when(econtractTemplateBaseMapperService.getTemplateBaseByHaiLuoTemplateCode(anyString())).thenReturn(null);
            when(contractTemplateQueryThriftServiceAdapter.queryContractTemplate(any())).thenReturn(resp);
            // Mock insert operation to return generated ID
            doAnswer(invocation -> {
                EcontractTemplateBaseEntity entity = invocation.getArgument(0);
                // Set new ID
                entity.setId(2);
                return null;
            }).when(econtractTemplateBaseMapper).insertTemplateBase(any());
            // act
            boolean result = econtractTemplateBaseService.copyEcontractTemplate(requestDTO);
            // assert
            assertTrue(result);
            verify(econtractTemplateBaseMapper).insertTemplateBase(any());
            verify(econtractTemplateOplogService).insertEcontractTemplateOplog(anyInt(), anyInt(), anyInt(), anyString(), anyString(), anyString());
            verify(econtractTemplateVersionService).insertTemplateVersion(any(), anyInt(), anyString(), anyString(), anyInt());
        }
    }

    /**
     * Test template copy when source template not found
     */
    @Test(expected = EcontractTemplateConfigException.class)
    public void testCopyEcontractTemplateSourceNotFound() throws Throwable {
        // arrange
        when(econtractTemplateBaseMapper.getTemplateBaseById(anyInt())).thenReturn(null);
        // act
        econtractTemplateBaseService.copyEcontractTemplate(requestDTO);
        // assert - exception expected
    }

    /**
     * Test template copy with hailuo integration enabled but no template code provided
     */
    @Test(expected = EcontractTemplateConfigException.class)
    public void testCopyEcontractTemplateHailuoEnabledButNoCode() throws Throwable {
        try (MockedStatic<MccConfig> mocked = Mockito.mockStatic(MccConfig.class)) {
            mocked.when(MccConfig::supportIntegrationHaiLuo).thenReturn(true);
            // arrange
            requestDTO.getTemplateBaseBo().setHaiLuoTemplateCode(null);
            when(econtractTemplateBaseMapper.getTemplateBaseById(anyInt())).thenReturn(sourceTemplate);
            // act
            econtractTemplateBaseService.copyEcontractTemplate(requestDTO);
            // assert - exception expected
        }
    }

    /**
     * Test template copy with hailuo integration disabled
     */
    @Test
    public void testCopyEcontractTemplateHailuoDisabled() throws Throwable {
        try (MockedStatic<MccConfig> mocked = Mockito.mockStatic(MccConfig.class)) {
            mocked.when(MccConfig::supportIntegrationHaiLuo).thenReturn(false);
            // Mock insert operation to return generated ID
            doAnswer(invocation -> {
                EcontractTemplateBaseEntity entity = invocation.getArgument(0);
                // Set new ID
                entity.setId(2);
                return null;
            }).when(econtractTemplateBaseMapper).insertTemplateBase(any());
            // arrange
            when(econtractTemplateBaseMapper.getTemplateBaseById(anyInt())).thenReturn(sourceTemplate);
            // act
            boolean result = econtractTemplateBaseService.copyEcontractTemplate(requestDTO);
            // assert
            assertTrue(result);
            verify(econtractTemplateBaseMapper).insertTemplateBase(any());
            verify(econtractTemplateBaseMapperService, never()).getTemplateBaseByHaiLuoTemplateCode(anyString());
        }
    }

    /**
     * Test template copy with hailuo validation failure (duplicate template code)
     */
    @Test(expected = EcontractTemplateConfigException.class)
    public void testCopyEcontractTemplateHailuoValidationFailure() throws Throwable {
        try (MockedStatic<MccConfig> mocked = Mockito.mockStatic(MccConfig.class)) {
            mocked.when(MccConfig::supportIntegrationHaiLuo).thenReturn(true);
            mocked.when(MccConfig::getXianFuAppCodeInHaiLuo).thenReturn("TEST_APP_CODE");
            // arrange
            when(econtractTemplateBaseMapper.getTemplateBaseById(anyInt())).thenReturn(sourceTemplate);
            // Simulate duplicate hailuo template by returning an existing template
            EcontractTemplateBaseEntity existingTemplate = new EcontractTemplateBaseEntity();
            // Ensure queryContractTemplate returns a valid response to avoid NPE
            QueryContractTemplateResp resp = new QueryContractTemplateResp();
            resp.setTemplate(Collections.emptyList());
            when(contractTemplateQueryThriftServiceAdapter.queryContractTemplate(any())).thenReturn(resp);
            // act
            econtractTemplateBaseService.copyEcontractTemplate(requestDTO);
            // assert - exception expected
        }
    }

    /**
     * Test template copy with hailuo validation failure (invalid association)
     */
    @Test(expected = EcontractTemplateConfigException.class)
    public void testCopyEcontractTemplateHailuoInvalidAssociation() throws Throwable {
        try (MockedStatic<MccConfig> mocked = Mockito.mockStatic(MccConfig.class)) {
            mocked.when(MccConfig::supportIntegrationHaiLuo).thenReturn(true);
            mocked.when(MccConfig::getXianFuAppCodeInHaiLuo).thenReturn("TEST_APP_CODE");
            // arrange
            when(econtractTemplateBaseMapper.getTemplateBaseById(anyInt())).thenReturn(sourceTemplate);
            // Simulate invalid association by returning empty template list
            QueryContractTemplateResp resp = new QueryContractTemplateResp();
            resp.setTemplate(Collections.emptyList());
            when(contractTemplateQueryThriftServiceAdapter.queryContractTemplate(any())).thenReturn(resp);
            // act
            econtractTemplateBaseService.copyEcontractTemplate(requestDTO);
            // assert - exception expected
        }
    }
}

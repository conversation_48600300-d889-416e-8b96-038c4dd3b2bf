package com.sankuai.meituan.waimai.econtract.server.service.biz.Impl;

import com.sankuai.meituan.waimai.econtract.server.adapter.UacAuthRemoteServiceAdapter;
import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractEntityMapper;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractEntity;
import com.sankuai.meituan.waimai.econtrct.client.domain.config.econtracttemplate.EcontractTemplateCopyRequestDTO;
import com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;

import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class EcontractServiceTest {

    @InjectMocks
    private EcontractServiceImpl econtractService;

    @Mock
    private EcontractEntityMapper econtractEntityMapper;

    @Mock
    private UacAuthRemoteServiceAdapter uacAuthRemoteServiceAdapter;

    private EcontractTemplateCopyRequestDTO requestDTO;

    @Before
    public void setUp() {
        requestDTO = new EcontractTemplateCopyRequestDTO();
        requestDTO.setId(1);
        requestDTO.setName("New Contract Template");
        requestDTO.setUserNameId(2);
        requestDTO.setEcontractType("AI");
        requestDTO.setOperatorId(3);
    }

    /**
     * 测试copyContractTemplateForAI方法，成功复制合同模板
     */
    @Test
    public void testCopyContractTemplateForAI_Success() throws EcontractException {
        try (MockedStatic<MccConfig> mccConfigMockedStatic = Mockito.mockStatic(MccConfig.class)) {
            mccConfigMockedStatic.when(MccConfig::getSuperAdminRoleId).thenReturn(1L);
            // arrange
            when(uacAuthRemoteServiceAdapter.getAllRoleIdByUid(requestDTO.getOperatorId())).thenReturn(Arrays.asList(1L, 2L, 3L));
            EcontractEntity sourceEntity = new EcontractEntity();
            when(econtractEntityMapper.queryValidEcontract(requestDTO.getId())).thenReturn(sourceEntity);
            when(econtractEntityMapper.insertSelective(any(EcontractEntity.class))).thenReturn(1); // 模拟返回值

            // act
            boolean result = econtractService.copyContractTemplateForAI(requestDTO);

            // assert
            assertTrue(result);
            verify(econtractEntityMapper, times(1)).insertSelective(any(EcontractEntity.class));
        }
    }

    /**
     * 测试copyContractTemplateForAI方法，当用户没有权限时抛出异常
     */
    @Test(expected = EcontractException.class)
    public void testCopyContractTemplateForAI_NoPermission() throws EcontractException {
        try (MockedStatic<MccConfig> mccConfigMockedStatic = Mockito.mockStatic(MccConfig.class)) {
            // arrange
            when(uacAuthRemoteServiceAdapter.getAllRoleIdByUid(requestDTO.getOperatorId())).thenReturn(Collections.emptyList());
            mccConfigMockedStatic.when(MccConfig::getSuperAdminRoleId).thenReturn(1L);
            mccConfigMockedStatic.when(MccConfig::getContractConfigRDRoleId).thenReturn(2L);
            mccConfigMockedStatic.when(MccConfig::getContractConfigYWRoleId).thenReturn(3L);

            // act
            econtractService.copyContractTemplateForAI(requestDTO);

            // assert is handled by the expected exception
        }
    }

    /**
     * 测试copyContractTemplateForAI方法，当源合同模板不存在时抛出异常
     */
    @Test(expected = EcontractException.class)
    public void testCopyContractTemplateForAI_SourceTemplateNotFound() throws EcontractException {
        try (MockedStatic<MccConfig> mccConfigMockedStatic = Mockito.mockStatic(MccConfig.class)) {
            // arrange
            when(uacAuthRemoteServiceAdapter.getAllRoleIdByUid(requestDTO.getOperatorId())).thenReturn(Arrays.asList(1L, 2L, 3L));
            when(econtractEntityMapper.queryValidEcontract(requestDTO.getId())).thenReturn(null);
            mccConfigMockedStatic.when(MccConfig::getSuperAdminRoleId).thenReturn(1L);
            // act
            econtractService.copyContractTemplateForAI(requestDTO);
            // assert is handled by the expected exception
        }
    }

    /**
     * 测试copyContractTemplateForAI方法，当请求参数校验失败时抛出异常
     */
    @Test(expected = EcontractException.class)
    public void testCopyContractTemplateForAI_InvalidParams() throws EcontractException {
        // arrange
        EcontractTemplateCopyRequestDTO invalidRequestDTO = new EcontractTemplateCopyRequestDTO();

        // act
        econtractService.copyContractTemplateForAI(invalidRequestDTO);

        // assert is handled by the expected exception
    }
}

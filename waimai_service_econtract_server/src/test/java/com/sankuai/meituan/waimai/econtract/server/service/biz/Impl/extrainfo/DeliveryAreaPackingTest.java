package com.sankuai.meituan.waimai.econtract.server.service.biz.Impl.extrainfo;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import com.sankuai.meituan.waimai.econtract.server.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordEntity;
import com.sankuai.meituan.waimai.econtract.server.utils.ContextUtils;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractPdfTypeEnum;
import com.sankuai.meituan.waimai.econtrct.client.domain.applybiz.SignAdditionInfoMeta;
import com.sankuai.meituan.waimai.econtrct.client.domain.applybiz.SignAdditionInfoText;
import com.sankuai.meituan.waimai.econtrct.client.domain.applybiz.WmContractContentAggreBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextDTO;
import com.sankuai.meituan.waimai.thrift.customer.service.WmEcontractSignThriftService;
import org.apache.commons.collections.MapUtils;
import com.sankuai.meituan.waimai.econtrct.client.domain.applybiz.SignAdditionInfoTypeEnum;
import org.junit.Before;
import org.junit.Test;
import org.mockito.*;

import static org.mockito.Mockito.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * DeliveryAreaPacking 测试类
 *
 * @Author: wangyongfang
 * @Date: 2024-05-07
 */
public class DeliveryAreaPackingTest {

    @Mock
    private WmEcontractSignThriftService wmEcontractSignThriftService;

    @InjectMocks
    private DeliveryAreaPacking deliveryAreaPacking;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试packing方法，当合同任务类型不在指定范围内时，不进行任何操作
     */
    @Test
    public void testPackingContractTaskTypeNotMatch() throws Exception {
        EcontractRecordEntity recordEntity = new EcontractRecordEntity();
        List<SignAdditionInfoMeta> additionInfoMetaList = new ArrayList<>();
        WmContractContentAggreBo aggreBo = new WmContractContentAggreBo();
        aggreBo.setContractTaskType("NOT_MATCH");

        deliveryAreaPacking.packing(recordEntity, additionInfoMetaList, aggreBo);

        assert additionInfoMetaList.isEmpty();
    }

    /**
     * 测试packing方法，当areaMap为空时，不添加任何信息
     */
    @Test
    public void testPackingAreaMapEmpty() throws Exception {
        EcontractRecordEntity recordEntity = new EcontractRecordEntity();
        recordEntity.setEcontractRecordContext(JSON.toJSONString(new EcontractContext()));
        List<SignAdditionInfoMeta> additionInfoMetaList = new ArrayList<>();
        WmContractContentAggreBo aggreBo = new WmContractContentAggreBo();
        aggreBo.setContractTaskType(EcontractPdfTypeEnum.DELIVERY.getName());

        deliveryAreaPacking.packing(recordEntity, additionInfoMetaList, aggreBo);

        assert additionInfoMetaList.isEmpty();
    }
}
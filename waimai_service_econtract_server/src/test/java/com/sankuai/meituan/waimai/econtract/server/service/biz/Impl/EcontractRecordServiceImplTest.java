package com.sankuai.meituan.waimai.econtract.server.service.biz.Impl;

import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractRecordEntityMapper;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordEntity;
import com.sankuai.meituan.waimai.econtract.server.exception.EcontractException;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractMetricService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyString;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractRecordContextMapper;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordContextEntity;
import org.junit.*;
import org.mockito.Spy;
import org.mockito.junit.*;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.Before;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/12/6 14:33
 */
@RunWith(MockitoJUnitRunner.class)
public class EcontractRecordServiceImplTest {

    @Mock
    private EcontractRecordEntityMapper econtractRecordEntityMapper;

    @Mock
    private EcontractMetricService econtractMetricService;

    @InjectMocks
    private EcontractRecordServiceImpl econtractRecordService;

    @Mock
    private EcontractBigRecordParseService econtractBigRecordParseService;

    private static final String VALID_HOT_CONTEXT_JSON = "{\"econtractUserEntity\":null,\"flowList\":null,\"stageInfoBoList\":null,\"stageBatchInfoBoList\":null}";

    private static final String VALID_COLD_CONTEXT_JSON = "{\"econtractUserEntity\":{\"id\":1},\"flowList\":[],\"stageInfoBoList\":[],\"stageBatchInfoBoList\":[]}";

    /**
     * 测试updateEcontractRecord方法，当MccConfig.updateEcontractRecordWithMysqlLockCheck()返回true时
     */
    @Test
    public void testUpdateEcontractRecord_WithMysqlLockCheckTrue() {
        try (MockedStatic<MccConfig> mccConfigMockedStatic = Mockito.mockStatic(MccConfig.class)) {
            mccConfigMockedStatic.when(MccConfig::updateEcontractRecordWithMysqlLockCheck).thenReturn(true);

            EcontractRecordEntity entity = buildEcontractRecordEntity();
            when(econtractRecordEntityMapper.updateNotFailRecordByPrimaryKeySelective(entity)).thenReturn(1);
            // 执行测试方法
            econtractRecordService.updateEcontractRecord(entity);

            // 验证updateNotFailRecordByPrimaryKeySelective方法被调用
            verify(econtractRecordEntityMapper, times(1)).updateNotFailRecordByPrimaryKeySelective(any(EcontractRecordEntity.class));

            // 验证econtractMetricService.metricUpdateFailRecord没有被调用
            verify(econtractMetricService, never()).metricUpdateFailRecord(anyString());
        }
    }
    @Test(expected = EcontractException.class)
    public void testUpdateEcontractRecord_WithMysqlLockCheckFail() {
        try (MockedStatic<MccConfig> mccConfigMockedStatic = Mockito.mockStatic(MccConfig.class)) {
            mccConfigMockedStatic.when(MccConfig::updateEcontractRecordWithMysqlLockCheck).thenReturn(true);

            EcontractRecordEntity entity = buildEcontractRecordEntity();
            when(econtractRecordEntityMapper.updateNotFailRecordByPrimaryKeySelective(entity)).thenReturn(0);
            doNothing().when(econtractMetricService).metricUpdateFailRecord(entity.getRecordKey());

            econtractRecordService.updateEcontractRecord(entity);
            verify(econtractRecordEntityMapper, times(1)).updateNotFailRecordByPrimaryKeySelective(any(EcontractRecordEntity.class));
            verify(econtractMetricService, times(1)).metricUpdateFailRecord(anyString());
        }
    }

    @Test(expected = EcontractException.class)
    public void testUpdateEcontractRecord_QueryNoData() {
        try (MockedStatic<MccConfig> mccConfigMockedStatic = Mockito.mockStatic(MccConfig.class)) {
            mccConfigMockedStatic.when(MccConfig::updateEcontractRecordWithMysqlLockCheck).thenReturn(false);

            EcontractRecordEntity targetEntity = buildEcontractRecordEntity();

            when(econtractRecordEntityMapper.queryRecordByRecordKey(targetEntity.getRecordKey())).thenReturn(null);

            econtractRecordService.updateEcontractRecord(targetEntity);
            verify(econtractRecordEntityMapper, times(0)).updateNotFailRecordByPrimaryKeySelective(any(EcontractRecordEntity.class));
            verify(econtractMetricService, times(0)).metricUpdateFailRecord(anyString());
        }
    }

    /**
     * 测试updateEcontractRecord方法，当数据库状态为FAIL且尝试更新为非FAIL状态时
     */
    @Test(expected = EcontractException.class)
    public void testUpdateEcontractRecord_DbStateFailToNonFail() {
        try (MockedStatic<MccConfig> mccConfigMockedStatic = Mockito.mockStatic(MccConfig.class)) {
            mccConfigMockedStatic.when(MccConfig::updateEcontractRecordWithMysqlLockCheck).thenReturn(false);

            EcontractRecordEntity entity = buildEcontractRecordEntity();

            // 执行测试方法
            econtractRecordService.updateEcontractRecord(entity);
            verify(econtractRecordEntityMapper, times(1)).updateNotFailRecordByPrimaryKeySelective(any(EcontractRecordEntity.class));
            verify(econtractMetricService, times(1)).metricUpdateFailRecord(anyString());
        }
    }
    
    private EcontractRecordEntity buildEcontractRecordEntity() {
        EcontractRecordEntity entity = new EcontractRecordEntity();
        
        return entity;
    }


    @Test
    public void testQueryRecordByRecordKeyWithNonEmptyContext() throws Throwable {
        // arrange
        String recordKey = "test-record-key-123";
        EcontractRecordEntity mockEntity = new EcontractRecordEntity();
        mockEntity.setRecordKey(recordKey);
        mockEntity.setEcontractRecordContext("valid-context-data");
        mockEntity.setId(1);
        mockEntity.setEcontractId(100);
        mockEntity.setEcontractType("TEST_TYPE");
        when(econtractRecordEntityMapper.queryRecordByRecordKey(recordKey)).thenReturn(mockEntity);
        // act
        EcontractRecordEntity result = econtractRecordService.queryRecordByRecordKey(recordKey);
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("Record key should match", recordKey, result.getRecordKey());
        assertEquals("Context should be preserved", "valid-context-data", result.getEcontractRecordContext());
        assertEquals("ID should be preserved", Integer.valueOf(1), result.getId());
        assertEquals("EcontractId should be preserved", Integer.valueOf(100), result.getEcontractId());
        assertEquals("EcontractType should be preserved", "TEST_TYPE", result.getEcontractType());
        // Verify that the mapper was called exactly once
        verify(econtractRecordEntityMapper, times(1)).queryRecordByRecordKey(recordKey);
    }

    @Test
    public void testQueryRecordByRecordKeyWithEmptyContextTriggersRetry() throws Throwable {
        // arrange
        String recordKey = "test-record-key-empty";
        // First call returns entity with empty context
        EcontractRecordEntity entityWithEmptyContext = new EcontractRecordEntity();
        entityWithEmptyContext.setRecordKey(recordKey);
        entityWithEmptyContext.setEcontractRecordContext("");
        entityWithEmptyContext.setId(2);
        // Subsequent retry calls return entity with valid context
        EcontractRecordEntity entityWithContext = new EcontractRecordEntity();
        entityWithContext.setRecordKey(recordKey);
        entityWithContext.setEcontractRecordContext("retry-success-context");
        entityWithContext.setId(2);
        when(econtractRecordEntityMapper.queryRecordByRecordKey(recordKey)).thenReturn(entityWithEmptyContext).thenReturn(entityWithContext);
        // act
        EcontractRecordEntity result = econtractRecordService.queryRecordByRecordKey(recordKey);
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("Record key should match", recordKey, result.getRecordKey());
        assertNotNull("Context should not be null after retry", result.getEcontractRecordContext());
        assertEquals("ID should be preserved", Integer.valueOf(2), result.getId());
        // Verify that the mapper was called multiple times due to retry logic
        verify(econtractRecordEntityMapper, atLeast(2)).queryRecordByRecordKey(recordKey);
    }

    @Test
    public void testQueryRecordByRecordKeyWithNullContextTriggersRetry() throws Throwable {
        // arrange
        String recordKey = "test-record-key-null";
        EcontractRecordEntity entityWithNullContext = new EcontractRecordEntity();
        entityWithNullContext.setRecordKey(recordKey);
        entityWithNullContext.setEcontractRecordContext(null);
        entityWithNullContext.setId(3);
        // Mock to always return entity with null context (simulating retry failure)
        when(econtractRecordEntityMapper.queryRecordByRecordKey(recordKey)).thenReturn(entityWithNullContext);
        // act
        EcontractRecordEntity result = econtractRecordService.queryRecordByRecordKey(recordKey);
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("Record key should match", recordKey, result.getRecordKey());
        assertEquals("ID should be preserved", Integer.valueOf(3), result.getId());
        // Verify that the mapper was called multiple times (1 original + 3 retries)
        verify(econtractRecordEntityMapper, times(4)).queryRecordByRecordKey(recordKey);
    }

    @Test
    public void testQueryRecordByRecordKeyWithCompleteEntityData() throws Throwable {
        // arrange
        String recordKey = "complete-record-key";
        EcontractRecordEntity completeEntity = new EcontractRecordEntity();
        completeEntity.setRecordKey(recordKey);
        completeEntity.setEcontractRecordContext("complete-context");
        completeEntity.setId(5);
        completeEntity.setEcontractId(200);
        completeEntity.setEcontractUserId(300);
        completeEntity.setEcontractType("COMPLETE_TYPE");
        completeEntity.setSaveUrl("http://example.com/save");
        completeEntity.setEcontractState("ACTIVE");
        completeEntity.setEcontractStage("SIGNED");
        completeEntity.setRecordBatchId(400);
        when(econtractRecordEntityMapper.queryRecordByRecordKey(recordKey)).thenReturn(completeEntity);
        // act
        EcontractRecordEntity result = econtractRecordService.queryRecordByRecordKey(recordKey);
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("Record key should match", recordKey, result.getRecordKey());
        assertEquals("Context should match", "complete-context", result.getEcontractRecordContext());
        assertEquals("ID should match", Integer.valueOf(5), result.getId());
        assertEquals("EcontractId should match", Integer.valueOf(200), result.getEcontractId());
        assertEquals("EcontractUserId should match", Integer.valueOf(300), result.getEcontractUserId());
        assertEquals("EcontractType should match", "COMPLETE_TYPE", result.getEcontractType());
        assertEquals("SaveUrl should match", "http://example.com/save", result.getSaveUrl());
        assertEquals("EcontractState should match", "ACTIVE", result.getEcontractState());
        assertEquals("EcontractStage should match", "SIGNED", result.getEcontractStage());
        assertEquals("RecordBatchId should match", Integer.valueOf(400), result.getRecordBatchId());
        verify(econtractRecordEntityMapper, times(1)).queryRecordByRecordKey(recordKey);
    }

    @Test(expected = EcontractException.class)
    public void testQueryRecordByRecordKeyWhenRecordDoesNotExist() throws Throwable {
        // arrange
        String recordKey = "non-existent-key";
        when(econtractRecordEntityMapper.queryRecordByRecordKey(recordKey)).thenReturn(null);
        // act
        econtractRecordService.queryRecordByRecordKey(recordKey);
        // assert - EcontractException expected
    }

    @Test
    public void testQueryRecordByRecordKeyWithSpecialCharacters() throws Throwable {
        // arrange
        String recordKey = "<EMAIL>";
        EcontractRecordEntity specialEntity = new EcontractRecordEntity();
        specialEntity.setRecordKey(recordKey);
        specialEntity.setEcontractRecordContext("special-context");
        specialEntity.setId(6);
        when(econtractRecordEntityMapper.queryRecordByRecordKey(recordKey)).thenReturn(specialEntity);
        // act
        EcontractRecordEntity result = econtractRecordService.queryRecordByRecordKey(recordKey);
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("Record key with special characters should match", recordKey, result.getRecordKey());
        assertEquals("Context should match", "special-context", result.getEcontractRecordContext());
        assertEquals("ID should match", Integer.valueOf(6), result.getId());
        verify(econtractRecordEntityMapper, times(1)).queryRecordByRecordKey(recordKey);
    }

    @Test(expected = RuntimeException.class)
    public void testQueryRecordByRecordKeyWhenMapperThrowsException() throws Throwable {
        // arrange
        String recordKey = "exception-key";
        when(econtractRecordEntityMapper.queryRecordByRecordKey(recordKey)).thenThrow(new RuntimeException("Database connection error"));
        // act
        econtractRecordService.queryRecordByRecordKey(recordKey);
        // assert - RuntimeException expected
    }

    @Test
    public void testQueryColdAndHotEcontractRecordByBatchId_NormalCase() throws Throwable {
        // arrange
        Integer batchId = 123;
        EcontractRecordEntity record1 = new EcontractRecordEntity();
        record1.setId(1);
        record1.setRecordKey("key1");
        record1.setEcontractRecordContext(VALID_HOT_CONTEXT_JSON);
        EcontractRecordEntity record2 = new EcontractRecordEntity();
        record2.setId(2);
        record2.setRecordKey("key2");
        record2.setEcontractRecordContext(VALID_HOT_CONTEXT_JSON);
        List<EcontractRecordEntity> mockRecords = Arrays.asList(record1, record2);
        when(econtractRecordEntityMapper.queryEcontractRecordByBatchId(batchId)).thenReturn(mockRecords);
        EcontractRecordContextEntity contextEntity1 = new EcontractRecordContextEntity();
        contextEntity1.setContext(VALID_COLD_CONTEXT_JSON);
        EcontractRecordContextEntity contextEntity2 = new EcontractRecordContextEntity();
        contextEntity2.setContext(VALID_COLD_CONTEXT_JSON);
        when(econtractBigRecordParseService.selectByRecordId(1)).thenReturn(contextEntity1);
        when(econtractBigRecordParseService.selectByRecordId(2)).thenReturn(contextEntity2);
        // act
        List<EcontractRecordEntity> result = econtractRecordService.queryColdAndHotEcontractRecordByBatchId(batchId);
        // assert
        assertEquals(2, result.size());
        assertNotNull(result.get(0));
        assertNotNull(result.get(1));
        assertNotNull(result.get(0).getEcontractRecordContext());
        assertNotNull(result.get(1).getEcontractRecordContext());
        verify(econtractRecordEntityMapper, times(1)).queryEcontractRecordByBatchId(batchId);
        verify(econtractBigRecordParseService, times(1)).selectByRecordId(1);
        verify(econtractBigRecordParseService, times(1)).selectByRecordId(2);
    }

    @Test
    public void testQueryColdAndHotEcontractRecordByBatchId_EmptyStringContext() throws Throwable {
        // arrange
        Integer batchId = 123;
        EcontractRecordEntity record1 = new EcontractRecordEntity();
        record1.setId(1);
        record1.setRecordKey("key1");
        // empty string
        record1.setEcontractRecordContext("");
        EcontractRecordEntity recordWithContext = new EcontractRecordEntity();
        recordWithContext.setId(1);
        recordWithContext.setRecordKey("key1");
        recordWithContext.setEcontractRecordContext(VALID_HOT_CONTEXT_JSON);
        when(econtractRecordEntityMapper.queryEcontractRecordByBatchId(batchId)).thenReturn(Collections.singletonList(record1));
        // First retry returns empty context, second succeeds
        // first retry
        // first retry
        // second retry
        when(econtractRecordEntityMapper.queryRecordByRecordKey("key1")).// second retry
        thenReturn(record1).thenReturn(recordWithContext);
        EcontractRecordContextEntity contextEntity = new EcontractRecordContextEntity();
        contextEntity.setContext(VALID_COLD_CONTEXT_JSON);
        when(econtractBigRecordParseService.selectByRecordId(1)).thenReturn(contextEntity);
        // act
        List<EcontractRecordEntity> result = econtractRecordService.queryColdAndHotEcontractRecordByBatchId(batchId);
        // assert
        assertEquals(1, result.size());
        assertNotNull(result.get(0));
        assertNotNull(result.get(0).getEcontractRecordContext());
        verify(econtractRecordEntityMapper, times(2)).queryRecordByRecordKey("key1");
        verify(econtractBigRecordParseService, times(1)).selectByRecordId(1);
    }

    @Test
    public void testQueryColdAndHotEcontractRecordByBatchId_NullContext() throws Throwable {
        // arrange
        Integer batchId = 123;
        EcontractRecordEntity record1 = new EcontractRecordEntity();
        record1.setId(1);
        record1.setRecordKey("key1");
        // null context
        record1.setEcontractRecordContext(null);
        EcontractRecordEntity recordWithContext = new EcontractRecordEntity();
        recordWithContext.setId(1);
        recordWithContext.setRecordKey("key1");
        recordWithContext.setEcontractRecordContext(VALID_HOT_CONTEXT_JSON);
        when(econtractRecordEntityMapper.queryEcontractRecordByBatchId(batchId)).thenReturn(Collections.singletonList(record1));
        // First retry succeeds
        when(econtractRecordEntityMapper.queryRecordByRecordKey("key1")).thenReturn(recordWithContext);
        EcontractRecordContextEntity contextEntity = new EcontractRecordContextEntity();
        contextEntity.setContext(VALID_COLD_CONTEXT_JSON);
        when(econtractBigRecordParseService.selectByRecordId(1)).thenReturn(contextEntity);
        // act
        List<EcontractRecordEntity> result = econtractRecordService.queryColdAndHotEcontractRecordByBatchId(batchId);
        // assert
        assertEquals(1, result.size());
        assertNotNull(result.get(0));
        assertNotNull(result.get(0).getEcontractRecordContext());
        verify(econtractRecordEntityMapper, times(1)).queryRecordByRecordKey("key1");
        verify(econtractBigRecordParseService, times(1)).selectByRecordId(1);
    }

    @Test
    public void testQueryColdAndHotEcontractRecordByBatchId_AllRetriesFail() throws Throwable {
        // arrange
        Integer batchId = 123;
        EcontractRecordEntity record = new EcontractRecordEntity();
        record.setId(1);
        record.setRecordKey("key1");
        record.setEcontractRecordContext("");
        when(econtractRecordEntityMapper.queryEcontractRecordByBatchId(batchId)).thenReturn(Collections.singletonList(record));
        // All retries return empty context
        when(econtractRecordEntityMapper.queryRecordByRecordKey("key1")).thenReturn(record);
        // Return null context entity to simulate no cold data scenario
        when(econtractBigRecordParseService.selectByRecordId(1)).thenReturn(null);
        // act
        List<EcontractRecordEntity> result = econtractRecordService.queryColdAndHotEcontractRecordByBatchId(batchId);
        // assert
        assertEquals(1, result.size());
        assertNotNull(result.get(0));
        assertEquals("", result.get(0).getEcontractRecordContext());
        verify(econtractRecordEntityMapper, times(3)).queryRecordByRecordKey("key1");
        verify(econtractBigRecordParseService, times(1)).selectByRecordId(1);
    }

    @Test
    public void testQueryColdAndHotEcontractRecordByBatchId_NullBatchId() throws Throwable {
        // arrange
        when(econtractRecordEntityMapper.queryEcontractRecordByBatchId(null)).thenReturn(Collections.emptyList());
        // act
        List<EcontractRecordEntity> result = econtractRecordService.queryColdAndHotEcontractRecordByBatchId(null);
        // assert
        assertTrue(result.isEmpty());
        verify(econtractRecordEntityMapper, times(1)).queryEcontractRecordByBatchId(null);
        verify(econtractBigRecordParseService, never()).selectByRecordId(any());
    }

    @Test
    public void testQueryColdAndHotEcontractRecordByBatchId_NoRecordsFound() throws Throwable {
        // arrange
        Integer batchId = 123;
        when(econtractRecordEntityMapper.queryEcontractRecordByBatchId(batchId)).thenReturn(Collections.emptyList());
        // act
        List<EcontractRecordEntity> result = econtractRecordService.queryColdAndHotEcontractRecordByBatchId(batchId);
        // assert
        assertTrue(result.isEmpty());
        verify(econtractRecordEntityMapper, times(1)).queryEcontractRecordByBatchId(batchId);
        verify(econtractBigRecordParseService, never()).selectByRecordId(any());
    }

    @Test
    public void testQueryColdAndHotEcontractRecordByBatchId_MixedRecords() throws Throwable {
        // arrange
        Integer batchId = 123;
        // Record with context already
        EcontractRecordEntity record1 = new EcontractRecordEntity();
        record1.setId(1);
        record1.setRecordKey("key1");
        record1.setEcontractRecordContext(VALID_HOT_CONTEXT_JSON);
        // Record needing retries
        EcontractRecordEntity record2 = new EcontractRecordEntity();
        record2.setId(2);
        record2.setRecordKey("key2");
        record2.setEcontractRecordContext("");
        EcontractRecordEntity record2WithContext = new EcontractRecordEntity();
        record2WithContext.setId(2);
        record2WithContext.setRecordKey("key2");
        record2WithContext.setEcontractRecordContext(VALID_HOT_CONTEXT_JSON);
        when(econtractRecordEntityMapper.queryEcontractRecordByBatchId(batchId)).thenReturn(Arrays.asList(record1, record2));
        // succeeds on first retry
        // succeeds on first retry
        when(econtractRecordEntityMapper.queryRecordByRecordKey("key2")).thenReturn(record2WithContext);
        EcontractRecordContextEntity contextEntity1 = new EcontractRecordContextEntity();
        contextEntity1.setContext(VALID_COLD_CONTEXT_JSON);
        EcontractRecordContextEntity contextEntity2 = new EcontractRecordContextEntity();
        contextEntity2.setContext(VALID_COLD_CONTEXT_JSON);
        when(econtractBigRecordParseService.selectByRecordId(1)).thenReturn(contextEntity1);
        when(econtractBigRecordParseService.selectByRecordId(2)).thenReturn(contextEntity2);
        // act
        List<EcontractRecordEntity> result = econtractRecordService.queryColdAndHotEcontractRecordByBatchId(batchId);
        // assert
        assertEquals(2, result.size());
        assertNotNull(result.get(0));
        assertNotNull(result.get(1));
        assertNotNull(result.get(0).getEcontractRecordContext());
        assertNotNull(result.get(1).getEcontractRecordContext());
        verify(econtractRecordEntityMapper, times(1)).queryRecordByRecordKey("key2");
        verify(econtractBigRecordParseService, times(1)).selectByRecordId(1);
        verify(econtractBigRecordParseService, times(1)).selectByRecordId(2);
    }

    @Test
    public void testQueryColdAndHotEcontractRecordByBatchId_LastRetrySucceeds() throws Throwable {
        // arrange
        Integer batchId = 123;
        EcontractRecordEntity record = new EcontractRecordEntity();
        record.setId(1);
        record.setRecordKey("key1");
        record.setEcontractRecordContext("");
        EcontractRecordEntity recordWithContext = new EcontractRecordEntity();
        recordWithContext.setId(1);
        recordWithContext.setRecordKey("key1");
        recordWithContext.setEcontractRecordContext(VALID_HOT_CONTEXT_JSON);
        when(econtractRecordEntityMapper.queryEcontractRecordByBatchId(batchId)).thenReturn(Collections.singletonList(record));
        // First two retries fail, third succeeds
        // first retry
        // first retry
        // second retry
        when(econtractRecordEntityMapper.queryRecordByRecordKey("key1")).// second retry
        thenReturn(// third retry
        record).// third retry
        thenReturn(record).thenReturn(recordWithContext);
        EcontractRecordContextEntity contextEntity = new EcontractRecordContextEntity();
        contextEntity.setContext(VALID_COLD_CONTEXT_JSON);
        when(econtractBigRecordParseService.selectByRecordId(1)).thenReturn(contextEntity);
        // act
        List<EcontractRecordEntity> result = econtractRecordService.queryColdAndHotEcontractRecordByBatchId(batchId);
        // assert
        assertEquals(1, result.size());
        assertNotNull(result.get(0));
        assertNotNull(result.get(0).getEcontractRecordContext());
        verify(econtractRecordEntityMapper, times(3)).queryRecordByRecordKey("key1");
        verify(econtractBigRecordParseService, times(1)).selectByRecordId(1);
    }

    @Test
    public void testQueryColdAndHotEcontractRecordByBatchId_LargeBatch() throws Throwable {
        // arrange
        Integer batchId = 123;
        List<EcontractRecordEntity> largeList = Arrays.asList(createRecordWithContext(1, "key1", VALID_HOT_CONTEXT_JSON), createRecordWithContext(2, "key2", VALID_HOT_CONTEXT_JSON), createRecordWithContext(3, "key3", VALID_HOT_CONTEXT_JSON), createRecordWithContext(4, "key4", VALID_HOT_CONTEXT_JSON), createRecordWithContext(5, "key5", VALID_HOT_CONTEXT_JSON));
        when(econtractRecordEntityMapper.queryEcontractRecordByBatchId(batchId)).thenReturn(largeList);
        // Mock context entities for all records
        for (int i = 1; i <= 5; i++) {
            EcontractRecordContextEntity contextEntity = new EcontractRecordContextEntity();
            contextEntity.setContext(VALID_COLD_CONTEXT_JSON);
            when(econtractBigRecordParseService.selectByRecordId(i)).thenReturn(contextEntity);
        }
        // act
        List<EcontractRecordEntity> result = econtractRecordService.queryColdAndHotEcontractRecordByBatchId(batchId);
        // assert
        assertEquals(5, result.size());
        for (int i = 0; i < 5; i++) {
            assertNotNull(result.get(i));
            assertNotNull(result.get(i).getEcontractRecordContext());
        }
        verify(econtractRecordEntityMapper, times(1)).queryEcontractRecordByBatchId(batchId);
        for (int i = 1; i <= 5; i++) {
            verify(econtractBigRecordParseService, times(1)).selectByRecordId(i);
        }
    }

    @Test
    public void testQueryColdAndHotEcontractRecordByBatchId_WrapReturnsNull() throws Throwable {
        // arrange
        Integer batchId = 123;
        EcontractRecordEntity record = new EcontractRecordEntity();
        record.setId(1);
        record.setRecordKey("key1");
        record.setEcontractRecordContext(VALID_HOT_CONTEXT_JSON);
        when(econtractRecordEntityMapper.queryEcontractRecordByBatchId(batchId)).thenReturn(Collections.singletonList(record));
        when(econtractBigRecordParseService.selectByRecordId(1)).thenReturn(null);
        // act
        List<EcontractRecordEntity> result = econtractRecordService.queryColdAndHotEcontractRecordByBatchId(batchId);
        // assert
        assertEquals(1, result.size());
        // The method should still return the record even if context is null
        assertNotNull(result.get(0));
        // Should return original hot context
        assertEquals(VALID_HOT_CONTEXT_JSON, result.get(0).getEcontractRecordContext());
        verify(econtractRecordEntityMapper, times(1)).queryEcontractRecordByBatchId(batchId);
        verify(econtractBigRecordParseService, times(1)).selectByRecordId(1);
    }

    @Test
    public void testQueryColdAndHotEcontractRecordByBatchId_ContextEntityNullContext() throws Throwable {
        // arrange
        Integer batchId = 123;
        EcontractRecordEntity record = new EcontractRecordEntity();
        record.setId(1);
        record.setRecordKey("key1");
        record.setEcontractRecordContext(VALID_HOT_CONTEXT_JSON);
        when(econtractRecordEntityMapper.queryEcontractRecordByBatchId(batchId)).thenReturn(Collections.singletonList(record));
        EcontractRecordContextEntity contextEntity = new EcontractRecordContextEntity();
        // null context
        contextEntity.setContext(null);
        when(econtractBigRecordParseService.selectByRecordId(1)).thenReturn(contextEntity);
        // act & assert - this should throw an exception due to JSON parsing null
        try {
            econtractRecordService.queryColdAndHotEcontractRecordByBatchId(batchId);
            fail("Expected exception due to null context parsing");
        } catch (Exception e) {
            // Expected behavior - JSON parsing should fail with null input
            assertTrue(e instanceof RuntimeException || e.getCause() instanceof RuntimeException);
        }
        verify(econtractRecordEntityMapper, times(1)).queryEcontractRecordByBatchId(batchId);
        verify(econtractBigRecordParseService, times(1)).selectByRecordId(1);
    }

    private EcontractRecordEntity createRecordWithContext(Integer id, String recordKey, String context) {
        EcontractRecordEntity record = new EcontractRecordEntity();
        record.setId(id);
        record.setRecordKey(recordKey);
        record.setEcontractRecordContext(context);
        return record;
    }
}

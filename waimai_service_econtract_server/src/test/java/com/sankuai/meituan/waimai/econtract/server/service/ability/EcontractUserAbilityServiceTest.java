package com.sankuai.meituan.waimai.econtract.server.service.ability;

import com.meituan.it.contract.platform.model.request.contracttype.SaveContractTypeApiReq;
import com.meituan.it.contract.platform.model.response.contracttype.SaveContractTypeApiResp;
import com.sankuai.meituan.waimai.econtract.server.adapter.WmEmployAdapter;
import com.sankuai.meituan.waimai.econtract.server.adapter.hailuo.ContractTypeThriftServiceAdapter;
import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractUserEntity;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractUserService;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractErrorCode;
import com.sankuai.meituan.waimai.econtrct.client.domain.config.econtractuser.EcontractUserSyncRequestDTO;
import com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.List;

import static com.sankuai.meituan.waimai.econtract.server.config.MccConfig.supportIntegrationHaiLuo;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class EcontractUserAbilityServiceTest {

    @InjectMocks
    private EcontractUserAbilityService econtractUserAbilityService;

    @Mock
    private ContractTypeThriftServiceAdapter contractTypeThriftServiceAdapter;

    @Mock
    private EcontractUserService econtractUserService;

    @Mock
    private WmEmployAdapter wmEmployAdapter;


    private EcontractUserEntity econtractUser;

    private Integer userId;

    private EcontractUserSyncRequestDTO requestDTO;


    @Before
    public void setUp() {
        econtractUser = new EcontractUserEntity();
        econtractUser.setId(1);
        econtractUser.setName("Test User");
        userId = 12345;


        requestDTO = new EcontractUserSyncRequestDTO();
    }

    /**
     * 测试当不支持海螺集成时的场景
     * 验证点：
     * 1. 返回值为false
     * 2. 确保不调用adapter
     */
    @Test
    public void testSyncEcontractUserToHaiLuo_NotSupportIntegration() throws Throwable {
        try (MockedStatic<MccConfig> mccConfigMockedStatic = Mockito.mockStatic(MccConfig.class)) {
            mccConfigMockedStatic.when(MccConfig::supportIntegrationHaiLuo).thenReturn(false);
            boolean result = econtractUserAbilityService.syncEcontractUserToHaiLuo(econtractUser, userId);
            assertFalse(result);
            verify(contractTypeThriftServiceAdapter, never()).syncEcontractUserToHaiLuo(any(SaveContractTypeApiReq.class));
        }
    }

    /**
     * 测试支持海螺集成且同步成功的场景
     * 验证点：
     * 1. 返回值为true
     * 2. 确保调用adapter
     */
    @Test
    public void testSyncEcontractUserToHaiLuo_Success() throws Throwable {
        try (MockedStatic<MccConfig> mccConfigMockedStatic = Mockito.mockStatic(MccConfig.class)) {
            mccConfigMockedStatic.when(MccConfig::supportIntegrationHaiLuo).thenReturn(true);

            SaveContractTypeApiResp resp = new SaveContractTypeApiResp();
            resp.setCode("200");
            when(contractTypeThriftServiceAdapter.syncEcontractUserToHaiLuo(any(SaveContractTypeApiReq.class))).thenReturn(resp);

            boolean result = econtractUserAbilityService.syncEcontractUserToHaiLuo(econtractUser, userId);

            assertTrue(result);
            verify(contractTypeThriftServiceAdapter, times(1)).syncEcontractUserToHaiLuo(any(SaveContractTypeApiReq.class));
        }
    }

    /**
     * 测试支持海螺集成但同步失败的场景
     * 验证点：
     * 1. 返回值为false
     * 2. 确保调用adapter
     */
    @Test
    public void testSyncEcontractUserToHaiLuo_Failure() throws Throwable {
        try (MockedStatic<MccConfig> mccConfigMockedStatic = Mockito.mockStatic(MccConfig.class)) {
            mccConfigMockedStatic.when(MccConfig::supportIntegrationHaiLuo).thenReturn(true);

            SaveContractTypeApiResp resp = new SaveContractTypeApiResp();
            resp.setCode("210");
            when(contractTypeThriftServiceAdapter.syncEcontractUserToHaiLuo(any(SaveContractTypeApiReq.class))).thenReturn(resp);

            boolean result = econtractUserAbilityService.syncEcontractUserToHaiLuo(econtractUser, userId);

            assertFalse(result);
            verify(contractTypeThriftServiceAdapter, times(1)).syncEcontractUserToHaiLuo(any(SaveContractTypeApiReq.class));
        }
    }

    /**
     * 测试构建请求参数时抛出EcontractException的场景
     * 验证点：
     * 1. 返回值为false
     * 2. 确保不调用adapter
     */
    @Test
    public void testSyncEcontractUserToHaiLuo_EcontractException() throws Throwable {
        try (MockedStatic<MccConfig> mccConfigMockedStatic = Mockito.mockStatic(MccConfig.class)) {
            mccConfigMockedStatic.when(MccConfig::supportIntegrationHaiLuo).thenReturn(true);

            doThrow(new EcontractException(EcontractErrorCode.BUSINESS_ERROR, "接入方同步到海螺失败")).when(contractTypeThriftServiceAdapter).syncEcontractUserToHaiLuo(any(SaveContractTypeApiReq.class));

            boolean result = econtractUserAbilityService.syncEcontractUserToHaiLuo(econtractUser, userId);

            assertFalse(result);
            verify(contractTypeThriftServiceAdapter, times(1)).syncEcontractUserToHaiLuo(any(SaveContractTypeApiReq.class));
        }
    }

    /**
     * 测试适配器调用时抛出异常的场景
     * 验证点：
     * 1. 返回值为false
     * 2. 确保调用adapter
     */
    @Test
    public void testSyncEcontractUserToHaiLuo_AdapterException() throws Throwable {
        try (MockedStatic<MccConfig> mccConfigMockedStatic = Mockito.mockStatic(MccConfig.class)) {
            mccConfigMockedStatic.when(MccConfig::supportIntegrationHaiLuo).thenReturn(true);

            when(contractTypeThriftServiceAdapter.syncEcontractUserToHaiLuo(any(SaveContractTypeApiReq.class))).thenThrow(new RuntimeException("Adapter Exception"));

            boolean result = econtractUserAbilityService.syncEcontractUserToHaiLuo(econtractUser, userId);

            assertFalse(result);
            verify(contractTypeThriftServiceAdapter, times(1)).syncEcontractUserToHaiLuo(any(SaveContractTypeApiReq.class));
        }
    }

    /**
     * 测试场景：不支持海螺集成
     */
    @Test
    public void testSyncEcontractUserToHailuo_NotSupportIntegration() throws Throwable {
        try (MockedStatic<MccConfig> mccConfigMockedStatic = Mockito.mockStatic(MccConfig.class)) {
            // arrange
            mccConfigMockedStatic.when(MccConfig::supportIntegrationHaiLuo).thenReturn(false);

            // act
            econtractUserAbilityService.syncEcontractUserToHailuo(requestDTO);

            // assert
//        verify(MccConfig.class, times(1)).supportIntegrationHaiLuo();
            verifyNoInteractions(econtractUserService, contractTypeThriftServiceAdapter, wmEmployAdapter);
        }
    }

    /**
     * 测试场景：同步所有用户
     */
    @Test
    public void testSyncEcontractUserToHailuo_SyncAllUsers() throws Throwable {
        try (MockedStatic<MccConfig> mccConfigMockedStatic = Mockito.mockStatic(MccConfig.class)) {
            // arrange
            // arrange
            when(supportIntegrationHaiLuo()).thenReturn(true);
            requestDTO.setSyncAll(true);
            List<EcontractUserEntity> userList = Collections.singletonList(new EcontractUserEntity());
            when(econtractUserService.queryEcontractUserList()).thenReturn(userList);

            // act
            econtractUserAbilityService.syncEcontractUserToHailuo(requestDTO);

            // assert
            verify(econtractUserService, times(1)).queryEcontractUserList();
//        verify(contractTypeThriftServiceAdapter, times(1)).executeBatchSync(userList);
        }
    }

    /**
     * 测试场景：指定同步用户
     */
    @Test
    public void testSyncEcontractUserToHailuo_SpecifiedUsers() throws Throwable {
        try (MockedStatic<MccConfig> mccConfigMockedStatic = Mockito.mockStatic(MccConfig.class)) {
            // arrange
            mccConfigMockedStatic.when(MccConfig::supportIntegrationHaiLuo).thenReturn(true);
            requestDTO.setSpecifiedEcontractUserName(Collections.singletonList("User1"));

            // 模拟queryUserByName方法的行为
            EcontractUserEntity user = new EcontractUserEntity();
            user.setName("User1");
            user.setMemberMis("mis1");
            when(econtractUserService.queryUserByName("User1")).thenReturn(user);

            // 模拟wmEmployAdapter.getEmployByMisId方法
            WmEmploy employ = new WmEmploy();
            employ.setUid(12345);
            when(wmEmployAdapter.getEmployByMisId("mis1")).thenReturn(employ);

            // 模拟syncEcontractUserToHaiLuo方法的行为
            SaveContractTypeApiResp resp = new SaveContractTypeApiResp();
            resp.setCode("200");
            when(contractTypeThriftServiceAdapter.syncEcontractUserToHaiLuo(any(SaveContractTypeApiReq.class))).thenReturn(resp);

            // act
            econtractUserAbilityService.syncEcontractUserToHailuo(requestDTO);

            // assert
            verify(econtractUserService, times(1)).queryUserByName("User1");
            verify(contractTypeThriftServiceAdapter, atLeastOnce()).syncEcontractUserToHaiLuo(any(SaveContractTypeApiReq.class));
        }
    }

    /**
     * 测试场景：用户列表为空
     */
    @Test
    public void testSyncEcontractUserToHailuo_EmptyUserList() throws Throwable {
        try (MockedStatic<MccConfig> mccConfigMockedStatic = Mockito.mockStatic(MccConfig.class)) {
            // arrange
            mccConfigMockedStatic.when(MccConfig::supportIntegrationHaiLuo).thenReturn(true);
            requestDTO.setSyncAll(false);
            requestDTO.setSpecifiedEcontractUserName(Collections.singletonList("NonExistentUser"));

            // 模拟queryUserByName方法返回null，表示用户不存在
            when(econtractUserService.queryUserByName("NonExistentUser")).thenReturn(null);

            // act
            econtractUserAbilityService.syncEcontractUserToHailuo(requestDTO);

            // assert
            verify(econtractUserService, times(1)).queryUserByName("NonExistentUser");
            // 由于用户列表为空，不应该调用syncEcontractUserToHaiLuo方法
            verify(contractTypeThriftServiceAdapter, never()).syncEcontractUserToHaiLuo(any(SaveContractTypeApiReq.class));
        }
    }

    /**
     * 测试场景：异常情况
     */
    @Test
    public void testSyncEcontractUserToHailuo_Exception() throws Throwable {
        try (MockedStatic<MccConfig> mccConfigMockedStatic = Mockito.mockStatic(MccConfig.class)) {
            // arrange
            mccConfigMockedStatic.when(MccConfig::supportIntegrationHaiLuo).thenReturn(true);
            requestDTO.setSyncAll(true);
            when(econtractUserService.queryEcontractUserList()).thenThrow(new RuntimeException("Test Exception"));

            // act
            try {
                econtractUserAbilityService.syncEcontractUserToHailuo(requestDTO);
                verify(econtractUserService, times(1)).queryEcontractUserList();
            } catch (Exception e) {
                // assert
                assertEquals("Test Exception", e.getMessage());
            }
        }
    }

}
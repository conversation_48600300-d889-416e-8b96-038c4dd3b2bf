package com.sankuai.meituan.waimai.econtract.server.service.idempotent;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.squirrel.client.StoreKey;
import com.sankuai.meituan.waimai.econtract.server.constants.EcontractIdempotentConstant;
import com.sankuai.meituan.waimai.econtract.server.utils.RedisKvUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class EcontractIdempotentRedisServiceSetReentrantTest {

    @Mock
    private RedisKvUtil redisKvUtil;

    @InjectMocks
    private EcontractIdempotentRedisService econtractIdempotentRedisService;

    private StoreKey testStoreKey;

    @Before
    public void setUp() {
        testStoreKey = new StoreKey("testCategory", "param1", "param2");
    }

    /**
     * 测试正常设置可重入状态成功的情况
     */
    @Test
    public void testSetReentrantSuccess() throws Throwable {
        // arrange
        int timeout = 60;
        when(redisKvUtil.set(testStoreKey, EcontractIdempotentConstant.ECONTRACT_IDEMPOTENT_REENTRANT, timeout)).thenReturn(true);
        // act
        boolean result = econtractIdempotentRedisService.setReentrant(testStoreKey, timeout);
        // assert
        assertTrue(result);
        verify(redisKvUtil).set(testStoreKey, EcontractIdempotentConstant.ECONTRACT_IDEMPOTENT_REENTRANT, timeout);
    }

    /**
     * 测试设置可重入状态失败的情况
     */
    @Test
    public void testSetReentrantFailure() throws Throwable {
        // arrange
        int timeout = 60;
        when(redisKvUtil.set(testStoreKey, EcontractIdempotentConstant.ECONTRACT_IDEMPOTENT_REENTRANT, timeout)).thenReturn(false);
        // act
        boolean result = econtractIdempotentRedisService.setReentrant(testStoreKey, timeout);
        // assert
        assertFalse(result);
        verify(redisKvUtil).set(testStoreKey, EcontractIdempotentConstant.ECONTRACT_IDEMPOTENT_REENTRANT, timeout);
    }

    /**
     * 测试Redis操作抛出异常的情况
     */
    @Test
    public void testSetReentrantWithException() throws Throwable {
        // arrange
        int timeout = 60;
        when(redisKvUtil.set(testStoreKey, EcontractIdempotentConstant.ECONTRACT_IDEMPOTENT_REENTRANT, timeout)).thenThrow(new RuntimeException("Redis error"));
        // act
        boolean result = econtractIdempotentRedisService.setReentrant(testStoreKey, timeout);
        // assert
        assertFalse(result);
        verify(redisKvUtil).set(testStoreKey, EcontractIdempotentConstant.ECONTRACT_IDEMPOTENT_REENTRANT, timeout);
    }

    /**
     * 测试timeout为0的情况
     */
    @Test
    public void testSetReentrantWithZeroTimeout() throws Throwable {
        // arrange
        int timeout = 0;
        when(redisKvUtil.set(testStoreKey, EcontractIdempotentConstant.ECONTRACT_IDEMPOTENT_REENTRANT, timeout)).thenReturn(true);
        // act
        boolean result = econtractIdempotentRedisService.setReentrant(testStoreKey, timeout);
        // assert
        assertTrue(result);
        verify(redisKvUtil).set(testStoreKey, EcontractIdempotentConstant.ECONTRACT_IDEMPOTENT_REENTRANT, timeout);
    }

    /**
     * 测试timeout为负数的情况
     */
    @Test
    public void testSetReentrantWithNegativeTimeout() throws Throwable {
        // arrange
        int timeout = -1;
        when(redisKvUtil.set(testStoreKey, EcontractIdempotentConstant.ECONTRACT_IDEMPOTENT_REENTRANT, timeout)).thenReturn(true);
        // act
        boolean result = econtractIdempotentRedisService.setReentrant(testStoreKey, timeout);
        // assert
        assertTrue(result);
        verify(redisKvUtil).set(testStoreKey, EcontractIdempotentConstant.ECONTRACT_IDEMPOTENT_REENTRANT, timeout);
    }
}

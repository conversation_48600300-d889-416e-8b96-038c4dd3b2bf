package com.sankuai.meituan.waimai.econtract.server.service.biz.Impl;

import com.itextpdf.text.DocumentException;
import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractFtlTemplateEntity;
import com.sankuai.meituan.waimai.econtract.server.pdfEngine.bo.PdfGenerator;
import com.sankuai.meituan.waimai.econtract.server.pdfEngine.bo.param.PdfGeneratorParam;
import com.sankuai.meituan.waimai.econtract.server.template.config.service.EcontractTemplateVersionService;
import com.sankuai.meituan.waimai.econtract.server.utils.JacksonUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException;
import com.sankuai.meituan.waimai.econtrct.client.template.config.domain.EcontractTemplateVersionBo;
import com.sankuai.meituan.waimai.econtrct.client.template.config.domain.EcontractTemplateVersionSimpleBo;
import com.sankuai.meituan.waimai.econtrct.client.template.config.exception.EcontractTemplateConfigException;
import com.sankuai.meituan.waimai.econtrct.client.util.MtCloudS3Util;
import freemarker.template.TemplateException;
import org.apache.commons.codec.digest.DigestUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * @description: EcontractPdfService单测类
 * @author: liuyunjie05
 * @create: 2024/3/26 10:59
 */
@RunWith(MockitoJUnitRunner.class)
public class EcontractPdfServiceImplTest {

    @Mock
    private EcontractTemplateVersionService econtractTemplateVersionService;

    @Mock
    private PdfGenerator pdfGenerator;

    @Mock
    private EcontractFtlTemplateServiceImpl econtractFtlTemplateService;

    @InjectMocks
    private EcontractPdfServiceImpl econtractPdfService;

    /**
     * 不指定模板信息的时候报错
     */
    @Test(expected = EcontractException.class)
    public void testOnlyCreatePdfParamException() throws EcontractException, TemplateException, DocumentException, IOException, EcontractTemplateConfigException {
        PdfContentInfoBo pdfContentInfoBo = new PdfContentInfoBo();
        econtractPdfService.onlyCreatePdf(pdfContentInfoBo);
    }

    /**
     * 指定模板id时能正常生成PDF
     */
    @Test
    public void testOnlyCreatePdfWithTemplateId() throws EcontractException, TemplateException, DocumentException, IOException, EcontractTemplateConfigException {
        String param = "{\"pdfBizContent\":[],\"pdfMetaContent\":{\"partAOfficialSeal\":\"测试缚升\",\"selfPlatformFeeInfo\":\"1.0%\",\"selfBusinessFeeInfo\":\"1) 若优惠后商品价格<22.0元，商业支持服务费费率22.0%\\n2) 若优惠后商品价格≥22.0元，商业支持服务费费率33.0%\\n\",\"signTime\":\"2024-03-26\",\"qkBusinessFeeInfo\":\"3.33%\",\"deliveryType\":\"\",\"qkGuaranteedAmount\":\"2.2\",\"partAName\":\"测试缚升\",\"guaranteedAmount\":\"3.21\",\"qkPlatformFeeInfo\":\"1.0%\",\"partAEstamp\":\"美团外卖商家签章专属\",\"poiInfo\":\"\",\"mtPlatformFeeInfo\":\"0.01%\",\"brandInfo\":\"呛渴筛提消拾珊(北京) ID:215178\",\"mtBusinessFeeInfo\":\"1) 若优惠后商品价格<20.0元，商业支持服务费费率66.0%\\n2) 若优惠后商品价格≥20.0元且<40.0元，商业支持服务费费率62.2%\\n3) 若优惠后商品价格≥40.0元，商业支持服务费费率57.89%\\n\"},\"pdfTemplateId\":376,\"pdfTemplateVersion\":0,\"vertical\":true}";
        PdfContentInfoBo pdfContentInfoBo = JacksonUtil.readValue(param, PdfContentInfoBo.class);

        String targetContent = "<style>    \n" +
                "  .pageBreak{\n" +
                "    page-break-after: always;\n" +
                "  }\n" +
                "  </style>\n" +
                "  <p style=\"font-family:宋体;text-align: center;\"><strong><font style=\"font-family:宋体;font-size:24px;line-height:32px\" data-size=\"24\">神抢手收费标准</font></strong></p><p style=\"font-family:宋体;\">&nbsp;</p><table style=\"height:20pt;border-style:solid;border-width:1px;border-color:#000000;\" class=\"ct-table\" name=\"utable\" data-borderstyle=\"solid\" data-borderwidth=\"1\" cellspacing=\"-1px;\" data-bordercolor=\"#000000\"><tbody><tr class=\"dynamic_row\"><td data-colwidth=\"75\" data-colwidth-sum=\"75\" width=\"75\" style=\"padding: 5.03pt; height: 20px; width: 75px; vertical-align: top; border-width: 1px; border-style: solid;border-color: #000000;\"><p style=\"font-family:宋体;\"><strong>品牌名称</strong></p></td><td data-colwidth=\"553\" data-colwidth-sum=\"553\" width=\"553\" style=\"padding: 5.03pt; height: 20px; width: 553px; vertical-align: top; border-width: 1px; border-style: solid;border-color: #000000;\"><p style=\"font-family:宋体;\"><span name=\"qe8o3GMt\" data_name=\"品牌名称\" data_remark=\"\" data_type=\"2\" data_text=\"茶百道（华北） ID1283309\" style=\"color: #333;\"><#if (pdfMetaContent.brandInfo)??><#list pdfMetaContent.brandInfo?split(\"\\n\") as dynamicItem><span style=\"color:#333333; font-family:宋体;\">${dynamicItem}</span><#if dynamicItem_has_next ><br/></#if></#list></#if></span></p><p style=\"font-family:宋体;\">注：该品牌下所有代理城市门店均适用此收费标准，具体门店名称以商家后台展示为准。</p></td></tr><tr class=\"dynamic_row\"><td data-colwidth=\"75\" data-colwidth-sum=\"75\" width=\"75\" style=\"padding: 5.03pt; height: 20px; width: 75px; vertical-align: top; border-width: 1px; border-style: solid;border-color: #000000;\"><p style=\"font-family:宋体;\"><strong>适用范围</strong></p></td><td data-colwidth=\"553\" data-colwidth-sum=\"553\" width=\"553\" style=\"padding: 5.03pt; height: 20px; width: 553px; vertical-align: top; border-width: 1px; border-style: solid;border-color: #000000;\"><p style=\"font-family:宋体;\"><strong>平台官方直播间商品、商家直播间商品、APP或微信小程序商品券列表展示的商品等。</strong></p><p style=\"font-family:宋体;\"><strong>注：如适用范围有调整，将通过商家后台提前通知，通知送达即生效。</strong></p></td></tr><tr class=\"dynamic_row\"><td rowspan=\"3\" data-colwidth=\"75\" data-colwidth-sum=\"75\" width=\"75\" style=\"padding: 5.03pt; height: 20px; width: 75px; vertical-align: top; border-width: 1px; border-style: solid;border-color: #000000;\"><p style=\"font-family:宋体;\"><strong>收费规则</strong></p></td><td data-colwidth=\"553\" data-colwidth-sum=\"553\" width=\"553\" style=\"padding: 5.03pt; height: 20px; width: 553px; vertical-align: top; border-width: 1px; border-style: solid;border-color: #000000;\"><p style=\"font-family:宋体;\"><strong>配送产品：美团专送</strong></p><p style=\"font-family:宋体;\"><strong>（1）平台使用费率：<span name=\"ZdZu42lH\" data_name=\"美配平台费\" data_remark=\"\" data_type=\"2\" data_text=\"1%\" style=\"color: #333;\"><#if (pdfMetaContent.mtPlatformFeeInfo)??><#list pdfMetaContent.mtPlatformFeeInfo?split(\"\\n\") as dynamicItem><span style=\"color:#333333; font-family:宋体;\">${dynamicItem}</span><#if dynamicItem_has_next ><br/></#if></#list></#if></span></strong></p><p style=\"font-family:宋体;\"><strong>（2）商业支持服务费率：</strong></p><p style=\"font-family:宋体;\"><strong><span name=\"D73LUY2x\" data_name=\"美配商业支持费\" data_remark=\"\" data_type=\"2\" data_text=\"1）若优惠后商品价格＜10元，商业支持服务费费率15%&nbsp;&nbsp;2）若优惠后商品价格≥10元且<20元，商业支持服务费费率14%&nbsp;&nbsp;3）若优惠后商品价格≥20元，商业支持服务费费率13%\" style=\"color: #333;\"><#if (pdfMetaContent.mtBusinessFeeInfo)??><#list pdfMetaContent.mtBusinessFeeInfo?split(\"\\n\") as dynamicItem><span style=\"color:#333333; font-family:宋体;\">${dynamicItem}</span><#if dynamicItem_has_next ><br/></#if></#list></#if></span></strong></p><p style=\"font-family:宋体;\"><strong>&nbsp;</strong></p><p style=\"font-family:宋体;\"><strong>注：</strong></p><p style=\"font-family:宋体;\"><strong>1）<span style=\"color: \">优惠后商品价格=优惠前商品价格（不含赠品价格）-商家承担活动款。</span></strong></p><p style=\"font-family:宋体;\"><strong>2）保底金额<span name=\"Hy0P3vdD\" data_name=\"美配保底\" data_remark=\"\" data_type=\"2\" data_text=\"1\" style=\"color: #333;\"><#if (pdfMetaContent.guaranteedAmount)??><#list pdfMetaContent.guaranteedAmount?split(\"\\n\") as dynamicItem><span style=\"color:#333333; font-family:宋体;\">${dynamicItem}</span><#if dynamicItem_has_next ><br/></#if></#list></#if></span>元。</strong></p></td></tr><tr class=\"dynamic_row\"><td data-colwidth=\"553\" data-colwidth-sum=\"553\" width=\"553\" style=\"padding: 5.03pt; height: 20px; width: 553px; vertical-align: top; border-width: 1px; border-style: solid;border-color: #000000;\"><p style=\"font-family:宋体;\"><strong>配送产品：商家自配</strong></p><p style=\"font-family:宋体;\"><strong>（1）平台使用费率：<span name=\"lwQZfABW\" data_name=\"自配平台费\" data_remark=\"\" data_type=\"2\" data_text=\"1%\" style=\"color: #333;\"><#if (pdfMetaContent.selfPlatformFeeInfo)??><#list pdfMetaContent.selfPlatformFeeInfo?split(\"\\n\") as dynamicItem><span style=\"color:#333333; font-family:宋体;\">${dynamicItem}</span><#if dynamicItem_has_next ><br/></#if></#list></#if></span></strong></p><p style=\"font-family:宋体;\"><strong>（2）商业支持服务费率：</strong></p><p style=\"font-family:宋体;\"><strong><span name=\"Qx3szUBP\" data_name=\"自配商业费\" data_remark=\"\" data_type=\"2\" data_text=\"9%\" style=\"color: #333;\"><#if (pdfMetaContent.selfBusinessFeeInfo)??><#list pdfMetaContent.selfBusinessFeeInfo?split(\"\\n\") as dynamicItem><span style=\"color:#333333; font-family:宋体;\">${dynamicItem}</span><#if dynamicItem_has_next ><br/></#if></#list></#if></span></strong></p></td></tr><tr class=\"dynamic_row\" name=\"\" data-name=\"\" data-description=\"\" data-type=\"\"><td data-colwidth=\"553\" data-colwidth-sum=\"553\" width=\"553\" style=\"padding: 5.03pt; vertical-align: top; height: 20px; width: 553px; border-width: 1px; border-style: solid;border-color: #000000;\"><p style=\"font-family:宋体;\"><strong><span style=\"color: null\">配送产品：美团配送-企客版</span></strong></p><p style=\"font-family:宋体;\"><strong><span style=\"color: null\">1.平台使用费率：<span name=\"KnSKTNwY\" data_name=\"企客的平台使用费\" data_remark=\"\" data_type=\"2\" data_text=\"1%\" style=\"color: #333;\"><#if (pdfMetaContent.qkPlatformFeeInfo)??><#list pdfMetaContent.qkPlatformFeeInfo?split(\"\\n\") as dynamicItem><span style=\"color:#333333; font-family:宋体;\">${dynamicItem}</span><#if dynamicItem_has_next ><br/></#if></#list></#if></span></span></strong></p><p style=\"font-family:宋体;\"><strong><span style=\"color: null\">2. 商业支持服务费率：</span></strong></p><p style=\"font-family:宋体;\"><span name=\"kTmkAV22\" data_name=\"企客的商业支持服务费\" data_remark=\"\" data_type=\"2\" data_text=\"1）若优惠后商品价格＜10元，商业支持服务费费率15%&nbsp;&nbsp;2）若优惠后商品价格≥10元且<20元，商业支持服务费费率14%&nbsp;&nbsp;3）若优惠后商品价格≥20元，商业支持服务费费率13%\" style=\"color: #333;\"><#if (pdfMetaContent.qkBusinessFeeInfo)??><#list pdfMetaContent.qkBusinessFeeInfo?split(\"\\n\") as dynamicItem><span style=\"color:#333333; font-family:宋体;\">${dynamicItem}</span><#if dynamicItem_has_next ><br/></#if></#list></#if></span></p><p style=\"font-family:宋体;\"><strong><span style=\"color: null\">注：</span></strong></p><p style=\"font-family:宋体;\"><strong><span style=\"color: null\">1）固定保底金额<span name=\"gdb7zzmn\" data_name=\"企客的保底\" data_remark=\"\" data_type=\"2\" data_text=\"3.2\" style=\"color: #333;\"><#if (pdfMetaContent.qkGuaranteedAmount)??><#list pdfMetaContent.qkGuaranteedAmount?split(\"\\n\") as dynamicItem><span style=\"color:#333333; font-family:宋体;\">${dynamicItem}</span><#if dynamicItem_has_next ><br/></#if></#list></#if></span>元。</span></strong></p></td></tr><tr class=\"dynamic_row\"><td data-colwidth=\"75\" data-colwidth-sum=\"75\" width=\"75\" style=\"padding: 5.03pt; height: 20px; width: 75px; vertical-align: top; border-width: 1px; border-style: solid;border-color: #000000;\"><p style=\"font-family:宋体;\"><strong>计算方式</strong></p></td><td data-colwidth=\"553\" data-colwidth-sum=\"553\" width=\"553\" style=\"padding: 5.03pt; height: 20px; width: 553px; vertical-align: top; border-width: 1px; border-style: solid;border-color: #000000;\"><p style=\"font-family:宋体;\"><strong>1.收费总额=平台使用费+商业支持服务费</strong></p><p style=\"font-family:宋体;margin-left: 20px;\"><strong>平台使用费=【优惠后商品价格+打包费】*平台使用费费率</strong></p><p style=\"font-family:宋体;margin-left: 20px;\"><strong>商业支持服务费=【优惠后商品价格+打包费】*商业支持服务费率</strong></p><p style=\"font-family:宋体;\"><strong>2.<u>商业支持服务费与保底金额取高，即：当商业支持服务费金额低于保底金额时，则按照固定保底金额收取</u>。</strong></p></td></tr></tbody></table><p style=\"font-family:宋体;\">&nbsp;</p><p style=\"font-family:宋体;text-align: right;\"><span name=\"oFocm7eK\" data_name=\"甲方公章\" data_remark=\"\" data_type=\"1\" data_text=\"成都茶百道餐饮公司\" style=\"color: #fff;\">${pdfMetaContent.partAEstamp!''}</span></p><p style=\"font-family:宋体;text-align: right;\">商家（电子签章）： <span name=\"GwJoLRqL\" data_name=\"甲方名称\" data_remark=\"\" data_type=\"2\" data_text=\"成都茶百道餐饮公司\" style=\"color: #333;\"><#if (pdfMetaContent.partAName)??><#list pdfMetaContent.partAName?split(\"\\n\") as dynamicItem><span style=\"color:#333333; font-family:宋体;\">${dynamicItem}</span><#if dynamicItem_has_next ><br/></#if></#list></#if></span></p><p style=\"font-family:宋体;text-align: right;\">签署日期：<span name=\"OwEWdqlO\" data_name=\"签约时间\" data_remark=\"\" data_type=\"2\" data_text=\"2023年5月1日\" style=\"color: #333;\"><#if (pdfMetaContent.signTime)??><#list pdfMetaContent.signTime?split(\"\\n\") as dynamicItem><span style=\"color:#333333; font-family:宋体;\">${dynamicItem}</span><#if dynamicItem_has_next ><br/></#if></#list></#if></span></p><p style=\"font-family:宋体;\">&nbsp;</p><p style=\"font-family:宋体;\">&nbsp;</p>";

        Integer version = 12;
        EcontractTemplateVersionSimpleBo versionSimpleBo = initEcontractTemplateVersionSimpleBo(version);
        when(econtractTemplateVersionService.getReleasedTemplateVersionSimple(pdfContentInfoBo.getPdfTemplateId())).thenReturn(versionSimpleBo);

        EcontractTemplateVersionBo econtractTemplateVersionBo = initEcontractTemplateVersionBo(targetContent);
        when(econtractTemplateVersionService.getTemplateVersionAndCheck(pdfContentInfoBo.getPdfTemplateId(), versionSimpleBo.getVersion())).thenReturn(econtractTemplateVersionBo);

        byte[] bytes = new byte[1024];
        String url = "/download/mos/5f83c50134079a6e5f164e4ea79a2c25.pdf";
        String fileName = "5f83c50134079a6e5f164e4ea79a2c25";

        List<PdfGeneratorParam> generatorParamList = new ArrayList<>();
//        when(pdfGenerator.generate(generatorParamList)).thenReturn(bytes);

        try (MockedStatic<DigestUtils> digestUtilsMockedStatic = Mockito.mockStatic(DigestUtils.class)) {
            digestUtilsMockedStatic.when(() -> DigestUtils.md5Hex(any(byte[].class))).thenReturn(fileName);
            try (MockedStatic<MtCloudS3Util> mtCloudS3UtilMockedStatic = Mockito.mockStatic(MtCloudS3Util.class)) {
                mtCloudS3UtilMockedStatic.when(() -> MtCloudS3Util.uploadFileFromBytes(any(byte[].class), anyString())).thenReturn(url);
                try (MockedStatic<MccConfig> mccConfigMockedStatic = Mockito.mockStatic(MccConfig.class)) {
                    mccConfigMockedStatic.when(MccConfig::getTheUpperLimitOfOnlyCreatePdf).thenReturn(300);
                    String pdfUrl = econtractPdfService.onlyCreatePdf(pdfContentInfoBo);
                    assertEquals(pdfUrl, url);
                }
            }
        }
    }

    /**
     * 指定模板Name时能正常生成PDF
     */
    @Test
    public void testOnlyCreatePdfWithTemplateName() throws EcontractException, TemplateException, DocumentException, IOException, EcontractTemplateConfigException {
        String param = "{\n" +
                "    \"pdfBizContent\": [],\n" +
                "    \"pdfMetaContent\": {\n" +
                "        \"partAOfficialSeal\": \"测试缚升\",\n" +
                "        \"selfPlatformFeeInfo\": \"1.0%\",\n" +
                "        \"selfBusinessFeeInfo\": \"1) 若优惠后商品价格<22.0元，商业支持服务费费率22.0%\\n2) 若优惠后商品价格≥22.0元，商业支持服务费费率33.0%\\n\",\n" +
                "        \"signTime\": \"2024-03-26\",\n" +
                "        \"qkBusinessFeeInfo\": \"3.33%\",\n" +
                "        \"deliveryType\": \"\",\n" +
                "        \"qkGuaranteedAmount\": \"2.2\",\n" +
                "        \"partAName\": \"测试缚升\",\n" +
                "        \"guaranteedAmount\": \"3.21\",\n" +
                "        \"qkPlatformFeeInfo\": \"1.0%\",\n" +
                "        \"partAEstamp\": \"美团外卖商家签章专属\",\n" +
                "        \"poiInfo\": \"\",\n" +
                "        \"mtPlatformFeeInfo\": \"0.01%\",\n" +
                "        \"brandInfo\": \"呛渴筛提消拾珊(北京) ID:215178\",\n" +
                "        \"mtBusinessFeeInfo\": \"1) 若优惠后商品价格<20.0元，商业支持服务费费率66.0%\\n2) 若优惠后商品价格≥20.0元且<40.0元，商业支持服务费费率62.2%\\n3) 若优惠后商品价格≥40.0元，商业支持服务费费率57.89%\\n\"\n" +
                "    },\n" +
                "    \"pdfTemplateName\": \"settle_moon_single_info_v3.ftl\",\n" +
                "    \"vertical\": true\n" +
                "}";
        PdfContentInfoBo pdfContentInfoBo = JacksonUtil.readValue(param, PdfContentInfoBo.class);

        EcontractFtlTemplateEntity templateEntity = initEcontractFtlTemplateEntity();
        when(econtractFtlTemplateService.selectByName(pdfContentInfoBo.getPdfTemplateName())).thenReturn(templateEntity);


        byte[] bytes = new byte[1024];
        String url = "/download/mos/5f83c50134079a6e5f164e4ea79a2c25.pdf";
        String fileName = "5f83c50134079a6e5f164e4ea79a2c25";

        List<PdfGeneratorParam> generatorParamList = new ArrayList<>();
//        when(pdfGenerator.generate(generatorParamList)).thenReturn(bytes);

        try (MockedStatic<DigestUtils> digestUtilsMockedStatic = Mockito.mockStatic(DigestUtils.class)) {
            digestUtilsMockedStatic.when(() -> DigestUtils.md5Hex(any(byte[].class))).thenReturn(fileName);
            try (MockedStatic<MtCloudS3Util> mtCloudS3UtilMockedStatic = Mockito.mockStatic(MtCloudS3Util.class)) {
                mtCloudS3UtilMockedStatic.when(() -> MtCloudS3Util.uploadFileFromBytes(any(byte[].class), anyString())).thenReturn(url);
                String pdfUrl = econtractPdfService.onlyCreatePdf(pdfContentInfoBo);
                assertEquals(pdfUrl, url);
            }
        }
    }

    /**
     * 指定模板Name时，模板不存在
     */
    @Test(expected = EcontractException.class)
    public void testOnlyCreatePdfWithTemplateNameException() throws EcontractException, TemplateException, DocumentException, IOException, EcontractTemplateConfigException {
        PdfContentInfoBo pdfContentInfoBo = new PdfContentInfoBo();
        pdfContentInfoBo.setPdfTemplateName("settle_moon_single_info_v3.ftl");
        when(econtractFtlTemplateService.selectByName(pdfContentInfoBo.getPdfTemplateName())).thenReturn(null);
        econtractPdfService.onlyCreatePdf(pdfContentInfoBo);
        Assert.fail();
    }

    private EcontractTemplateVersionSimpleBo initEcontractTemplateVersionSimpleBo(Integer version) {
        EcontractTemplateVersionSimpleBo versionSimpleBo = new EcontractTemplateVersionSimpleBo();
        versionSimpleBo.setVersion(version);
        return versionSimpleBo;
    }

    private EcontractTemplateVersionBo initEcontractTemplateVersionBo( String targetContent) {
        EcontractTemplateVersionBo templateVersionBo = new EcontractTemplateVersionBo();
        templateVersionBo.setTargetContent(targetContent);
        return templateVersionBo;
    }

    private EcontractFtlTemplateEntity initEcontractFtlTemplateEntity() {
        EcontractFtlTemplateEntity econtractFtlTemplate = new EcontractFtlTemplateEntity();
        econtractFtlTemplate.setOptionTemplate("{\"vertical\":true}");
        return econtractFtlTemplate;
    }

    /**
     * 测试 optionTemplate 为空白字符串
     */
    @Test
    public void testParseVerticalBlankString() {
        boolean result = econtractPdfService.parseVertical(" ");
        Assert.assertTrue(result);
    }

    /**
     * 测试 optionTemplate 不是有效的 JSON 字符串
     */
    @Test(expected = com.alibaba.fastjson.JSONException.class)
    public void testParseVerticalInvalidJson() {
        econtractPdfService.parseVertical("invalid json");
    }

    /**
     * 测试 optionTemplate 是有效的 JSON 字符串，但不包含 "vertical" 键
     */
    @Test
    public void testParseVerticalNoVerticalKey() {
        boolean result = econtractPdfService.parseVertical("{}");
        Assert.assertTrue(result);
    }

    /**
     * 测试 optionTemplate 包含 "vertical" 键，且值为 true
     */
    @Test
    public void testParseVerticalTrue() {
        boolean result = econtractPdfService.parseVertical("{\"vertical\": true}");
        Assert.assertTrue(result);
    }

    /**
     * 测试 optionTemplate 包含 "vertical" 键，且值为 false
     */
    @Test
    public void testParseVerticalFalse() {
        boolean result = econtractPdfService.parseVertical("{\"vertical\": false}");
        Assert.assertFalse(result);
    }

}
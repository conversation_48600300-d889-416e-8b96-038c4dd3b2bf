package com.sankuai.meituan.waimai.econtract.server.service.idempotent;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordEntity;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class EcontractIdempotentGrayTest {

    /**
     * Test when ID % 10 is less than gray value, should return true
     */
    @Test
    public void testIsIdempotentGrayWhenModuloLessThanGray() throws Throwable {
        // arrange
        EcontractContext context = mock(EcontractContext.class);
        EcontractRecordEntity recordEntity = mock(EcontractRecordEntity.class);
        when(context.getEcontractRecordEntity()).thenReturn(recordEntity);
        // 5 % 10 = 5
        when(recordEntity.getId()).thenReturn(5);
        try (MockedStatic<MccConfig> mocked = Mockito.mockStatic(MccConfig.class)) {
            // 5 < 6
            mocked.when(MccConfig::getEcontractIdempotentGray).thenReturn(6);
            // act
            boolean result = EcontractIdempotentGray.isIdempotentGray(context);
            // assert
            assertTrue(result);
        }
    }

    /**
     * Test when ID % 10 equals gray value, should return false
     */
    @Test
    public void testIsIdempotentGrayWhenModuloEqualsGray() throws Throwable {
        // arrange
        EcontractContext context = mock(EcontractContext.class);
        EcontractRecordEntity recordEntity = mock(EcontractRecordEntity.class);
        when(context.getEcontractRecordEntity()).thenReturn(recordEntity);
        // 5 % 10 = 5
        when(recordEntity.getId()).thenReturn(5);
        try (MockedStatic<MccConfig> mocked = Mockito.mockStatic(MccConfig.class)) {
            // 5 == 5
            mocked.when(MccConfig::getEcontractIdempotentGray).thenReturn(5);
            // act
            boolean result = EcontractIdempotentGray.isIdempotentGray(context);
            // assert
            assertFalse(result);
        }
    }

    /**
     * Test when ID % 10 is greater than gray value, should return false
     */
    @Test
    public void testIsIdempotentGrayWhenModuloGreaterThanGray() throws Throwable {
        // arrange
        EcontractContext context = mock(EcontractContext.class);
        EcontractRecordEntity recordEntity = mock(EcontractRecordEntity.class);
        when(context.getEcontractRecordEntity()).thenReturn(recordEntity);
        // 7 % 10 = 7
        when(recordEntity.getId()).thenReturn(7);
        try (MockedStatic<MccConfig> mocked = Mockito.mockStatic(MccConfig.class)) {
            // 7 > 6
            mocked.when(MccConfig::getEcontractIdempotentGray).thenReturn(6);
            // act
            boolean result = EcontractIdempotentGray.isIdempotentGray(context);
            // assert
            assertFalse(result);
        }
    }

    /**
     * Test boundary case with ID just below multiple of 10
     */
    @Test
    public void testIsIdempotentGrayWithBoundaryValueBelowMultipleOf10() throws Throwable {
        // arrange
        EcontractContext context = mock(EcontractContext.class);
        EcontractRecordEntity recordEntity = mock(EcontractRecordEntity.class);
        when(context.getEcontractRecordEntity()).thenReturn(recordEntity);
        // 9 % 10 = 9
        when(recordEntity.getId()).thenReturn(9);
        try (MockedStatic<MccConfig> mocked = Mockito.mockStatic(MccConfig.class)) {
            // 9 < 10
            mocked.when(MccConfig::getEcontractIdempotentGray).thenReturn(10);
            // act
            boolean result = EcontractIdempotentGray.isIdempotentGray(context);
            // assert
            assertTrue(result);
        }
    }

    /**
     * Test boundary case with ID at multiple of 10
     */
    @Test
    public void testIsIdempotentGrayWithBoundaryValueAtMultipleOf10() throws Throwable {
        // arrange
        EcontractContext context = mock(EcontractContext.class);
        EcontractRecordEntity recordEntity = mock(EcontractRecordEntity.class);
        when(context.getEcontractRecordEntity()).thenReturn(recordEntity);
        // 10 % 10 = 0
        when(recordEntity.getId()).thenReturn(10);
        try (MockedStatic<MccConfig> mocked = Mockito.mockStatic(MccConfig.class)) {
            // 0 < 1
            mocked.when(MccConfig::getEcontractIdempotentGray).thenReturn(1);
            // act
            boolean result = EcontractIdempotentGray.isIdempotentGray(context);
            // assert
            assertTrue(result);
        }
    }

    /**
     * Test when context is null, should throw NullPointerException
     */
    @Test(expected = NullPointerException.class)
    public void testIsIdempotentGrayWhenContextIsNull() throws Throwable {
        // arrange
        EcontractContext context = null;
        try (MockedStatic<MccConfig> mocked = Mockito.mockStatic(MccConfig.class)) {
            mocked.when(MccConfig::getEcontractIdempotentGray).thenReturn(5);
            // act
            EcontractIdempotentGray.isIdempotentGray(context);
            // assert - exception expected
        }
    }

    /**
     * Test when record entity is null, should throw NullPointerException
     */
    @Test(expected = NullPointerException.class)
    public void testIsIdempotentGrayWhenRecordEntityIsNull() throws Throwable {
        // arrange
        EcontractContext context = mock(EcontractContext.class);
        when(context.getEcontractRecordEntity()).thenReturn(null);
        try (MockedStatic<MccConfig> mocked = Mockito.mockStatic(MccConfig.class)) {
            mocked.when(MccConfig::getEcontractIdempotentGray).thenReturn(5);
            // act
            EcontractIdempotentGray.isIdempotentGray(context);
            // assert - exception expected
        }
    }

    /**
     * Test when ID is null, should throw NullPointerException
     */
    @Test(expected = NullPointerException.class)
    public void testIsIdempotentGrayWhenIdIsNull() throws Throwable {
        // arrange
        EcontractContext context = mock(EcontractContext.class);
        EcontractRecordEntity recordEntity = mock(EcontractRecordEntity.class);
        when(context.getEcontractRecordEntity()).thenReturn(recordEntity);
        when(recordEntity.getId()).thenReturn(null);
        try (MockedStatic<MccConfig> mocked = Mockito.mockStatic(MccConfig.class)) {
            mocked.when(MccConfig::getEcontractIdempotentGray).thenReturn(5);
            // act
            EcontractIdempotentGray.isIdempotentGray(context);
            // assert - exception expected
        }
    }
}

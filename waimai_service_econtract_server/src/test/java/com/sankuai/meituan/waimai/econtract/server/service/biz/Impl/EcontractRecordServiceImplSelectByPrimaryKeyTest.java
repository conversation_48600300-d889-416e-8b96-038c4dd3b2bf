package com.sankuai.meituan.waimai.econtract.server.service.biz.Impl;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractRecordEntityMapper;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordEntity;
import java.util.concurrent.TimeUnit;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;
import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordContextEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractUserEntity;
import com.sankuai.meituan.waimai.econtract.server.service.biz.Impl.EcontractBigRecordParseService;
import java.lang.reflect.Method;
import java.util.Collections;
import org.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class EcontractRecordServiceImplSelectByPrimaryKeyTest {

    @Mock
    private EcontractRecordEntityMapper econtractRecordEntityMapper;

    @Mock
    private Logger logger;

    @InjectMocks
    private EcontractRecordServiceImpl econtractRecordService;

    private static final Integer TEST_ID = 123;

    private static final String TEST_RECORD_KEY = "test-key";

    private static final String TEST_CONTEXT = "test-context";

    @Mock
    private EcontractBigRecordParseService econtractBigRecordParseService;

    private static final String VALID_JSON_CONTEXT = "{\"econtractUserEntity\":{},\"flowList\":[],\"stageInfoBoList\":[],\"stageBatchInfoBoList\":[]}";



    /**
     * Test exception handling when mapper throws exception
     */
    @Test(expected = RuntimeException.class)
    public void testSelectByPrimaryKey_MapperException() throws Throwable {
        // arrange
        when(econtractRecordEntityMapper.selectByPrimaryKey(TEST_ID)).thenThrow(new RuntimeException("Database error"));
        // act
        econtractRecordService.selectByPrimaryKey(TEST_ID);
        // assert - exception expected
    }

    private EcontractRecordEntity invokeRetryWrapColdAndHotData(EcontractRecordEntity recordEntity) throws Exception {
        Method method = EcontractRecordServiceImpl.class.getDeclaredMethod("retryWrapColdAndHotData", EcontractRecordEntity.class);
        method.setAccessible(true);
        return (EcontractRecordEntity) method.invoke(econtractRecordService, recordEntity);
    }

    @Test(expected = Exception.class)
    public void testRetryWrapColdAndHotData_NullRecordEntity() throws Throwable {
        // act & assert - 预期会抛出异常
        try {
            invokeRetryWrapColdAndHotData(null);
        } catch (Exception e) {
            // 验证是由于NPE导致的
            assertTrue(e.getCause() instanceof NullPointerException);
            throw e;
        }
    }

    @Test
    public void testRetryWrapColdAndHotData_WithNonEmptyContext() throws Throwable {
        // arrange
        EcontractRecordEntity recordEntity = new EcontractRecordEntity();
        recordEntity.setRecordKey("test-key");
        recordEntity.setEcontractRecordContext(VALID_JSON_CONTEXT);
        recordEntity.setId(1);
        EcontractRecordContextEntity contextEntity = new EcontractRecordContextEntity();
        contextEntity.setContext(VALID_JSON_CONTEXT);
        contextEntity.setEcontractRecordId(1);
        when(econtractBigRecordParseService.selectByRecordId(1)).thenReturn(contextEntity);
        // act
        EcontractRecordEntity result = invokeRetryWrapColdAndHotData(recordEntity);
        // assert
        assertNotNull(result);
        assertEquals("test-key", result.getRecordKey());
        assertNotNull(result.getEcontractRecordContext());
        verify(econtractRecordEntityMapper, never()).queryRecordByRecordKey(anyString());
        verify(econtractBigRecordParseService, times(1)).selectByRecordId(1);
    }

    @Test
    public void testRetryWrapColdAndHotData_EmptyContextSuccessOnFirstRetry() throws Throwable {
        // arrange
        EcontractRecordEntity emptyRecord = new EcontractRecordEntity();
        emptyRecord.setRecordKey("test-key");
        emptyRecord.setEcontractRecordContext("");
        emptyRecord.setId(1);
        EcontractRecordEntity recordWithContext = new EcontractRecordEntity();
        recordWithContext.setRecordKey("test-key");
        recordWithContext.setEcontractRecordContext(VALID_JSON_CONTEXT);
        recordWithContext.setId(1);
        EcontractRecordContextEntity contextEntity = new EcontractRecordContextEntity();
        contextEntity.setContext(VALID_JSON_CONTEXT);
        contextEntity.setEcontractRecordId(1);
        when(econtractRecordEntityMapper.queryRecordByRecordKey("test-key")).thenReturn(recordWithContext);
        when(econtractBigRecordParseService.selectByRecordId(1)).thenReturn(contextEntity);
        // act
        EcontractRecordEntity result = invokeRetryWrapColdAndHotData(emptyRecord);
        // assert
        assertNotNull(result);
        assertEquals("test-key", result.getRecordKey());
        assertNotNull(result.getEcontractRecordContext());
        verify(econtractRecordEntityMapper, times(1)).queryRecordByRecordKey("test-key");
        verify(econtractBigRecordParseService, times(1)).selectByRecordId(1);
    }

    @Test
    public void testRetryWrapColdAndHotData_EmptyContextSuccessOnSecondRetry() throws Throwable {
        // arrange
        EcontractRecordEntity emptyRecord = new EcontractRecordEntity();
        emptyRecord.setRecordKey("test-key");
        emptyRecord.setEcontractRecordContext("");
        emptyRecord.setId(1);
        EcontractRecordEntity stillEmptyRecord = new EcontractRecordEntity();
        stillEmptyRecord.setRecordKey("test-key");
        stillEmptyRecord.setEcontractRecordContext("");
        stillEmptyRecord.setId(1);
        EcontractRecordEntity recordWithContext = new EcontractRecordEntity();
        recordWithContext.setRecordKey("test-key");
        recordWithContext.setEcontractRecordContext(VALID_JSON_CONTEXT);
        recordWithContext.setId(1);
        EcontractRecordContextEntity contextEntity = new EcontractRecordContextEntity();
        contextEntity.setContext(VALID_JSON_CONTEXT);
        contextEntity.setEcontractRecordId(1);
        when(econtractRecordEntityMapper.queryRecordByRecordKey("test-key")).thenReturn(stillEmptyRecord).thenReturn(recordWithContext);
        when(econtractBigRecordParseService.selectByRecordId(1)).thenReturn(contextEntity);
        // act
        EcontractRecordEntity result = invokeRetryWrapColdAndHotData(emptyRecord);
        // assert
        assertNotNull(result);
        assertEquals("test-key", result.getRecordKey());
        assertNotNull(result.getEcontractRecordContext());
        verify(econtractRecordEntityMapper, times(2)).queryRecordByRecordKey("test-key");
        verify(econtractBigRecordParseService, times(1)).selectByRecordId(1);
    }

    @Test(expected = Exception.class)
    public void testRetryWrapColdAndHotData_EmptyContextAllRetriesFailed() throws Throwable {
        // arrange
        EcontractRecordEntity emptyRecord = new EcontractRecordEntity();
        emptyRecord.setRecordKey("test-key");
        emptyRecord.setEcontractRecordContext("");
        emptyRecord.setId(1);
        EcontractRecordEntity stillEmptyRecord = new EcontractRecordEntity();
        stillEmptyRecord.setRecordKey("test-key");
        stillEmptyRecord.setEcontractRecordContext("");
        stillEmptyRecord.setId(1);
        EcontractRecordContextEntity contextEntity = new EcontractRecordContextEntity();
        contextEntity.setContext(VALID_JSON_CONTEXT);
        contextEntity.setEcontractRecordId(1);
        when(econtractRecordEntityMapper.queryRecordByRecordKey("test-key")).thenReturn(stillEmptyRecord);
        when(econtractBigRecordParseService.selectByRecordId(1)).thenReturn(contextEntity);
        try {
            // act
            invokeRetryWrapColdAndHotData(emptyRecord);
        } catch (Exception e) {
            // assert
            verify(econtractRecordEntityMapper, times(3)).queryRecordByRecordKey("test-key");
            verify(econtractBigRecordParseService, times(1)).selectByRecordId(1);
            // 验证是由于JSON解析失败导致的异常
            assertTrue(e.getCause() instanceof NullPointerException);
            throw e;
        }
    }

    @Test
    public void testRetryWrapColdAndHotData_NullContext() throws Throwable {
        // arrange
        EcontractRecordEntity recordWithNullContext = new EcontractRecordEntity();
        recordWithNullContext.setRecordKey("test-key");
        recordWithNullContext.setEcontractRecordContext(null);
        recordWithNullContext.setId(1);
        EcontractRecordEntity recordWithContext = new EcontractRecordEntity();
        recordWithContext.setRecordKey("test-key");
        recordWithContext.setEcontractRecordContext(VALID_JSON_CONTEXT);
        recordWithContext.setId(1);
        EcontractRecordContextEntity contextEntity = new EcontractRecordContextEntity();
        contextEntity.setContext(VALID_JSON_CONTEXT);
        contextEntity.setEcontractRecordId(1);
        when(econtractRecordEntityMapper.queryRecordByRecordKey("test-key")).thenReturn(recordWithContext);
        when(econtractBigRecordParseService.selectByRecordId(1)).thenReturn(contextEntity);
        // act
        EcontractRecordEntity result = invokeRetryWrapColdAndHotData(recordWithNullContext);
        // assert
        assertNotNull(result);
        assertEquals("test-key", result.getRecordKey());
        assertNotNull(result.getEcontractRecordContext());
        verify(econtractRecordEntityMapper, times(1)).queryRecordByRecordKey("test-key");
        verify(econtractBigRecordParseService, times(1)).selectByRecordId(1);
    }

    @Test(expected = Exception.class)
    public void testRetryWrapColdAndHotData_InterruptedException() throws Throwable {
        // arrange
        EcontractRecordEntity emptyRecord = new EcontractRecordEntity();
        emptyRecord.setRecordKey("test-key");
        emptyRecord.setEcontractRecordContext("");
        emptyRecord.setId(1);
        EcontractRecordEntity stillEmptyRecord = new EcontractRecordEntity();
        stillEmptyRecord.setRecordKey("test-key");
        stillEmptyRecord.setEcontractRecordContext("");
        stillEmptyRecord.setId(1);
        EcontractRecordContextEntity contextEntity = new EcontractRecordContextEntity();
        contextEntity.setContext(VALID_JSON_CONTEXT);
        contextEntity.setEcontractRecordId(1);
        when(econtractRecordEntityMapper.queryRecordByRecordKey("test-key")).thenReturn(stillEmptyRecord);
        when(econtractBigRecordParseService.selectByRecordId(1)).thenReturn(contextEntity);
        // 中断当前线程以模拟InterruptedException
        Thread.currentThread().interrupt();
        try {
            // act
            invokeRetryWrapColdAndHotData(emptyRecord);
        } catch (Exception e) {
            // assert
            verify(econtractRecordEntityMapper, times(3)).queryRecordByRecordKey("test-key");
            verify(econtractBigRecordParseService, times(1)).selectByRecordId(1);
            // 验证是由于JSON解析失败导致的异常
            assertTrue(e.getCause() instanceof NullPointerException);
            throw e;
        } finally {
            // 清除中断状态
            Thread.interrupted();
        }
    }

    @Test
    public void testRetryWrapColdAndHotData_NullContextEntity() throws Throwable {
        // arrange
        EcontractRecordEntity recordEntity = new EcontractRecordEntity();
        recordEntity.setRecordKey("test-key");
        recordEntity.setEcontractRecordContext(VALID_JSON_CONTEXT);
        recordEntity.setId(1);
        when(econtractBigRecordParseService.selectByRecordId(1)).thenReturn(null);
        // act
        EcontractRecordEntity result = invokeRetryWrapColdAndHotData(recordEntity);
        // assert
        assertNotNull(result);
        assertEquals("test-key", result.getRecordKey());
        assertEquals(VALID_JSON_CONTEXT, result.getEcontractRecordContext());
        verify(econtractRecordEntityMapper, never()).queryRecordByRecordKey(anyString());
        verify(econtractBigRecordParseService, times(1)).selectByRecordId(1);
    }
}

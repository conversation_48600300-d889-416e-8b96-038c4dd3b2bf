package com.sankuai.meituan.waimai.econtract.server.template.config.service;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.meituan.it.contract.platform.model.response.template.QueryContractTemplateResp;
import com.sankuai.meituan.waimai.econtract.server.adapter.WmEmployAdapter;
import com.sankuai.meituan.waimai.econtract.server.template.config.dao.EcontractTemplateBaseMapper;
import com.sankuai.meituan.waimai.econtract.server.template.config.entity.EcontractTemplateBaseEntity;
import com.sankuai.meituan.waimai.econtract.server.template.config.service.mapperservice.EcontractTemplateBaseMapperService;
import com.sankuai.meituan.waimai.econtract.server.template.config.utils.EcontractTemplateTransUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.config.econtracttemplate.ContractTemplateCancelAuditRequestDTO;
import com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException;
import com.sankuai.meituan.waimai.econtrct.client.template.config.constants.EcontractTemplateConstants;
import com.sankuai.meituan.waimai.econtrct.client.template.config.domain.EcontractTemplateVersionBo;
import com.sankuai.meituan.waimai.econtrct.client.template.config.exception.EcontractTemplateConfigException;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import org.elasticsearch.common.Strings;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class EcontractTemplateBaseServiceTest {

    @Mock
    private EcontractTemplateVersionService econtractTemplateVersionService;

    @Mock
    private EcontractTemplateBaseMapper econtractTemplateBaseMapper;

    @Mock
    private EcontractTemplateOplogService econtractTemplateOplogService;

    @Mock
    private WmEmployAdapter wmEmployAdapter;

    @InjectMocks
    private EcontractTemplateBaseService econtractTemplateBaseService;

    private ContractTemplateCancelAuditRequestDTO requestDTO;

    private EcontractTemplateVersionBo versionBo;

    private EcontractTemplateBaseEntity templateBase;

    @Before
    public void setUp() {
        requestDTO = new ContractTemplateCancelAuditRequestDTO();
        requestDTO.setId(1);
        requestDTO.setOperatorId(100);
        versionBo = new EcontractTemplateVersionBo();
        versionBo.setId(1);
        versionBo.setTemplateId(10);
        versionBo.setStatus(EcontractTemplateConstants.VersionStatusEnum.UNDER_REVIEW.getValue());
        templateBase = new EcontractTemplateBaseEntity();
        templateBase.setId(10);
        templateBase.setHailuoTemplateCode("HAILUO123");
    }

    /**
     * Test when version template is not found
     */
    @Test(expected = EcontractTemplateConfigException.class)
    public void testCancelAuditWhenVersionNotFound() throws Throwable {
        // arrange
        when(econtractTemplateVersionService.getTemplateVersionById(requestDTO.getId())).thenReturn(null);
        // act
        econtractTemplateBaseService.cancelAudit(requestDTO);
        // assert - exception expected
    }

    /**
     * Test when version status is not UNDER_REVIEW
     */
    @Test(expected = EcontractTemplateConfigException.class)
    public void testCancelAuditWhenStatusNotUnderReview() throws Throwable {
        // arrange
        versionBo.setStatus(EcontractTemplateConstants.VersionStatusEnum.RELEASED.getValue());
        when(econtractTemplateVersionService.getTemplateVersionById(requestDTO.getId())).thenReturn(versionBo);
        // act
        econtractTemplateBaseService.cancelAudit(requestDTO);
        // assert - exception expected
    }

    /**
     * Test when template base is not found
     */
    @Test(expected = EcontractTemplateConfigException.class)
    public void testCancelAuditWhenTemplateBaseNotFound() throws Throwable {
        // arrange
        when(econtractTemplateVersionService.getTemplateVersionById(requestDTO.getId())).thenReturn(versionBo);
        when(econtractTemplateBaseMapper.getTemplateBaseById(versionBo.getTemplateId())).thenReturn(null);
        // act
        econtractTemplateBaseService.cancelAudit(requestDTO);
        // assert - exception expected
    }

    /**
     * Test when hailuo template code is empty
     */
    @Test(expected = EcontractTemplateConfigException.class)
    public void testCancelAuditWhenHailuoTemplateCodeEmpty() throws Throwable {
        // arrange
        templateBase.setHailuoTemplateCode(null);
        when(econtractTemplateVersionService.getTemplateVersionById(requestDTO.getId())).thenReturn(versionBo);
        when(econtractTemplateBaseMapper.getTemplateBaseById(versionBo.getTemplateId())).thenReturn(templateBase);
        // act
        econtractTemplateBaseService.cancelAudit(requestDTO);
        // assert - exception expected
    }

    /**
     * Test successful cancellation
     */
    @Test
    public void testCancelAuditSuccessfully() throws Throwable {
        // arrange
        when(econtractTemplateVersionService.getTemplateVersionById(requestDTO.getId())).thenReturn(versionBo);
        when(econtractTemplateBaseMapper.getTemplateBaseById(versionBo.getTemplateId())).thenReturn(templateBase);
        // act
        econtractTemplateBaseService.cancelAudit(requestDTO);
        // assert
        verify(econtractTemplateVersionService).cancelEcontractTemplateVersionAudit(versionBo, templateBase, requestDTO.getOperatorId());
    }
}

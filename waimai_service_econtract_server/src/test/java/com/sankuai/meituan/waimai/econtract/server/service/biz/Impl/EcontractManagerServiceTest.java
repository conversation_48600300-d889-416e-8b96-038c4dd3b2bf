package com.sankuai.meituan.waimai.econtract.server.service.biz.Impl;

import com.sankuai.meituan.waimai.econtract.server.adapter.UacAuthRemoteServiceAdapter;
import com.sankuai.meituan.waimai.econtract.server.adapter.WmEmployAdapter;
import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractUserEntity;
import com.sankuai.meituan.waimai.econtract.server.service.api.EcontractManagerServiceImpl;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractUserService;
import com.sankuai.meituan.waimai.econtract.server.utils.JacksonUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.biz.EcontractTemplateBo;
import com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Date;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/8/6 14:34
 */
@RunWith(MockitoJUnitRunner.class)
public class EcontractManagerServiceTest {

    @Mock
    private EcontractService econtractService;

    @Mock
    private WmEmployAdapter employAdapter;

    @Mock
    private EcontractUserService userService;

    @Mock
    private UacAuthRemoteServiceAdapter uacAuthRemoteServiceAdapter;

    @InjectMocks
    private EcontractManagerServiceImpl econtractManagerService;

    @Test
    public void testQueryEcontractTemplateByIdWithNullResult() {
        when(econtractService.selectByPrimaryKey(any(Integer.class))).thenReturn(null);
        EcontractTemplateBo templateBo = econtractManagerService.queryEcontractTemplateById(1, 1);
        assertNull(templateBo);
    }

    @Test
    public void testQueryEcontractTemplateByIdWithSuperAdminAuth() {
        when(econtractService.selectByPrimaryKey(1)).thenReturn(initEcontractEntity());
        when(uacAuthRemoteServiceAdapter.getAllRoleIdByUid(1)).thenReturn(Lists.newArrayList(111L));
        try (MockedStatic<MccConfig> mccConfigMockedStatic = Mockito.mockStatic(MccConfig.class)) {
            mccConfigMockedStatic.when(MccConfig::getSuperAdminRoleId).thenReturn(111L);
            EcontractTemplateBo templateBo = econtractManagerService.queryEcontractTemplateById(1, 1);
            assertEquals(JacksonUtil.writeAsJsonStr(templateBo), JacksonUtil.writeAsJsonStr(initEcontractTemplateBo()));
        }
    }

    @Test
    public void testQueryEcontractTemplateByIdNoAuth() {
        when(econtractService.selectByPrimaryKey(1)).thenReturn(initEcontractEntity());
        when(uacAuthRemoteServiceAdapter.getAllRoleIdByUid(1)).thenReturn(Lists.newArrayList(111L));
        try (MockedStatic<MccConfig> mccConfigMockedStatic = Mockito.mockStatic(MccConfig.class)) {
            mccConfigMockedStatic.when(MccConfig::getSuperAdminRoleId).thenReturn(112L);
            mccConfigMockedStatic.when(MccConfig::getContractConfigRDRoleId).thenReturn(113L);
            EcontractTemplateBo templateBo = econtractManagerService.queryEcontractTemplateById(1, 1);
            assertNull(templateBo);
        }
    }

    @Test
    public void testQueryEcontractTemplateByIdWithNoRdAuth() {
        when(econtractService.selectByPrimaryKey(1)).thenReturn(initEcontractEntity());
        when(uacAuthRemoteServiceAdapter.getAllRoleIdByUid(1)).thenReturn(Lists.newArrayList(111L));
        try (MockedStatic<MccConfig> mccConfigMockedStatic = Mockito.mockStatic(MccConfig.class)) {
            mccConfigMockedStatic.when(MccConfig::getSuperAdminRoleId).thenReturn(112L);
            mccConfigMockedStatic.when(MccConfig::getContractConfigRDRoleId).thenReturn(111L);
            when(employAdapter.getById(1)).thenReturn(new WmEmploy());
//            when(userService.checkUserPermission(1, "liuyunjie05")).thenReturn(false);
            EcontractTemplateBo templateBo = econtractManagerService.queryEcontractTemplateById(1, 1);
            assertNull(templateBo);
        } catch (EcontractException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void testQueryEcontractTemplateByIdWithRdAuth() {
        when(econtractService.selectByPrimaryKey(1)).thenReturn(initEcontractEntity());
        when(uacAuthRemoteServiceAdapter.getAllRoleIdByUid(1)).thenReturn(Lists.newArrayList(111L));
        try (MockedStatic<MccConfig> mccConfigMockedStatic = Mockito.mockStatic(MccConfig.class)) {
            mccConfigMockedStatic.when(MccConfig::getSuperAdminRoleId).thenReturn(112L);
            mccConfigMockedStatic.when(MccConfig::getContractConfigRDRoleId).thenReturn(111L);
            when(employAdapter.getById(1)).thenReturn(new WmEmploy());
            when(userService.checkUserPermission(1, null)).thenReturn(true);
            EcontractTemplateBo templateBo = econtractManagerService.queryEcontractTemplateById(1, 1);
            assertEquals(JacksonUtil.writeAsJsonStr(templateBo), JacksonUtil.writeAsJsonStr(initEcontractTemplateBo()));
        } catch (EcontractException e) {
            throw new RuntimeException(e);
        }
    }

    private EcontractEntity initEcontractEntity() {
        EcontractEntity entity = new EcontractEntity();
        entity.setId(2);
        entity.setEcontractType("test");
        entity.setName("测试");
        entity.setProcedureTemplate("ceshi");
        entity.setAuthorityMisId("liuyunjie05");
        entity.setEcontractUserId(1);
        return entity;
    }

    private EcontractTemplateBo initEcontractTemplateBo() {
        EcontractTemplateBo templateBo = new EcontractTemplateBo();
        templateBo.setId(2);
        templateBo.setEcontractType("test");
        templateBo.setName("测试");
        templateBo.setProcedureTemplate("ceshi");
        templateBo.setAuthorityMisId("liuyunjie05");
        return templateBo;
    }

    @Test
    public void testQuerySignFlowTemplateIdByIdAndUidWithNullResult() {
        when(econtractService.selectByPrimaryKey(any(Integer.class))).thenReturn(null);
        EcontractTemplateBo templateBo = econtractManagerService.querySignFlowTemplateIdByIdAndUid(1, 1);
        assertNull(templateBo);
    }


    @Test
    public void testQueryEcontractTemplateByIdAndUidWithSuperAdminAuth() {
        when(econtractService.selectByPrimaryKey(1)).thenReturn(initEcontractEntity());
        when(uacAuthRemoteServiceAdapter.getAllRoleIdByUid(1)).thenReturn(Lists.newArrayList(111L));
        try (MockedStatic<MccConfig> mccConfigMockedStatic = Mockito.mockStatic(MccConfig.class)) {
            mccConfigMockedStatic.when(MccConfig::getSuperAdminRoleId).thenReturn(111L);
            EcontractTemplateBo templateBo = econtractManagerService.querySignFlowTemplateIdByIdAndUid(1, 1);
            assertEquals(JacksonUtil.writeAsJsonStr(templateBo), JacksonUtil.writeAsJsonStr(initEcontractTemplateBo()));
        }
    }

    @Test
    public void testQueryEcontractTemplateByIdAndUidNoAuth() {
        when(econtractService.selectByPrimaryKey(1)).thenReturn(initEcontractEntity());
        when(uacAuthRemoteServiceAdapter.getAllRoleIdByUid(1)).thenReturn(Lists.newArrayList(111L));
        try (MockedStatic<MccConfig> mccConfigMockedStatic = Mockito.mockStatic(MccConfig.class)) {
            mccConfigMockedStatic.when(MccConfig::getSuperAdminRoleId).thenReturn(112L);
            mccConfigMockedStatic.when(MccConfig::getContractConfigRDRoleId).thenReturn(113L);
            EcontractTemplateBo templateBo = econtractManagerService.queryEcontractTemplateById(1, 1);
            assertNull(templateBo);
        }
    }

    @Test
    public void testQueryEcontractTemplateByIdAndUidWithNoRdAuth() {
        when(econtractService.selectByPrimaryKey(1)).thenReturn(initEcontractEntity());
        when(uacAuthRemoteServiceAdapter.getAllRoleIdByUid(1)).thenReturn(Lists.newArrayList(111L));
        try (MockedStatic<MccConfig> mccConfigMockedStatic = Mockito.mockStatic(MccConfig.class)) {
            mccConfigMockedStatic.when(MccConfig::getSuperAdminRoleId).thenReturn(112L);
            mccConfigMockedStatic.when(MccConfig::getContractConfigRDRoleId).thenReturn(111L);
            when(employAdapter.getById(1)).thenReturn(new WmEmploy());
//            when(userService.checkUserPermission(1, "liuyunjie05")).thenReturn(false);
            EcontractTemplateBo templateBo = econtractManagerService.queryEcontractTemplateById(1, 1);
            assertNull(templateBo);
        } catch (EcontractException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void testQueryEcontractTemplateByIdAndUidWithRdAuth() {
        when(econtractService.selectByPrimaryKey(1)).thenReturn(initEcontractEntity());
        when(uacAuthRemoteServiceAdapter.getAllRoleIdByUid(1)).thenReturn(Lists.newArrayList(111L));
        try (MockedStatic<MccConfig> mccConfigMockedStatic = Mockito.mockStatic(MccConfig.class)) {
            mccConfigMockedStatic.when(MccConfig::getSuperAdminRoleId).thenReturn(112L);
            mccConfigMockedStatic.when(MccConfig::getContractConfigRDRoleId).thenReturn(111L);
            when(employAdapter.getById(1)).thenReturn(new WmEmploy());
            when(userService.checkUserPermission(1, null)).thenReturn(true);
            EcontractTemplateBo templateBo = econtractManagerService.queryEcontractTemplateById(1, 1);
            assertEquals(JacksonUtil.writeAsJsonStr(templateBo), JacksonUtil.writeAsJsonStr(initEcontractTemplateBo()));
        } catch (EcontractException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void testNoAuthToCreateSignFlowTemplate() {
//        when(uacAuthRemoteServiceAdapter.getAllRoleIdByUid(1)).thenReturn(Lists.newArrayList(111L));
        boolean result = econtractManagerService.createSignFlowTemplate(initEcontractTemplateBo(), 111);
        assertFalse(result);
    }

    @Test
    public void testSuperAdminAuthToCreateSignFlowTemplate() {
        int uid = 7877471;
        when(uacAuthRemoteServiceAdapter.getAllRoleIdByUid(uid)).thenReturn(Lists.newArrayList(111L));
        try (MockedStatic<MccConfig> mccConfigMockedStatic = Mockito.mockStatic(MccConfig.class)) {
            mccConfigMockedStatic.when(MccConfig::getSuperAdminRoleId).thenReturn(111L);
            when(userService.queryUserByName(initEcontractTemplateBo().getUserName())).thenReturn(initEcontractUserEntity());
            doNothing().when(econtractService).insertSelective(any(EcontractEntity.class));
            boolean result = econtractManagerService.createSignFlowTemplate(initEcontractTemplateBo(), uid);
            assertTrue(result);
        }
    }

    @Test
    public void testNotSuperAdminAuthToCreateSignFlowTemplate() {
        int uid = 7877471;
        when(uacAuthRemoteServiceAdapter.getAllRoleIdByUid(uid)).thenReturn(Lists.newArrayList(111L));
        try (MockedStatic<MccConfig> mccConfigMockedStatic = Mockito.mockStatic(MccConfig.class)) {
            mccConfigMockedStatic.when(MccConfig::getSuperAdminRoleId).thenReturn(112L);
            boolean result = econtractManagerService.createSignFlowTemplate(initEcontractTemplateBo(), uid);
            assertFalse(result);
        }
    }

    private EcontractUserEntity initEcontractUserEntity() {
        EcontractUserEntity userEntity = new EcontractUserEntity();
        userEntity.setId(1111);
        return userEntity;
    }

}

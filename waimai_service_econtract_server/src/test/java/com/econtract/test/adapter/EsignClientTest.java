/*
package com.econtract.test.adapter;

import com.alibaba.fastjson.JSON;
import com.econtract.test.BaseSpringJunit;
import com.meituan.sankuai.bsi.esign.common.model.entity.result.ApplyCertResult;
import com.sankuai.meituan.waimai.econtract.server.adapter.EsignClient;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.CAType;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.CertifyInfoBo;

import org.junit.Test;

import javax.annotation.Resource;

public class EsignClientTest {

    @Resource
    private EsignClient esignClient;

    @Test
    public void TestSelectById() throws Exception {
        CertifyInfoBo certifyInfoBo = new CertifyInfoBo();
        certifyInfoBo.setCaType(CAType.COMPANY);
        certifyInfoBo.setQuaNum("89329102298803928871");
        certifyInfoBo.setCustomerName("哎呦我去测试公司");
        certifyInfoBo.setMobile("13099276512");
        certifyInfoBo.setEmail("<EMAIL>");
        ApplyCertResult certResult = esignClient.applyCert(certifyInfoBo);
        System.out.println(JSON.toJSONString(certResult));
    }



}
*/

package com.econtract.test.service;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonObject;
import com.meituan.sankuai.bsi.esign.common.model.dict.NotifyType;
import com.meituan.sankuai.bsi.esign.common.model.entity.result.SignContractAsyncResult;
import com.meituan.service.mobile.mtthrift.util.json.JacksonUtils;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.Impl.AbstractExecutor;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2022-01-20 11:41
 * Email: <EMAIL>
 * Desc:
 */
public class AsyncTaskTest {

    public static void main(String[] args) {
        String contextJson = "{\n" +
                "    \"batch\": true,\n" +
                "    \"callBackUrl\": \"http://douxumeng-oqbvh-sl-customer.waimai.test.sankuai.com/customer/callback/v1/w/notify\",\n" +
                "    \"coldDataList\": [\n" +
                "        \"econtractUserEntity\",\n" +
                "        \"flowList\",\n" +
                "        \"stageInfoBoList\",\n" +
                "        \"stageBatchInfoBoList\"\n" +
                "    ],\n" +
                "    \"contextState\": \"context_to_execute\",\n" +
                "    \"currentTaskNode\": {\n" +
                "        \"callBackTask\": {\n" +
                "            \"canJump\": false,\n" +
                "            \"needNotify\": false,\n" +
                "            \"nextTaskName\": \"\",\n" +
                "            \"stageInfoBoKey\": \"\",\n" +
                "            \"taskId\": 2668904,\n" +
                "            \"taskName\": \"estamp_call_back\",\n" +
                "            \"taskType\": \"process_call_back_task\",\n" +
                "            \"toCallBackTaskName\": \"econtract_stamp_b\"\n" +
                "        },\n" +
                "        \"canJump\": false,\n" +
                "        \"needNotify\": false,\n" +
                "        \"nextTask\": {\n" +
                "            \"callBackTask\": {\n" +
                "                \"canJump\": false,\n" +
                "                \"needNotify\": false,\n" +
                "                \"nextTaskName\": \"\",\n" +
                "                \"stageInfoBoKey\": \"\",\n" +
                "                \"taskId\": 2668907,\n" +
                "                \"taskName\": \"estamp_call_back\",\n" +
                "                \"taskType\": \"process_call_back_task\",\n" +
                "                \"toCallBackTaskName\": \"econtract_stamp_a\"\n" +
                "            },\n" +
                "            \"canJump\": false,\n" +
                "            \"needNotify\": false,\n" +
                "            \"nextTask\": {\n" +
                "                \"callBackTask\": {\n" +
                "                    \"canJump\": false,\n" +
                "                    \"needNotify\": false,\n" +
                "                    \"nextTaskName\": \"\",\n" +
                "                    \"stageInfoBoKey\": \"\",\n" +
                "                    \"taskId\": 2668909,\n" +
                "                    \"taskName\": \"estamp_call_back\",\n" +
                "                    \"taskType\": \"process_call_back_task\",\n" +
                "                    \"toCallBackTaskName\": \"econtract_stamp_e\"\n" +
                "                },\n" +
                "                \"canJump\": false,\n" +
                "                \"needNotify\": false,\n" +
                "                \"nextTask\": {\n" +
                "                    \"canJump\": false,\n" +
                "                    \"needNotify\": true,\n" +
                "                    \"nextTask\": {\n" +
                "                        \"canJump\": false,\n" +
                "                        \"needNotify\": true,\n" +
                "                        \"nextTask\": {\n" +
                "                            \"callBackTask\": {\n" +
                "                                \"canJump\": false,\n" +
                "                                \"needNotify\": false,\n" +
                "                                \"nextTaskName\": \"\",\n" +
                "                                \"stageInfoBoKey\": \"\",\n" +
                "                                \"taskId\": 2668905,\n" +
                "                                \"taskName\": \"estamp_call_back\",\n" +
                "                                \"taskType\": \"process_call_back_task\",\n" +
                "                                \"toCallBackTaskName\": \"econtract_stamp_d\"\n" +
                "                            },\n" +
                "                            \"canJump\": false,\n" +
                "                            \"needNotify\": false,\n" +
                "                            \"nextTask\": {\n" +
                "                                \"canJump\": false,\n" +
                "                                \"needNotify\": true,\n" +
                "                                \"nextTask\": {\n" +
                "                                    \"canJump\": false,\n" +
                "                                    \"needNotify\": true,\n" +
                "                                    \"nextTaskName\": \"\",\n" +
                "                                    \"stageInfoBoKey\": \"\",\n" +
                "                                    \"taskId\": 2668903,\n" +
                "                                    \"taskName\": \"econtract_finish\",\n" +
                "                                    \"taskType\": \"econtract_finish\",\n" +
                "                                    \"toCallBackTaskName\": \"\"\n" +
                "                                },\n" +
                "                                \"nextTaskName\": \"econtract_finish\",\n" +
                "                                \"stageInfoBoKey\": \"\",\n" +
                "                                \"taskId\": 2668902,\n" +
                "                                \"taskName\": \"econtract_filing\",\n" +
                "                                \"taskType\": \"econtract_filing\",\n" +
                "                                \"toCallBackTaskName\": \"\"\n" +
                "                            },\n" +
                "                            \"nextTaskName\": \"econtract_filing\",\n" +
                "                            \"stageInfoBoKey\": \"econtract_stamp_d\",\n" +
                "                            \"taskId\": 2668901,\n" +
                "                            \"taskName\": \"econtract_stamp_d\",\n" +
                "                            \"taskType\": \"econtract_stamp_task\",\n" +
                "                            \"toCallBackTaskName\": \"\"\n" +
                "                        },\n" +
                "                        \"nextTaskName\": \"econtract_stamp_d\",\n" +
                "                        \"stageInfoBoKey\": \"confirm_stamp_d\",\n" +
                "                        \"taskId\": 2668900,\n" +
                "                        \"taskName\": \"confirm_stamp_d\",\n" +
                "                        \"taskType\": \"confirm_stamp_task\",\n" +
                "                        \"toCallBackTaskName\": \"\"\n" +
                "                    },\n" +
                "                    \"nextTaskName\": \"confirm_stamp_d\",\n" +
                "                    \"stageInfoBoKey\": \"real_name_auth_d\",\n" +
                "                    \"taskId\": 2668899,\n" +
                "                    \"taskName\": \"sms_signer_d\",\n" +
                "                    \"taskType\": \"sms_signer_task\",\n" +
                "                    \"toCallBackTaskName\": \"\"\n" +
                "                },\n" +
                "                \"nextTaskName\": \"sms_signer_d\",\n" +
                "                \"stageInfoBoKey\": \"econtract_stamp_e\",\n" +
                "                \"taskId\": 2668908,\n" +
                "                \"taskName\": \"econtract_stamp_e\",\n" +
                "                \"taskType\": \"econtract_stamp_task\",\n" +
                "                \"toCallBackTaskName\": \"\"\n" +
                "            },\n" +
                "            \"nextTaskName\": \"econtract_stamp_e\",\n" +
                "            \"stageInfoBoKey\": \"econtract_stamp_a\",\n" +
                "            \"taskId\": 2668906,\n" +
                "            \"taskName\": \"econtract_stamp_a\",\n" +
                "            \"taskType\": \"econtract_stamp_task\",\n" +
                "            \"toCallBackTaskName\": \"\"\n" +
                "        },\n" +
                "        \"nextTaskName\": \"econtract_stamp_a\",\n" +
                "        \"stageInfoBoKey\": \"econtract_stamp_b\",\n" +
                "        \"taskId\": 2668898,\n" +
                "        \"taskName\": \"econtract_stamp_b\",\n" +
                "        \"taskState\": \"task_running\",\n" +
                "        \"taskType\": \"econtract_stamp_task\",\n" +
                "        \"toCallBackTaskName\": \"\"\n" +
                "    },\n" +
                "    \"econtractEntity\": {\n" +
                "        \"authorityMisId\": \"xuezhangang\",\n" +
                "        \"ctime\": 1556156787000,\n" +
                "        \"econtractType\": \"type_framecontract_c1_qdb_shsankuai_v2\",\n" +
                "        \"econtractUserId\": 2,\n" +
                "        \"id\": 110,\n" +
                "        \"name\": \"V4.0C1电子合同流程-钱袋宝_上海三快\",\n" +
                "        \"utime\": 1598594520000,\n" +
                "        \"valid\": 1\n" +
                "    },\n" +
                "    \"econtractRecordEntity\": {\n" +
                "        \"ctime\": 1642602255000,\n" +
                "        \"econtractId\": 110,\n" +
                "        \"econtractRecordContext\": \"\",\n" +
                "        \"econtractStage\": \"ca_certify_d\",\n" +
                "        \"econtractState\": \"state_running\",\n" +
                "        \"econtractType\": \"type_framecontract_c1_qdb_shsankuai_v2\",\n" +
                "        \"econtractUserId\": 2,\n" +
                "        \"failMessage\": \"\",\n" +
                "        \"id\": 159501,\n" +
                "        \"recordKey\": \"EC_v2_88d50573-170e-41\",\n" +
                "        \"saveUrl\": \"/download/mos/ebb526af65d27c70b93f52c5e43833e1.pdf\",\n" +
                "        \"utime\": 1642602257000,\n" +
                "        \"valid\": 1,\n" +
                "        \"version\": 11\n" +
                "    },\n" +
                "    \"econtractUserEntity\": {\n" +
                "        \"ctime\": 1509170258000,\n" +
                "        \"id\": 2,\n" +
                "        \"name\": \"waimai_contract\",\n" +
                "        \"token\": \"USR_waimai_contract_7377f61f-11af-42\",\n" +
                "        \"type\": \"type_app\",\n" +
                "        \"valid\": 1\n" +
                "    },\n" +
                "    \"executeTaskId\": 2668895,\n" +
                "    \"flowList\": [\n" +
                "        \"c1contract\"\n" +
                "    ],\n" +
                "    \"singerStep\": \"D\",\n" +
                "    \"stageBatchInfoBoList\": [\n" +
                "        {\n" +
                "            \"metaFlowList\": [\n" +
                "                \"c1contract\"\n" +
                "            ],\n" +
                "            \"paramInfoBoMap\": {},\n" +
                "            \"pdfContentInfoBoMap\": {\n" +
                "                \"c1contract\": [\n" +
                "                    {\n" +
                "                        \"ftlInfo\": \"\",\n" +
                "                        \"pdfBizContent\": [],\n" +
                "                        \"pdfMetaContent\": {\n" +
                "                            \"partBEstamp\": \"美团外卖美团签章专属\",\n" +
                "                            \"partAPhone\": \"15527157601\",\n" +
                "                            \"partSH_BJEstamp\": \"美团外卖美团签章专属\",\n" +
                "                            \"performanceServiceFeeEstamp\": \"美团上海专属签章\",\n" +
                "                            \"signerName\": \"全渠道\",\n" +
                "                            \"signerEmail\": \"\",\n" +
                "                            \"qdbNumber\": \"QDB-WMDZ-011837878\",\n" +
                "                            \"performanceServiceFeeName\": \"上海三快智送科技有限公司\",\n" +
                "                            \"legalPerson\": \"全渠道\",\n" +
                "                            \"qdbEstamp\": \"美团外卖支付签章专属\",\n" +
                "                            \"validate\": \"2022-05-30\",\n" +
                "                            \"partBPhone\": \"13002321259\",\n" +
                "                            \"partAContact\": \"全渠道\",\n" +
                "                            \"partBContact\": \"窦旭蒙\",\n" +
                "                            \"acceptPhone\": \"15527157601\",\n" +
                "                            \"contractNumber\": \"WMYE-010-01-011056803\",\n" +
                "                            \"partAEstamp\": \"美团外卖商家签章专属\",\n" +
                "                            \"signerIDCareNumber\": \"******************\",\n" +
                "                            \"partASignTime\": \"2022-01-19\",\n" +
                "                            \"partAStampName\": \"测试墩瑚\",\n" +
                "                            \"partAAddress\": \"测试地址\",\n" +
                "                            \"partA\": \"测试墩瑚\",\n" +
                "                            \"partBSignTime\": \"2022-01-19\",\n" +
                "                            \"partSH_BJStampName\": \"北京三快在线科技有限公司\",\n" +
                "                            \"signerPhone\": \"15527157601\"\n" +
                "                        },\n" +
                "                        \"pdfTemplateId\": 142,\n" +
                "                        \"pdfTemplateVersion\": 24,\n" +
                "                        \"vertical\": true\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"ftlInfo\": \"\",\n" +
                "                        \"pdfBizContent\": [],\n" +
                "                        \"pdfMetaContent\": {\n" +
                "                            \"signerEmail\": \"\",\n" +
                "                            \"partAContact\": \"全渠道\",\n" +
                "                            \"qdbNumber\": \"QDB-WMDZ-011837878\",\n" +
                "                            \"partAStampName\": \"测试墩瑚\",\n" +
                "                            \"partAAddress\": \"测试地址\",\n" +
                "                            \"partA\": \"测试墩瑚\",\n" +
                "                            \"partBSignTime\": \"2022-01-19\",\n" +
                "                            \"partAPhone\": \"15527157601\",\n" +
                "                            \"qdbEstamp\": \"美团外卖支付签章专属\",\n" +
                "                            \"partAEstamp\": \"美团外卖商家签章专属\"\n" +
                "                        },\n" +
                "                        \"pdfTemplateId\": 146,\n" +
                "                        \"pdfTemplateVersion\": 8,\n" +
                "                        \"vertical\": true\n" +
                "                    }\n" +
                "                ]\n" +
                "            },\n" +
                "            \"stageName\": \"create_pdf\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"certifyInfoBo\": {\n" +
                "                \"caType\": \"COMPANY\",\n" +
                "                \"customerName\": \"测试墩瑚\",\n" +
                "                \"email\": \"\",\n" +
                "                \"isNewStampIface\": 1,\n" +
                "                \"mobile\": \"15527157601\",\n" +
                "                \"quaNum\": \"513775168435543582\",\n" +
                "                \"signerIdentity\": \"******************\",\n" +
                "                \"signerIdentityType\": \"0\",\n" +
                "                \"signerMobile\": \"15527157601\",\n" +
                "                \"signerName\": \"全渠道\",\n" +
                "                \"signerType\": 3,\n" +
                "                \"stampKey\": \"ssq\"\n" +
                "            },\n" +
                "            \"stageName\": \"ca_certify_d\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"certifyInfoBo\": {\n" +
                "                \"caType\": \"COMPANY\",\n" +
                "                \"customerName\": \"测试墩瑚\",\n" +
                "                \"email\": \"\",\n" +
                "                \"isNewStampIface\": 0,\n" +
                "                \"mobile\": \"15527157601\",\n" +
                "                \"quaNum\": \"513775168435543582\",\n" +
                "                \"signerType\": 0\n" +
                "            },\n" +
                "            \"estampInfoBo\": {\n" +
                "                \"estampMap\": {\n" +
                "                    \"estamp_sign_key\": \"美团外卖商家签章专属\"\n" +
                "                }\n" +
                "            },\n" +
                "            \"metaFlowList\": [\n" +
                "                \"c1contract\"\n" +
                "            ],\n" +
                "            \"stageName\": \"econtract_stamp_d\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"certifyInfoBo\": {\n" +
                "                \"customerName\": \"meituan_ca\",\n" +
                "                \"isNewStampIface\": 0,\n" +
                "                \"signerType\": 0\n" +
                "            },\n" +
                "            \"stageName\": \"ca_certify_b\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"certifyInfoBo\": {\n" +
                "                \"customerName\": \"meituan_ca\",\n" +
                "                \"isNewStampIface\": 0,\n" +
                "                \"signerType\": 0,\n" +
                "                \"stampKey\": \"ssq\"\n" +
                "            },\n" +
                "            \"estampInfoBo\": {\n" +
                "                \"estampMap\": {\n" +
                "                    \"estamp_sign_key\": \"美团外卖美团签章专属\"\n" +
                "                }\n" +
                "            },\n" +
                "            \"metaFlowList\": [\n" +
                "                \"c1contract\"\n" +
                "            ],\n" +
                "            \"stageName\": \"econtract_stamp_b\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"certifyInfoBo\": {\n" +
                "                \"customerId\": \"8B3C2196CA58228E\",\n" +
                "                \"isNewStampIface\": 0,\n" +
                "                \"signerType\": 0\n" +
                "            },\n" +
                "            \"stageName\": \"ca_certify_e\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"certifyInfoBo\": {\n" +
                "                \"customerId\": \"8B3C2196CA58228E\",\n" +
                "                \"isNewStampIface\": 0,\n" +
                "                \"signerType\": 0\n" +
                "            },\n" +
                "            \"estampInfoBo\": {\n" +
                "                \"estampMap\": {\n" +
                "                    \"estamp_sign_key\": \"美团上海专属签章\"\n" +
                "                }\n" +
                "            },\n" +
                "            \"metaFlowList\": [\n" +
                "                \"c1contract\"\n" +
                "            ],\n" +
                "            \"stageName\": \"econtract_stamp_e\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"certifyInfoBo\": {\n" +
                "                \"customerName\": \"qian_dai_bao_ca\",\n" +
                "                \"isNewStampIface\": 0,\n" +
                "                \"signerType\": 0\n" +
                "            },\n" +
                "            \"stageName\": \"ca_certify_a\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"certifyInfoBo\": {\n" +
                "                \"customerName\": \"qian_dai_bao_ca\",\n" +
                "                \"isNewStampIface\": 0,\n" +
                "                \"signerType\": 0\n" +
                "            },\n" +
                "            \"estampInfoBo\": {\n" +
                "                \"estampMap\": {\n" +
                "                    \"estamp_sign_key\": \"美团外卖支付签章专属\"\n" +
                "                }\n" +
                "            },\n" +
                "            \"metaFlowList\": [\n" +
                "                \"c1contract\"\n" +
                "            ],\n" +
                "            \"stageName\": \"econtract_stamp_a\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"signerInfoBo\": {\n" +
                "                \"bankCardNo\": \"\",\n" +
                "                \"bankName\": \"\",\n" +
                "                \"certifyH5InfoBo\": {\n" +
                "                    \"certPhone\": \"155****7601\",\n" +
                "                    \"certType\": 1,\n" +
                "                    \"companyName\": \"测试墩瑚\",\n" +
                "                    \"companyNum\": \"513775168435543582\",\n" +
                "                    \"signerCardNum\": \"4****************1\",\n" +
                "                    \"signerCardType\": 1,\n" +
                "                    \"signerName\": \"全**\"\n" +
                "                },\n" +
                "                \"channelList\": [\n" +
                "                    \"SMS\",\n" +
                "                    \"wm_merchant_channel\"\n" +
                "                ],\n" +
                "                \"clientId\": \"sms-contract_kefu\",\n" +
                "                \"clientSecret\": \"AA81E263E10F5D64B7FCDAC71D11AB8D\",\n" +
                "                \"idCardNo\": \"******************\",\n" +
                "                \"mobileList\": [\n" +
                "                    \"15527157601\"\n" +
                "                ],\n" +
                "                \"name\": \"全渠道\",\n" +
                "                \"phone\": \"15527157601\",\n" +
                "                \"sendSms\": true,\n" +
                "                \"smsParamMap\": {\n" +
                "                    \"other\": \"录入完成\",\n" +
                "                    \"phone\": \"13002321259\",\n" +
                "                    \"module\": \"合同\",\n" +
                "                    \"name\": \"窦旭蒙\",\n" +
                "                    \"detail\": \"测试墩瑚\",\n" +
                "                    \"platform\": \"美团外卖\"\n" +
                "                },\n" +
                "                \"smsTemplateId\": \"20365\",\n" +
                "                \"smsTempletVersion\": 2\n" +
                "            },\n" +
                "            \"stageName\": \"real_name_auth_d\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"stageNameAndCustomerId\": {\n" +
                "        \"ca_certify_d\": \"81E6BEF71278D88A\"\n" +
                "    },\n" +
                "    \"stampKey\": \"ssq\",\n" +
                "    \"taskContext\": {\n" +
                "        \"customerId\": \"7434842FE8B5AF3A\",\n" +
                "        \"econtractStage\": \"econtract_stamp_b\",\n" +
                "        \"errorCode\": 0,\n" +
                "        \"executorResult\": {},\n" +
                "        \"executorType\": \"estampExecutor\",\n" +
                "        \"retryTimes\": 0,\n" +
                "        \"signKeyWord\": \"美团外卖美团签章专属\",\n" +
                "        \"stageInfoBo\": {\n" +
                "            \"certifyInfoBo\": {\n" +
                "                \"$ref\": \"$.stageBatchInfoBoList[4].certifyInfoBo\"\n" +
                "            },\n" +
                "            \"estampInfoBo\": {\n" +
                "                \"$ref\": \"$.stageBatchInfoBoList[4].estampInfoBo\"\n" +
                "            },\n" +
                "            \"pdfContentInfoBoList\": [],\n" +
                "            \"stageName\": \"econtract_stamp_b\"\n" +
                "        },\n" +
                "        \"state\": \"task_running\",\n" +
                "        \"taskId\": 2668898\n" +
                "    },\n" +
                "    \"taskTypeAndContractIdMap\": {\n" +
                "        \"single_task_type\": \"164260225601000002\"\n" +
                "    },\n" +
                "    \"templateProcessorType\": \"commonProcessor\",\n" +
                "    \"useMafka\": true\n" +
                "}";
        EcontractContext context = JSONObject.parseObject(contextJson, EcontractContext.class);
        AbstractExecutor abstractExecutor = new AbstractExecutor();
        SignContractAsyncResult result = new SignContractAsyncResult();
        result.setAsyncTaskId("测试asyncId");
        result.setCode(123);
        result.setMessage("测试message");
        System.out.println(JacksonUtils.serialize(context));
        System.out.println(JacksonUtils.serialize(result));
        abstractExecutor.saveAsyncTaskRel(context, result, NotifyType.SIGN_CONTRACT_ASYNC.getName());
    }
}

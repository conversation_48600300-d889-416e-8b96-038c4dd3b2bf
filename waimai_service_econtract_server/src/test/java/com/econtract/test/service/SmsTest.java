package com.econtract.test.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.util.SMSUtil;
import com.sankuai.meituan.waimai.util.domain.SmsParamVo;
import com.sankuai.meituan.waimai.util.domain.SmsResultVo;
import org.apache.http.HttpStatus;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2017/11/17
 * @time 下午7:55
 */
public class SmsTest {

    private static final String CLIENT_ID = "sms-contract_kefu";
    private static final String CLIENT_SECRET = "AA81E263E10F5D64B7FCDAC71D11AB8D";

    public static void main(String[] args) {

        SmsParamVo smsParamVo = new SmsParamVo();
        smsParamVo.setClientId(CLIENT_ID);
        smsParamVo.setClientSecret(CLIENT_SECRET);
        smsParamVo.setSmsTemplateId("14349");
        smsParamVo.setMobileList(Arrays.asList("18811598290"));
        Map<String,Object> parammap = Maps.newHashMap();
        parammap.put("shortlink","aa");
        parammap.put("bdName","aa");
        parammap.put("bdPhone","aa");
        smsParamVo.setSmsParamMap(parammap);


        SMSUtil.sendIndustrySmsByHttp(smsParamVo);

        SmsResultVo smsResultVo = SMSUtil.sendIndustrySmsByHttp(smsParamVo);
        System.out.println(JSON.toJSONString(smsResultVo));



//        Map<String,Object> smsParamMap = Maps.newHashMap();
//        smsParamMap.put("number", "number");
//        smsParamMap.put("name", "name");
//        smsParamMap.put("phone", "phone");
//        smsParamMap.put("shortlink","aa");
//        smsParamVo.setSmsParamMap(smsParamMap);
//        smsParamVo.setSmsTemplateId("6183");
//        smsParamVo.setMobileList(Arrays.asList("18811598290"));
//        SMSUtil.sendIndustrySmsByHttp(smsParamVo);
//
//        smsResultVo = SMSUtil.sendIndustrySmsByHttp(smsParamVo);
//        System.out.println(JSON.toJSONString(smsResultVo));


    }
}

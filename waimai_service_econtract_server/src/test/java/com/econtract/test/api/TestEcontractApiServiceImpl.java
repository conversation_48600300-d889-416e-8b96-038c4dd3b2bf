/*
package com.econtract.test.api;

import com.alibaba.fastjson.JSON;
import com.econtract.test.BaseSpringJunit;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractTaskEntityMapper;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractTaskEntity;

import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;

public class TestEcontractApiServiceImpl extends BaseSpringJunit {

    private static final Logger LOGGER = LoggerFactory.getLogger(TestEcontractApiServiceImpl.class);

    @Resource
    private EcontractTaskEntityMapper econtractTaskEntityMapper;

    @Test
    public void TestSelectById() throws Exception {
        EcontractTaskEntity taskEntity = econtractTaskEntityMapper.selectByPrimaryKey(1);
        System.out.println(JSON.toJSONString(taskEntity));
    }

}
*/

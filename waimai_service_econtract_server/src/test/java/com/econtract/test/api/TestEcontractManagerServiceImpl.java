/*
package com.econtract.test.api;

import com.alibaba.fastjson.JSON;
import com.econtract.test.BaseTest;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.SignPageInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.biz.PageAndListInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException;
import com.sankuai.meituan.waimai.econtrct.client.service.EcontractManagerService;

import org.apache.thrift.TException;
import org.junit.Test;

public class TestEcontractManagerServiceImpl extends BaseTest {

    @Test
    public void createPdf() throws EcontractException, TException {
        EcontractManagerService econtractBizService = ctx.getBean(EcontractManagerService.class);
        PdfContentInfoBo pdfContentInfoBo = new PdfContentInfoBo.Builder()
            .setPdfTemplateName("test")
            .setJsonInfo("{}")
            .setOptionsInfo("{'vertical':true}")
            .setFtlInfo("<div>123</div>")
            .setVertical(true)
            .build();
        String res = econtractBizService.createPdf(pdfContentInfoBo);
        System.out.println("res = " + res);
    }

    @Test
    public void savePdfTemplate() throws Exception {
        EcontractManagerService econtractBizService = ctx.getBean(EcontractManagerService.class);
        PdfContentInfoBo pdfContentInfoBo = new PdfContentInfoBo.Builder()
            .setPdfTemplateName("createPdf_test")
            .setJsonInfo("<div>123</div>")
            .setOptionsInfo("")
            .setFtlInfo("{}")
            .build();
        econtractBizService.savePdfTemplate("waimai_contract", pdfContentInfoBo);
    }

    @Test
    public void testListPdfTemplate() throws EcontractException, TException {
        EcontractManagerService econtractBizService = ctx.getBean(EcontractManagerService.class);
        PageAndListInfoBo pageAndListInfoBo = econtractBizService.listPdfTemplate("waimai_contract", 1, 20);
        System.out.println("pageAndListInfoBo = " + JSON.toJSONString(pageAndListInfoBo));
    }

    @Test
    public void testGetPdfTemplate() throws EcontractException, TException {
        EcontractManagerService econtractBizService = ctx.getBean(EcontractManagerService.class);
        PdfContentInfoBo pdfContentInfoBo = econtractBizService.getPdfTemplate("waimai_contract", "test");
        System.out.println("pdfContentInfoBo = " + JSON.toJSONString(pdfContentInfoBo));
    }

    @Test
    public void testSaveSignPageTemplate() throws EcontractException, TException {
        EcontractManagerService econtractBizService = ctx.getBean(EcontractManagerService.class);
        SignPageInfoBo signPageInfoBo = new SignPageInfoBo.Builder()
            .description("测试测试")
            .guideText("测试测试")
            .templateName("测试测试")
            .title("测试测试")
            .build();
        econtractBizService.saveSignPageTemplate("waimai_contract", signPageInfoBo);
    }

    @Test
    public void testListSignPageTemplate() throws EcontractException, TException {
        EcontractManagerService econtractBizService = ctx.getBean(EcontractManagerService.class);
        PageAndListInfoBo pageAndListInfoBo = econtractBizService.listSignPageTemplate("waimai_contract", 1, 20);
        System.out.println("pageAndListInfoBo:" + JSON.toJSONString(pageAndListInfoBo));
    }

    @Test
    public void testGetSignPageTemplate() throws EcontractException, TException {
        EcontractManagerService econtractBizService = ctx.getBean(EcontractManagerService.class);
        SignPageInfoBo signPageInfoBo = econtractBizService.getSignPageTemplate("waimai_contract", "test");
        System.out.println("signPageInfoBo:" + JSON.toJSONString(signPageInfoBo));
    }

    @Test
    public void testBindSignPageTemplate() throws EcontractException, TException {
        EcontractManagerService econtractBizService = ctx.getBean(EcontractManagerService.class);
        econtractBizService.bindSignPageTemplate("type_framecontract_c1", "c2contract_signpage");
    }
}
*/

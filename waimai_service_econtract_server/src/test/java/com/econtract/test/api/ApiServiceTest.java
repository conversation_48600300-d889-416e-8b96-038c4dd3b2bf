package com.econtract.test.api;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.econtract.test.BaseTest;
import com.sankuai.meituan.waimai.econtrct.client.constants.CertifyConstant;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractAPIResponseConstant;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.CAType;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.CertifyInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EstampInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.SignerInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.service.EcontractAPIResponse;
import com.sankuai.meituan.waimai.econtrct.client.service.EcontractAPIService;

import java.util.Arrays;
import java.util.Map;

/**
 * <AUTHOR> Hou
 * @date 2017/10/23
 * @time 上午10:50
 */
public class ApiServiceTest extends BaseTest {


    public static void main(String[] args) throws Exception {
        String recordKey;
        EcontractBo econtractBo;
        resendSms("EC_exclusive_2c95bd05-8464-40");
//        econtractBo = constructEcontractAuthEcontractBo();
//        econtractBo = constructExclusiveEcontractBo();
//          econtractBo = constructWMC3EcontractBo();

//        recordKey = applyEcontract(econtractBo);
//            System.out.println(recordKey);
//        resendSms("exclusive_1510660880");
//        callBackType("EC_act_42b40c71-05ce-4b",TaskConstant.CREATE_AUTH_COMMIT_CALL_BACK);
    }

    public static String applyEcontract(EcontractBo econtractBo){
        EcontractAPIService econtractAPIService = ctx.getBean(EcontractAPIService.class);
        return econtractAPIService.applyEcontract(econtractBo).getReturnData().get(EcontractAPIResponseConstant.UUID);
    }

    public static EcontractBo constructEcontractAuthEcontractBo() {

        //实名认证模块
        Map<String, String> smsParamMap = Maps.newHashMap();
        smsParamMap.put("number", "number");
        smsParamMap.put("name", "name");
        smsParamMap.put("phone", "phone");
        smsParamMap.put("poi1", "poi1");
        smsParamMap.put("poi2", "poi2");
        smsParamMap.put("contractId", "contractId");


        SignerInfoBo signerInfoBo = new SignerInfoBo.Builder()
                .setName("侯璐")
                .setIdCardNo("610582199106210513")
                .setPhone("***********")
                .setBankName("中国银行")
                .setBankCardNo("6216615300013001992")
                .setClientId("sms-contract_kefu")
                .setClientSecret("AA81E263E10F5D64B7FCDAC71D11AB8D")
                .setSmsTemplateId("6183")
                .setSmsParamMap(smsParamMap)
                .setMobileList(Arrays.asList("***********"))
                .build();

        StageInfoBo realNameStage = new StageInfoBo.Builder()
                .setSignerInfoBo(signerInfoBo)
                .setStageName(TaskConstant.REAL_NAME_AUTH_A)
                .build();


        //新建一份合同参数
        EcontractBo econtractBo = new EcontractBo.Builder().setToken("USR_waimai_contract_7377f61f-11af-42")
                .setEcontractType(TaskConstant.TYPE_WM_AUTH_ACT)
                .setStageInfoBoList(Lists.newArrayList(realNameStage))
                .setEcontractName("ceshi001")
                .setCallBackUrl("http://econtract.waimai.dev.sankuai.com/econtract/restful/api/v1/call_back/test/notifier_call_back")
                .build();

        return econtractBo;
    }

    public static EcontractBo constructExclusiveEcontractBo() {

        //合同内容模块
        Map<String, String> metaMap = Maps.newHashMap();
        metaMap.put("testVal", "testVal");
        PdfContentInfoBo pdfContentInfoBo = new PdfContentInfoBo.Builder()
                .setPdfMetaContent(metaMap)
                .setPdfTemplateName("test-contract.ftl")
                .build();
        StageInfoBo createStage = new StageInfoBo.Builder()
                .setStageName(TaskConstant.CREATE_PDF)
                .setPdfContentInfoBoList((Lists.newArrayList(pdfContentInfoBo)))
                .build();


        //实名认证模块
        Map<String, String> smsParamMap = Maps.newHashMap();
        smsParamMap.put("number", "number");
        smsParamMap.put("name", "name");
        smsParamMap.put("phone", "phone");

        SignerInfoBo signerInfoBo = new SignerInfoBo.Builder()
                .setName("侯璐")
                .setIdCardNo("610582199106210513")
                .setPhone("***********")
                .setBankName("中国银行")
                .setBankCardNo("6216615300013001992")
                .setClientId("sms-contract_kefu")
                .setClientSecret("AA81E263E10F5D64B7FCDAC71D11AB8D")
                .setSmsTemplateId("6183")
                .setSmsParamMap(smsParamMap)
                .setMobileList(Arrays.asList("***********"))
                .build();
        StageInfoBo realNameStage = new StageInfoBo.Builder()
                .setSignerInfoBo(signerInfoBo)
                .setStageName(TaskConstant.REAL_NAME_AUTH_A)
                .build();

        //CA认证模块
        CertifyInfoBo certifyInfoBo = new CertifyInfoBo.Builder()
                .setCustomerName("侯璐")
                .setEmail("<EMAIL>")
                .setMobile("***********")
                .setQuaNum("610582199106210513")
                .setCaType(CAType.PERSON)
                .build();
        StageInfoBo caStage = new StageInfoBo.Builder()
                .setStageName(TaskConstant.CA_CERTIFY_PART_A)
                .setCertifyInfoBo(certifyInfoBo)
                .build();

        //新建一份合同参数
        EcontractBo econtractBo = new EcontractBo.Builder().setToken("USR_waimai_contract_7377f61f-11af-42")
                .setEcontractType(TaskConstant.TYPE_WM_EXCLUSIVE_ACT)
                .setStageInfoBoList(Lists.newArrayList(createStage, realNameStage, caStage))
                .setEcontractName("ceshi001")
                .setCallBackUrl("http://econtract.waimai.dev.sankuai.com/econtract/restful/api/v1/call_back/test/notifier_call_back")
                .build();

        return econtractBo;
    }


    public static EcontractBo constructWMC3EcontractBo() {

        //合同内容模块
        Map<String, String> metaMap = Maps.newHashMap();
        metaMap.put("testVal", "testVal");
        PdfContentInfoBo pdfContentInfoBo = new PdfContentInfoBo.Builder()
                .setPdfMetaContent(metaMap)
                .setPdfTemplateName("test-contract.ftl")
                .build();
        StageInfoBo createStage = new StageInfoBo.Builder()
                .setStageName(TaskConstant.CREATE_PDF)
                .setPdfContentInfoBoList((Lists.newArrayList(pdfContentInfoBo)))
                .build();

        //CA认证模块
        CertifyInfoBo certifyInfoABo = new CertifyInfoBo.Builder()
                .setCustomerName(CertifyConstant.MEI_TUAN_CA)
                .setCaType(CAType.PERSON)
                .build();
        StageInfoBo caAStage = new StageInfoBo.Builder()
                .setStageName(TaskConstant.CA_CERTIFY_PART_A)
                .setCertifyInfoBo(certifyInfoABo)
                .build();

        //实名认证模块
        Map<String, String> smsParamMap = Maps.newHashMap();
        smsParamMap.put("wmC3ContractNum", "xxx合同");
        smsParamMap.put("name", "name");
        smsParamMap.put("phone", "phone");

        SignerInfoBo signerInfoBo = new SignerInfoBo.Builder()
                .setName("侯璐")
                .setIdCardNo("610582199106210513")
                .setPhone("***********")
                .setBankName("中国银行")
                .setBankCardNo("6216615300013001992")
                .setClientId("sms-contract_kefu")
                .setClientSecret("AA81E263E10F5D64B7FCDAC71D11AB8D")
                .setSmsTemplateId("6183")
                .setSmsParamMap(smsParamMap)
                .setMobileList(Arrays.asList("***********"))
                .build();
        StageInfoBo realNameStage = new StageInfoBo.Builder()
                .setSignerInfoBo(signerInfoBo)
                .setStageName(TaskConstant.REAL_NAME_AUTH_B)
                .build();

        //CA认证模块
        CertifyInfoBo certifyInfoBBo = new CertifyInfoBo.Builder()
                .setCustomerName("侯璐")
                .setEmail("<EMAIL>")
                .setMobile("***********")
                .setQuaNum("610582199106210513")
                .setCaType(CAType.PERSON)
                .build();
        StageInfoBo caBStage = new StageInfoBo.Builder()
                .setStageName(TaskConstant.CA_CERTIFY_PART_B)
                .setCertifyInfoBo(certifyInfoBBo)
                .build();

        //ESTAMP模块
        Map<String, String> estampParamPam = Maps.newHashMap();
        estampParamPam.put(TaskConstant.PDF_ESTAMP_SIGN_KEY, PdfConstant.WM_EXCLUSIVE_SIGNKEY);
        EstampInfoBo estampInfoBo = new EstampInfoBo.Builder()
                .setEstampMap(estampParamPam)
                .build();
        StageInfoBo estampBStage = new StageInfoBo.Builder()
                .setStageName(TaskConstant.ECONTRACT_STAMP_B)
                .setEstampInfoBo(estampInfoBo)
                .build();

        //新建一份合同参数
        EcontractBo econtractBo = new EcontractBo.Builder().setToken("USR_waimai_contract_7377f61f-11af-42")
                .setEcontractType(TaskConstant.TYPE_WM_C3)
                .setStageInfoBoList(Lists.newArrayList(createStage, realNameStage, caAStage,caBStage,estampBStage))
                .setEcontractName("ceshi001")
                .setCallBackUrl("http://econtract.waimai.dev.sankuai.com/econtract/restful/api/v1/call_back/test/notifier_call_back")
                .build();

        return econtractBo;
    }

    public static void callBackType(String key,String callBackType) {
        EcontractAPIService econtractAPIService = ctx.getBean(EcontractAPIService.class);
        econtractAPIService.callBackEcontractByRecordKey(key,callBackType, null);
    }

    public static void resendSms(String key) {
        EcontractAPIService econtractAPIService = ctx.getBean(EcontractAPIService.class);
        EcontractAPIResponse response = econtractAPIService.doRetrySms(key,"USR_waimai_contract_7377f61f-11af-42");
        System.out.println(response.getCode());
        System.out.println(response.getMsg());

    }

}

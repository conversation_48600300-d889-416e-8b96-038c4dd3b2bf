/*
package com.econtract.test.api;

import com.alibaba.fastjson.JSON;
import com.econtract.test.BaseTest;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.SignPageInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException;
import com.sankuai.meituan.waimai.econtrct.client.service.EcontractBizService;

import org.apache.thrift.TException;
import org.junit.Test;

public class TestEcontractBizServiceImpl extends BaseTest {

    @Test
    public void testQuerySignPageInfoBySecretParam() throws EcontractException, TException {
        EcontractBizService econtractBizService = ctx.getBean(EcontractBizService.class);
        SignPageInfoBo signPageInfoBo = econtractBizService.querySignPageInfoBySecretParam("9A32D65F7C88EB2CE07F6E8AD7609FDC3F963213FD9896F72CFEFDB4CABFFAD9");
        System.out.println("signPageInfoBo:" + JSON.toJSONString(signPageInfoBo));
    }

    @Test
    public void testQueryRecordKeyBySecretParam() throws EcontractException, TException {
        EcontractBizService econtractBizService = ctx.getBean(EcontractBizService.class);
        String recordKey = econtractBizService.queryRecordKeyBySecretParam("9A32D65F7C88EB2CE07F6E8AD7609FDC3F963213FD9896F72CFEFDB4CABFFAD9");
        System.out.println("recordKey:" + recordKey);
    }

}
*/

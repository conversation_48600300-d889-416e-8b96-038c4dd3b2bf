package com.econtract.test.uitl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2017/11/29
 * @time 下午4:11
 */
public class DateTestUtil {

    public static void main(String[] args) throws ParseException {

//        System.out.println(new SimpleDateFormat("yyyy年MM月dd日 HH时mm分ss秒").format(new Date(1505923200*1000l)));
//        System.out.println(System.currentTimeMillis()/1000);
        long data = new Long(new SimpleDateFormat("yyyy-MM-dd").parse("2018-01-22").getTime())/1000;
        int old = 1516550300;
        if(data>old){
            System.out.println("true");
        }else {
            System.out.println("false");

        }
    }
}

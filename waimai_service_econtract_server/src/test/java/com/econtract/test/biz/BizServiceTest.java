package com.econtract.test.biz;

import com.alibaba.fastjson.JSON;
import com.econtract.test.BaseTest;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.constants.EcontractRecordConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.biz.EcontractRecordBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.biz.EcontractRecordWebQueryBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.biz.PageInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.service.EcontractBizService;
import com.sankuai.meituan.waimai.econtrct.client.service.EcontractManagerService;
import org.springframework.dao.DataAccessException;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> <PERSON>
 * @date 2017/10/23
 * @time 上午10:50
 */
public class BizServiceTest extends BaseTest {


    public static void main(String[] args) throws Exception {
        queryRecordPage();
    }

    public static void queryRecordPage(){
        EcontractManagerService econtractBizService = ctx.getBean(EcontractManagerService.class);
        EcontractRecordWebQueryBo econtractQueryBo = new EcontractRecordWebQueryBo();
        econtractQueryBo.setRecordKey("");
        econtractQueryBo.setEcontractUserName("");
        econtractQueryBo.setRecordBizKey("");
        econtractQueryBo.setEcontractType("");
        econtractQueryBo.setEcontractStage("");
        econtractQueryBo.setEcontractState("");
        econtractQueryBo.setPageNum(1);
        econtractQueryBo.setStartTime(0l);
        econtractQueryBo.setEndTime(0l);
        PageInfoBo pageInfoBo = econtractBizService.queryEcontractRecordPageInfo(econtractQueryBo);
        List<EcontractRecordBo> recordBoList = econtractBizService.queryEcontractRecordByPage(econtractQueryBo);
        System.out.println(JSON.toJSONString(econtractQueryBo));

        for(EcontractRecordBo econtractRecordBo:recordBoList){
            System.out.println(econtractRecordBo.getRecordKey());
            System.out.println(econtractRecordBo.getSaveUrl());

        }
    }


}

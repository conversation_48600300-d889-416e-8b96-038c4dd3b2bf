package com.econtract.test.executor;

import com.econtract.test.BaseTest;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordEntity;
import com.sankuai.meituan.waimai.econtract.server.exception.EcontractException;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.Impl.UploadExecutor;

/**
 * <AUTHOR>
 * @date 2017/10/24
 * @time 下午12:08
 */
public class UploadExecutorTest extends BaseTest {


    public static void main(String[] args) throws EcontractException {
        UploadExecutor uploadExecutor = ctx.getBean(UploadExecutor.class);
        EcontractRecordEntity recordEntity = new EcontractRecordEntity();
        recordEntity.setSaveUrl("/download/mos/7da020d88eda3a29b3a0624927ea111f.pdf");
        recordEntity.setRecordKey("ceshikey");

        EcontractContext econtractContext = new EcontractContext();
        econtractContext.setEcontractRecordEntity(recordEntity);
        uploadExecutor.executeTask(econtractContext);
    }

}

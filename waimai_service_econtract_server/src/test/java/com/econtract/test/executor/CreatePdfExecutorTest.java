/*
package com.econtract.test.executor;

import com.google.common.collect.Lists;

import com.econtract.test.BaseTest;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractFtlTemplateEntity;
import com.sankuai.meituan.waimai.econtract.server.service.biz.Impl.EcontractFtlTemplateServiceImpl;
import com.sankuai.meituan.waimai.econtract.server.utils.ImageUtil;
import com.sankuai.meituan.waimai.econtrct.client.util.MtCloudS3Util;

import org.junit.Test;

import java.io.IOException;
import java.util.Map;

public class CreatePdfExecutorTest extends BaseTest {

    @Test
    public void testCreatePdf() throws IOException {
        EcontractFtlTemplateServiceImpl econtractFtlTemplateService = ctx.getBean(EcontractFtlTemplateServiceImpl.class);
        Map<String, EcontractFtlTemplateEntity> ftlTemplateMap = econtractFtlTemplateService.batchSelectMapByNameList(
            Lists.newArrayList("test"));
        String ftl = ftlTemplateMap.get("test").getFtlTemplate();
        MtCloudS3Util.uploadFileFromBytes(ftl.getBytes(), "ftl_test");

        byte[] bytes = ImageUtil.getCloudPicBytes("ftl_test");
        System.out.println("length:" + bytes.length);
        System.out.println("bytes:" + new String(bytes));
    }

}
*/

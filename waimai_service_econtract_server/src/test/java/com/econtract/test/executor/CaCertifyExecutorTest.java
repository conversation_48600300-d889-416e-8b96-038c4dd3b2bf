package com.econtract.test.executor;

import com.econtract.test.BaseTest;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.bo.task.TaskContext;
import com.sankuai.meituan.waimai.econtract.server.exception.EcontractException;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.Impl.CaCertifyExecutor;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.CAType;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.CertifyInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageInfoBo;

/**
 * <AUTHOR>
 * @date 2017/10/24
 * @time 下午12:08
 */
public class CaCertifyExecutorTest extends BaseTest {


    public static void main(String[] args) throws EcontractException {
        CaCertifyExecutor caCertifyTaskExecutor = ctx.getBean(CaCertifyExecutor.class);

        CertifyInfoBo certifyInfoBo = new CertifyInfoBo();
        certifyInfoBo.setCaType(CAType.PERSON);
        certifyInfoBo.setQuaNum("610582199106210513");
        certifyInfoBo.setMobile("15810116872");
        certifyInfoBo.setEmail("<EMAIL>");
        certifyInfoBo.setCustomerName("houlu");
        EcontractContext econtractContext = new EcontractContext();
        TaskContext taskContext = new TaskContext();
        StageInfoBo stageInfoBo = new StageInfoBo();
        stageInfoBo.setCertifyInfoBo(certifyInfoBo);
        taskContext.setStageInfoBo(stageInfoBo);
        econtractContext.setTaskContext(taskContext);
        caCertifyTaskExecutor.executeTask(econtractContext);
    }

}

package com.econtract.test.executor;

import com.alibaba.fastjson.JSON;
import com.econtract.test.BaseTest;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.bo.task.TaskContext;
import com.sankuai.meituan.waimai.econtract.server.exception.EcontractException;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.Impl.CreatePdfExecutor;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageInfoBo;

import java.util.Map;

/**
 * <AUTHOR> <PERSON>
 * @date 2017/10/24
 * @time 下午12:08
 */
public class PdfExecutorTest extends BaseTest {


    public static void main(String[] args) throws EcontractException {
        CreatePdfExecutor createPdfExecutor = ctx.getBean(CreatePdfExecutor.class);
        StageInfoBo stageInfoBo = new StageInfoBo();
        PdfContentInfoBo pdfContentInfoBo = new PdfContentInfoBo();
        Map<String,String> metaContent = Maps.newHashMap();
        metaContent.put("testVal","testVal");
        pdfContentInfoBo.setPdfMetaContent(metaContent);
        stageInfoBo.setPdfContentInfoBoList(Lists.newArrayList(pdfContentInfoBo));

        TaskContext taskContext = new TaskContext();
        taskContext.setStageInfoBo(stageInfoBo);
        taskContext.setExecutorResult(Maps.newHashMap());

        EcontractContext econtractContext = new EcontractContext();
        econtractContext.setTaskContext(taskContext);
        createPdfExecutor.executeTask(econtractContext);
        System.out.println(JSON.toJSONString(taskContext.getExecutorResult()));
    }

}

package com.econtract.test;

import com.sankuai.meituan.config.configuration.MccConfiguration;
import com.sankuai.meituan.config.configuration.PropertiesConfiguration;
import com.sankuai.meituan.config.configuration.SystemConfiguration;
import com.sankuai.meituan.util.ConfigUtilAdapter;

import org.junit.BeforeClass;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.IOException;

//@RunWith(SpringJUnit4ClassRunner.class)
//@ContextConfiguration(locations = "classpath:spring/application-context.xml")
public class BaseSpringJunit {

    private static final Logger LOGGER = LoggerFactory.getLogger(BaseSpringJunit.class);

//    @BeforeClass
//    public static void before() throws IOException {
//        System.setProperty("workdir", "target");
//        ConfigUtilAdapter.addConfiguration(new MccConfiguration("com.sankuai.waimai.m.econtractserver", "", ""));
//        ConfigUtilAdapter.addConfiguration(new SystemConfiguration());
//        ConfigUtilAdapter.addConfiguration(new PropertiesConfiguration(System.getProperty("workdir") + "/classes/conf"));
//        ConfigUtilAdapter.init();
//    }

}

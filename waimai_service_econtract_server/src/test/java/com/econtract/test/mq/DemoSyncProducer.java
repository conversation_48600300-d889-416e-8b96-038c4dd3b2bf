package com.econtract.test.mq;

import com.alibaba.fastjson.JSON;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import java.util.Properties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 说明：以下相关参数请到mq管理平台的主题或者消费组页面查看，在业务接入标签下有相关demo，请设置成demo展示的参数，否则会报错
 * 说明：以下相关参数请到mq管理平台的主题或者消费组页面查看，在业务接入标签下有相关demo，请设置成demo展示的参数，否则会报错
 * 说明：以下相关参数请到mq管理平台的主题或者消费组页面查看，在业务接入标签下有相关demo，请设置成demo展示的参数，否则会报错
 * */
public class DemoSyncProducer {
    private final static Logger logger = LoggerFactory.getLogger(DemoSyncProducer.class);

    /**
     * producer实例请在业务初始化的时候创建好.
     * producer资源创建好后，再开放业务流量.
     * 请不要频繁创建producer实例：即不要发送一条消息都创建一次producer。一是性能不好，二是服务端会限制连接次数影响消息发送。发送完毕后，请进行close。
     * 注意：服务端对单ip创建相同主题的生产者实例数有限制，超过100个拒绝创建，如有特殊需求请联系mq同学进行白名单设置.
     * */
    private static IProducerProcessor producer;

    // 注意：执行main函数若抛出ERROR级别异常，请务必进行观察处理
    public static void main(String[] args) throws Exception {

        Properties properties = new Properties();
        // 设置业务BG的namespace；namespace从管理平台demo示例查看得到
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, "waimai");
        // 设置业务的appkey；appkey从管理平台申请得到
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, "waimai");

        // 创建topic对应的producer对象（注意每次build会产生一个新的实例）；topic从管理平台申请得到
        // 请注意：若调用MafkaClient.buildProduceFactory()创建实例抛出有异常，请重点关注并排查异常原因，不可频繁调用该方法给服务端带来压力。
        producer = MafkaClient.buildProduceFactory(properties, "waimai.econtract.task.monitor.execute.topic");
        try {
            for (int i = 0; i < 10; ++ i) {
                try {
                    // TODO:业务侧的发送消息逻辑代码
                    // 同步发送，注意：producer只实例化一次，不要每次调用sendMessage方法前都创建producer实例
                    ProducerResult result = producer.sendMessage(JSON.toJSON("sync demo message " + i));
                    System.out.println("send " + i + " status: " + result.getProducerStatus());
                } catch (Exception e) {
                    logger.info("send msg exception.", e);
                }
            }
        } finally {
           // producer.close();
        }
    }
}


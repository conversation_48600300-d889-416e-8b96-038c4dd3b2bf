package com.econtract.test.mq;

import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import com.meituan.mafka.client.consumer.IMessageListener;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import java.util.Properties;
 
public class DemoConsumer {

    /**
    * 注意：服务端对单ip创建相同主题相同队列的消费者实例数有限制，超过100个拒绝创建，如有特殊需求请联系mq同学进行白名单设置.
    * */
    private static IConsumerProcessor consumer;

    public static void main(String[] args) throws Exception {
        Properties properties = new Properties();
        // 设置业务所在BG的namespace，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, "waimai");
        // 设置消费者appkey，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, "waimai");
        // 设置订阅组group，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.SubscribeGroup, "waimai.econtract.task.monitor.execute");
 
        // 创建topic对应的consumer对象（注意每次build调用会产生一个新的实例），此处配topic名字，请按照demo正确配置
        consumer = MafkaClient.buildConsumerFactory(properties, "waimai.econtract.task.monitor.execute.topic");
 
        // 调用recvMessageWithParallel设置listener
        // 注意1：可以修改String.class以支持自定义数据类型
        // 注意2：针对同一个consumer对象，只能调用一次该方法；多次调用的话，后面的调用都会报异常
        System.out.println("DDDD");
        consumer.recvMessageWithParallel(String.class, new IMessageListener() {
            @Override
            public ConsumeStatus recvMessage(MafkaMessage message, MessagetContext context) {
                //TODO:业务侧的消费逻辑代码
                try {
                    System.out.println("message=[" + message.getBody().toString() + "]  partition=" + message.getParttion());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return ConsumeStatus.CONSUME_SUCCESS;
            }
        });
        System.out.println("bbb");

       // consumer.close();
 
        //如上简单demo可以达到消费者一直处于消费状态,业务可以主动调用consumer.close()来关闭consumer,如消费100个消息主动关闭
    }
}
/*
package com.econtract.test.mq;

import com.alibaba.fastjson.JSON;
import com.econtract.test.BaseTest;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.bo.task.TaskContext;
import com.sankuai.meituan.waimai.econtract.server.service.mq.TaskMsg;
import com.sankuai.meituan.waimai.econtract.server.service.mq.TaskProducer;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.CAType;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.CertifyInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageInfoBo;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;

*/
/**
 * mq数据测试
 *
 * <AUTHOR>
 * @date 2018/05/28
 *//*

public class TaskProducerTest extends BaseTest {




    */
/**
     * kafka入队列
     *//*

    @Test
    public void testKafkaProduceTask() {

        TaskProducer  taskProducer= ctx.getBean(TaskProducer.class);

        TaskMsg taskMsg=new TaskMsg();
        taskMsg.setMessageType("msg_execute");
        taskMsg.setEcontractRecordKey("EC_c1_2749c8c4-8597-48");

        CertifyInfoBo certifyInfoBo = new CertifyInfoBo();
        certifyInfoBo.setCaType(CAType.PERSON);
        certifyInfoBo.setQuaNum("610582199106210513");
        certifyInfoBo.setMobile("15810116872");
        certifyInfoBo.setEmail("<EMAIL>");
        certifyInfoBo.setCustomerName("houlu");
        EcontractContext econtractContext = new EcontractContext();
        TaskContext taskContext = new TaskContext();
        StageInfoBo stageInfoBo = new StageInfoBo();
        stageInfoBo.setCertifyInfoBo(certifyInfoBo);
        taskContext.setStageInfoBo(stageInfoBo);
        econtractContext.setTaskContext(taskContext);

        taskMsg.setMessageBody(JSON.toJSONString(econtractContext));

        taskProducer.kafkaProduceTask(taskMsg);

        System.out.println("success");
        System.out.println("testKafkaProduceTask");
    }
}
*/

package com.econtract.test.dao;

import com.econtract.test.BaseTest;
import com.sankuai.meituan.waimai.econtract.server.constants.EcontractUserConstant;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractEntityMapper;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractRealNameAuthEntityMapper;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractUserEntityMapper;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRealNameAuthEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractUserEntity;
import com.sankuai.meituan.waimai.econtract.server.service.api.EcontractBizServiceImpl;
import com.sankuai.meituan.waimai.econtract.server.utils.TimeUtils;
import com.sankuai.meituan.waimai.econtract.server.utils.UserTokenUtils;
import com.sankuai.meituan.waimai.econtrct.client.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtrct.client.service.EcontractBizService;

import java.util.Date;

/**
 * <AUTHOR> Hou
 * @date 2017/10/20
 * @time 下午12:57
 */
public class EcontractDaoTest extends BaseTest{


    public static void main(String[] args) {
//        insertUser();
//        insertEcontract();
        queryRealName("A1CC721FDF53A246D1E4C130B6429C89");

    }

    public static void insertUser(){
        EcontractUserEntity userEntity = new EcontractUserEntity();
        userEntity.setName("waimai_contract");
        userEntity.setToken(UserTokenUtils.getUserToken(userEntity.getName()));
        userEntity.setType(EcontractUserConstant.TYPE_APP);
        userEntity.setValid(EcontractUserConstant.VALID);
        userEntity.setCtime(TimeUtils.getCTime());
        EcontractUserEntityMapper userEntityMapper = ctx.getBean(EcontractUserEntityMapper.class);
        userEntityMapper.insertSelective(userEntity);
    }

    public static void insertEcontract(){
        EcontractEntity entity = new EcontractEntity();
        entity.setName("外卖独家合同");
        entity.setEcontractType(TaskConstant.ECONTRACT_TYPE_WM_EXCLUSIVE);
        entity.setEcontractUserId(2);
        entity.setCtime(new Date());
        EcontractEntityMapper econtractEntityMapper = ctx.getBean(EcontractEntityMapper.class);
        econtractEntityMapper.insertSelective(entity);
    }

    public static void queryRealName(String md5){
        EcontractRealNameAuthEntityMapper realNameAuthEntityMapper = ctx.getBean(EcontractRealNameAuthEntityMapper.class);
        EcontractRealNameAuthEntity realNameAuthEntity = realNameAuthEntityMapper.queryRealNameAuthEntityByMd5(md5);
        System.out.println(realNameAuthEntity);
    }

    public static void testDecript(){
        EcontractBizService econtractBizService = ctx.getBean(EcontractBizServiceImpl.class);
        System.out.println(econtractBizService.getCertPhone("3BD5AF0BB214B2267A1EE915E0190255336EF0A233265D866CBFAF589E53788D"));

    }
}

/*
package com.econtract.test.dao;

import com.alibaba.fastjson.JSON;
import com.econtract.test.BaseTest;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractUserRelMapper;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractUserRelEntity;

import org.junit.Test;

public class EcontractUserRelDaoTest extends BaseTest {

    @Test
    public void selectByUid(){
        EcontractUserRelMapper userRelMapper = ctx.getBean(EcontractUserRelMapper.class);
        EcontractUserRelEntity userRelEntity = userRelMapper.selectByUid(40415);
        System.out.println(JSON.toJSONString(userRelEntity));
    }

}
*/

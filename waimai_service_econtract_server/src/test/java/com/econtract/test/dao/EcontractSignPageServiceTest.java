/*
package com.econtract.test.dao;

import com.alibaba.fastjson.JSON;
import com.econtract.test.BaseTest;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractSignPageTemplateMapper;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractSignPageTemplateEntity;

import org.junit.Test;

import java.util.List;

public class EcontractSignPageServiceTest extends BaseTest {

    @Test
    public void selectByUid(){
        EcontractSignPageTemplateMapper userRelMapper = ctx.getBean(EcontractSignPageTemplateMapper.class);
//        List<EcontractSignPageTemplateEntity> list = userRelMapper.listSignPage(1, 1, 20);
//        System.out.println("list:" + JSON.toJSONString(list));
    }

}
*/

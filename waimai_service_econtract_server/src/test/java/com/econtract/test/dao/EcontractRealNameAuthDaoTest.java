/*
package com.econtract.test.dao;

import com.econtract.test.BaseTest;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractRealNameAuthEntityMapper;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRealNameAuthEntity;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

*/
/**
 * <AUTHOR>
 * @date 2017/10/20
 * @time 下午3:02
 *//*

public class EcontractRealNameAuthDaoTest extends BaseTest{

    @Autowired
    private EcontractRealNameAuthEntityMapper econtractRealNameAuthEntityMapper;

    @Test
    public void insert(){
        EcontractRealNameAuthEntity econtractRealNameAuthEntity = new EcontractRealNameAuthEntity();
        econtractRealNameAuthEntity.setName("ceshi");
        econtractRealNameAuthEntity.setPhone("***********");
        econtractRealNameAuthEntity.setBankName("asadsd");
        econtractRealNameAuthEntityMapper.insertSelective(econtractRealNameAuthEntity);
    }


}
*/

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy;
import com.sankuai.meituan.waimai.econtrct.client.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.*;
import com.sankuai.meituan.waimai.econtrct.client.service.EcontractAPIResponse;
import com.sankuai.meituan.waimai.econtrct.client.service.EcontractAPIService;

import java.util.Arrays;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2017/11/10
 * @time 下午5:41
 */
public class ProxyTest {

    public static void main(String[] args) throws Exception {
        applyPdf();
//        retrySms();
    }

    public static void applyPdf() throws Exception {

        //合同内容模块
        Map<String, String> metaMap = Maps.newHashMap();
        metaMap.put("testVal", "testVal");
        PdfContentInfoBo pdfContentInfoBo = new PdfContentInfoBo.Builder()
                .setPdfMetaContent(metaMap)
                .setPdfTemplateName("test-contract.ftl")
                .build();
        StageInfoBo createStage = new StageInfoBo.Builder()
                .setStageName(TaskConstant.CREATE_PDF)
                .setPdfContentInfoBoList((Lists.newArrayList(pdfContentInfoBo)))
                .build();


        //实名认证模块
        Map<String, String> smsParamMap = Maps.newHashMap();
        smsParamMap.put("number", "number");
        smsParamMap.put("name", "name");
        smsParamMap.put("phone", "phone");

        SignerInfoBo signerInfoBo = new SignerInfoBo.Builder()
                .setName("侯璐")
                .setIdCardNo("610582199106210513")
                .setPhone("***********")
                .setBankName("中国银行")
                .setBankCardNo("6216615300013001992")
                .setClientId("sms-contract_kefu")
                .setClientSecret("AA81E263E10F5D64B7FCDAC71D11AB8D")
                .setSmsTemplateId("6183")
                .setSmsParamMap(smsParamMap)
                .setMobileList(Arrays.asList("***********"))
                .build();
        StageInfoBo realNameStage = new StageInfoBo.Builder()
                .setSignerInfoBo(signerInfoBo)
                .setStageName(TaskConstant.REAL_NAME_AUTH_A)
                .build();

        //CA认证模块
        CertifyInfoBo certifyInfoBo = new CertifyInfoBo.Builder()
                .setCustomerName("侯璐")
                .setEmail("<EMAIL>")
                .setMobile("***********")
                .setQuaNum("610582199106210513")
                .setCaType(CAType.PERSON)
                .build();
        StageInfoBo caStage = new StageInfoBo.Builder()
                .setStageName(TaskConstant.CA_CERTIFY_PART_A)
                .setCertifyInfoBo(certifyInfoBo)
                .build();

        //新建一份合同参数
        EcontractBo econtractBo = new EcontractBo.Builder().setToken("USR_waimai_contract_7377f61f-11af-42")
                .setEcontractType(TaskConstant.ECONTRACT_TYPE_WM_EXCLUSIVE)
                .setStageInfoBoList(Lists.newArrayList(createStage, realNameStage, caStage))
                .setCallBackUrl("http://econtract.waimai.dev.sankuai.com/econtract/restful/api/v1/call_back/test/notifier_call_back")
                .setEcontractName("testEcontract")
                .build();

        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setAppKey("demo");
        proxy.setRemoteAppkey("com.sankuai.waimai.m.econtractserver");
        proxy.setServiceInterface(EcontractAPIService.class);
        proxy.setRemoteServerPort(9001);
        proxy.afterPropertiesSet();
        EcontractAPIService apiService = (EcontractAPIService) proxy.getObject();
        EcontractAPIResponse response = apiService.applyEcontract(econtractBo);
        System.out.println(response);
    }

    public static void retrySms() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setAppKey("demo");
        proxy.setRemoteAppkey("com.sankuai.waimai.m.econtractserver");
        proxy.setServiceInterface(EcontractAPIService.class);
        proxy.setRemoteServerPort(9001);
        proxy.afterPropertiesSet();
        EcontractAPIService apiService = (EcontractAPIService) proxy.getObject();
        EcontractAPIResponse response = apiService.doRetrySms("exclusive_1510651836","waimai_contract-1509170258098");
        System.out.println(response);
    }
}

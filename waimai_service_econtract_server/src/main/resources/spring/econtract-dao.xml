<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:tx="http://www.springframework.org/schema/tx" xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
		http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd"
       default-autowire="byName">

    <import resource="classpath:spring/datasources.xml"/>

    <!-- 激活自动代理功能 -->
    <aop:aspectj-autoproxy />
    <tx:annotation-driven transaction-manager="econtractTxManager" proxy-target-class="true"/>


    <bean id="econtractTxManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="dbEContractDataSource"/>
    </bean>


    <bean id="econtractSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="dbEContractDataSource"/>
        <property name="configLocation" value="classpath:/econtract-mybatis/econtract-mybatisConfig.xml"/>
        <property name="mapperLocations" value="classpath*:/econtract-mybatis/sqlmap/*.xml"/>
    </bean>

    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.sankuai.meituan.waimai.econtract.server.dao,com.sankuai.meituan.waimai.econtract.server.template.config.dao"/>
        <property name="sqlSessionFactoryBeanName" value="econtractSqlSessionFactory"/>
    </bean>


</beans>
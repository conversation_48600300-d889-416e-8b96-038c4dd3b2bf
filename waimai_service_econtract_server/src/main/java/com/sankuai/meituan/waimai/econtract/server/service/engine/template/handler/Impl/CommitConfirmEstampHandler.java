package com.sankuai.meituan.waimai.econtract.server.service.engine.template.handler.Impl;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.bo.task.TaskContext;
import com.sankuai.meituan.waimai.econtract.server.bo.task.TaskNodeBo;
import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import com.sankuai.meituan.waimai.econtract.server.constants.ContextConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.EcontractRecordConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractTaskEntity;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractRecordService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractTaskService;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.TaskManager;
import com.sankuai.meituan.waimai.econtract.server.service.engine.template.handler.TaskHandler;
import com.sankuai.meituan.waimai.econtract.server.service.engine.template.processor.Impl.AbstractProcessor;
import com.sankuai.meituan.waimai.econtract.server.service.mq.TaskMsg;
import com.sankuai.meituan.waimai.econtract.server.utils.TimeUtils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR> Hou
 * @date 2017/11/26
 * @time 下午1:23
 */
@Service
public class CommitConfirmEstampHandler extends AbstractProcessor implements TaskHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(CommitConfirmEstampHandler.class);

    @Autowired
    private EcontractRecordService econtractRecordService;
    @Autowired
    private EcontractTaskService econtractTaskService;
    @Autowired
    private TaskManager taskManager;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleTask(EcontractContext context) {
        LOGGER.info("econtract context record : {} commit confirm estamp : {} ", context.getEcontractRecordEntity().getRecordKey(),context.getCurrentTaskNode().getTaskName());

        TaskNodeBo currentTaskNode = context.getCurrentTaskNode();
        TaskNodeBo nextTaskNode = currentTaskNode.getNextTask();
        currentTaskNode.setTaskState(TaskConstant.TASK_SUCCESS);
        nextTaskNode.setTaskState(TaskConstant.TASK_RUNNIG);
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(context.getEcontractRecordEntity().getRecordKey());


        //1.更新老task
        EcontractTaskEntity oldTaskEntity = econtractTaskService.selectByPrimaryKey(currentTaskNode.getTaskId());
        oldTaskEntity.setTaskState(TaskConstant.TASK_SUCCESS);
        oldTaskEntity.setUtime(TimeUtils.getUTime());
        econtractTaskService.updateByPrimaryKeySelective(oldTaskEntity);


        //2.更新record状态，存储URL
        context.setCurrentTaskNode(nextTaskNode);
        context.setContextState(ContextConstant.CONTEXT_TO_EXECUTE);
        recordEntity.setUtime(TimeUtils.getUTime());
        recordEntity.setEcontractRecordContext(JSON.toJSONString(context));
        recordEntity.setEcontractState(EcontractRecordConstant.RUNNING);
        econtractRecordService.updateByPrimaryKeySelective(recordEntity);

        if (currentTaskNode.isNeedNotify()) {
            TaskContext taskContext = createNotifierTaskContext(context,recordEntity.getEcontractStage(),TaskConstant.TASK_SUCCESS);
            doNotifier(context, taskContext);
        }

        String contextStr = JSON.toJSONString(context);
        if (context.getUseMafka() && contextStr.getBytes().length > MccConfig.MESSAGE_SIZE){
            context.setContextId(messageProducerHandler.getEntityId(contextStr));
        }

    }

    @Override
    public void handleTaskWithNoTransactionalMessage(EcontractContext context) {
        CommitConfirmEstampHandler commitConfirmEstampHandler = (CommitConfirmEstampHandler) AopContext
                .currentProxy();
        LOGGER.info("#commitConfirmEstampHandler={},recordKey:{}", commitConfirmEstampHandler,context.getEcontractRecordEntity().getRecordKey());
        boolean isMafka = taskManager.mafkaMigrateSwitch();
        context.setUseMafka(isMafka);
        commitConfirmEstampHandler.handleTask(context);
        TaskMsg taskMsg = handleMessage(context,isMafka);
        taskManager.commitTask(taskMsg,isMafka);
    }
}

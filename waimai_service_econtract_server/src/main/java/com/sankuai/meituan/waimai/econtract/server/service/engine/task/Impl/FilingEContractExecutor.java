package com.sankuai.meituan.waimai.econtract.server.service.engine.task.Impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.meituan.sankuai.bsi.esign.common.model.dict.NotifyType;
import com.meituan.sankuai.bsi.esign.common.model.entity.result.ArchiveContractAsyncResult;
import com.meituan.sankuai.bsi.esign.common.model.entity.result.ArchiveContractResult;
import com.meituan.sankuai.bsi.esign.common.model.entity.result.SignContractAsyncResult;
import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.econtract.server.adapter.EsignClient;
import com.sankuai.meituan.waimai.econtract.server.annotation.EcontractIdempotent;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.bo.task.FilingContractResultBo;
import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import com.sankuai.meituan.waimai.econtract.server.constants.ContextConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.KmsConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.MccConstant;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractAsyncRecordEntity;
import com.sankuai.meituan.waimai.econtract.server.exception.EcontractException;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.TaskExecutor;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.util.monitor.AsyncEstampDelayMonitor;
import com.sankuai.meituan.waimai.econtract.server.utils.CaSignUtil;
import com.sankuai.meituan.waimai.econtract.server.utils.EcontractStampRouteUtil;
import com.sankuai.meituan.waimai.econtract.server.utils.HttpClientUtil;

import deps.redis.clients.util.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

/**
 * <AUTHOR> Hou
 * @date 2017/10/23
 * @time 下午12:43
 */
@Service
public class FilingEContractExecutor extends AbstractExecutor implements TaskExecutor {

    private static final Logger LOGGER = LoggerFactory.getLogger(FilingEContractExecutor.class);

    private static final Set<Integer> PERMITTED_ERROR_CODES = Sets.newHashSet();

    private static final int SUPPLIER_CONTRACT_COMPLETE_ERROR_CODE = 300031; // SUPPLIER_CONTRACT_COMPLETE_ERROR(300031, "合同已归档", 241423),

    private static final int DUPLICATE_FILING_CONTRACT_ERROR_CODE = 510001;

    static {
        PERMITTED_ERROR_CODES.add(com.meituan.sankuai.bsi.esign.common.exception.biz.BizErrorCode.CONTRACT_HAS_BE_ARCHIVED.getErrorCode());
        PERMITTED_ERROR_CODES.add(SUPPLIER_CONTRACT_COMPLETE_ERROR_CODE); // bsi-esign-sdk 依赖包最新版本里有这个枚举，后续升级依赖包版本后可引用
        PERMITTED_ERROR_CODES.add(DUPLICATE_FILING_CONTRACT_ERROR_CODE);
    }

    @Resource
    private EsignClient esignClient;

    @Autowired
    private AsyncEstampDelayMonitor asyncEstampDelayMonitor;

    private static final String FILING_ECONTRACT = "filing_econtract";

    @Override
    @EcontractIdempotent
    public void executeTask(EcontractContext context) {
        try {
            LOGGER.info("FilingEContractExecutor executeTask,recordKey:{}",context.getEcontractRecordEntity().getRecordKey());
            if(asyncFilingProportion(context) || asyncFilingEcontractType(context)){
                //异步归档合同
                asyncFiling(context);
            }else{
                //同步归档合同
                syncFiling(context);
            }
            metricExecutorResult(FILING_ECONTRACT, true);
        } catch (Exception e) {
            metricExecutorResult(FILING_ECONTRACT, false);
            LOGGER.error("fail to FilingEContractExecutor :" + context.getEcontractRecordEntity().getRecordKey() + ",msg :" + e.getMessage(), e);
            executeFail(context, e);
        }
    }

    private void syncFiling(EcontractContext context) throws KmsResultNullException, NoSuchAlgorithmException, ParseException, IOException {
        FilingContractResultBo filingContractResultBo = routePostForFiling(context);
        if (1000 == filingContractResultBo.getCode()) {
            context.setContextState(ContextConstant.CONTEXT_FINISH);
            executeSuccess(context);
            LOGGER.info("FilingEContractExecutor success , recordkey : {} ", context.getEcontractRecordEntity().getRecordKey());
        } else if (PERMITTED_ERROR_CODES.contains(filingContractResultBo.getCode())) {
            // 如果是因"合同已归档"等错误导致的归档失败，则不认为任务失败
            context.setContextState(ContextConstant.CONTEXT_FINISH);
            executeSuccess(context);
            LOGGER.warn("FilingEContractExecutor error , recordkey : {} , msg : {}", context.getEcontractRecordEntity().getRecordKey(), filingContractResultBo.getMsg());
        } else {
            EcontractException.FILING_ECONTRACT_EXCEPTION.newInstance(filingContractResultBo.getMsg());
        }
    }

    private void asyncFiling(EcontractContext context) {
        LOGGER.info("asyncFiling，recordKey:{}",context.getEcontractRecordEntity().getRecordKey());
        //调用归档异步接口
        String taskType = getTaskType(context);
        String contractId = context.getTaskTypeAndContractIdMap().get(taskType);
        ArchiveContractAsyncResult asyncFilingResult = esignClient.archiveContractAsync(contractId);
        //记录异步任务ID与taskId的关系
        EcontractAsyncRecordEntity asyncRecordEntity = saveAsyncTaskRel(context, asyncFilingResult, NotifyType.ARCHIVE_CONTRACT_ASYNC.getName());
        //监控接口调用情况
        asyncEstampDelayMonitor.addAsyncTask(asyncRecordEntity);

    }

    @Deprecated
    private FilingContractResultBo postForFiling(EcontractContext context) throws IOException, ParseException, NoSuchAlgorithmException, KmsResultNullException {
        Map<String, String> params = Maps.newHashMap();
        String filingUrl = ConfigUtilAdapter.getString(MccConstant.CERTIFY_FILING_URL, "");


        //获取app_id,配置于KMS上
        String contractId = genContractId(context, context.getEcontractRecordEntity().getRecordKey());
        String app_id = Kms.getByName(MccConstant.APP_KEY, KmsConstant.CERTIFY_APP_ID);
        //获取app_secret,配置于KMS上
        String app_secret = Kms.getByName(MccConstant.APP_KEY, KmsConstant.CERTIFY_APP_SECRET);
        Timestamp ts = new Timestamp(System.currentTimeMillis());
        DateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String timestamp = sdf.format(ts);
        params.put("timestamp", timestamp);
        params.put("app_id", app_id);
        params.put("v", "2.0");
        params.put("contract_id", contractId);
        params.put("msg_digest", CaSignUtil.getMsgDigestForFileUpload(timestamp, contractId, app_id, app_secret));
        //设置版本，默认2.0，非必填
        String postResult = HttpClientUtil.doPostRequest(params, filingUrl);
        FilingContractResultBo filingContractResultBo = JSON.parseObject(postResult, FilingContractResultBo.class);
        LOGGER.info("record {}， filing contract result : {} ", context.getEcontractRecordEntity().getRecordKey(), JSON.toJSONString(filingContractResultBo));


        return filingContractResultBo;
    }

    private FilingContractResultBo postForSSQFiling(EcontractContext context) {
        String taskType = getTaskType(context);
        String contractId = context.getTaskTypeAndContractIdMap().get(taskType);
        ArchiveContractResult result = esignClient.archiveContract(contractId);
        LOGGER.info("FilingEContractExecutor.postForSSQFiling，recordKey={}，contractId={}, result={}", context.getEcontractRecordEntity().getRecordKey(), contractId, JSON.toJSONString(result));
        FilingContractResultBo filingContractResultBo = new FilingContractResultBo();
        filingContractResultBo.setCode(contractId.equals(result.getContractId()) ? 1000 : result.getCode());
        filingContractResultBo.setMsg(result.getMessage());
        return filingContractResultBo;
    }

    private FilingContractResultBo routePostForFiling(EcontractContext context)
        throws KmsResultNullException, NoSuchAlgorithmException, ParseException, IOException {
        if (EcontractStampRouteUtil.checkRoutingToSSQ(context.getStampKey())) {
            return postForSSQFiling(context);
        } else {
            return postForFiling(context);
        }
    }

    private boolean asyncFilingProportion(EcontractContext context) {
        if(MccConfig.asyncFilingSwitch()) {
            Integer recordId = context.getEcontractRecordEntity().getId();
            Integer asyncProportion = recordId % 100;
            if(asyncProportion <= MccConfig.asyncFilingProportion()) {
                return true;
            }
        }
        return false;
    }

    private boolean asyncFilingEcontractType(EcontractContext context){
        List<String> openEcontrctTypeList = Lists.newArrayList(MccConfig.asyncFilingEcontractType().split(","));
        String econtractType = context.getEcontractEntity().getEcontractType();
        if(StringUtils.isNotEmpty(econtractType) && CollectionUtils.isNotEmpty(openEcontrctTypeList)){
            return openEcontrctTypeList.contains(econtractType);
        }
        return false;
    }


}

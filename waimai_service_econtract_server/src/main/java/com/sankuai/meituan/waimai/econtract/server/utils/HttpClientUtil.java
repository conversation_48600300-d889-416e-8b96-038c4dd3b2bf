package com.sankuai.meituan.waimai.econtract.server.utils;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Hou
 * @date 2017/10/29
 * @time 上午12:27
 */
public class HttpClientUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(HttpClientUtil.class);


    //设置超时时间
    private static final int HTTP_SOCKET_TIMEOUT = 100000;
    private static final int HTTP_CONNECT_TIMEOUT = 100000;
    private static final int HTTP_CONNECTION_REQUEST_TIMEOUT = 100000;

    private static final String UPLOAD_FILE = "up_load_file";

    public static final String HTTP_RETURN_ERROR = "http_return_error";


    public static String doPostRequest(Map<String, String> paramsMap, String postUrl) throws IOException {
        LOGGER.info("doPostRequest url is : {} , param is :{}", postUrl, JSON.toJSONString(paramsMap));

        HttpPost httpPost = new HttpPost(postUrl);
        String result;
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            List<NameValuePair> kvs = Lists.newArrayList();
            Iterator<Map.Entry<String, String>> it = paramsMap.entrySet().iterator();
            while (it.hasNext()) {
                Map.Entry<String, String> entry = it.next();
                kvs.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
            }
            httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
            httpPost.setEntity(new UrlEncodedFormEntity(kvs, StandardCharsets.UTF_8));
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(HTTP_SOCKET_TIMEOUT).setConnectionRequestTimeout(HTTP_CONNECTION_REQUEST_TIMEOUT).setConnectTimeout(HTTP_CONNECT_TIMEOUT).build();
            httpPost.setConfig(requestConfig);
            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                result = EntityUtils.toString(response.getEntity());
            }
        }
        LOGGER.info("doPostRequest result is : {} , param is :{}",JSON.toJSONString(result) , JSON.toJSONString(paramsMap));
        return result;
    }


    public static String doGetRequest(Map<String, String> paramsMap, String getUrl) throws IOException {
        HttpGet httpGet = new HttpGet(getUrl);
        String result;
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(HTTP_SOCKET_TIMEOUT).setConnectionRequestTimeout(HTTP_CONNECTION_REQUEST_TIMEOUT).setConnectTimeout(HTTP_CONNECT_TIMEOUT).build();
            httpGet.setConfig(requestConfig);
            try (CloseableHttpResponse response = httpClient.execute(httpGet)) {
                result = EntityUtils.toString(response.getEntity());
            }
        }
        return result;
    }

    public static byte[] doGetRequest(String getUrl) throws IOException {
        HttpGet httpGet = new HttpGet(getUrl);
        byte[] bytes = null;
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(HTTP_SOCKET_TIMEOUT).setConnectionRequestTimeout(HTTP_CONNECTION_REQUEST_TIMEOUT).setConnectTimeout(HTTP_CONNECT_TIMEOUT).build();
            httpGet.setConfig(requestConfig);
            try (CloseableHttpResponse response = httpClient.execute(httpGet)) {
                try (ByteArrayOutputStream outstream = new ByteArrayOutputStream(4 * 1024)) {
                    byte[] buffer = new byte[4096];
                    int len;
                    while ((len = response.getEntity().getContent().read(buffer)) > 0) {
                        outstream.write(buffer, 0, len);
                    }
                    bytes = outstream.toByteArray();
                }
            }
        }
        return bytes;
    }


    public static String doPostForUpLoad(byte[] bytes, Map<String, String> paramsMap, String postUrl) throws ParseException, IOException {
        HttpPost httpPost = new HttpPost(postUrl);
        String result;
        try (CloseableHttpClient httpClient = HttpClients.createDefault();) {
            MultipartEntityBuilder entityBuilder = MultipartEntityBuilder.create();
            entityBuilder.addBinaryBody("file", bytes, ContentType.DEFAULT_BINARY, UPLOAD_FILE);
            Iterator<Map.Entry<String, String>> it = paramsMap.entrySet().iterator();
            while (it.hasNext()) {
                Map.Entry<String, String> entry = it.next();
                entityBuilder.addTextBody(entry.getKey(), entry.getValue());
            }
            httpPost.setEntity(entityBuilder.build());
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(HTTP_SOCKET_TIMEOUT).setConnectionRequestTimeout(HTTP_CONNECTION_REQUEST_TIMEOUT).setConnectTimeout(HTTP_CONNECT_TIMEOUT).build();
            httpPost.setConfig(requestConfig);
            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                if (HttpStatus.SC_OK == response.getStatusLine().getStatusCode()) {
                    result = EntityUtils.toString(response.getEntity());
                } else {
                    result = HTTP_RETURN_ERROR;
                }
            }
        }
        return result;
    }

    public static void main(String[] args) throws IOException {
        doGetRequest("http://43.241.229.98/getdocs.action?app_id=802008&v=2.0&timestamp=20171108174550&transaction_id=exclusive_1510134286_ES_709_15&msg_digest=Q0Y1M0M5REYxOThEQ0YwNTJCRDZFNUY2REYxQTIwMzhDNUUxNjAwNQ==");
    }
}

package com.sankuai.meituan.waimai.econtract.server.crane;

import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executors;
import com.google.common.util.concurrent.*;
import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.cip.crane.client.spring.annotation.Crane;
import org.apache.commons.collections.CollectionUtils;
import com.sankuai.meituan.waimai.econtract.server.constants.EncryptionTypeConstant;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractEncryptionRecordMapper;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractEncryptionRecordEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordEntity;
import com.sankuai.meituan.waimai.econtract.server.service.encryption.EncryptionService;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractRecordContextMapper;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordContextEntity;
import com.sankuai.meituan.waimai.econtrct.client.constants.TaskConstant;
import com.sankuai.meituan.waimai.util.DateUtil;
import javax.annotation.Nullable;

/**
 * 冷数据表-历史数据加密定时job
 *
 * Created by lixuepeng on 2021/9/16
 */
@Component
public class ColdRecordEncryptionTask {

    private static final Logger LOGGER = LoggerFactory.getLogger(ColdRecordEncryptionTask.class);

    @Autowired
    private EcontractRecordContextMapper    econtractRecordContextMapper;
    @Autowired
    private EcontractEncryptionRecordMapper econtractEncryptionRecordMapper;
    @Autowired
    private EncryptionService               encryptionService;

    private static Calendar calendar = Calendar.getInstance();

    private static final ListeningExecutorService executorService;

    static {
        executorService = MoreExecutors.listeningDecorator(Executors.newFixedThreadPool(20));
    }

    @Crane("cold.record.data.encryption")
    public void handleColdRecordDataEncryption(String specialLastIdAndSize) {
        try {
            List<String> inputStrList = Arrays.asList(specialLastIdAndSize.split("#"));
            String specialLastIdStr = inputStrList.get(0);
            String perPageSizeStr = inputStrList.get(1);

            int size = Integer.valueOf(perPageSizeStr);
            long lastId = Long.valueOf(specialLastIdStr);

            List<EcontractRecordContextEntity> recordEntities = econtractRecordContextMapper.queryColdEntityListWithLabel4Encryption(lastId, size);

            LOGGER.info("handleColdRecordDataEncryption startTime:{}", DateUtil.unixTime());
            while (CollectionUtils.isNotEmpty(recordEntities)) {
                CountDownLatch latch = new CountDownLatch(recordEntities.size());
                for (EcontractRecordContextEntity recordEntity : recordEntities) {
                    ListenableFuture<Boolean> future = executorService.submit(new Callable() {
                        @Override
                        public Boolean call() throws Exception {
                            //针对每条记录生成密文并插入密文表
                            handelSingleRecordEncryption(recordEntity);
                            return true;
                        }
                    });
                    Futures.addCallback(future, new FutureCallback<Boolean>() {
                        @Override
                        public void onSuccess(@Nullable Boolean result) {
                            latch.countDown();
                        }
                        @Override
                        public void onFailure(Throwable t) {
                            LOGGER.error("handleColdRecordDataEncryption 执行数据清理任务异常 异常id:{}", recordEntity.getId());
                            latch.countDown();
                        }
                    });
                }
                latch.await();

                //时间判断-不在执行时间则结束
                if (MccConfig.dataEncryptionLimitHour() < getHourByDate(new Date())) {
                    break;
                }

                //翻页获取下一页信息
                Thread.sleep(100);//延迟100ms再执行下一批
                lastId = recordEntities.get(recordEntities.size() -1).getId();
                recordEntities = econtractRecordContextMapper.queryColdEntityListWithLabel4Encryption(lastId, size);
            }
            LOGGER.info("handleColdRecordDataEncryption endTime:{} lastId:{}", DateUtil.unixTime(), lastId);
        } catch (Exception e) {
            LOGGER.error("handleColdRecordDataEncryption 执行冷数据历史数据加密任务异常", e);
        }
    }
    
    private void handelSingleRecordEncryption(EcontractRecordContextEntity recordEntity) {
        try {
            int recordType = EncryptionTypeConstant.COLD_RECORD;
            long recordId = Long.valueOf(recordEntity.getId());

            //记录存在则表示已经被加密过-则不处理
            Integer cnt = econtractEncryptionRecordMapper.selectCntByRecordTypeAndId(recordType, recordId);
            if (cnt != null && cnt > 0){
                return;
            }
            //开始加密
            //1.获取加密后信息
            List<EcontractEncryptionRecordEntity> encryptionRecordEntityList =
                    encryptionService.queryEncrypttionRecordList("context", recordEntity.getContext());
            //2.保存加密信息
            encryptionService.doSaveEncryptionRecords(recordType, recordId, encryptionRecordEntityList);
        } catch (Exception e) {
            LOGGER.error("handelSingleRecordEncryption recordEntityId:{}", recordEntity.getId(), e);
        }
    }

    public static int getHourByDate(Date date) {
        if (date == null) {
            return 0;
        }
        synchronized (calendar) {
            calendar.setTime(date);

            return calendar.get(Calendar.HOUR_OF_DAY);
        }
    }
}

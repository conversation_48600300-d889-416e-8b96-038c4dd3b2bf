package com.sankuai.meituan.waimai.econtract.server.service.api;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.*;
import com.meituan.mtrace.thread.pool.TraceExecutors;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.econtract.server.bo.EcontractBatchOpExtBo;
import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import com.sankuai.meituan.waimai.econtract.server.constants.MccConstant;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractBatchOpEntity;
import com.sankuai.meituan.waimai.econtract.server.service.PirateService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractSignPageBizService;
import com.sankuai.meituan.waimai.econtract.server.utils.JacksonUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractAPIResponseConstant;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractErrorCode;
import com.sankuai.meituan.waimai.econtrct.client.constants.WebViewConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.*;
import com.sankuai.meituan.waimai.econtrct.client.domain.applybiz.SignAdditionInfoMeta;
import com.sankuai.meituan.waimai.econtrct.client.domain.applybiz.SpAreaDataQueryRequestDTO;
import com.sankuai.meituan.waimai.econtrct.client.domain.applybiz.SpAreaDataQueryRespDTO;
import com.sankuai.meituan.waimai.econtrct.client.domain.applybiz.WmContractContentAggreBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.base.BoolResult;
import com.sankuai.meituan.waimai.econtrct.client.domain.biz.EcontractRecordBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.common.BaseResponse;
import com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException;
import com.sankuai.meituan.waimai.econtrct.client.service.EcontractAPIResponse;
import com.sankuai.meituan.waimai.econtrct.client.service.EcontractBizService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 *
 * h5签约功能
 * 
 * <AUTHOR> Hou
 * @date 2017/10/21
 * @time 上午10:30
 */
@Service
public class EcontractBizServiceImpl implements EcontractBizService {

    private static final Logger LOGGER = LoggerFactory.getLogger(EcontractBizServiceImpl.class);

    private static final ExecutorService executorService = TraceExecutors
            .getTraceExecutorService(new ThreadPoolExecutor(10, 100, 0L, TimeUnit.MILLISECONDS,
                    new LinkedBlockingDeque<>(500), new ThreadPoolExecutor.CallerRunsPolicy()));

    @Autowired
    private EcontractSignPageBizService econtractSignPageBizService;
    @Autowired
    private PirateService pirateService;

    private static ListeningExecutorService pdfexecutorService;

    static {
        //初始化线程池
        pdfexecutorService = MoreExecutors.listeningDecorator(Executors.newFixedThreadPool(ConfigUtilAdapter.getInt("contract_pdf_thread_num",10)));
    }

    @Override
    @Deprecated
    public String queryRealNameRedirectUrl(String recordKey) {
        try {
            return econtractSignPageBizService.queryRealNameRedirectUrl(recordKey);
        } catch (com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException e) {
            LOGGER.warn("queryRealNameRedirectUrl, recordKey = " + recordKey);
            return StringUtils.EMPTY;
        } catch (TException e) {
            LOGGER.error("queryRealNameRedirectUrl, recordKey = " + recordKey);
            return StringUtils.EMPTY;
        }
    }

    @Override
    @Deprecated
    public SignH5InfoBo querySignH5InoBySecretParam(String param) {
        try {
            return econtractSignPageBizService.querySignH5InoBySecretParam(param);
        } catch (com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException e) {
            LOGGER.warn("querySignH5InoBySecretParam, param = " + param);
            return new SignH5InfoBo();
        } catch (TException e) {
            LOGGER.error("querySignH5InoBySecretParam, param = " + param);
            return new SignH5InfoBo();
        }
    }

    @Override
    @Deprecated
    public String getCertPhone(String param) {
        try {
            return econtractSignPageBizService.getCertPhone(param);
        } catch (com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException e) {
            LOGGER.warn("getCertPhone, param = " + param);
            return StringUtils.EMPTY;
        } catch (TException e) {
            LOGGER.error("getCertPhone, param = " + param);
            return StringUtils.EMPTY;
        }
    }

    @Override
    @Deprecated
    public String getLongUrl(String param) {// 规则维护在服务化内部
        try {
            return econtractSignPageBizService.getLongUrl(param);
        } catch (com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException e) {
            LOGGER.warn("getLongUrl, param = " + param);
            return StringUtils.EMPTY;
        } catch (TException e) {
            LOGGER.error("getLongUrl, param = " + param);
            return StringUtils.EMPTY;
        }
    }

    @Override
    @Deprecated
    public String querySignerPageTypeBySecretParam(String param) {
        try {
            return econtractSignPageBizService.querySignerPageTypeBySecretParam(param);
        } catch (com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException e) {
            LOGGER.warn("querySignerPageTypeBySecretParam, param = " + param);
            return StringUtils.EMPTY;
        } catch (TException e) {
            LOGGER.error("querySignerPageTypeBySecretParam, param = " + param);
            return StringUtils.EMPTY;
        }
    }

    @Override
    @Deprecated
    public SignPageInfoBo querySignPageInfoBySecretParam(String param) {
        try {
            return econtractSignPageBizService.querySignPageInfoBySecretParam(param);
        } catch (com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException e) {
            LOGGER.warn("querySignPageInfoBySecretParam, param = " + param);
            return new SignPageInfoBo();
        } catch (TException e) {
            LOGGER.error("querySignPageInfoBySecretParam, param = " + param);
            return new SignPageInfoBo();
        }
    }

    @Override
    @Deprecated
    public boolean sendPhoneCap(String recordKey) {
        try {
            return econtractSignPageBizService.sendPhoneCap(recordKey).isValue();
        } catch (com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException e) {
            LOGGER.warn("sendPhoneCap, recordKey = " + recordKey);
            return Boolean.FALSE;
        } catch (TException e) {
            LOGGER.error("sendPhoneCap, recordKey = " + recordKey);
            return Boolean.FALSE;
        }
    }

    @Override
    @Deprecated
    public boolean sendPhoneCapAtMid(String certPhone) {
        try {
            return econtractSignPageBizService.sendPhoneCapAtMid(certPhone).isValue();
        } catch (com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException e) {
            LOGGER.warn("sendPhoneCapAtMid, certPhone = " + certPhone);
            return Boolean.FALSE;
        } catch (TException e) {
            LOGGER.error("sendPhoneCapAtMid, certPhone = " + certPhone);
            return Boolean.FALSE;
        }
    }

    @Override
    @Deprecated
    public boolean verifyPhoneCaptcha(String recordKey, String code) {
        try {
            return econtractSignPageBizService.verifyPhoneCaptcha(recordKey, code).isValue();
        } catch (com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException e) {
            LOGGER.warn("verifyPhoneCaptcha, recordKey = " + recordKey);
            return Boolean.FALSE;
        } catch (TException e) {
            LOGGER.error("verifyPhoneCaptcha, recordKey = " + recordKey);
            return Boolean.FALSE;
        }
    }

    @Override
    @Deprecated
    public boolean verifyPhoneCaptchaAtMid(String certPhone, String code) {
        try {
            return econtractSignPageBizService.verifyPhoneCaptchaAtMid(certPhone, code).isValue();
        } catch (com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException e) {
            LOGGER.warn("verifyPhoneCaptcha, certPhone = " + certPhone);
            return Boolean.FALSE;
        } catch (TException e) {
            LOGGER.error("verifyPhoneCaptcha, certPhone = " + certPhone);
            return Boolean.FALSE;
        }
    }

    @Override
    @Deprecated
    public void confirmSignEContract(String recordKey) {
        try {
            econtractSignPageBizService.confirmSignEContract(recordKey);
        } catch (com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException e) {
            LOGGER.warn("confirmSignEContract, recordKey = " + recordKey);
        } catch (TException e) {
            LOGGER.error("confirmSignEContract, recordKey = " + recordKey);
        }
    }

    @Override
    @Deprecated
    public void cancelSignEContract(String recordKey, String msg) {
        try {
            econtractSignPageBizService.cancelSignEContract(recordKey, msg);
        } catch (com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException e) {
            LOGGER.info("cancelSignEContract, recordKey = " + recordKey);
        } catch (TException e) {
            LOGGER.error("cancelSignEContract, recordKey = " + recordKey);
        }
    }

    @Override
    @Deprecated
    public String queryEcontractSaveUrl(String recordKey) {
        try {
            return econtractSignPageBizService.queryEcontractSaveUrl(recordKey);
        } catch (com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException e) {
            LOGGER.warn("cancelSignEContract, recordKey = " + recordKey);
            return StringUtils.EMPTY;
        } catch (TException e) {
            LOGGER.error("cancelSignEContract, recordKey = " + recordKey);
            return StringUtils.EMPTY;
        }
    }

    @Override
    @Deprecated
    public void writeRealName(String userToken, SignerInfoBo signerInfoBo) {
        try {
            econtractSignPageBizService.writeRealName(userToken, signerInfoBo);
        } catch (com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException e) {
            LOGGER.warn("writeRealName, userToken = " + userToken);
        } catch (TException e) {
            LOGGER.error("writeRealName, userToken = " + userToken);
        }
    }

    @Override
    @Deprecated
    public void writeCaPool(String userToken, CertifyInfoBo certifyInfoBo) {
        try {
            econtractSignPageBizService.writeCaPool(userToken, certifyInfoBo);
        } catch (com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException e) {
            LOGGER.warn("writeCaPool, userToken = " + userToken);
        } catch (TException e) {
            LOGGER.error("writeCaPool, userToken = " + userToken);
        }
    }

    @Override
    @Deprecated
    public String queryCaCustomerId(CertifyInfoBo certifyInfoBo) {
        try {
            return econtractSignPageBizService.queryCaCustomerId(certifyInfoBo);
        } catch (com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException e) {
            LOGGER.warn("writeCaPool, certifyInfoBo = " + certifyInfoBo, e);
            return StringUtils.EMPTY;
        } catch (TException e) {
            LOGGER.error("writeCaPool, certifyInfoBo = " + certifyInfoBo, e);
            return StringUtils.EMPTY;
        }
    }

    @Override
    @Deprecated
    public boolean hasRealName(SignerInfoBo signerInfoBo) {
        try {
            return econtractSignPageBizService.hasRealName(signerInfoBo).isValue();
        } catch (com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException e) {
            LOGGER.warn("writeCaPool, signerInfoBo = " + signerInfoBo, e);
            return Boolean.FALSE;
        } catch (TException e) {
            LOGGER.error("writeCaPool, signerInfoBo = " + signerInfoBo, e);
            return Boolean.FALSE;
        }
    }

    @Override
    public boolean doRefreshCaPool() {
        try {
            return econtractSignPageBizService.doRefreshCaPool().isValue();
        } catch (com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException e) {
            LOGGER.warn("doRefreshCaPool error", e);
            return Boolean.FALSE;
        } catch (TException e) {
            LOGGER.error("doRefreshCaPool error", e);
            return Boolean.FALSE;
        }
    }

    @Override
    public String queryRealNameRedirectUrlV2(String recordKey)
            throws com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException, TException {
        return econtractSignPageBizService.queryRealNameRedirectUrl(recordKey);
    }

    @Override
    public SignH5InfoBo querySignH5InoBySecretParamV2(String param)
            throws com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException, TException {
        return econtractSignPageBizService.querySignH5InoBySecretParam(param);
    }

    @Override
    public String getCertPhoneV2(String param)
            throws com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException, TException {
        return econtractSignPageBizService.getCertPhone(param);
    }

    @Override
    public String getLongUrlV2(String param)
            throws com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException, TException {
        return econtractSignPageBizService.getLongUrl(param);
    }

    @Override
    public String querySignerPageTypeBySecretParamV2(String param)
            throws com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException, TException {
        return econtractSignPageBizService.querySignerPageTypeBySecretParam(param);
    }

    @Override
    public SignPageInfoBo querySignPageInfoBySecretParamV2(String param)
            throws com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException, TException {
        return econtractSignPageBizService.querySignPageInfoBySecretParam(param);
    }

    @Override
    public BoolResult sendPhoneCapV2(String recordKey)
            throws com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException, TException {
        return econtractSignPageBizService.sendPhoneCap(recordKey);
    }

    @Override
    public BoolResult sendPhoneCapAtMidV2(String certPhone)
            throws com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException, TException {
        return econtractSignPageBizService.sendPhoneCapAtMid(certPhone);
    }

    @Override
    public BoolResult verifyPhoneCaptchaV2(String recordKey, String code)
            throws com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException, TException {
        return econtractSignPageBizService.verifyPhoneCaptcha(recordKey, code);
    }

    @Override
    public BoolResult verifyPhoneCaptchaAtMidV2(String certPhone, String code)
            throws com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException, TException {
        return econtractSignPageBizService.verifyPhoneCaptchaAtMid(certPhone, code);
    }

    @Override
    public BoolResult confirmSignEContractV2(String recordKey)
            throws com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException, TException {
        return econtractSignPageBizService.confirmSignEContract(recordKey);
    }

    @Override
    public BoolResult cancelSignEContractV2(String recordKey, String msg)
            throws com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException, TException {
        return econtractSignPageBizService.cancelSignEContract(recordKey, msg);
    }

    @Override
    public String queryEcontractSaveUrlV2(String recordKey)
            throws com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException, TException {
        return econtractSignPageBizService.queryEcontractSaveUrl(recordKey);
    }

    @Override
    public BoolResult writeRealNameV2(String userToken, SignerInfoBo signerInfoBo)
            throws com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException, TException {
        return econtractSignPageBizService.writeRealName(userToken, signerInfoBo);
    }

    @Override
    public BoolResult writeCaPoolV2(String userToken, CertifyInfoBo certifyInfoBo)
            throws com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException, TException {
        return econtractSignPageBizService.writeCaPool(userToken, certifyInfoBo);
    }

    @Override
    public String queryCaCustomerIdV2(CertifyInfoBo certifyInfoBo)
            throws com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException, TException {
        return econtractSignPageBizService.queryCaCustomerId(certifyInfoBo);
    }

    @Override
    public BoolResult hasRealNameV2(SignerInfoBo signerInfoBo)
            throws com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException, TException {
        return econtractSignPageBizService.hasRealName(signerInfoBo);
    }

    @Override
    public BoolResult doRefreshCaPoolV2()
            throws com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException, TException {
        return econtractSignPageBizService.doRefreshCaPool();
    }

    @Override
    public String queryRecordKeyBySecretParam(String param)
            throws com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException, TException {
        return econtractSignPageBizService.queryRecordKeyBySecretParam(param);
    }

    @Override
    public String queryEcontractData(String recordKey, String type) throws EcontractException, TException {
        return econtractSignPageBizService.queryEcontractData(recordKey, type);
    }

    @Override
    public String queryExternalJumpData(String recordKey, String type) throws EcontractException, TException {
        return econtractSignPageBizService.queryEcontractExternalJumpData(recordKey, type);
    }

    @Override
    public String postForCA(CertifyInfoBo certifyInfoBo) throws EcontractException, TException {
        return econtractSignPageBizService.postForCA(certifyInfoBo);
    }

    @Override
    public Map<String, String> queryEcontractSpAreaData(String recordKey) throws EcontractException, TException {
        return econtractSignPageBizService.queryEcontractSpAreaData(recordKey);
    }

    @Override
    public WmContractContentAggreBo queryEcontractContentAggre(String recordKey) throws EcontractException, TException {
        WmContractContentAggreBo aggreBo = econtractSignPageBizService.queryEcontractContentAggre(recordKey);
        LOGGER.info("queryEcontractContentAggre aggreBo:{}", JSON.toJSONString(aggreBo));
        return aggreBo;
    }

    @Override
    public List<WmContractContentAggreBo> queryEcontractContentAggreList(String recordKey)
            throws EcontractException, TException {
        List<WmContractContentAggreBo> aggreBoList = econtractSignPageBizService.queryEcontractContentAggreList(recordKey);
        LOGGER.info("queryEcontractContentAggreList aggreBoList:{}", JSON.toJSONString(aggreBoList));
        return aggreBoList;
    }

    @Override
    public CertifyH5InfoBo queryH5CertifyInfoBySecretParam(String param) throws EcontractException, TException {
        LOGGER.info("queryH5CertifyInfoBySecretParam param:{}", param);
        return econtractSignPageBizService.queryH5CertifyInfoBySecretParam(param);
    }

    @Override
    public List<SignAdditionInfoMeta> queryEcontractListExtraInfo(String recordKey, WmContractContentAggreBo aggreBo)
            throws EcontractException, TException {
        LOGGER.info("queryEcontractListExtraInfo recordKey:{}", recordKey);
        return econtractSignPageBizService.queryEcontractListExtraInfo(recordKey, aggreBo);
    }

    @Override
    public List<EcontractRecordBo> queryEcontractRecordByBatchId(Integer batchId) throws EcontractException, TException {
        LOGGER.info("queryEcontractRecordByBatchId batchId:{}", batchId);
        return econtractSignPageBizService.queryEcontractRecordByBatchId(batchId);
    }

    @Override
    public EcontractRecordBo queryEcontractRecordByRecordKey(String recordKey) throws EcontractException, TException {
        LOGGER.info("queryEcontractRecordByRecordKey recordKey:{}", recordKey);
        return econtractSignPageBizService.queryEcontractRecordByRecordKey(recordKey);
    }

    @Override
    public String queryBatchSmsRecordKeyByBatchId(Integer batchId) throws EcontractException, TException {
        LOGGER.info("queryBatchSmsRecordKeyByBatchId batchId:{}", batchId);
        return econtractSignPageBizService.queryBatchSmsRecordKeyByBatchId(batchId);
    }

    @Override
    public Boolean isForceAllOp(Integer batchId) throws EcontractException, TException {
        return econtractSignPageBizService.isForceAllOp(batchId);
    }

    @Override
    public Map<String, List<WmContractContentAggreBo>> queryAggreBoByRecordKeys(List<String> recordKeys)
            throws EcontractException, TException {
        LOGGER.info("queryAggreBoByRecordKeys recordKeys:{}", recordKeys);
        return econtractSignPageBizService.queryEcontractContentAggreMap(recordKeys);
    }

    @Override
    public EcontractAPIResponse batchOpByRecordKeys(List<String> recordKeys, Integer opType)
            throws EcontractException, TException {
        LOGGER.info("batchOpByRecordKeys recordKeys:{}, opType:{}", recordKeys, opType);
        // 前置校验(若recordKey为批量发起签约且强制全部操作，判断recordKey集合是否满足)
        econtractSignPageBizService.checkRecordKeys(recordKeys);
        EcontractAPIResponse response = new EcontractAPIResponse();
        // 生成批次ID
        Long batchOpId = econtractSignPageBizService.initBatchOpInfo(recordKeys, opType);
        if (batchOpId != null && batchOpId.longValue() > 0) {// 正常初始化批次信息
            // 异步执行批次操作
            executorService.execute(() -> {
                try {
                    econtractSignPageBizService.doBatchOpByRecordKeys(recordKeys, opType);
                } catch (Exception e) {
                    LOGGER.error("batchOpByRecordKeys#异步发起批量操作失败 recordKeys:{}, opType:{}", recordKeys, opType, e);
                }
            });
            // 返回成功信息
            Map<String, String> returnData = Maps.newHashMap();
            returnData.put("batchOpId", String.valueOf(batchOpId));
            response.setCode(EcontractAPIResponseConstant.SUCCESS_CODE);
            response.setStatus(EcontractAPIResponseConstant.SUCCESS);
            response.setReturnData(returnData);
        } else {// 未初始化批次信息
            response.setCode(EcontractAPIResponseConstant.SERVER_ERROR);
            response.setStatus(EcontractAPIResponseConstant.FAIL);
        }
        LOGGER.info("batchOpByRecordKeys recordKeys:{}, opType:{}, response:{}", recordKeys, opType, JSON.toJSONString(response));
        return response;
    }

    @Override
    public EcontractAPIResponse queryBatchOpStatus(Long batchOpId) throws EcontractException, TException {
        LOGGER.info("queryBatchOpStatus batchOpId:{}", batchOpId);
        EcontractAPIResponse response = new EcontractAPIResponse();
        EcontractBatchOpEntity batchOpEntity = econtractSignPageBizService.queryBatchOpEntityById(batchOpId);
        EcontractBatchOpExtBo extBo = JSON.parseObject(batchOpEntity.getExt(), EcontractBatchOpExtBo.class);
        // 未查询到数据
        if (batchOpEntity == null) {
            response.setCode(EcontractAPIResponseConstant.SERVER_ERROR);
            response.setStatus(EcontractAPIResponseConstant.FAIL);
            return response;
        }
        Map<String, String> returnData = Maps.newHashMap();
        assemblyBatchOpStatus(batchOpEntity);
        returnData.put("status", String.valueOf(batchOpEntity.getStatus()));
        String msg = "";
        // 存在失败的场景-取第一个
        if (extBo.getFailRecordKeyList().size() > 0) {
            Map<String, List<WmContractContentAggreBo>> aggreBoMap = queryAggreBoByRecordKeys(extBo.getFailRecordKeyList());
            String tipContractName = aggreBoMap.values().stream().findFirst().get().get(0).getContractName();
            int ContractCnt = 0;
            for (Map.Entry<String, List<WmContractContentAggreBo>> entry : aggreBoMap.entrySet()) {
                ContractCnt += entry.getValue().size();
            }
            msg = MccConfig.batchOpFailTips().replace("{X}", tipContractName).replace("{Y}", String.valueOf(ContractCnt));
        }
        returnData.put("msg", msg);

        response.setCode(EcontractAPIResponseConstant.SUCCESS_CODE);
        response.setStatus(EcontractAPIResponseConstant.SUCCESS);
        response.setReturnData(returnData);
        return response;
    }

    @Override
    public SignH5InfoBo querySignH5InoByRecordKey(String recordKey) throws EcontractException, TException {
        LOGGER.info("querySignH5InoByRecordKey recordKey:{}", recordKey);
        return econtractSignPageBizService.querySignH5InoByRecordKey(recordKey);
    }

    @Override
    public BoolResult queryHasEcontractData(String recordKey,String host) throws EcontractException, TException {
        String pfhHost = ConfigUtilAdapter.getString(MccConstant.SIGN_H5_URL_PHF, MccConstant.SIGN_H5_URL_PHF_DEFAULT);
        if (pfhHost.equals(host)) {
            //拼好饭新模板页面
            EcontractRecordBo recordBo = econtractSignPageBizService.queryEcontractRecordByRecordKey(recordKey);
            if (recordBo == null) {
                return new BoolResult(false);
            }
            List<WmContractContentAggreBo> aggreBoList = Lists.newArrayList();
            if (recordBo.getRecordBatchId() == null || recordBo.getRecordBatchId() == 0) {
                //单合同
                aggreBoList = econtractSignPageBizService.queryEcontractContentAggreList(recordKey);
            } else {
                List<EcontractRecordBo> recordBoList = econtractSignPageBizService.queryEcontractRecordByBatchId(recordBo.getRecordBatchId());
                if (CollectionUtils.isNotEmpty((recordBoList))) {
                    List<String> recordKeyList = recordBoList.stream().map(EcontractRecordBo::getRecordKey).collect(Collectors.toList());
                    List<WmContractContentAggreBo> aggreBoFuture = Lists.newArrayList();
                    CountDownLatch latch = new CountDownLatch(recordKeyList.size());
                    for (String record : recordKeyList) {
                        ListenableFuture<List<WmContractContentAggreBo>> future = pdfexecutorService.submit(new Callable() {
                            @Override
                            public List<WmContractContentAggreBo> call() throws Exception {
                                return econtractSignPageBizService.queryEcontractContentAggreList(record);
                            }
                        });
                        Futures.addCallback(future, new FutureCallback<List<WmContractContentAggreBo>>() {
                            @Override
                            public void onSuccess(@Nullable List<WmContractContentAggreBo> result) {
                                aggreBoFuture.addAll(result);
                                latch.countDown();
                            }
                            @Override
                            public void onFailure(Throwable t) {
                                LOGGER.error("queryEcontractContentAggreList 查询合同列表数据异常record:{}", record);
                                latch.countDown();
                            }
                        });
                    }
                    try {
                        latch.await();
                    } catch (InterruptedException e) {
                        throw new EcontractException(EcontractErrorCode.BUSINESS_ERROR, "判断是否有合同异常");
                    }
                    aggreBoList.addAll(aggreBoFuture);
                }
            }
            List<WmContractContentAggreBo> filterList = aggreBoList.stream().filter(bo -> bo != null && (WebViewConstant.STAGE_SUCCESS.equals(bo.getViewStage())
                    || WebViewConstant.STAGE_WAIT_SIGN.equals(bo.getViewStage())
                    || WebViewConstant.STAGE_WAIT_ASY_EXECUTE.equals(bo.getViewStage())
                    || WebViewConstant.STAGE_WAIT_REAL_NAME.equals(bo.getViewStage()))).collect(Collectors.toList());

            return new BoolResult(filterList.size() > 0);
        } else {
            SignH5InfoBo signH5InfoBo = econtractSignPageBizService.querySignH5InoByRecordKey(recordKey);
            if (signH5InfoBo == null || WebViewConstant.STAGE_FAIL.equals(signH5InfoBo.getViewStage()) || WebViewConstant.STAGE_CANCEL_SIGN.equals(signH5InfoBo.getViewStage())) {
                return new BoolResult(false);
            }
            return new BoolResult(true);
        }
    }

    private void assemblyBatchOpStatus(EcontractBatchOpEntity batchOpEntity) throws EcontractException, TException {
        // 对象为空or状态为进行中or无需等待上游状态,直接返回
        if (batchOpEntity == null || batchOpEntity.getStatus() == 1 || !MccConfig.isBatchOpStatusWaitForUpstream()) {
            return;
        }
        // 不存在成功的任务,直接返回
        EcontractBatchOpExtBo extBo = JSON.parseObject(batchOpEntity.getExt(), EcontractBatchOpExtBo.class);
        if (CollectionUtils.isEmpty(extBo.getSuccessRecordKeyList())) {
            return;
        }
        // 若成功的record记录对应的viewStage为STAGE_WAIT_ASY_EXECUTE
        // 则代表处于等待上游状态通知阶段
        // batchOp任务状态置为1且返回
        for (String recordKey : extBo.getSuccessRecordKeyList()) {
            SignH5InfoBo signH5InfoBo = econtractSignPageBizService.querySignH5InoByRecordKey(recordKey);
            if (signH5InfoBo != null && WebViewConstant.STAGE_WAIT_ASY_EXECUTE.equals(signH5InfoBo.getViewStage())) {
                batchOpEntity.setStatus(1);
                return;
            }
        }
    }

    @Override
    public List<EcontractRecordBo> queryEcontractRecordList(List<String> recordKeyList)
        throws EcontractException, TException {

        List<EcontractRecordBo> econtractRecordBoList = econtractSignPageBizService.queryEcontractRecordList(
            recordKeyList);
        LOGGER.info("econtractRecordBoList={}", JSON.toJSONString(econtractRecordBoList));
        return econtractRecordBoList;
    }

    @Override
    public BaseResponse<SignEcontractInfoBo> queryEcontractDataInfo(DaocanContractRequestDTO request) {
        LOGGER.info("EcontractBizServiceImpl#queryEcontractDataInfoWD, DaocanContractRequestDTO = {}", JSON.toJSONString(request));
        try {
            SignEcontractInfoBo data = econtractSignPageBizService.queryEcontractDataInfoWD(request);
            LOGGER.info("queryEcontractDataInfoWD data = {}", JSON.toJSONString(data));
            return BaseResponse.success(data);
        } catch (EcontractException e) {
            LOGGER.error("EcontractBizServiceImpl#queryEcontractDataInfoWD, DaocanContractRequestDTO = {}", JSON.toJSONString(request), e);
            return BaseResponse.fail(e.getMsg());
        } catch (Exception e) {
            LOGGER.error("EcontractBizServiceImpl#queryEcontractDataInfoWD, DaocanContractRequestDTO = {}", JSON.toJSONString(request), e);
            return BaseResponse.fail("查询失败");
        }
    }

    @Override
    public BaseResponse<String> queryCoopPreviewHtml(CoopPreviewHtmlRequestDTO coopPreviewHtmlRequestDTO) throws EcontractException, TException {
        LOGGER.info("EcontractBizServiceImpl#queryCoopPreviewHtml, coopPreviewHtmlRequestDTO = {}", JSON.toJSONString(coopPreviewHtmlRequestDTO));
        try {
            String previewHtml = econtractSignPageBizService.queryCoopPreviewHtml(coopPreviewHtmlRequestDTO);
            return BaseResponse.success(previewHtml);
        } catch (EcontractException e) {
            LOGGER.error("EcontractBizServiceImpl#queryCoopPreviewHtml, coopPreviewHtmlRequestDTO = {}", JSON.toJSONString(coopPreviewHtmlRequestDTO), e);
            return BaseResponse.fail(e.getMsg());
        } catch (Exception e) {
            LOGGER.error("EcontractBizServiceImpl#queryCoopPreviewHtml, coopPreviewHtmlRequestDTO = {}", JSON.toJSONString(coopPreviewHtmlRequestDTO), e);
            return BaseResponse.fail("查询失败");
        }
    }

    @Override
    public BaseResponse<SignEcontractCompleteInfoBo> getEcontractCompleteInfo(DaocanContractRequestDTO request) throws EcontractException, TException {
        LOGGER.info("EcontractBizServiceImpl#getEcontractCompleteInfo, param = {}", JSON.toJSONString(request));
        try {
            SignEcontractCompleteInfoBo signEcontractCompleteInfoBo = econtractSignPageBizService.getEcontractCompleteInfo(request);
            return BaseResponse.success(signEcontractCompleteInfoBo);
        } catch (EcontractException e) {
            LOGGER.error("EcontractBizServiceImpl#getEcontractCompleteInfo", e);
            return BaseResponse.fail(e.getMsg());
        } catch (Exception e) {
            LOGGER.error("EcontractBizServiceImpl#getEcontractCompleteInfo", e);
            return BaseResponse.fail("查询失败");
        }
    }

    @Override
    public BaseResponse<Long> confirmSign(DaocanContractRequestDTO request) {
        try {
            LOGGER.info("EcontractBizServiceImpl#confirmSign, param: {}", JSON.toJSONString(request));
            Long confirmResult = econtractSignPageBizService.confirmSign(request);
            LOGGER.info("EcontractBizServiceImpl#confirmSign, confirmResult: {}", confirmResult);
            return BaseResponse.success(confirmResult);
        } catch (EcontractException e) {
            LOGGER.warn("EcontractBizServiceImpl#confirmSign, warn", e);
            return BaseResponse.fail(e.getMsg());
        } catch (Exception e) {
            LOGGER.error("EcontractBizServiceImpl#confirmSign, error", e);
            return BaseResponse.fail("签约确认失败");
        }
    }

    @Override
    public BaseResponse<SignStatusViewVo> querySignStatus(DaocanContractRequestDTO request) {
        try {
            LOGGER.info("EcontractBizServiceImpl#querySignStatus, param: {}", JSON.toJSONString(request));
            SignStatusViewVo signStatus = econtractSignPageBizService.querySignStatus(request);
            LOGGER.info("EcontractBizServiceImpl#querySignStatus, signStatus: {}", JacksonUtil.writeAsJsonStr(signStatus));
            return BaseResponse.success(signStatus);
        } catch (EcontractException e) {
            LOGGER.warn("EcontractBizServiceImpl#querySignStatus, warn", e);
            return BaseResponse.fail(e.getMsg());
        } catch (Exception e) {
            LOGGER.error("EcontractBizServiceImpl#querySignStatus, error", e);
            return BaseResponse.fail("查询签约状态异常");
        }
    }

    @Override
    public BaseResponse<PdfImageBo> queryPdfImage(String url) {
        LOGGER.info("EcontractBizServiceImpl#transPdfUrlToImage, pdfUrl = {}", url);
        try {
            PdfImageBo pdfImageBo = econtractSignPageBizService.queryPdfImageByUrl(url);
            return BaseResponse.success(pdfImageBo);
        } catch (EcontractException e) {
            LOGGER.error("EcontractBizServiceImpl#transPdfUrlToImage error, pdfUrl = {}", url, e);
            return BaseResponse.fail("转换失败");
        } catch (Exception e) {
            LOGGER.error("EcontractBizServiceImpl#transPdfUrlToImage error, pdfUrl = {}", url, e);
            return BaseResponse.fail("转换失败");
        }
    }

    @Override
    public BaseResponse<List<SpAreaDataQueryRespDTO>> queryDeliveryAreaData(SpAreaDataQueryRequestDTO request) {
        try {
            LOGGER.info("EcontractBizServiceImpl#querySpAreaData, request: {}", JacksonUtil.writeAsJsonStr(request));
            return BaseResponse.success(econtractSignPageBizService.querySpAreaData(request));
        } catch (EcontractException e) {
            LOGGER.warn("EcontractBizServiceImpl#querySpAreaData, warn", e);
            return BaseResponse.fail(e.getMsg());
        } catch (Exception e) {
            LOGGER.error("EcontractBizServiceImpl#querySpAreaData, error", e);
            return BaseResponse.fail("配送范围查看失败, 请稍后重试");
        }
    }

}

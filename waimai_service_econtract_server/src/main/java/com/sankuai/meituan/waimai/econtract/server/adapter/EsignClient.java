package com.sankuai.meituan.waimai.econtract.server.adapter;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.meituan.sankuai.bsi.esign.common.model.dict.CompanyType;
import com.meituan.sankuai.bsi.esign.common.model.dict.IdentityType;
import com.meituan.sankuai.bsi.esign.common.model.entity.request.CompanyRequest;
import com.meituan.sankuai.bsi.esign.common.model.entity.request.ContractRequest;
import com.meituan.sankuai.bsi.esign.common.model.entity.request.PersonRequest;
import com.meituan.sankuai.bsi.esign.common.model.entity.request.SignContractRequest;
import com.meituan.sankuai.bsi.esign.common.model.entity.result.*;
import com.meituan.sankuai.bsi.esign.sdk.client.thrift.EsignThriftClient;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EstampSubjectBo;
import com.sankuai.meituan.waimai.econtract.server.constants.MccConstant;
import com.sankuai.meituan.waimai.econtract.server.exception.EcontractException;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.Impl.AbstractExecutor;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.Impl.EstampExecutor;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.util.factory.EstampSubject;
import com.sankuai.meituan.waimai.econtract.server.service.SpringBeanUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.EstampSubjectInfoEnum;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.CAType;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.CertifyInfoBo;
import com.meituan.sankuai.bsi.esign.common.model.entity.request.CompanyLegalRequest;
import com.meituan.sankuai.bsi.esign.common.model.entity.request.CompanyCertigierRequest;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

import static com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException.CA_PARAM_ERROR;

/**
 * 参考文档
 * https://km.sankuai.com/page/13023834
 */
@Service
public class EsignClient {

    private static final Logger LOGGER = LoggerFactory.getLogger(EsignClient.class);

    private static final Long EXPIRE_TIME = 3 * 365 * 24 * 60 * 60 * 1000L;

    @Autowired
    private EsignThriftClient esignThriftClient;

    @Autowired
    private AbstractExecutor abstractExecutor;

    @Autowired
    private EstampExecutor estampExecutor;

    private static final String STATIC_ESTAMP_SUBJECT_HANDLER = "staticEstampSubject";

    private static final String DYNAMIC_ESTAMP_SUBJECT_HANDLER = "dynamicEstampSubject";

    /**
     * 申请CA认证
     */
    public ApplyCertResult applyCert(CertifyInfoBo certifyInfoBo) throws EcontractException {
        if (CAType.COMPANY.equals(certifyInfoBo.getCaType())) {
            return applyCompanyCert(certifyInfoBo);
        } else if (CAType.PERSON.equals(certifyInfoBo.getCaType())) {
            return applyPersonCert(certifyInfoBo);
        }
        throw new EcontractException(CA_PARAM_ERROR, "无法识别的CA认证类型");
    }

    /**
     * 申请企业CA认证
     */
    private ApplyCertResult applyCompanyCert(CertifyInfoBo certifyInfoBo) {
        CompanyRequest companyRequest = new CompanyRequest();
        companyRequest.setRegCode(certifyInfoBo.getQuaNum());
        companyRequest.setCompanyName(certifyInfoBo.getCustomerName());
        companyRequest.setContactorMobile(certifyInfoBo.getMobile());
        if (StringUtils.isNotBlank(certifyInfoBo.getEmail())) {
            companyRequest.setContactorMail(certifyInfoBo.getEmail());
        }
        if (certifyInfoBo.getIsNewStampIface() == 1) { //新增签约人信息
            companyRequest.setLegalPersonIdentityType(IdentityType.fromCode(certifyInfoBo.getSignerIdentityType()));
            companyRequest.setLegalPersonIdentity(certifyInfoBo.getSignerIdentity());
        }
        LOGGER.info("applyCompanyCert certifyInfoBo = {}, companyRequest = {}", JSON.toJSONString(certifyInfoBo), JSON.toJSONString(companyRequest));
        ApplyCertResult result = esignThriftClient.applyCompanyCert(companyRequest);
        LOGGER.info("applyCompanyCert result = {}", JSON.toJSONString(result));
        return result;
    }

    /**
     * 申请企业CA认证-法人签署场景
     */
    private ApplyCertResult applyCompanyCertByLegalPerson(CertifyInfoBo certifyInfoBo) {
        CompanyLegalRequest legalRequest = new CompanyLegalRequest();
        legalRequest.setRegCode(certifyInfoBo.getQuaNum());
        legalRequest.setCompanyName(certifyInfoBo.getCustomerName());
        legalRequest.setLegalPersonName(certifyInfoBo.getSignerName());
        legalRequest.setLegalPersonIdentity(certifyInfoBo.getSignerIdentity());
        legalRequest.setLegalPersonIdentityType(IdentityType.fromCode(certifyInfoBo.getSignerIdentityType()));
        legalRequest.setLegalPersonMobile(certifyInfoBo.getSignerMobile());
        legalRequest.setContactorMobile(certifyInfoBo.getMobile());

        LOGGER.info("applyCompanyCertByLegalPerson certifyInfoBo = {}, legalRequest = {}", JSON.toJSONString(certifyInfoBo), JSON.toJSONString(legalRequest));
        ApplyCertResult result = esignThriftClient.applyCompanyCertByLegalPerson(legalRequest);
        LOGGER.info("applyCompanyCertByLegalPerson result = {}", JSON.toJSONString(result));
        return result;
    }

    /**
     * 申请企业CA认证-授权人签署场景
     */
    private ApplyCertResult applyCompanyCertByCertigier(CertifyInfoBo certifyInfoBo) {
        CompanyCertigierRequest certigierRequest = new CompanyCertigierRequest();
        certigierRequest.setRegCode(certifyInfoBo.getQuaNum());
        certigierRequest.setCompanyName(certifyInfoBo.getCustomerName());
        certigierRequest.setCertigierName(certifyInfoBo.getSignerName());
        certigierRequest.setCertigierIdentity(certifyInfoBo.getSignerIdentity());
        certigierRequest.setCertigierIdentityType(IdentityType.fromCode(certifyInfoBo.getSignerIdentityType()));
        certigierRequest.setCertigierMobile(certifyInfoBo.getSignerMobile());
        certigierRequest.setContactorMobile(certifyInfoBo.getMobile());
        LOGGER.info("applyCompanyCertByCertigier certifyInfoBo = {}, certigierRequest = {}", JSON.toJSONString(certifyInfoBo), JSON.toJSONString(certigierRequest));
        ApplyCertResult result = esignThriftClient.applyCompanyCertByCertigier(certigierRequest);
        LOGGER.info("applyCompanyCertByCertigier result = {}", JSON.toJSONString(result));
        return result;
    }


    private ApplyCertResult applyCompanyCertWithCondition(CertifyInfoBo certifyInfoBo) {
        if (certifyInfoBo.getIsNewStampIface() == 0) {//老接口
            return applyCompanyCert(certifyInfoBo);
        } else if (certifyInfoBo.getIsNewStampIface() == 1) {//新接口
            if (certifyInfoBo.getSignerType() == 2) { // 授权人签署
                return applyCompanyCertByCertigier(certifyInfoBo);
            } else if (certifyInfoBo.getSignerType() == 3) {//法人签署
                return applyCompanyCertByLegalPerson(certifyInfoBo);
            }
        }
        return null;
    }

    /**
     * 申请个人CA认证
     */
    private ApplyCertResult applyPersonCert(CertifyInfoBo certifyInfoBo) {
        PersonRequest personRequest = new PersonRequest();
        personRequest.setPersonName(certifyInfoBo.getCustomerName());
        personRequest.setIdentity(certifyInfoBo.getQuaNum());
        personRequest.setIdentityType(certifyInfoBo.getIdentityType() == null?IdentityType.ID_CARD:IdentityType.fromCode(certifyInfoBo.getIdentityType().getCode()));
        personRequest.setContactMobile(certifyInfoBo.getMobile());
        if (StringUtils.isNotBlank(certifyInfoBo.getEmail())) {
            personRequest.setContactEmail(certifyInfoBo.getEmail());
        }
        LOGGER.info("applyPersonCert certifyInfoBo = {}", JSON.toJSONString(certifyInfoBo));
        ApplyCertResult result = esignThriftClient.applyPersonCert(personRequest);
        LOGGER.info("applyPersonCert result = {}", JSON.toJSONString(result));
        return result;
    }

    /**
     * 申请电子合同
     */
    public CreateContractResult createContract(ContractRequest contractRequest) {
        contractRequest.setCreatorCustomerId(ConfigUtilAdapter.getString(MccConstant.SSQ_MEITUAN_CUSTOMER__ID, "CEFB29C49AB5068F"));
        contractRequest.setExpireAt(DateUtil.unixTime() * 1000L + EXPIRE_TIME);
        contractRequest.setTitle("外卖电子合同");
        contractRequest.setDescription("申请外卖C1/C2/C3电子合同甲乙方生成电子签章");
        LOGGER.info("createContract request = {}", JSON.toJSONString(contractRequest));
        CreateContractResult result = esignThriftClient.createContract(contractRequest);
        LOGGER.info("createContract result = {}", JSON.toJSONString(result));
        return result;
    }

    /**
     * 电子签章
     */
    public SignContractResult signContract(SignContractRequest signContractRequest) {
        signContractRequest.setOffsetX(ConfigUtilAdapter.getInt("sign.request.offsetx", 0));
        signContractRequest.setOffsetY(ConfigUtilAdapter.getInt("sign.request.offsety", -60));
        LOGGER.info("signContract signContractRequest = {}", JSON.toJSONString(signContractRequest));
        SignContractResult result = esignThriftClient.signContract(signContractRequest);
        LOGGER.info("signContract result = {}", JSON.toJSONString(result));
        return result;
    }

    /**
     * 电子签章-异步接口
     */
    public SignContractAsyncResult signContractAsync(EcontractContext context) {
        String taskType = abstractExecutor.getTaskType(context);
        SignContractRequest signContractRequest = new SignContractRequest();
        signContractRequest.setContractId(context.getTaskTypeAndContractIdMap().get(taskType));
        signContractRequest.setCustomerId(context.getTaskContext().getCustomerId());
        signContractRequest.setKeyWord(context.getTaskContext().getSignKeyWord());
        Map<String,String> resultMap = warpSignContractCustomerNameAndIdCode(context);
        signContractRequest.setCustomerName(resultMap.get("customerName"));
        signContractRequest.setCustomerIdCode(resultMap.get("customerIdCode"));
        signContractRequest.setOffsetX(ConfigUtilAdapter.getInt("sign.request.offsetx", 0));
        signContractRequest.setOffsetY(ConfigUtilAdapter.getInt("sign.request.offsety", -60));
        LOGGER.info("signContractAsync recordKey:{}, signContractRequest:{}",context.getRecordBizKey(), JSON.toJSONString(signContractRequest));
        SignContractAsyncResult result = esignThriftClient.signContractAsync(signContractRequest);
        LOGGER.info("signContractAsync recordKey:{},signContractResult = {}",context.getRecordBizKey(), JSON.toJSONString(result));
        //1000成功，300039重复签署，两种状态视为正常流程
        if(!(result.getCode() == 1000 || result.getCode() == 300039)){
            LOGGER.error("异步签章请求失败，errorCode = {}, errorMsg = {}, recordkey = {}, taskId = {}",
                    result.getCode(), result.getMessage(), context.getEcontractRecordEntity().getRecordKey(), context.getTaskContext().getTaskId());
            throw new EcontractException(EcontractException.ASYNC_CALL_ERROR, "异步签章调用异常code");
        }
        return result;
    }

    public Map<String,String> warpSignContractCustomerNameAndIdCode(EcontractContext context) throws EcontractException {
        Map<String,String> resultMap = Maps.newHashMap();
        //获取当前需要待盖章的主体
        EstampSubject estampSubject = null;
        EstampSubjectInfoEnum estampSubjectInfoEnum = estampExecutor.getEstampSignKey(context);
        if(estampSubjectInfoEnum != null){
            if(EstampSubjectInfoEnum.staticEstampSubjectList.contains(estampSubjectInfoEnum)){
                estampSubject = (EstampSubject) SpringBeanUtil.getBean(STATIC_ESTAMP_SUBJECT_HANDLER);
            }else if(EstampSubjectInfoEnum.dynamicEstampSubjectList.contains(estampSubjectInfoEnum)){
                estampSubject = (EstampSubject) SpringBeanUtil.getBean(DYNAMIC_ESTAMP_SUBJECT_HANDLER);
            }
        }else {
            LOGGER.error("签章接口入参封装异常，recordkey = {}", context.getEcontractRecordEntity().getRecordKey());
            throw new EcontractException(EcontractException.ESIGN_PARAM_PARSE_ERROR, "签章接口入参封装异常");
        }
        EstampSubjectBo estampSubjectBo = estampSubject.getCaSubjectInfo(context);
        String customerName = estampSubjectBo.getCustomerName();
        String customerIdCode = estampSubjectBo.getCustomerIdCode();
        resultMap.put("customerName",customerName);
        resultMap.put("customerIdCode",customerIdCode);
        return resultMap;
    }

    /**
     * 合同归档，同步
     */
    public ArchiveContractResult archiveContract(String contractId) {
        LOGGER.info("archiveContract contractId = {}", contractId);
        ArchiveContractResult result = esignThriftClient.archiveContract(contractId);
        LOGGER.info("archiveContract result = {}", JSON.toJSONString(result));
        return result;
    }

    /**
     * 合同归档，异步
     */
    public ArchiveContractAsyncResult archiveContractAsync(String contractId) {
        LOGGER.info("archiveContractAsync contractId = {}", contractId);
        ArchiveContractAsyncResult result = esignThriftClient.archiveContractAsync(contractId);
        LOGGER.info("archiveContractAsync result = {}", JSON.toJSONString(result));
        return result;
    }

    /**
     * 下载合同
     */
    public GetContractResult downloadContract(String contractId) {
        LOGGER.info("downloadContract contractId = {}", contractId);
        GetContractResult result = esignThriftClient.downloadContract(contractId);
        return result;
    }
}

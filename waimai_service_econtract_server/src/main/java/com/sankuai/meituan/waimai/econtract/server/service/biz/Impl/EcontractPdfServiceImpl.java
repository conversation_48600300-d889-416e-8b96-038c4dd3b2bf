package com.sankuai.meituan.waimai.econtract.server.service.biz.Impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.Lists;

import com.itextpdf.text.DocumentException;
import com.itextpdf.text.PageSize;
import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractFtlTemplateEntity;
import com.sankuai.meituan.waimai.econtract.server.pdfEngine.bo.PdfStampMocker;
import com.sankuai.meituan.waimai.econtract.server.pdfEngine.bo.PdfStringTemplateManager;
import com.sankuai.meituan.waimai.econtract.server.pdfEngine.bo.PdfTemplateManager;
import com.sankuai.meituan.waimai.econtract.server.pdfEngine.bo.param.PdfBasicParam;
import com.sankuai.meituan.waimai.econtract.server.pdfEngine.bo.param.PdfManagerParam;
import com.sankuai.meituan.waimai.econtract.server.pdfEngine.bo.param.PdfTemplateParam;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractPdfService;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.Impl.CreatePdfExecutor;
import com.sankuai.meituan.waimai.econtract.server.template.config.service.EcontractTemplateVersionService;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException;
import com.sankuai.meituan.waimai.econtrct.client.template.config.domain.EcontractTemplateVersionBo;
import com.sankuai.meituan.waimai.econtrct.client.template.config.domain.EcontractTemplateVersionSimpleBo;
import com.sankuai.meituan.waimai.econtrct.client.template.config.exception.EcontractTemplateConfigException;
import com.sankuai.meituan.waimai.econtrct.client.template.config.service.EcontractTemplateConfigThriftService;
import com.sankuai.meituan.waimai.econtrct.client.util.MtCloudS3Util;

import freemarker.template.TemplateException;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.net.URISyntaxException;
import java.util.*;

import static com.sankuai.meituan.waimai.econtrct.client.constants.EcontractAPIResponseConstant.IO_ERROR;
import static com.sankuai.meituan.waimai.econtrct.client.constants.EcontractAPIResponseConstant.PARAM_ERROR;

@Slf4j
@Service
public class EcontractPdfServiceImpl implements EcontractPdfService {

    public String createPdf() {
        return "";
    }

    @Autowired
    private PdfStampMocker pdfStampMocker;

    @Resource
    private EcontractTemplateVersionService econtractTemplateVersionService;

    @Resource
    private EcontractFtlTemplateServiceImpl econtractFtlTemplateService;

    @Resource
    private CreatePdfExecutor createPdfExecutor;

    @Override
    public String createPdfByFtl(List<PdfContentInfoBo> pdfContentInfoBo)
        throws DocumentException, TemplateException, IOException {
        if (CollectionUtils.isEmpty(pdfContentInfoBo)) {
            return StringUtils.EMPTY;
        }

        List<PdfManagerParam> pageList = Lists.newArrayList();
        pdfContentInfoBo.forEach(page -> CollectionUtils.addIgnoreNull(pageList, genPdfManager(page)));
        return createPdfByStringManager(pageList);
    }

    @Override
    public String createPdfByFtlAndStamp(List<PdfContentInfoBo> pdfContentInfoBo) throws DocumentException, TemplateException, IOException, URISyntaxException {
        if (CollectionUtils.isEmpty(pdfContentInfoBo)) {
            return StringUtils.EMPTY;
        }

        List<PdfManagerParam> pageList = Lists.newArrayList();
        pdfContentInfoBo.forEach(page -> CollectionUtils.addIgnoreNull(pageList, genPdfManager(page)));
        return createPdfByStringManagerAndStamp(pageList);
    }

    @Override
    public String createPdf(List<PdfContentInfoBo> pdfContentInfoBo)
        throws DocumentException, TemplateException, IOException {
        if (CollectionUtils.isEmpty(pdfContentInfoBo)) {
            return StringUtils.EMPTY;
        }

        List<PdfManagerParam> pageList = Lists.newArrayList();
        pdfContentInfoBo.forEach(page -> CollectionUtils.addIgnoreNull(pageList, genPdfManager(page)));
        return createPdfByPdfManager(pageList);
    }

    @Override
    public String onlyCreatePdf(PdfContentInfoBo pdfContentInfoBo) throws EcontractException, TemplateException, DocumentException, IOException, EcontractTemplateConfigException {
        if (pdfContentInfoBo.getPdfTemplateId() != null && pdfContentInfoBo.getPdfTemplateId() > 0) {
            return onlyCreatePdfByTemplateId(pdfContentInfoBo);
        }

        if (!Strings.isEmpty(pdfContentInfoBo.getPdfTemplateName())) {
            return onlyCreatePdfByTemplateName(pdfContentInfoBo);
        }
        throw new EcontractException(IO_ERROR, "没有指定模板");
    }

    @Override
    public List<String> batchOnlyCreatePdf(List<PdfContentInfoBo> pdfBos) throws DocumentException, TemplateException, IOException, EcontractException, EcontractTemplateConfigException {
        // 批量创建合同pdf（使用同一个合同模版）
        if (CollectionUtils.isEmpty(pdfBos)) {
            return Collections.emptyList();
        }

        List<String> pdfUrls = new ArrayList<>();
        Map<Integer, String> key2FtlInfo = new HashMap<>();

        for (PdfContentInfoBo pdfBo : pdfBos) {
            String url;
            // 已有PDF链接的特殊处理
            if (StringUtils.isNotEmpty(pdfBo.getPdfUrl())) {
                url = createPdfExecutor.genePdfWithUrl(pdfBo.getPdfUrl());
            } else {
                Integer templateId = pdfBo.getPdfTemplateId();
                if (!key2FtlInfo.containsKey(templateId)) {
                    String ftlInfo = extractFtlInfoAndSetVertical(pdfBo);
                    key2FtlInfo.put(templateId, ftlInfo);
                }
                String ftlInfo = key2FtlInfo.get(templateId);

                pdfBo.setFtlInfo(ftlInfo);
                pdfBo.setVertical(true);
                url = createPdf(Collections.singletonList(pdfBo));
            }
            url = MccConfig.getPdfUrlPrefix() + url;
            pdfUrls.add(url);
        }

        log.info("batchOnlyCreatePdf pdfUrls:{}", JSONObject.toJSONString(pdfUrls));
        return pdfUrls;
    }


    /**
     * 提取ftlInfo，并且设置vertical属性，仅支持通过templateId获取模版信息
     * @param pdfContentInfoBo
     */
    private String extractFtlInfoAndSetVertical(PdfContentInfoBo pdfContentInfoBo) throws EcontractException, EcontractTemplateConfigException {
        // 如果已经有PDF链接直接跳过
        if (StringUtils.isNotEmpty(pdfContentInfoBo.getPdfUrl())) {
            return Strings.EMPTY;
        }

        if (pdfContentInfoBo.getPdfTemplateId() != null && pdfContentInfoBo.getPdfTemplateId() > 0) {

            return extractFtlInfoByTemplateId(pdfContentInfoBo);
        }

        throw new EcontractException(IO_ERROR, "没有指定模板");
    }

    private String extractFtlInfoByTemplateId(PdfContentInfoBo pdfContentInfoBo) throws EcontractTemplateConfigException {
        if (pdfContentInfoBo.getPdfTemplateVersion() == null || pdfContentInfoBo.getPdfTemplateVersion() <= 0) {
            EcontractTemplateVersionSimpleBo releasedVersion = econtractTemplateVersionService.getReleasedTemplateVersionSimple(pdfContentInfoBo.getPdfTemplateId());
            pdfContentInfoBo.setPdfTemplateVersion(releasedVersion != null ? releasedVersion.getVersion() : pdfContentInfoBo.getPdfTemplateVersion());
        }

        log.info("EcontractPdfServiceImpl#onlyCreatePdfByTemplateId, 模板id: {}, 版本号: {}", pdfContentInfoBo.getPdfTemplateId(), pdfContentInfoBo.getPdfTemplateVersion());
        EcontractTemplateVersionBo econtractTemplateVersionBo = econtractTemplateVersionService
                .getTemplateVersionAndCheck(pdfContentInfoBo.getPdfTemplateId(), pdfContentInfoBo.getPdfTemplateVersion());
        if (Objects.nonNull(econtractTemplateVersionBo)) {
            return econtractTemplateVersionBo.getTargetContent();
        }

        return Strings.EMPTY;
    }


    private String onlyCreatePdfByTemplateId(PdfContentInfoBo pdfContentInfoBo) throws EcontractTemplateConfigException, TemplateException, DocumentException, IOException, EcontractException {
        if (Objects.nonNull(pdfContentInfoBo.getPdfBizContent())
                && pdfContentInfoBo.getPdfBizContent().size() > MccConfig.getTheUpperLimitOfOnlyCreatePdf()) {
            throw new EcontractException(PARAM_ERROR, "门店数目超出上限");
        }
        if (pdfContentInfoBo.getPdfTemplateVersion() == null || pdfContentInfoBo.getPdfTemplateVersion() <= 0) {
            EcontractTemplateVersionSimpleBo releasedVersion = econtractTemplateVersionService.getReleasedTemplateVersionSimple(pdfContentInfoBo.getPdfTemplateId());
            pdfContentInfoBo.setPdfTemplateVersion(releasedVersion != null ? releasedVersion.getVersion() : pdfContentInfoBo.getPdfTemplateVersion());
        }
        log.info("EcontractPdfServiceImpl#onlyCreatePdfByTemplateId, 模板id: {}, 版本号: {}", pdfContentInfoBo.getPdfTemplateId(), pdfContentInfoBo.getPdfTemplateVersion());
        EcontractTemplateVersionBo econtractTemplateVersionBo = econtractTemplateVersionService
                .getTemplateVersionAndCheck(pdfContentInfoBo.getPdfTemplateId(), pdfContentInfoBo.getPdfTemplateVersion());
        pdfContentInfoBo.setFtlInfo(econtractTemplateVersionBo.getTargetContent());
        // 设置垂直显示
        pdfContentInfoBo.setVertical(true);
        return createPdf(Collections.singletonList(pdfContentInfoBo));
    }

    private String onlyCreatePdfByTemplateName(PdfContentInfoBo pdfContentInfoBo) throws TemplateException, DocumentException, IOException, EcontractException {
        EcontractFtlTemplateEntity templateEntity = econtractFtlTemplateService.selectByName(pdfContentInfoBo.getPdfTemplateName());
        if (templateEntity == null) {
            throw new EcontractException(IO_ERROR, "没有找到模板信息");
        }
        pdfContentInfoBo.setFtlInfo(templateEntity.getFtlTemplate());
        pdfContentInfoBo.setVertical(parseVertical(templateEntity.getOptionTemplate()));
        return createPdf(Collections.singletonList(pdfContentInfoBo));
    }

    private String createPdfByStringManager(List<PdfManagerParam> pageList) throws DocumentException, TemplateException, IOException {
        //1 生成pdf
        PdfStringTemplateManager manager = new PdfStringTemplateManager();
        byte[] bytes = manager.generate(pageList);

        //2 上传美团云, 返回url
        String url = DigestUtils.md5Hex(bytes);
        return MtCloudS3Util.uploadFileFromBytes(bytes, url + ".pdf");
    }

    private String createPdfByStringManagerAndStamp(List<PdfManagerParam> pageList) throws DocumentException, TemplateException, IOException, URISyntaxException {
        //1 生成pdf
        PdfStringTemplateManager manager = new PdfStringTemplateManager();
        byte[] bytes = manager.generate(pageList);

        //2 模拟盖章
        bytes = pdfStampMocker.stampMock(bytes);

        //3 上传美团云, 返回url
        String url = DigestUtils.md5Hex(bytes);
        return MtCloudS3Util.uploadFileFromBytes(bytes, url + ".pdf");
    }

    private String createPdfByPdfManager(List<PdfManagerParam> pageList) throws DocumentException, TemplateException, IOException {
        //1 生成pdf
        PdfTemplateManager manager = new PdfTemplateManager();
        byte[] bytes = manager.generate(pageList);

        //2 上传美团云, 返回url
        String url = DigestUtils.md5Hex(bytes);
        return MtCloudS3Util.uploadFileFromBytes(bytes, url + ".pdf");
    }

    private PdfManagerParam genPdfManager(PdfContentInfoBo pdfContentInfoBo) {
        //PDF参数信息
        PdfTemplateParam templateParam = new PdfTemplateParam(pdfContentInfoBo.getPdfTemplateName());
        templateParam.setFtlContent(pdfContentInfoBo.getFtlInfo());
        templateParam.put(PdfConstant.PDF_META_CONTENT, pdfContentInfoBo.getPdfMetaContent());
        templateParam.put(PdfConstant.PDF_BIZ_CONTENT, pdfContentInfoBo.getPdfBizContent());
        //PDF基本信息
        PdfBasicParam basicParam = new PdfBasicParam();
        basicParam.setRectangle(pdfContentInfoBo.getVertical()? PageSize.A4:PageSize.A4.rotate());
        //生成Pdf参数信息
        PdfManagerParam managerParam = new PdfManagerParam();
        managerParam.setTemplateParam(templateParam);
        managerParam.setBasicParam(basicParam);
        return managerParam;
    }

    @VisibleForTesting
    protected boolean parseVertical(String optionTemplate) {
        if (StringUtils.isBlank(optionTemplate)) {
            return Boolean.TRUE;
        }
        JSONObject jo = JSON.parseObject(optionTemplate);
        if (jo.get("vertical") == null) {
            return Boolean.TRUE;
        }
        return jo.getBoolean("vertical");
    }

}

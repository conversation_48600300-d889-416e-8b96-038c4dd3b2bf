package com.sankuai.meituan.waimai.econtract.server.adapter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.banma.business.poi.sparea.client.base.dto.IdentityDTO;
import com.sankuai.meituan.banma.business.poi.sparea.client.support.processsparea.request.SpAreaSignInfoRequest;
import com.sankuai.meituan.banma.business.poi.sparea.client.support.processsparea.response.SpAreaSignInfoResponse;
import com.sankuai.meituan.banma.business.poi.sparea.client.support.processsparea.service.BmOpenSignSpAreaThriftService;
import com.sankuai.meituan.banma.business.poi.sparea.client.support.sys.service.GrayControlThriftService;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.delivery.batch.EcontractBatchDeliveryAreaInfoBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import com.sankuai.meituan.banma.poi.sparea.thrift.param.common.Identity;
import com.sankuai.meituan.banma.poi.sparea.thrift.exception.BmSpareaException;
import com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException;
import com.sankuai.meituan.banma.poi.sparea.thrift.service.BmOpenSpAreaSignThriftService;
import com.sankuai.meituan.banma.poi.sparea.thrift.param.sign.BmOpenSpAreaSignInfoRequest;
import com.sankuai.meituan.banma.poi.sparea.thrift.param.sign.BmOpenSpAreaSignInfoResponse;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractErrorCode;
import com.sankuai.meituan.banma.poi.sparea.thrift.constants.BmSpAreaCustomerSourceConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.delivery.single.EcontractSingleDeliveryAreaInfoBo;

/**
 * 配送范围相关接口
 * Created by lixuepeng on 2023/6/5
 */
@Slf4j
@Service
public class BmOpenSpAreaAdapter {

    /**
     * 范围签约旧服务接口
     */
    @Autowired
    private BmOpenSpAreaSignThriftService bmOpenSpAreaSignThriftService;

    /**
     * 范围签约新服务接口
     */
    @Autowired
    private BmOpenSignSpAreaThriftService bmOpenSignSpAreaThriftService;

    /**
     * 范围签约新服务接口灰度控制
     */
    @Autowired
    private GrayControlThriftService grayControlThriftService;

    /**
     * 灰度控制静态入参，用于标记灰度批次
     */
    public static final String GRAY_VERSION = "v1.1_getSignInfo";

    public String queryAreaSignDataWithRetry(Map<Long, Long> wmPoiAndBizMap) throws EcontractException {
        if (MapUtils.isEmpty(wmPoiAndBizMap)) {
            log.warn("#queryAreaSignDataWithRetry wmPoiAndBizMap is empty!");
            return null;
        }
        int retryTimes = MccConfig.queryEcontractDataRetryTimes();
        for (int i = 0; i < retryTimes; i++) {
            try {
                Map<Long, EcontractSingleDeliveryAreaInfoBo> areaInfoBoMap = getSignInfo(wmPoiAndBizMap);
                if (MapUtils.isEmpty(areaInfoBoMap)) {
                    return null;
                }
                EcontractBatchDeliveryAreaInfoBo econtractBatchDeliveryAreaInfoBo = new EcontractBatchDeliveryAreaInfoBo();
                econtractBatchDeliveryAreaInfoBo.setBatchAreaInfoMap(areaInfoBoMap);
                String result = JSON.toJSONString(econtractBatchDeliveryAreaInfoBo);
                log.info("#queryAreaSignDataWithRetry result:{}",result);
                return result;
            } catch (BmSpareaException e) {
                log.error("#queryAreaSignDataWithRetry 获取范围签约信息异常 wmPoiAndBizMap:{} msg:{}", wmPoiAndBizMap, e.getMsg());
                // 系统异常则重试，否则直接抛出异常详情
                if (e.getCode() != BmSpareaException.SYSTEM_EXCEPTION) {
                    throw new EcontractException(EcontractErrorCode.BUSINESS_ERROR, e.getMsg());
                }
            } catch (com.sankuai.meituan.banma.business.poi.sparea.client.base.exception.BmSpareaException e) {
                log.error("#queryAreaSignDataWithRetry 新接口获取范围签约信息异常 wmPoiAndBizMap:{} msg:{}", wmPoiAndBizMap, e.getMsg());
                // 系统异常则重试，否则直接抛出异常详情
                if (e.getCode() != BmSpareaException.SYSTEM_EXCEPTION) {
                    throw new EcontractException(EcontractErrorCode.BUSINESS_ERROR, e.getMsg());
                }
            } catch (Exception e) {
                log.error("#queryAreaSignDataWithRetry 获取范围签约信息异常 wmPoiAndBizMap:{}", wmPoiAndBizMap, e);
                throw new EcontractException(EcontractErrorCode.BUSINESS_ERROR, "获取配送范围签约数据异常");
            }
        }
        return null;
    }

    /**
     * 获取新接口签约范围数据
     *
     * @param wmPoiAndBizMap
     * @param saleChannelType
     * @return
     * @throws EcontractException
     */
    public String queryNewAreaSignDataWithRetry(Map<Long, Long> wmPoiAndBizMap, Integer saleChannelType) throws EcontractException {
        if (MapUtils.isEmpty(wmPoiAndBizMap)) {
            log.warn("#queryNewAreaSignDataWithRetry wmPoiAndBizMap is empty!");
            return null;
        }
        int retryTimes = MccConfig.queryEcontractDataRetryTimes();
        for (int i = 0; i < retryTimes; i++) {
            try {
                Map<Long, EcontractSingleDeliveryAreaInfoBo> areaInfoBoMap = callNewGetSignInfo(wmPoiAndBizMap, saleChannelType);
                if (MapUtils.isEmpty(areaInfoBoMap)) {
                    return null;
                }
                EcontractBatchDeliveryAreaInfoBo econtractBatchDeliveryAreaInfoBo = new EcontractBatchDeliveryAreaInfoBo();
                econtractBatchDeliveryAreaInfoBo.setBatchAreaInfoMap(areaInfoBoMap);
                String result = JSON.toJSONString(econtractBatchDeliveryAreaInfoBo);
                log.info("#queryNewAreaSignDataWithRetry result:{}",result);
                return result;
            } catch (com.sankuai.meituan.banma.business.poi.sparea.client.base.exception.BmSpareaException e) {
                log.error("#queryNewAreaSignDataWithRetry 新接口获取范围签约信息异常 wmPoiAndBizMap:{} msg:{}", wmPoiAndBizMap, e.getMsg());
                // 系统异常则重试，否则直接抛出异常详情
                if (e.getCode() != BmSpareaException.SYSTEM_EXCEPTION) {
                    throw new EcontractException(EcontractErrorCode.BUSINESS_ERROR, e.getMsg());
                }
            } catch (Exception e) {
                log.error("#queryNewAreaSignDataWithRetry 获取范围签约信息异常 wmPoiAndBizMap:{}", wmPoiAndBizMap, e);
                throw new EcontractException(EcontractErrorCode.BUSINESS_ERROR, "获取配送范围签约数据异常");
            }
        }
        return null;
    }

    /**
     * 灰度迁移签约范围的新接口
     *
     * @param wmPoiAndBizMap
     * @return
     * @throws BmSpareaException
     */
    private Map<Long, EcontractSingleDeliveryAreaInfoBo> getSignInfo(Map<Long, Long> wmPoiAndBizMap) throws BmSpareaException, com.sankuai.meituan.banma.business.poi.sparea.client.base.exception.BmSpareaException {
        log.info("#queryAreaSignDataWithRetry wmPoiAndBizMap is {}.", JSON.toJSONString(wmPoiAndBizMap));
        // 根据入参的第一个门店判断是否调用新接口
        if (isGray(wmPoiAndBizMap.keySet().stream().findFirst().orElse(-1L))) {
            log.info("#queryAreaSignDataWithRetry grayControlThriftService.eventApiNeedGray return true");
            return callNewGetSignInfo(wmPoiAndBizMap, BmSpAreaCustomerSourceConstants.WAI_MAI);
        }
        return callOldGetSignInfo(wmPoiAndBizMap);
    }

    /**
     * 旧签约范围接口调用, copy原始代码
     *
     * @param wmPoiAndBizMap
     * @return
     * @throws BmSpareaException
     */
    private Map<Long, EcontractSingleDeliveryAreaInfoBo> callOldGetSignInfo(Map<Long, Long> wmPoiAndBizMap) throws BmSpareaException {
        List<BmOpenSpAreaSignInfoRequest> requestList = new ArrayList<>();
        for (Map.Entry<Long, Long> entry : wmPoiAndBizMap.entrySet()) {
            BmOpenSpAreaSignInfoRequest request = new BmOpenSpAreaSignInfoRequest();
            request.setPoiId(entry.getKey());
            request.setSessionId(entry.getValue());
            requestList.add(request);
        }
        Identity identity = Identity.builder().customerSourceType(BmSpAreaCustomerSourceConstants.WAI_MAI).build();
        List<BmOpenSpAreaSignInfoResponse> responseList = bmOpenSpAreaSignThriftService.getSignInfo(requestList, identity);
        if (CollectionUtils.isEmpty(responseList)) {
            return null;
        }
        log.info("#queryAreaSignDataWithRetry bmOpenSpAreaSignThriftService.getSignInfo response:{}",JSON.toJSONString(responseList));
        Map<Long, EcontractSingleDeliveryAreaInfoBo> areaInfoBoMap = new HashMap<>();
        for (BmOpenSpAreaSignInfoResponse response : responseList) {
            EcontractSingleDeliveryAreaInfoBo areaInfoBo = JSON.parseObject(response.getSignInfo(), EcontractSingleDeliveryAreaInfoBo.class);
            areaInfoBoMap.put(response.getPoiId(), areaInfoBo);
        }
        return areaInfoBoMap;
    }

    /**
     * 新签约范围接口调用
     *
     * @param wmPoiAndBizMap
     * @return
     * @throws BmSpareaException
     */
    private Map<Long, EcontractSingleDeliveryAreaInfoBo> callNewGetSignInfo(Map<Long, Long> wmPoiAndBizMap, Integer saleChannelType) throws com.sankuai.meituan.banma.business.poi.sparea.client.base.exception.BmSpareaException {
        List<SpAreaSignInfoRequest> requestList = wmPoiAndBizMap.entrySet().stream()
                .map(e -> buildSpAreaSignInfoRequest(e.getKey(), e.getValue()))
                .collect(Collectors.toList());
        IdentityDTO identity = buildIdentityDTO(saleChannelType);
        List<SpAreaSignInfoResponse> responseList = bmOpenSignSpAreaThriftService.getSignInfo(requestList, identity);
        if (CollectionUtils.isEmpty(responseList)) {
            log.info("#queryAreaSignDataWithRetry bmOpenSignSpAreaThriftService.getSignInfo response is empty.");
            return null;
        }
        log.info("#queryAreaSignDataWithRetry bmOpenSignSpAreaThriftService.getSignInfo response:{}", JSON.toJSONString(responseList));
        return responseList.stream()
                .filter(e -> StringUtils.isNotBlank(e.getSignInfo()))
                .collect(Collectors.toMap(SpAreaSignInfoResponse::getPoiId,
                        e -> JSON.parseObject(e.getSignInfo(), EcontractSingleDeliveryAreaInfoBo.class),
                        (k1, k2) -> k1));
    }

    /**
     * 构造SpAreaSignInfoRequest
     *
     * @param poiId
     * @param sessinId
     * @return
     */
    private SpAreaSignInfoRequest buildSpAreaSignInfoRequest(Long poiId, Long sessinId) {
        SpAreaSignInfoRequest request = new SpAreaSignInfoRequest();
        request.setPoiId(poiId);
        request.setSessionId(sessinId);
        return request;
    }

    /**
     * 构建入参identityDTO
     *
     * @param identity
     * @return
     */
    private IdentityDTO buildIdentityDTO(Integer identity) {
        IdentityDTO identityDTO = new IdentityDTO();
        identityDTO.setCustomerSourceType(identity);
        return identityDTO;
    }

    /**
     * 判断是否命中灰度
     *
     * @param poiId
     * @return
     */
    private boolean isGray(Long poiId) {
        try {
            return grayControlThriftService.eventApiNeedGray(poiId, GRAY_VERSION);
        } catch (Exception e) {
            log.error("BmOpenSpAreaAdapter grayControlThriftService exception, ", e);
        }
        return false;
    }
}

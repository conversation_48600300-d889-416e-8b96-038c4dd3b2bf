package com.sankuai.meituan.waimai.econtract.server.service.cache;

import com.alibaba.fastjson.JSON;
import com.dianping.squirrel.client.StoreKey;
import com.sankuai.meituan.waimai.econtract.server.utils.RedisKvUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class RedisKvService {

    @Autowired
    private RedisKvUtil redisKvUtil;

    private final String CONFIRM_SIGN_KEY_PREFIX = "econtract_confirm_cancel_sign";

    public boolean tryLock(StoreKey key, int expireSeconds) {
        try {
            log.debug("RedisKvService try lock key: {}", JSON.toJSONString(key));
            Boolean result = redisKvUtil.retrySetnx(key, Thread.currentThread().getName(), expireSeconds);
            if (result) {
                log.info("RedisKvService get lock, key: {}", JSON.toJSONString(key));
                return Boolean.TRUE;
            }
            Object desc = redisKvUtil.get(key);
            log.info("key: {} locked by another business: {}", JSON.toJSONString(key), desc.toString());
        } catch (Exception e) {
            log.error("RedisKvService#尝试获取锁发生异常", e);
        }
        return Boolean.FALSE;
    }

    public boolean tryLock(StoreKey key, Object value, int expireSeconds) {
        try {
            log.info("RedisKvService try lock key: {}, value:{},expireSeconds:{}", JSON.toJSONString(key),
                    JSON.toJSONString(value), expireSeconds);
            Boolean result = redisKvUtil.retrySetnx(key, value, expireSeconds);
            if (result) {
                log.info("RedisKvService get lock, key: {}, value:{}", JSON.toJSONString(key), JSON.toJSONString(value));
                return Boolean.TRUE;
            }
            Object desc = redisKvUtil.get(key);
            log.info("key: {} locked by another business: {}", JSON.toJSONString(key), desc.toString());
        } catch (Exception e) {
            log.error("RedisKvService#尝试获取锁发生异常", e);
        }
        return Boolean.FALSE;
    }

    public void unLock(StoreKey key) {
        try {
            redisKvUtil.delete(key);
            log.info("RedisKvService release lock, key: {}", key);
        } catch (Exception e) {
            log.error("RedisKvService#尝试释放锁发生异常", e);
        }
    }

    public boolean isLockedByOther(StoreKey key){
        boolean isLocked = redisKvUtil.exists(key);
        log.info("key: {} is locked : {}", JSON.toJSONString(key), isLocked);
        if(isLocked){
            log.info("key: {} locked by other", JSON.toJSONString(key));
            return true;
        }
        return false;
    }

    public Long decrBy(StoreKey key, Long amount) {
        return redisKvUtil.decrBy(key, amount);
    }

    public StoreKey genConfirmSignKey(String bizId){
        return new StoreKey(CONFIRM_SIGN_KEY_PREFIX, bizId);
    }

}

package com.sankuai.meituan.waimai.econtract.server.crane;

import com.alibaba.fastjson.JSON;
import com.cip.crane.client.spring.annotation.Crane;
import com.google.common.collect.Lists;
import com.sankuai.meituan.common.time.TimeUtil;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractSignRecordBatchEntity;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractSignRecordBatchService;
import com.sankuai.meituan.waimai.econtract.server.service.engine.template.manager.TemplateManager;
import com.sankuai.meituan.waimai.econtrct.client.constants.BatchSignBizLineEnum;
import com.sankuai.meituan.waimai.econtrct.client.constants.BatchSignStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.service.WmEcontractSignThriftService;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2022-03-30 19:54
 * Email: <EMAIL>
 * Desc:
 */
@Slf4j
@Component
public class WmManualPackSmsPushTask {

    @Autowired
    private EcontractSignRecordBatchService econtractSignRecordBatchService;

    @Autowired
    private WmEcontractSignThriftService wmEcontractSignThriftService;

    @Autowired
    private TemplateManager templateManager;

    @Crane("wm.manual.pack.sms.push")
    public void pushWmManualPackSmsTask() {
        long startTime = TimeUtil.unixtime();
        long intervalTime = com.sankuai.meituan.util.ConfigUtilAdapter.getInt("wm_send_sms_time_before", 60);
        long queryTime = DateUtil.unixTime() - intervalTime;
        log.info("pushWmManualPackSmsTask#执行发送短信开始，任务开始时间:{}，任务查询的记录生成时间:{}", startTime, queryTime);
        Integer offset = 0;
        Integer limit = ConfigUtilAdapter.getInt("pushWmManualPackSmsTask_limit",100);
        List<EcontractSignRecordBatchEntity> recordBatchEntities = Lists.newArrayList();
        List<EcontractSignRecordBatchEntity> partationList = econtractSignRecordBatchService.queryBatchEntityListByBizlineAndStatusPartition(
                BatchSignBizLineEnum.WM.getCode(), BatchSignStatusEnum.COMPLETE_BATCH.getCode(), offset, limit, queryTime);
        if (CollectionUtils.isEmpty(partationList)) {
            log.info("外卖业务线当前无发起完成短信未发送的任务");
            return;
        }
        List<Long> partationIdList = partationList.stream().map(EcontractSignRecordBatchEntity::getId).collect(Collectors.toList());
        log.info("pushWmManualPackSmsTask#partationIdList:{}",JSON.toJSONString(partationIdList));
        recordBatchEntities.addAll(partationList);
        while(partationList.size() == limit){
            offset += limit;
            partationList = econtractSignRecordBatchService.queryBatchEntityListByBizlineAndStatusPartition(
                    BatchSignBizLineEnum.WM.getCode(), BatchSignStatusEnum.COMPLETE_BATCH.getCode(), offset, limit, queryTime);
            recordBatchEntities.addAll(partationList);
        }
        List<Long> recordBatchEntitieIdList = recordBatchEntities.stream().map(EcontractSignRecordBatchEntity::getId).collect(Collectors.toList());
        log.info("pushWmManualPackSmsTask#recordBatchEntitieIdList:{}",JSON.toJSONString(recordBatchEntitieIdList));
        for (EcontractSignRecordBatchEntity recordBatch : recordBatchEntities) {
            //签约系统的packid
            try {
                Long bizId = recordBatch.getBizId();
                List<String> recordKeyList = wmEcontractSignThriftService.queryRecordKeyBySignPackIdOrderByPriority(bizId);
                String recordKey = recordKeyList.get(0);
                List<String> phoneList = templateManager.doRetrySmsForBatch(recordKey);
                if(CollectionUtils.isNotEmpty(phoneList)){
                    log.info("外卖业务发送打包签约短信发送成功，bizId:{}，mobile:{}", recordBatch.getBizId(), JSON.toJSONString(phoneList));
                }
            } catch (Exception e) {
                log.error("外卖业务发送打包签约短信失败，外卖系统bizId:{}", recordBatch.getBizId(), e);
            }
        }
    }
}

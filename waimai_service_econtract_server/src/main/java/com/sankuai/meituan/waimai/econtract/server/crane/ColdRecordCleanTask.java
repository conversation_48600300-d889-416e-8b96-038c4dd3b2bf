package com.sankuai.meituan.waimai.econtract.server.crane;

import java.util.Arrays;
import java.util.Calendar;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.cip.crane.client.spring.annotation.Crane;
import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractRecordContextMapper;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordContextEntity;
import com.sankuai.meituan.waimai.util.DateUtil;

/**
 * 冷数据表-历史数据加密定时job
 *
 * Created by lixuepeng on 2021/10/26
 */
@Component
public class ColdRecordCleanTask {

    private static final Logger LOGGER = LoggerFactory.getLogger(ColdRecordCleanTask.class);

    @Autowired
    private EcontractRecordContextMapper    econtractRecordContextMapper;

    private static Calendar calendar = Calendar.getInstance();

    @Crane("cold.record.data.clean")
    public void handleColdRecordDataClean(String specialLastIdAndSize) {
        try {
            List<String> inputStrList = Arrays.asList(specialLastIdAndSize.split("#"));
            String specialLastIdStr = inputStrList.get(0);
            String perPageSizeStr = inputStrList.get(1);

            int size = Integer.valueOf(perPageSizeStr);
            long lastId = Long.valueOf(specialLastIdStr);

            LOGGER.info("handleColdRecordDataClean startTime:{}", DateUtil.unixTime());

            //获取明文数据
            List<EcontractRecordContextEntity> recordEntities = econtractRecordContextMapper.queryColdEntityListWithLabel4Encryption(lastId, size);

            while (CollectionUtils.isNotEmpty(recordEntities)) {
                //历史数据明文下线
                if (MccConfig.isRemoveOriginalRecord()) {
                    List<Integer> ids = recordEntities.stream().map(EcontractRecordContextEntity::getId).collect(Collectors.toList());
                    econtractRecordContextMapper.batchUpdateOriginalRecordByIds(ids);
                }
                //翻页获取下一页信息
                Thread.sleep(100);//延迟100ms再执行下一批
                lastId = recordEntities.get(recordEntities.size() -1).getId();
                LOGGER.info("handleColdRecordDataClean subLastId:{}", lastId);
                recordEntities = econtractRecordContextMapper.queryColdEntityListWithLabel4Encryption(lastId, size);
            }
            LOGGER.info("handleColdRecordDataClean endTime:{} lastId:{}", DateUtil.unixTime(), lastId);
        } catch (Exception e) {
            LOGGER.error("handleColdRecordDataClean 执行明文数据下线任务异常", e);
        }
    }
}

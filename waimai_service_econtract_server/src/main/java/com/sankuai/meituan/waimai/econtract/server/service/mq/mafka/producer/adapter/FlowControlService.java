package com.sankuai.meituan.waimai.econtract.server.service.mq.mafka.producer.adapter;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.constants.MqConstant;
import com.sankuai.meituan.waimai.econtract.server.service.mq.TaskMsg;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class FlowControlService {

    public boolean isRouteToSlowQueue(TaskMsg msg) {
        log.debug("isRouteToSlowQueue#msg:{}", JSON.toJSONString(msg));
        //上线过程中兼容QueueName无赋值的场景，默认走老队列即快队列
        if(StringUtils.isEmpty(msg.getQueueName())){
            return false;
        }
        return msg.getQueueName().equals(MqConstant.SLOW_QUEUE);
    }
}

package com.sankuai.meituan.waimai.econtract.server.adapter.hailuo;

import com.meituan.it.contract.platform.model.request.template.SyncHistoryTemplateReq;
import com.meituan.it.contract.platform.model.request.template.TemplateWithdrawReq;
import com.meituan.it.contract.platform.model.request.template.UploadCodingAttachmentReq;
import com.meituan.it.contract.platform.model.response.template.SyncHistoryTemplateResp;
import com.meituan.it.contract.platform.model.response.template.TemplateWithdrawResp;
import com.meituan.it.contract.platform.model.response.template.UploadCodingAttachmentResp;
import com.sankuai.meituan.waimai.econtract.server.utils.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.meituan.it.contract.platform.service.template.ContractTemplateOperateThriftService;

import javax.annotation.Resource;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2025/4/8 19:31
 */
@Service
@Slf4j
public class ContractTemplateOperateThriftServiceAdapter {

    @Resource
    private ContractTemplateOperateThriftService contractTemplateOperateThriftService;

    public UploadCodingAttachmentResp notifyHaiLuoAudit(UploadCodingAttachmentReq req) {
        log.info("ContractTemplateOperateThriftServiceAdapter#notifyHaiLuoAudit, req:{}", JacksonUtil.writeAsJsonStr(req));
        UploadCodingAttachmentResp resp = contractTemplateOperateThriftService.uploadCodingAttachment(req);
        log.info("ContractTemplateOperateThriftServiceAdapter#notifyHaiLuoAudit, resp:{}", JacksonUtil.writeAsJsonStr(resp));
        return resp;
    }

    public TemplateWithdrawResp cancelAudit(TemplateWithdrawReq req) {
        log.info("ContractTemplateOperateThriftServiceAdapter#cancelAudit, req:{}", JacksonUtil.writeAsJsonStr(req));
        TemplateWithdrawResp resp = contractTemplateOperateThriftService.templateWithdraw(req);
        log.info("ContractTemplateOperateThriftServiceAdapter#cancelAudit, resp:{}",  JacksonUtil.writeAsJsonStr(resp));
        return resp;
    }
    
    
    public SyncHistoryTemplateResp getTemplateInfo(SyncHistoryTemplateReq req) {
        if (req == null) {
            return null;
        }
        log.info("ContractTemplateOperateThriftServiceAdapter#getTemplateInfo, req:{}", JacksonUtil.writeAsJsonStr(req));
        SyncHistoryTemplateResp resp = contractTemplateOperateThriftService.syncHistoryTemplate(req);
        log.info("ContractTemplateOperateThriftServiceAdapter#getTemplateInfo, resp:{}",  JacksonUtil.writeAsJsonStr(resp));
        return resp;
    }
    
    
    
}

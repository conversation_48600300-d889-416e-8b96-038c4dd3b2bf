package com.sankuai.meituan.waimai.econtract.server.service.mq.mafka.handler;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import com.sankuai.meituan.waimai.econtract.server.constants.MessageStatusEnum;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractRecordContextMapper;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractBigMessageEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordContextEntity;
import com.sankuai.meituan.waimai.econtract.server.service.biz.Impl.EcontractBigContextManageService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.Impl.EcontractBigMessageServiceImpl;
import com.sankuai.meituan.waimai.econtract.server.service.biz.Impl.EcontractBigRecordParseService;
import com.sankuai.meituan.waimai.econtract.server.service.mq.TaskMsg;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MessageConsumerHandler {

    @Resource
    private EcontractBigMessageServiceImpl econtractBigMessageService;

    @Autowired
    private EcontractBigRecordParseService econtractBigRecordParseService;

    @Autowired
    private EcontractRecordContextMapper econtractRecordContextMapper;

    public void handler(TaskMsg obj) throws Exception {
        if (obj == null || obj.getContextId() == null) {
            log.info("MessageConsumerHandler#handler, 简单消息体，无需处理！");
            return;
        }
        Thread.sleep(MccConfig.CONSUMER_SLEEP_TIME);
        Long contextId = obj.getContextId();
        EcontractBigMessageEntity entity = null;
        for (int i = 0; i < MccConfig.CONSUMER_FAIL_READ_MASTER_COUNT; i++) {
            entity = econtractBigMessageService.selectById(contextId);
            if (entity != null) {
                log.info("第 {} 次查询，context id = {} 从库查到数据！", i, entity.getId());
                break;
            }
            Thread.sleep(MccConfig.CONSUMER_SLEEP_TIME);
        }
        if (entity == null) {
            log.warn("从库读取context记录失败！准备从主库读,context_id = {}", obj.getContextId());
            entity = econtractBigMessageService.selectByPrimaryKeyMaster(contextId);
        }

        obj.setMessageBody(queryFinalContextStr(entity));
    }

    public void updateMessageStatus(TaskMsg obj,Integer status) {
        if (obj == null || obj.getContextId() == null) {
            log.info("MessageConsumerHandler#updateMessageStatus, 简单消息体，无需处理！");
            return;
        }
        Long contextId = obj.getContextId();
        econtractBigMessageService.updateStatus(status, contextId);
    }

    private String queryFinalContextStr(EcontractBigMessageEntity entity) {
        EcontractContext context = JSON.parseObject(entity.getContext(), EcontractContext.class);
        EcontractRecordContextEntity contextEntity = econtractBigRecordParseService.selectByRecordId(context.getEcontractRecordEntity().getId());
        if (contextEntity == null || StringUtils.isEmpty(contextEntity.getContext())) {
            log.info("主从延迟导致读取冷数据消息为空 entity:{}", JSON.toJSONString(entity));
            contextEntity = econtractBigRecordParseService.selectByRecordIdWithRT(context.getEcontractRecordEntity().getId());
        }
        EcontractContext coldContext = JSON.parseObject(contextEntity.getContext(), EcontractContext.class);
        context.setEcontractUserEntity(coldContext.getEcontractUserEntity());
        context.setFlowList(coldContext.getFlowList());
        context.setStageInfoBoList(coldContext.getStageInfoBoList());
        context.setStageBatchInfoBoList(coldContext.getStageBatchInfoBoList());
        return JSON.toJSONString(context);
    }
}

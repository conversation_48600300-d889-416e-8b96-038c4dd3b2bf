package com.sankuai.meituan.waimai.econtract.server.service.api;

import com.alibaba.fastjson.JSON;
import com.meituan.sankuai.bsi.esign.common.model.entity.result.GetContractResult;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.econtract.server.adapter.EsignClient;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractSignBatchEventEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractSignRecordBatchEntity;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractSignRecordBatchEventService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractSignRecordBatchService;
import com.sankuai.meituan.waimai.econtract.server.service.sms.SendMessageService;
import com.sankuai.meituan.waimai.econtract.server.utils.UrlConvetUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.BatchSignBizLineEnum;
import com.sankuai.meituan.waimai.econtrct.client.domain.base.BoolResult;
import com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException;
import com.sankuai.meituan.waimai.econtrct.client.service.TestThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service
@Slf4j
public class TestThriftServiceServiceImpl implements TestThriftService {

    @Autowired
    private SendMessageService sendMessageService;

    @Autowired
    private EcontractSignRecordBatchEventService econtractSignRecordBatchEventService;
    @Autowired
    private EcontractSignRecordBatchService econtractSignRecordBatchService;

    @Autowired
    private EsignClient esignClient;

    @Override
    public void smsTest(String mobilePhone,String templateId,Map<String, String> pair) {
        sendMessageService.sms(mobilePhone, templateId, pair);
    }

    @Override
    public void phfSmsTest(Integer queryTime) throws EcontractException {
        log.info("批量签约发送短信入参：queryTime={}", queryTime);
        if (queryTime == null) {
            log.info("批量签约发送短信入参为空");
        }
        List<EcontractSignBatchEventEntity> eventEntities = econtractSignRecordBatchEventService.getUnExecuteEventTask(queryTime);
        if (CollectionUtils.isEmpty(eventEntities)) {
            log.info("批量签约发送短信未查询到需要发送短信的事件：queryTime={}", queryTime);
        }
        //过滤出拼好饭业务线
        log.info("batchSignSendSms#eventEntities={}", JSON.toJSONString(eventEntities));
        List<Integer> batchIdList = eventEntities.stream().map(EcontractSignBatchEventEntity::getBatchId).collect(Collectors.toList());
        log.info("batchSignSendSms#batchIdList={}", JSON.toJSONString(batchIdList));
        List<EcontractSignRecordBatchEntity> recordBatchEntities = econtractSignRecordBatchService.queryBatchEntityListByIds(batchIdList);
        log.info("batchSignSendSms#recordBatchEntities={}", JSON.toJSONString(recordBatchEntities));
        List<Long> phfRecordBatchEntitieIdList = recordBatchEntities.stream()
                .filter(record->record.getBizLine() == BatchSignBizLineEnum.PHF.getCode())
                .map(EcontractSignRecordBatchEntity::getId)
                .collect(Collectors.toList());
        log.info("batchSignSendSms#phfRecordBatchEntitieIdList={}", JSON.toJSONString(phfRecordBatchEntitieIdList));
        eventEntities = eventEntities.stream().filter(event -> phfRecordBatchEntitieIdList.contains(event.getBatchId().longValue())).collect(Collectors.toList());
        log.info("batchSignSendSms#eventEntities:{}", JSON.toJSONString(eventEntities));
    }

    @Override
    public String downloadPdfOperateTool(String contractId) throws EcontractException {
        log.info("downloadPdfOperateTool contractId={}", contractId);
        GetContractResult getContractResult = esignClient.downloadContract(contractId);
        String stampUrl = UrlConvetUtil.changeEstampUrlOperateTool(getContractResult.getContract());
        String domain = ConfigUtilAdapter.getString("downloadPdfOperateTool_domain", "econtract.meituan.com");
        return domain + stampUrl;
    }


}

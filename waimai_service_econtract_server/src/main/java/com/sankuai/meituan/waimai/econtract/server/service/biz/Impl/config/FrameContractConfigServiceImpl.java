package com.sankuai.meituan.waimai.econtract.server.service.biz.Impl.config;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.sankuai.meituan.uac.sdk.entity.UacRoleEntity;
import com.sankuai.meituan.waimai.econtract.server.adapter.UacAuthRemoteServiceAdapter;
import com.sankuai.meituan.waimai.econtract.server.adapter.UacRoleRemoteServiceAdapter;
import com.sankuai.meituan.waimai.econtract.server.adapter.WmCustomerFrameContractConfigThriftServiceAdapter;
import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import com.sankuai.meituan.waimai.econtract.server.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractEntity;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractUserService;
import com.sankuai.meituan.waimai.econtract.server.service.config.EcontractSignPageService;
import com.sankuai.meituan.waimai.econtract.server.service.dto.config.CustomerTypeInfoDTO;
import com.sankuai.meituan.waimai.econtract.server.service.dto.config.framecontract.*;
import com.sankuai.meituan.waimai.econtract.server.utils.ContractContentTemplateUtil;
import com.sankuai.meituan.waimai.econtract.server.utils.JacksonUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.config.EstmapPartEnum;
import com.sankuai.meituan.waimai.econtrct.client.constants.config.FrameContractConfigStatusEnum;
import com.sankuai.meituan.waimai.econtract.server.constants.config.ConfigContractCommonEstampEstampEnum;
import com.sankuai.meituan.waimai.econtract.server.constants.config.LockKeyConstant;
import com.sankuai.meituan.waimai.econtract.server.entity.config.FrameContractConfigInfoPo;
import com.sankuai.meituan.waimai.econtract.server.service.biz.Impl.FrameContractConfigService;
import com.sankuai.meituan.waimai.econtract.server.service.config.EcontractConfigCommonService;
import com.sankuai.meituan.waimai.econtract.server.service.config.FrameContractConfigInfoBaseService;
import com.sankuai.meituan.waimai.econtract.server.template.config.service.EcontractTemplateBaseService;
import com.sankuai.meituan.waimai.econtract.server.utils.TairLocker;
import com.sankuai.meituan.waimai.econtrct.client.constants.config.PartEstampOrderEnum;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.biz.EcontractTemplateBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.biz.EcontractUserBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.common.PageInfoDTO;
import com.sankuai.meituan.waimai.econtrct.client.domain.common.PageInfoSearchParam;
import com.sankuai.meituan.waimai.econtrct.client.domain.config.econtracttemplate.EcontractTemplateBaseInfo;
import com.sankuai.meituan.waimai.econtrct.client.domain.config.framecontract.*;
import com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException;
import com.sankuai.meituan.waimai.econtrct.client.template.config.domain.EcontractTemplateBaseBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.elasticsearch.common.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/5/17 17:15
 */
@Service
@Slf4j
public class FrameContractConfigServiceImpl implements FrameContractConfigService {

    @Resource
    private UacAuthRemoteServiceAdapter uacAuthRemoteServiceAdapter;

    @Resource
    private FrameContractConfigInfoBaseService frameContractConfigInfoBaseService;

    @Resource
    private EcontractTemplateBaseService econtractTemplateBaseService;

    @Resource
    private EcontractConfigCommonService econtractConfigCommonService;

    @Resource
    private EcontractService econtractService;

    @Resource(name = "econtractTairLocker")
    private TairLocker tairLocker;

    @Resource
    private WmCustomerFrameContractConfigThriftServiceAdapter wmFrameContractConfigThriftServiceAdapter;

    @Resource
    private UacRoleRemoteServiceAdapter uacRoleRemoteServiceAdapter;

    @Resource
    private EcontractUserService econtractUserService;

    @Resource
    private EcontractSignPageService signPageService;

    private static final Integer DEFAULT_PAGE_NUMBER = 1;

    private static final Integer DEFAULT_PAGE_SIZE = 20;

    private static final Joiner COMMON_JOIN = Joiner.on(",").skipNulls();

    private static final Long BASE_CONTRACT_ID = 100L;

    @Override
    public FrameContractCompleteConfigResponseDTO getCompleteConfigInfo(CompleteConfigInfoRequestDTO requestDTO) throws EcontractException {
        checkOpAuthority(requestDTO.getOperatorId());
        FrameContractConfigInfoPo configInfoPo = frameContractConfigInfoBaseService.getCompleteConfigById(requestDTO.getId());
        if (configInfoPo == null) {
            throw EcontractException.PARAM_ERROR_EXCEPTION;
        }
        return convertConfigInfoPoToResponseDTO(configInfoPo);
    }

    @Override
    public FrameContractConfigQueryResponse queryContractInfo(FrameContractConfigQueryParam queryParam) throws EcontractException {
        fillDefaultQueryParam(queryParam);
        checkQueryParam(queryParam);

        List<Long> roleIdList = uacAuthRemoteServiceAdapter.getAllRoleIdByUid(queryParam.getBaseParam().getOperatorId());
        if (!roleIdList.contains(MccConfig.getSuperAdminRoleId())) {
            PageInfoDTO pageInfoDTO = new PageInfoDTO(0L, queryParam.getPageParam().getPageNumber(), queryParam.getPageParam().getPageSize());
            return constructFrameContractConfigQueryResponse(pageInfoDTO, new ArrayList<>());
        }

        FrameContractQueryCondition queryCondition = constructFrameContractQueryCondition(queryParam);
        Integer totalNumber = frameContractConfigInfoBaseService.getTotalNumber(queryCondition);
        if (totalNumber == null || totalNumber <= 0) {
            PageInfoDTO pageInfoDTO = new PageInfoDTO(0L, queryParam.getPageParam().getPageNumber(), queryParam.getPageParam().getPageSize());
            return constructFrameContractConfigQueryResponse(pageInfoDTO, new ArrayList<>());
        }
        List<FrameContractConfigInfoDTO> configInfoList = doQuery(queryCondition);
        PageInfoDTO pageInfoDTO = new PageInfoDTO(Long.valueOf(totalNumber), queryParam.getPageParam().getPageNumber(), queryParam.getPageParam().getPageSize());
        return constructFrameContractConfigQueryResponse(pageInfoDTO, configInfoList);
    }

    @Override
    public List<FrameContractConfigStatusInfo> queryContractStatus() {
        List<FrameContractConfigStatusInfo> statusInfoList = new ArrayList<>();
        for (FrameContractConfigStatusEnum statusEnum : FrameContractConfigStatusEnum.values()) {
            FrameContractConfigStatusInfo statusInfo = new FrameContractConfigStatusInfo();
            statusInfo.setConfigStatusCode(statusEnum.getCode());
            statusInfo.setConfigStatusDesc(statusEnum.getDesc());
            statusInfoList.add(statusInfo);
        }
        return statusInfoList;
    }

    @Override
    public List<EstmapPartTypeInfo> queryEstmapPartType() {
        List<EstmapPartTypeInfo> partTypeInfoList = new ArrayList<>();
        for (EstmapPartEnum estmapPartEnum : EstmapPartEnum.values()) {
            EstmapPartTypeInfo partTypeInfo = new EstmapPartTypeInfo();
            partTypeInfo.setPartTypeCode(estmapPartEnum.getCode());
            partTypeInfo.setPartTypeDesc(estmapPartEnum.getDesc());
            partTypeInfoList.add(partTypeInfo);
        }
        return partTypeInfoList;
    }

    @Override
    public List<EstmapPartOrderInfo> queryEstmapOrderInfo() {
        List<EstmapPartOrderInfo> estmapPartOrderInfos = new ArrayList<>();
        for (PartEstampOrderEnum estampOrderEnum : PartEstampOrderEnum.values()) {
            EstmapPartOrderInfo orderInfo = new EstmapPartOrderInfo();
            orderInfo.setOrder(estampOrderEnum.getOrder());
            orderInfo.setOrderDesc(estampOrderEnum.getDesc());
            estmapPartOrderInfos.add(orderInfo);
        }
        return estmapPartOrderInfos;
    }

    @Override
    public Boolean saveContract(FrameContractConfigSaveParam saveParam) throws EcontractException {
        if (saveParam.getId() == null) {
            checkInsertParam(saveParam);
            checkOpAuthority(saveParam.getOperatorId());
            FrameContractConfigInfoPo configInfoPo = convertConfigSaveParamToPo(saveParam);
            return doInsertContract(configInfoPo);
        } else {
            checkUpdateParam(saveParam);
            checkOpAuthority(saveParam.getOperatorId());
            FrameContractConfigInfoPo configInfoPo = convertConfigSaveParamToPo(saveParam);
            return doUpdateContract(configInfoPo);
        }
    }

    private Boolean doUpdateContract(FrameContractConfigInfoPo configInfoPo) {
        return frameContractConfigInfoBaseService.updateContract(configInfoPo) == 1;
    }

    private void checkUpdateParam(FrameContractConfigSaveParam saveParam) throws EcontractException {
        econtractConfigCommonService.checkParamNotEmpty(saveParam, "请求参数异常");
        econtractConfigCommonService.checkParamNotEmpty(saveParam.getId(), "请求参数异常");
        econtractConfigCommonService.checkParamNotEmpty(saveParam.getSupportOpInfo(), "请求参数异常");
        econtractConfigCommonService.checkParamNotEmpty(saveParam.getSupportOpInfo(), "请求参数异常");
        econtractConfigCommonService.checkParamNotEmpty(saveParam.getContractProperty(), "请求参数异常");
        econtractConfigCommonService.checkParamNotEmpty(saveParam.getCreateAuthInfo(), "请求参数异常");

        FrameContractProperty contractProperty = saveParam.getContractProperty();
        if (CollectionUtils.isNotEmpty(contractProperty.getPartPropertyList())) {
            int poiStampCount = 0;
            for (PartProperty partProperty : contractProperty.getPartPropertyList()) {
                if (partProperty.getPartType() == EstmapPartEnum.POI.getCode()) {
                    poiStampCount++;
                }
            }
            if (poiStampCount > 1) {
                throw new EcontractException(EcontractException.PARAM_ERROR, "只能有一个商家类型");
            }
        }
        FrameContractConfigInfoPo configInfoPo = frameContractConfigInfoBaseService.getCompleteConfigById(saveParam.getId());
        if (configInfoPo == null) {
            throw new EcontractException(EcontractException.PARAM_ERROR, "合同不存在");
        }
        if (configInfoPo.getStatus() == FrameContractConfigStatusEnum.ENABLE.getCode()) {
            throw new EcontractException(EcontractException.PARAM_ERROR, "启用中的合同无法编辑");
        }

//        CreateAuthInfo createAuthInfo = saveParam.getCreateAuthInfo();
//        boolean customerTypeCheckResult = createAuthInfo.getCustomerTypeList() != null && createAuthInfo.getCustomerTypeList().stream().anyMatch(Objects::isNull);
//        boolean roleIdCheckResult = createAuthInfo.getRoleInfoList() != null && createAuthInfo.getRoleInfoList().stream().map(UacRoleInfo::getRoleId).anyMatch(Objects::isNull);
//        if (customerTypeCheckResult || roleIdCheckResult) {
//            throw new EcontractException(EcontractException.PARAM_ERROR, "参数信息异常");
//        }
    }

    @Override
    public Boolean disableContract(FrameContractStatusRequestDTO requestDTO) throws EcontractException {
        checkOpAuthority(requestDTO.getOperatorId());
        FrameContractConfigInfoPo configInfoPo = frameContractConfigInfoBaseService.getCompleteConfigById(requestDTO.getId());
        if (configInfoPo == null) {
            throw new EcontractException(EcontractException.PARAM_ERROR, "合同不存在");
        }
        return frameContractConfigInfoBaseService.disableContract(requestDTO.getId()) == 1;
    }

    @Override
    public boolean enableContract(FrameContractStatusRequestDTO requestDTO) throws EcontractException {
        checkOpAuthority(requestDTO.getOperatorId());
        FrameContractConfigInfoPo configInfoPo = frameContractConfigInfoBaseService.getCompleteConfigById(requestDTO.getId());
        if (configInfoPo == null) {
            throw new EcontractException(EcontractException.PARAM_ERROR, "配置化合同不存在");
        }
        if (configInfoPo.getWmEcontractTemplateId() == null || configInfoPo.getWmEcontractTemplateId() <= 0) {
            throw new EcontractException(EcontractException.PARAM_ERROR, "框架合同未关联内容模板，无法启用");
        }
        return frameContractConfigInfoBaseService.enableContract(requestDTO.getId()) == 1;
    }

    @Override
    public FrameContractProperty queryDefaultEstampInfo(List<EstampInfoRequestDTO> requestDTOList) {
        List<PartProperty> partPropertyList = new ArrayList<>();
        for (EstampInfoRequestDTO requestDTO : requestDTOList) {
            PartProperty partProperty = new PartProperty();
            partProperty.setPartType(requestDTO.getPartType());
            partProperty.setPartName(requestDTO.getPartName());
            partProperty.setOrder(requestDTO.getOrder());

            PartEstampOrderEnum estampOrderEnum = PartEstampOrderEnum.getOrderEnum(requestDTO.getOrder());
            partProperty.setOrderDesc(estampOrderEnum.getDesc());
            ConfigContractCommonEstampEstampEnum dynamicParamEnum = ConfigContractCommonEstampEstampEnum.getByCodeAndCustomerName(requestDTO.getPartType(), requestDTO.getPartName());
            partProperty.setPartStampVariable("pdfMetaContent.part" + estampOrderEnum.name() + "Estamp");
            partProperty.setPartStampType(dynamicParamEnum.getEstampSubjectInfoEnum().getSignKeyName());
            partProperty.setPartNameVariable("pdfMetaContent.part" + estampOrderEnum.name() + "StampName");
            partProperty.setPartStampName(dynamicParamEnum.getStageStampName());
            partPropertyList.add(partProperty);
        }

        FrameContractProperty frameContractProperty = new FrameContractProperty();
        frameContractProperty.setPartPropertyList(partPropertyList);
        frameContractProperty.setSignTimeVariableName("pdfMetaContent.partASignTime");
        frameContractProperty.setDueTimeVariableName("pdfMetaContent.partADueTime");
        return frameContractProperty;
    }

    @Override
    public boolean associateContentTemplate(AssociateContentTemplateRequestDTO requestDTO) throws EcontractException {
        checkAssociateParam(requestDTO);
        checkOpAuthority(requestDTO.getOperatorId());
        return frameContractConfigInfoBaseService.associateContentTemplate(requestDTO.getId(), requestDTO.getContentTemplateId()) == 1;
    }

    @Override
    public List<FrameContractCompleteConfigResponseDTO> getAllContractConfigInfo() {
        List<FrameContractConfigInfoPo> configInfoPoList = frameContractConfigInfoBaseService.getAllConfigContract();
        if (CollectionUtils.isEmpty(configInfoPoList)) {
            return Collections.emptyList();
        }
        return configInfoPoList.stream()
                .map(this::convertConfigInfoPoToResponseDTO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public FrameContractCompleteConfigResponseDTO getCompleteConfigInfoByContractId(Integer contractId) throws EcontractException {
        FrameContractConfigInfoPo configInfoPo = frameContractConfigInfoBaseService.getCompleteConfigInfoByContractId(contractId);
        if (configInfoPo == null) {
            return null;
        }
        return convertConfigInfoPoToResponseDTO(configInfoPo);
    }

    @Override
    public FrameContractCompleteConfigResponseDTO getConfigInfoByContractIdIgnoreValid(Integer contractId) throws EcontractException {
        FrameContractConfigInfoPo configInfoPo = frameContractConfigInfoBaseService.getCompleteConfigInfoByContractId(contractId);
        if (configInfoPo == null) {
            return null;
        }
        return convertConfigInfoPoToResponseDTO(configInfoPo);
    }

    @Override
    public void completeContractInfoAfterConfig(EcontractBatchBo econtractBatchBo) throws EcontractException {
        try {
            if (!Strings.isEmpty(econtractBatchBo.getEcontractType())) {
                return;
            }
            log.info("FrameContractConfigServiceImpl#completeContractInfoAfterConfig, 未指定流程模板, 尝试自动补齐");

            StageBatchInfoBo stageBatchInfoBo = findCreatePdfStageBatchInfoBo(econtractBatchBo);
            Integer pdfTemplateId = ContractContentTemplateUtil.extractTemplateIdFromStageBatchInfoBo(stageBatchInfoBo);

            EcontractTemplateBaseBo econtractTemplate = econtractTemplateBaseService.getValidEcontractTemplate(pdfTemplateId);
            if (econtractTemplate == null) {
                throw new EcontractException(EcontractException.PARAM_ERROR, "内容模板不存在");
            }
            if (econtractTemplate.getSignFlowTemplateId() == null || econtractTemplate.getSignFlowTemplateId() <= 0) {
                throw new EcontractException(EcontractException.PARAM_ERROR, "没有关联流程模板");
            }
            EcontractEntity econtractEntity = econtractService.queryValidEcontract(econtractTemplate.getSignFlowTemplateId());
            if (econtractEntity == null) {
                throw new EcontractException(EcontractException.PARAM_ERROR, "流程模板不存在");
            }
            econtractBatchBo.setEcontractType(econtractEntity.getEcontractType());
        } catch (Exception e) {
            log.error("FrameContractConfigServiceImpl#completeContractInfoAfterConfig, error", e);
        }
    }

    @Override
    public List<EstampSubjectInfo> queryAllEstampSubject() {
        List<EstampSubjectInfo> subjectInfoList = new ArrayList<>();
        for (ConfigContractCommonEstampEstampEnum estampEnum : ConfigContractCommonEstampEstampEnum.values()) {
            if (ConfigContractCommonEstampEstampEnum.DEFAULT == estampEnum || ConfigContractCommonEstampEstampEnum.POI == estampEnum) {
                continue;
            }
            EstampSubjectInfo estampSubjectInfo = new EstampSubjectInfo();
            estampSubjectInfo.setSignKeyName(estampEnum.getEstampSubjectInfoEnum().getSignKeyName());
            estampSubjectInfo.setCaCetifyResult(estampEnum.getEstampSubjectInfoEnum().getCaCetifyResult());
            estampSubjectInfo.setCustomerIdCode(estampEnum.getEstampSubjectInfoEnum().getCustomerIdCode());
            estampSubjectInfo.setCustomerName(estampEnum.getEstampSubjectInfoEnum().getCustomerName());
            subjectInfoList.add(estampSubjectInfo);
        }
        return subjectInfoList;
    }

    @Override
    public List<CustomerTypeInfo> getAllCustomerType() {
        List<CustomerTypeInfoDTO> allCustomerTypeDTOList = wmFrameContractConfigThriftServiceAdapter.getAllCustomerTypeInfo();
        List<CustomerTypeInfo> customerTypeInfoList = new ArrayList<>();

        for (CustomerTypeInfoDTO sourceCustomerType : allCustomerTypeDTOList) {
            CustomerTypeInfo targetCustomerType = new CustomerTypeInfo();
            BeanUtils.copyProperties(sourceCustomerType, targetCustomerType);
            customerTypeInfoList.add(targetCustomerType);
        }
        return customerTypeInfoList;
    }

    @Override
    public ConfigContractOperationInfo getAllOperation() {
        ConfigContractOperationInfo operationInfo = new ConfigContractOperationInfo();
        operationInfo.setStatusMap(buildStausMap());
        operationInfo.setDataMapList(buildDataMapList());
        return operationInfo;
    }

    @Override
    public FrameContractCompleteConfigResponseDTO getCompleteConfigInfoByContractCode(String contractCode) {
        FrameContractConfigInfoPo configInfoPo = frameContractConfigInfoBaseService.getCompleteConfigInfoByContractCode(contractCode);
        if (configInfoPo == null) {
            return null;
        }
        return convertConfigInfoPoToResponseDTO(configInfoPo);
    }

    private List<Map<String, String>> buildDataMapList() {
        List<Map<String, String>> dataMapList = new ArrayList<>();
        dataMapList.add(buildDataMapInAuto());
        dataMapList.add(buildDataMapInManual());
        dataMapList.add(buildDataMapInSigning());
        dataMapList.add(buildDataMapInEffect());
        dataMapList.add(buildDataMapInFail());
        dataMapList.add(buildDataMapInToEffect());
        dataMapList.add(buildDataMapInEffective());
        return dataMapList;
    }

    private Map<String, String> buildDataMapInEffective() {
        Map<String, String> dataMap = new LinkedHashMap<>();
        dataMap.put("status", "已失效");
        dataMap.put("task_packCommit", "0");
        dataMap.put("task_resendMsg", "0");
        dataMap.put("task_cancelSign", "0");
        dataMap.put("task_viewLink", "0");
        dataMap.put("task_viewContract", "1");
        dataMap.put("contract_editContract", "0");
        dataMap.put("contract_resendMsg", "0");
        dataMap.put("contract_cancelSign", "0");
        dataMap.put("contract_destroyContract", "0");
        dataMap.put("contract_viewContract", "1");
        return dataMap;
    }

    private Map<String, String> buildDataMapInToEffect() {
        Map<String, String> dataMap = new LinkedHashMap<>();
        dataMap.put("status", "签约成功/待生效");
        dataMap.put("task_packCommit", "0");
        dataMap.put("task_resendMsg", "0");
        dataMap.put("task_cancelSign", "0");
        dataMap.put("task_viewLink", "0");
        dataMap.put("task_viewContract", "1");
        dataMap.put("contract_editContract", "0");
        dataMap.put("contract_resendMsg", "0");
        dataMap.put("contract_cancelSign", "0");
        dataMap.put("contract_destroyContract", "0");
        dataMap.put("contract_viewContract", "1");
        return dataMap;
    }

    private Map<String, String> buildDataMapInFail() {
        Map<String, String> dataMap = new LinkedHashMap<>();
        dataMap.put("status", "签约失败");
        dataMap.put("task_packCommit", "0");
        dataMap.put("task_resendMsg", "0");
        dataMap.put("task_cancelSign", "0");
        dataMap.put("task_viewLink", "0");
        dataMap.put("task_viewContract", "1");
        dataMap.put("contract_editContract", "1");
        dataMap.put("contract_resendMsg", "0");
        dataMap.put("contract_cancelSign", "0");
        dataMap.put("contract_destroyContract", "0");
        dataMap.put("contract_viewContract", "1");
        return dataMap;
    }

    private Map<String, String> buildDataMapInEffect() {
        Map<String, String> dataMap = new LinkedHashMap<>();
        dataMap.put("status", "签约成功/已生效");
        dataMap.put("task_packCommit", "0");
        dataMap.put("task_resendMsg", "0");
        dataMap.put("task_cancelSign", "0");
        dataMap.put("task_viewLink", "0");
        dataMap.put("task_viewContract", "1");
        dataMap.put("contract_editContract", "1");
        dataMap.put("contract_resendMsg", "0");
        dataMap.put("contract_cancelSign", "0");
        dataMap.put("contract_destroyContract", "2");
        dataMap.put("contract_viewContract", "1");
        return dataMap;
    }

    private Map<String, String> buildDataMapInSigning() {
        Map<String, String> dataMap = new LinkedHashMap<>();
        dataMap.put("status", "签约中");
        dataMap.put("task_packCommit", "0");
        dataMap.put("task_resendMsg", "1");
        dataMap.put("task_cancelSign", "1");
        dataMap.put("task_viewLink", "1");
        dataMap.put("task_viewContract", "1");
        dataMap.put("contract_editContract", "0");
        dataMap.put("contract_resendMsg", "1");
        dataMap.put("contract_cancelSign", "1");
        dataMap.put("contract_destroyContract", "0");
        dataMap.put("contract_viewContract", "1");
        return dataMap;
    }

    private Map<String, String> buildDataMapInManual() {
        Map<String, String> dataMap = new LinkedHashMap<>();
        dataMap.put("status", "待手动发起签约");
        dataMap.put("task_packCommit", "1");
        dataMap.put("task_resendMsg", "0");
        dataMap.put("task_cancelSign", "1");
        dataMap.put("task_viewLink", "0");
        dataMap.put("task_viewContract", "0");
        dataMap.put("contract_editContract", "0");
        dataMap.put("contract_resendMsg", "0");
        dataMap.put("contract_cancelSign", "1");
        dataMap.put("contract_destroyContract", "0");
        dataMap.put("contract_viewContract", "0");
        return dataMap;
    }

    private Map<String, String> buildDataMapInAuto() {
        Map<String, String> dataMap = new LinkedHashMap<>();
        dataMap.put("status", "待自动发起签约");
        dataMap.put("task_packCommit", "0");
        dataMap.put("task_resendMsg", "0");
        dataMap.put("task_cancelSign", "1");
        dataMap.put("task_viewLink", "0");
        dataMap.put("task_viewContract", "0");
        dataMap.put("contract_editContract", "0");
        dataMap.put("contract_resendMsg", "0");
        dataMap.put("contract_cancelSign", "1");
        dataMap.put("contract_destroyContract", "0");
        dataMap.put("contract_viewContract", "0");
        return dataMap;
    }

    private Map<String, Integer> buildStausMap() {
        Map<String, Integer> map = new HashMap<>();
        map.put("NOT_DISPLAY", 0);
        map.put("NOT_EDITABLE_TRUE", 1);
        map.put("EDITABLE", 2);
        return map;
    }

    private StageBatchInfoBo findCreatePdfStageBatchInfoBo(EcontractBatchBo econtractBatchBo) throws EcontractException {
        return econtractBatchBo.getStageInfoBoList().stream()
                .filter(v -> TaskConstant.CREATE_PDF.equals(v.getStageName()))
                .findFirst()
                .orElseThrow(() -> new EcontractException(EcontractException.PARAM_ERROR, "请求参数异常"));
    }

    private Integer extractPdfTemplateId(StageBatchInfoBo stageBatchInfoBo) throws EcontractException {
        if (MapUtils.isNotEmpty(stageBatchInfoBo.getPdfContentInfoBoMap())) {
            for (List<PdfContentInfoBo> pdfContentInfoBoList : stageBatchInfoBo.getPdfContentInfoBoMap().values()) {
                if (CollectionUtils.isNotEmpty(pdfContentInfoBoList)) {
                    PdfContentInfoBo anyPdfContentInfoBo = pdfContentInfoBoList.stream()
                            .filter(pdfContentInfoBo -> pdfContentInfoBo.getPdfTemplateId() != null && pdfContentInfoBo.getPdfTemplateId() > 0)
                            .findAny()
                            .orElseThrow(() -> new EcontractException(EcontractException.PARAM_ERROR, "请求参数异常"));
                    return anyPdfContentInfoBo.getPdfTemplateId();
                }
            }
        } else {
            return stageBatchInfoBo.getPdfContentInfoBoList().stream()
                    .filter(pdfContentInfoBo -> pdfContentInfoBo.getPdfTemplateId() != null && pdfContentInfoBo.getPdfTemplateId() > 0)
                    .findAny()
                    .orElseThrow(() -> new EcontractException(EcontractException.PARAM_ERROR, "请求参数异常"))
                    .getPdfTemplateId();
        }
        return 0;
    }

    private void checkAssociateParam(AssociateContentTemplateRequestDTO requestDTO) throws EcontractException {
        econtractConfigCommonService.checkParamNotEmpty(requestDTO, "请求参数异常");
        econtractConfigCommonService.checkParamNotEmpty(requestDTO.getId(), "请求参数异常");
        econtractConfigCommonService.checkParamNotEmpty(requestDTO.getContentTemplateId(), "请求参数异常");

        FrameContractConfigInfoPo configInfoPo = frameContractConfigInfoBaseService.getCompleteConfigById(requestDTO.getId());
        if (configInfoPo == null) {
            throw new EcontractException(EcontractException.PARAM_ERROR, "合同不存在");
        }
        if (configInfoPo.getStatus() == FrameContractConfigStatusEnum.ENABLE.getCode()) {
            throw new EcontractException(EcontractException.PARAM_ERROR, "启用中的合同无法编辑”");
        }

        EcontractTemplateBaseBo templateBaseBo = econtractTemplateBaseService.getTemplateBaseById(requestDTO.getContentTemplateId());
        if (templateBaseBo == null) {
            throw new EcontractException(EcontractException.PARAM_ERROR, "合同内容模板不存在");
        }
        if (templateBaseBo.getSignFlowTemplateId() == null || templateBaseBo.getSignFlowTemplateId() <= 0) {
            throw new EcontractException(EcontractException.PARAM_ERROR, "合同内容模板未关联签约流程模板");
        }
        if (templateBaseBo.getSignPageTemplateId() == null || templateBaseBo.getSignPageTemplateId() <= 0) {
            throw new EcontractException(EcontractException.PARAM_ERROR, "合同内容模板未关联h5模板");
        }
    }

    private Boolean doInsertContract(FrameContractConfigInfoPo configInfoPo) throws EcontractException {
        try {
            boolean lockResult = tairLocker.tryLock(LockKeyConstant.CONFIG_FRAME_CONTRACT_LOCK.getKey(), 3);
            if (!lockResult) {
                log.warn("FrameContractConfigServiceImpl#doInsertContract, 获取锁失败");
                throw new EcontractException(EcontractException.LOCK_ERROR, "请勿重复提交");
            }
            Long maxId = frameContractConfigInfoBaseService.getMaxId();
            if (maxId == null) {
                maxId = 0L;
            }
            configInfoPo.setContractId(generateContractId(maxId));
            log.info("FrameContractConfigServiceImpl#doInsertContract, configInfoPo: {}", JacksonUtil.writeAsJsonStr(configInfoPo));
            return frameContractConfigInfoBaseService.insert(configInfoPo) == 1;
        } finally {
            tairLocker.unLock(LockKeyConstant.CONFIG_FRAME_CONTRACT_LOCK.getKey());
        }
    }

    private Integer generateContractId(Long maxId) {
        long newBaseContractId = BASE_CONTRACT_ID + maxId;
        String contractId = newBaseContractId + "2" + newBaseContractId;
        int finalContractId = Integer.parseInt(contractId);
        log.info("FrameContractConfigServiceImpl#generateContractId, maxId: {}, contractId: {}", maxId, contractId);
        return finalContractId;
    }

    private FrameContractConfigInfoPo convertConfigSaveParamToPo(FrameContractConfigSaveParam saveParam) {
        FrameContractConfigInfoPo configInfoPo = new FrameContractConfigInfoPo();
        if (saveParam.getId() != null) {
            configInfoPo.setId(saveParam.getId().longValue());
        }
        configInfoPo.setContractName(saveParam.getContractName());
        configInfoPo.setContractTypeCode(saveParam.getContractCode());
        configInfoPo.setContractDescription(saveParam.getContractDesc());

        SourceAuthContext sourceAuthContext = new SourceAuthContext();
        BeanUtils.copyProperties(saveParam.getSourceAuthInfo(), sourceAuthContext);
        configInfoPo.setSourceAuthInfo(sourceAuthContext);

        OperateSupportContext operateSupportContext = new OperateSupportContext();
        BeanUtils.copyProperties(saveParam.getSupportOpInfo(), operateSupportContext);
        configInfoPo.setSupportOperationInfo(operateSupportContext);

        configInfoPo.setContractProperty(convertToContractPropertyContext(saveParam.getContractProperty()));
        configInfoPo.setSaveAuthInfo(convertToCreateAuthContext(saveParam.getCreateAuthInfo()));

        configInfoPo.setStatus(FrameContractConfigStatusEnum.DISABLE.getCode());
        configInfoPo.setWmEcontractTemplateId(0);
        log.info("FrameContractConfigServiceImpl#convertConfigSaveParamToPo, configInfoPo: {}", JacksonUtil.writeAsJsonStr(configInfoPo));
        return configInfoPo;
    }

    private CreateAuthContext convertToCreateAuthContext(CreateAuthInfo createAuthInfo) {
        CreateAuthContext createAuthContext = new CreateAuthContext();
        createAuthContext.setCustomerTypeList(createAuthInfo.getCustomerTypeList());

        List<UacRoleInfo> roleInfoList = createAuthInfo.getRoleInfoList();
        if (CollectionUtils.isNotEmpty(roleInfoList)) {
            createAuthContext.setRoleIdList(roleInfoList.stream().map(UacRoleInfo::getRoleId).collect(Collectors.toList()));
        }
        return createAuthContext;
    }

    private FrameContractPropertyContext convertToContractPropertyContext(FrameContractProperty contractProperty) {
        FrameContractPropertyContext targetContext = new FrameContractPropertyContext();

        List<PartPropertyContext> targetPropertyList = new ArrayList<>();
        for (PartProperty sourceProperty : contractProperty.getPartPropertyList()) {
            PartPropertyContext targetProperty = new PartPropertyContext();
            BeanUtils.copyProperties(sourceProperty, targetProperty);
            targetPropertyList.add(targetProperty);
        }

        targetContext.setPartPropertyList(targetPropertyList);
        targetContext.setContractValidityProperty(convertToContractValidityPropertyContext(contractProperty.getContractValidityProperty()));
        targetContext.setContractEffectiveTimeProperty(convertToContractEffectiveTimePropertyContext(contractProperty.getContractEffectiveTimeProperty()));
        targetContext.setCanEdit(contractProperty.getCanEdit());
        targetContext.setSignTimeVariableName(contractProperty.getSignTimeVariableName());
        targetContext.setDueTimeVariableName(contractProperty.getDueTimeVariableName());
        return targetContext;
    }

    private ContractValidityPropertyContext convertToContractValidityPropertyContext(ContractValidityProperty sourceValidityProperty) {
        ContractValidityPropertyContext targetValidityProperty = new ContractValidityPropertyContext();
        BeanUtils.copyProperties(sourceValidityProperty, targetValidityProperty);

        if (sourceValidityProperty.getLeftInterval() != null) {
            ContractTimePropertyContext targetLeftInterval = new ContractTimePropertyContext();
            BeanUtils.copyProperties(sourceValidityProperty.getLeftInterval(), targetLeftInterval);
            targetValidityProperty.setLeftInterval(targetLeftInterval);
        }

        if (sourceValidityProperty.getRightInterval() != null) {
            ContractTimePropertyContext targetRightInterval = new ContractTimePropertyContext();
            BeanUtils.copyProperties(sourceValidityProperty.getRightInterval(), targetRightInterval);
            targetValidityProperty.setRightInterval(targetRightInterval);
        }
        return targetValidityProperty;
    }

    private ContractEffectiveTimePropertyContext convertToContractEffectiveTimePropertyContext(ContractEffectiveTimeProperty sourceEffectiveTimeProperty) {
        ContractEffectiveTimePropertyContext targetEffectiveTimeProperty = new ContractEffectiveTimePropertyContext();
        BeanUtils.copyProperties(sourceEffectiveTimeProperty, targetEffectiveTimeProperty);

        if (sourceEffectiveTimeProperty.getLeftInterval() != null) {
            ContractTimePropertyContext targetLeftInterval = new ContractTimePropertyContext();
            BeanUtils.copyProperties(sourceEffectiveTimeProperty.getLeftInterval(), targetLeftInterval);
            targetEffectiveTimeProperty.setLeftInterval(targetLeftInterval);
        }

        if (sourceEffectiveTimeProperty.getRightInterval() != null) {
            ContractTimePropertyContext targetRightInterval = new ContractTimePropertyContext();
            BeanUtils.copyProperties(sourceEffectiveTimeProperty.getRightInterval(), targetRightInterval);
            targetEffectiveTimeProperty.setRightInterval(targetRightInterval);
        }
        return targetEffectiveTimeProperty;
    }

    private void checkInsertParam(FrameContractConfigSaveParam saveParam) throws EcontractException {
        econtractConfigCommonService.checkParamNotEmpty(saveParam, "请求参数异常");
        econtractConfigCommonService.checkParamNotEmpty(saveParam.getSupportOpInfo(), "请求参数异常");
        econtractConfigCommonService.checkParamNotEmpty(saveParam.getSupportOpInfo(), "请求参数异常");
        econtractConfigCommonService.checkParamNotEmpty(saveParam.getContractProperty(), "请求参数异常");
        econtractConfigCommonService.checkParamNotEmpty(saveParam.getCreateAuthInfo(), "请求参数异常");
        econtractConfigCommonService.checkParamNotEmpty(saveParam.getContractName(), "合同名称必填");
        econtractConfigCommonService.checkParamNotEmpty(saveParam.getContractCode(), "合同Code必填");

        econtractConfigCommonService.checkStringParamLength(saveParam.getContractName(), "合同名称最多15个字", 15);
        econtractConfigCommonService.checkStringParamLength(saveParam.getContractCode(), "合同code最多50个字", 50);
        econtractConfigCommonService.checkStringParamLength(saveParam.getContractDesc(), "使用场景最多200个字", 200);

        // 检查合同Code是否包含中文字符
        if (!saveParam.getContractCode().matches("^[\\x00-\\xff]*$")) {
            throw new EcontractException(EcontractException.PARAM_ERROR, "合同Code不能包含中文字符");
        }

        EcontractTaskApplyTypeEnum applyTypeEnum = EcontractTaskApplyTypeEnum.getByName(saveParam.getContractCode());
        if (applyTypeEnum != EcontractTaskApplyTypeEnum.UNKNOW) {
            throw new EcontractException(EcontractException.PARAM_ERROR, "合同Code与非配置化任务类型冲突");
        }

        FrameContractCompleteConfigResponseDTO responseDTO = getCompleteConfigInfoByContractCode(saveParam.getContractCode());
        if (responseDTO != null) {
            throw new EcontractException(EcontractException.PARAM_ERROR, "合同Code已经存在");
        }

        FrameContractProperty contractProperty = saveParam.getContractProperty();
        if (CollectionUtils.isNotEmpty(contractProperty.getPartPropertyList())) {
            if (contractProperty.getPartPropertyList().size() > 2) {
                throw new EcontractException(EcontractException.PARAM_ERROR, "暂时只支持配置甲乙方");
            }
            int poiStampCount = 0;
            for (PartProperty partProperty : contractProperty.getPartPropertyList()) {
                if (partProperty.getPartType() == EstmapPartEnum.POI.getCode()) {
                    poiStampCount++;
                }
            }
            if (poiStampCount > 1) {
                throw new EcontractException(EcontractException.PARAM_ERROR, "只能有一个商家类型");
            }
        }
    }

    private List<FrameContractConfigInfoDTO> doQuery(FrameContractQueryCondition queryCondition) {
        List<FrameContractConfigInfoPo> configInfoPoList = frameContractConfigInfoBaseService.queryContractInfo(queryCondition);
        return configInfoPoList.stream().map(this::convertConfigInfoPoToDTO).collect(Collectors.toList());
    }

    private FrameContractConfigQueryResponse constructFrameContractConfigQueryResponse(PageInfoDTO pageInfoDTO, List<FrameContractConfigInfoDTO> configInfoList) {
        FrameContractConfigQueryResponse queryResponse = new FrameContractConfigQueryResponse();
        queryResponse.setConfigInfoList(configInfoList);
        queryResponse.setPageInfoDTO(pageInfoDTO);
        return queryResponse;
    }

    private void checkQueryParam(FrameContractConfigQueryParam queryParam) throws EcontractException {
        if (queryParam == null || queryParam.getBaseParam() == null) {
            throw EcontractException.PARAM_ERROR_EXCEPTION;
        }

        if (queryParam.getPageParam().getPageNumber() <= 0) {
            throw new EcontractException(-1, "页码小于");
        }

        if (queryParam.getPageParam().getPageSize() <= 0) {
            throw new EcontractException(-1, "页大小小于");
        }

    }

    private void fillDefaultQueryParam(FrameContractConfigQueryParam queryParam) {
        PageInfoSearchParam pageParam = queryParam.getPageParam();
        if (pageParam == null) {
            pageParam = new PageInfoSearchParam();
            queryParam.setPageParam(pageParam);
        }
        if (pageParam.getPageSize() == null) {
            pageParam.setPageSize(DEFAULT_PAGE_SIZE);
        }
        if (pageParam.getPageNumber() == null) {
            pageParam.setPageNumber(DEFAULT_PAGE_NUMBER);
        }
    }

    private void checkOpAuthority(Integer operatorId) throws EcontractException {
        List<Long> roleIdList = uacAuthRemoteServiceAdapter.getAllRoleIdByUid(operatorId);
        if (!roleIdList.contains(MccConfig.getSuperAdminRoleId())) {
            throw new EcontractException(EcontractException.NOT_AUTHORITY, "没有权限注册框架合同");
        }
    }

    private FrameContractQueryCondition constructFrameContractQueryCondition(FrameContractConfigQueryParam queryParam) {
        return FrameContractQueryCondition.builder()
                .contractTypeCode(queryParam.getBaseParam().getContractCode())
                .contractName(queryParam.getBaseParam().getContractName())
                .registerStartTime(queryParam.getBaseParam().getRegisterStartTime())
                .registerEndTime(queryParam.getBaseParam().getRegisterEndTime())
                .status(queryParam.getBaseParam().getStatus())
                .pageSize(queryParam.getPageParam().getPageSize())
                .offset((queryParam.getPageParam().getPageNumber() - 1) * queryParam.getPageParam().getPageSize())
                .build();
    }

    private FrameContractConfigInfoDTO convertConfigInfoPoToDTO(FrameContractConfigInfoPo configInfoPo) {
        FrameContractConfigInfoDTO configInfoDTO = new FrameContractConfigInfoDTO();
        configInfoDTO.setId(configInfoPo.getId());
        configInfoDTO.setContractId(configInfoPo.getContractId());
        configInfoDTO.setContractCode(configInfoPo.getContractTypeCode());
        configInfoDTO.setContractName(configInfoPo.getContractName());
        configInfoDTO.setSourceInfo(assembleSourceConfigInfo(configInfoPo));
        configInfoDTO.setStatus(FrameContractConfigStatusEnum.DISABLE.getDescByCode(configInfoPo.getStatus()));
        configInfoDTO.setPdfTemplateInfo(assemblePdfTemplate(configInfoPo));
        configInfoDTO.setAuthInfo(assembleAuthInfo(configInfoPo));
        configInfoDTO.setRegisterTime(Long.valueOf(configInfoPo.getCtime()));
        configInfoDTO.setOperationInfo(assembleOperationInfo(configInfoPo));
        return configInfoDTO;
    }

    private OperationSupport assembleOperationInfo(FrameContractConfigInfoPo configInfoPo) {
        OperationSupport operationSupport = new OperationSupport();
        operationSupport.setSupportEditBaseInfo(true);
        operationSupport.setSupportEditDynamicInfo(true);

        Integer status = configInfoPo.getStatus();
        operationSupport.setSupportEnableContract(FrameContractConfigStatusEnum.DISABLE.getCode() == status);
        operationSupport.setSupportSuspendContract(FrameContractConfigStatusEnum.ENABLE.getCode() == status);
        return operationSupport;
    }

    private AuthConfigInfo assembleAuthInfo(FrameContractConfigInfoPo configInfoPo) {
        AuthConfigInfo targetAuthInfo = new AuthConfigInfo();
        List<Long> roleIdList = configInfoPo.getSaveAuthInfo().getRoleIdList();
        if (CollectionUtils.isNotEmpty(roleIdList)) {
            targetAuthInfo.setAuthUacRole(assembleUacRoleInfo(roleIdList));
        } else {
            targetAuthInfo.setAuthUacRole("");
        }
        if (CollectionUtils.isNotEmpty(configInfoPo.getSaveAuthInfo().getCustomerTypeList())) {
            targetAuthInfo.setAuthCustomerType(assembleCustomerInfo(configInfoPo.getSaveAuthInfo().getCustomerTypeList()));
        } else {
            targetAuthInfo.setAuthCustomerType("");
        }
        return targetAuthInfo;
    }

    private String assembleUacRoleInfo(List<Long> roleIdList) {
        List<UacRoleEntity> uacRoleEntityList = uacRoleRemoteServiceAdapter.batchGetRoleById(roleIdList);
        Map<Long, String> roleId2NameMap = uacRoleEntityList.stream()
                .collect(Collectors.toMap(UacRoleEntity::getRoleId, UacRoleEntity::getRoleName));
        List<String> roleInfoList = Lists.newArrayListWithCapacity(roleIdList.size());
        for (Long roleId : roleIdList) {
            String roleName = roleId2NameMap.get(roleId);
            String roleInfo = Strings.isEmpty(roleName) ? "" : roleName + "(" + roleId + ")";
            roleInfoList.add(roleInfo);
        }
        return COMMON_JOIN.join(roleInfoList);
    }

    private String assembleCustomerInfo(List<Integer> customerTypeList) {
        Map<Integer, String> customerTypeInfoDTOMap = wmFrameContractConfigThriftServiceAdapter.getAllCustomerTypeInfo()
                .stream()
                .collect(Collectors.toMap(CustomerTypeInfoDTO::getValue, CustomerTypeInfoDTO::getName));
        List<String> customerInfoList = new ArrayList<>();
        for (Integer customerType : customerTypeList) {
            String customerTypeName = customerTypeInfoDTOMap.get(customerType);
            String customerInfo = Strings.isEmpty(customerTypeName) ? "" : customerTypeName + "(" + customerType + ")";
            customerInfoList.add(customerInfo);
        }
        return COMMON_JOIN.join(customerInfoList);
    }

    private EcontractTemplateBaseBo assemblePdfTemplate(FrameContractConfigInfoPo configInfoPo) {
        Integer econtractTemplateId = configInfoPo.getWmEcontractTemplateId();
        return econtractTemplateBaseService.getTemplateBaseById(econtractTemplateId);
    }

    private SourceConfigInfo assembleSourceConfigInfo(FrameContractConfigInfoPo configInfoPo) {
        SourceConfigInfo sourceConfigInfo = new SourceConfigInfo();
        BeanUtils.copyProperties(configInfoPo.getSourceAuthInfo(), sourceConfigInfo);
        return sourceConfigInfo;
    }

    private FrameContractCompleteConfigResponseDTO convertConfigInfoPoToResponseDTO(FrameContractConfigInfoPo configInfoPo) {
        try {
            SourceOpAuthInfo sourceOpAuthInfo = new SourceOpAuthInfo();
            BeanUtils.copyProperties(configInfoPo.getSourceAuthInfo(), sourceOpAuthInfo);

            SupportOperateInfo supportOperateInfo = new SupportOperateInfo();
            BeanUtils.copyProperties(configInfoPo.getSupportOperationInfo(), supportOperateInfo);

            FrameContractProperty frameContractProperty = convertContractProperty(configInfoPo.getContractProperty(), configInfoPo.getStatus());

            FrameContractCompleteConfigResponseDTO responseDTO = new FrameContractCompleteConfigResponseDTO();
            responseDTO.setId(configInfoPo.getId().intValue());
            responseDTO.setContractName(configInfoPo.getContractName());
            responseDTO.setContractDesc(configInfoPo.getContractDescription());
            responseDTO.setContractCode(configInfoPo.getContractTypeCode());
            responseDTO.setSourceAuthInfo(sourceOpAuthInfo);
            responseDTO.setSupportOpInfo(supportOperateInfo);
            responseDTO.setContractProperty(frameContractProperty);
            responseDTO.setCreateAuthInfo(convertToCreateAuthInfo(configInfoPo.getSaveAuthInfo()));
            responseDTO.setContractId(configInfoPo.getContractId());
            responseDTO.setStatus(configInfoPo.getStatus());
            responseDTO.setEcontractTemplateId(configInfoPo.getWmEcontractTemplateId());

            EcontractTemplateBaseInfo templateBaseInfo = econtractTemplateBaseService.getTemplateBaseInfoById(configInfoPo.getWmEcontractTemplateId());
            responseDTO.setEcontractTemplateBaseInfo(templateBaseInfo);
            if (templateBaseInfo != null) {
                EcontractUserBo userBo = econtractUserService.queryValidUserById(templateBaseInfo.getUserNameId());
                responseDTO.setEcontractUserBo(userBo);

                if (templateBaseInfo.getFlowTemplateSimpleInfo() != null && templateBaseInfo.getFlowTemplateSimpleInfo().getTemplateId() != null) {
                    EcontractTemplateBo templateBo = econtractService.querySignFlowTemplateBo(templateBaseInfo.getFlowTemplateSimpleInfo().getTemplateId());
                    responseDTO.setSignFlowTemplateBo(templateBo);
                }
            }
            return responseDTO;
        } catch (Exception e) {
            log.error("FrameContractConfigServiceImpl#convertConfigInfoPoToResponseDTO, configInfoPo: {}, error", JacksonUtil.writeAsJsonStr(configInfoPo), e);
            return null;
        }
    }

    private CreateAuthInfo convertToCreateAuthInfo(CreateAuthContext saveAuthInfo) {
        CreateAuthInfo createAuthInfo = new CreateAuthInfo();
        createAuthInfo.setCustomerTypeList(saveAuthInfo.getCustomerTypeList());

        List<Long> roleIdList = saveAuthInfo.getRoleIdList();
        List<UacRoleEntity> uacRoleEntityList = uacRoleRemoteServiceAdapter.batchGetRoleById(roleIdList);

        List<UacRoleInfo> roleInfoList = new ArrayList<>();
        for (UacRoleEntity uacRoleEntity : uacRoleEntityList) {
            UacRoleInfo userRoleInfo = new UacRoleInfo();
            BeanUtils.copyProperties(uacRoleEntity, userRoleInfo);
            roleInfoList.add(userRoleInfo);
        }
        createAuthInfo.setRoleInfoList(roleInfoList);
        return createAuthInfo;
    }

    private FrameContractProperty convertContractProperty(FrameContractPropertyContext sourceContractProperty, Integer status) {
        FrameContractProperty targetContractProperty = new FrameContractProperty();

        List<PartProperty> targetPartPropertyList = new ArrayList<>();
        for (PartPropertyContext sourcePartProperty : sourceContractProperty.getPartPropertyList()) {
            PartProperty partProperty = new PartProperty();
            BeanUtils.copyProperties(sourcePartProperty, partProperty);

            PartEstampOrderEnum estampOrderEnum = PartEstampOrderEnum.getOrderEnum(sourcePartProperty.getOrder());
            partProperty.setOrderDesc(estampOrderEnum.getDesc());
            targetPartPropertyList.add(partProperty);
        }

        targetContractProperty.setPartPropertyList(targetPartPropertyList);
        targetContractProperty.setContractValidityProperty(convertToContractValidityProperty(sourceContractProperty.getContractValidityProperty()));
        targetContractProperty.setContractEffectiveTimeProperty(convertToContractEffectiveTimeProperty(sourceContractProperty.getContractEffectiveTimeProperty()));
        targetContractProperty.setDueTimeVariableName(sourceContractProperty.getDueTimeVariableName());
        targetContractProperty.setSignTimeVariableName(sourceContractProperty.getSignTimeVariableName());
        targetContractProperty.setCanEdit(status != FrameContractConfigStatusEnum.ENABLE.getCode());
        return targetContractProperty;
    }

    private ContractValidityProperty convertToContractValidityProperty(ContractValidityPropertyContext sourceContractValidityProperty) {
        ContractValidityProperty targetValidityProperty = new ContractValidityProperty();
        BeanUtils.copyProperties(sourceContractValidityProperty, targetValidityProperty);

        targetValidityProperty.setLeftInterval(convertToContractTimeProperty(sourceContractValidityProperty.getLeftInterval()));
        targetValidityProperty.setRightInterval(convertToContractTimeProperty(sourceContractValidityProperty.getRightInterval()));
        return targetValidityProperty;
    }

    private ContractEffectiveTimeProperty convertToContractEffectiveTimeProperty(ContractEffectiveTimePropertyContext sourceContractEffectiveTimeProperty) {
        ContractEffectiveTimeProperty targetEffectiveTimeProperty = new ContractEffectiveTimeProperty();
        BeanUtils.copyProperties(sourceContractEffectiveTimeProperty, targetEffectiveTimeProperty);

        targetEffectiveTimeProperty.setLeftInterval(convertToContractTimeProperty(sourceContractEffectiveTimeProperty.getLeftInterval()));
        targetEffectiveTimeProperty.setRightInterval(convertToContractTimeProperty(sourceContractEffectiveTimeProperty.getRightInterval()));
        return targetEffectiveTimeProperty;
    }

    private ContractTimeProperty convertToContractTimeProperty(ContractTimePropertyContext source) {
        if (source == null) {
            return null;
        }
        ContractTimeProperty target = new ContractTimeProperty();
        BeanUtils.copyProperties(source, target);
        return target;
    }

}

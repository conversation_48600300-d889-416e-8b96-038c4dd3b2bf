package com.sankuai.meituan.waimai.econtract.server.service.engine.task.asyncHandler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.sankuai.bsi.esign.common.model.entity.result.GetContractResult;
import com.sankuai.meituan.waimai.econtract.server.adapter.EsignClient;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.constants.ContextConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.EcontractAsyncTaskConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractAsyncRecordMapper;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractAsyncRecordEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordEntity;
import com.sankuai.meituan.waimai.econtract.server.exception.EcontractException;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractRecordService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.Impl.EcontractBigRecordParseService;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.Impl.AbstractExecutor;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.Impl.EstampExecutor;
import com.sankuai.meituan.waimai.econtract.server.utils.DealVersionUtils;
import com.sankuai.meituan.waimai.econtract.server.utils.UrlConvetUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.CallbackConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Set;

/**
 * 【Author】: 窦旭蒙
 * 【MIS】: douxumeng
 * 【Date】: 2022-12-15 15:53
 * 【Email】: <EMAIL>
 * 【Desc】:
 */
@Slf4j
@Service
public class AsyncFilingContractCommon extends AsyncCommon {

    private static final Set<Integer> PERMITTED_ERROR_CODES = Sets.newHashSet();

    private static final int SUPPLIER_CONTRACT_COMPLETE_ERROR_CODE = 300031; // SUPPLIER_CONTRACT_COMPLETE_ERROR(300031, "合同已归档", 241423),

    static {
        PERMITTED_ERROR_CODES.add(com.meituan.sankuai.bsi.esign.common.exception.biz.BizErrorCode.CONTRACT_HAS_BE_ARCHIVED.getErrorCode());
        PERMITTED_ERROR_CODES.add(SUPPLIER_CONTRACT_COMPLETE_ERROR_CODE); // bsi-esign-sdk 依赖包最新版本里有这个枚举，后续升级依赖包版本后可引用
    }


    public void handlerAsyncTaskCommon(JSONObject resultObject, EcontractAsyncRecordEntity econtractAsyncRecord) {
        log.debug("AsyncFilingContractHandler#handlerAsyncTask#econtractAsyncRecord:{}", JSON.toJSONString(econtractAsyncRecord));
        if (null == econtractAsyncRecord || StringUtils.isEmpty(econtractAsyncRecord.getTaskContext())) {
            log.error("异步归档合同处理流水信息为空，msg:{}", resultObject.toJSONString());
            throw new EcontractException(EcontractException.ASYNC_HANDLER_ERROR, "异步归档合同处理流水信息为空");
        }
        EcontractContext context = parseContext(econtractAsyncRecord);
        try {
            //获取任务执行结果
            JSONObject data = resultObject.getJSONObject("data");
            if (null == data) {
                throw new EcontractException(EcontractException.ESIGN_CALLBACK_ERROR, "签章平台回调结果格式异常");
            }

            Integer code = data.getInteger("code");
            String msg = data.getString("message");
            if (1000 == code) {
                context.setContextState(ContextConstant.CONTEXT_FINISH);
                abstractExecutor.executeSuccess(context);
                log.info("AsyncFilingEContractExecutor success , recordkey : {} ",
                        context.getEcontractRecordEntity().getRecordKey());
                //更新异步任务信息
                updateAsyncTaskStatus(econtractAsyncRecord, EcontractAsyncTaskConstant.SUCCESS);
                //记录异步归档耗时
                metricAsyncFilingCost(econtractAsyncRecord);
                //记录签章执行状态
                metricFilingStatus("合同归档成功", "异步归档");
            } else if (PERMITTED_ERROR_CODES.contains(code)) {
                // 如果是因"合同已归档"等错误导致的归档失败，则不认为任务失败
                context.setContextState(ContextConstant.CONTEXT_FINISH);
                abstractExecutor.executeSuccess(context);
                log.warn("AsyncFilingEContractExecutor error , recordkey : {} , msg : {}",
                        context.getEcontractRecordEntity().getRecordKey(), msg);
            } else {
                EcontractException.FILING_ECONTRACT_EXCEPTION.newInstance(msg);
            }
        } catch (EcontractException econtractException) {
            //失败重试
            log.error("handlerAsyncTask签章平台处理异常，errorCode:{}, errorMsg:{}, result:{}",
                    econtractException.getErrorCode(), econtractException.getMessage(), resultObject.toJSONString());
            updateAsyncTaskStatus(econtractAsyncRecord, EcontractAsyncTaskConstant.FAIL);
            failRetry(context, econtractException);
            metricFilingStatus("合同归档失败", "异步归档");
        } catch (Exception exception) {
            //失败重试
            log.error("handlerAsyncTask系统异常，result:{}", resultObject.toJSONString(), exception);
            updateAsyncTaskStatus(econtractAsyncRecord, EcontractAsyncTaskConstant.FAIL);
            failRetry(context, exception);
            metricFilingStatus("合同归档失败", "异步归档");
        }
    }

    private void failRetry(EcontractContext context, Exception e) {
        log.error("fail to FilingEContractExecutor :" + context.getEcontractRecordEntity().getRecordKey() + ",msg :" + e.getMessage(), e);
        abstractExecutor.executeFail(context, e);
    }

    private void metricAsyncFilingCost(EcontractAsyncRecordEntity econtractAsyncRecord) {
        Long currentTime = System.currentTimeMillis();
        Long costTime = currentTime - econtractAsyncRecord.getCtime();
        econtractMetricService.metricAsyncOperateCost("filingContract", costTime);
    }

    private void metricFilingStatus(String filingStatus, String filingType) {
        econtractMetricService.metricFilingContractStatus(filingStatus, filingType);
    }

}

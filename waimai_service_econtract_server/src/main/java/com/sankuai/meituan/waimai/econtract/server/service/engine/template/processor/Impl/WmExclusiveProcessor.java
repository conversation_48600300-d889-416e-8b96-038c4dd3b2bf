package com.sankuai.meituan.waimai.econtract.server.service.engine.template.processor.Impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.bizsso.thrift.CaptchaTService;
import com.sankuai.meituan.waimai.bizsso.thrift.PhoneCaptcha;
import com.sankuai.meituan.waimai.bizsso.thrift.Source;
import com.sankuai.meituan.waimai.bizsso.thrift.exception.BizssoServerException;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.bo.task.TaskContext;
import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import com.sankuai.meituan.waimai.econtract.server.constants.ContextConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.EcontractRecordConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.MccConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.RealNameConstanst;
import com.sankuai.meituan.waimai.econtract.server.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.Impl.SmsExecutor;
import com.sankuai.meituan.waimai.econtract.server.utils.*;
import com.sankuai.meituan.waimai.econtrct.client.constants.UpstreamStatusEnum;
import com.sankuai.meituan.waimai.econtrct.client.constants.WebViewConstant;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractSmsDealEntityMapper;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRealNameAuthEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRealNameDealEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractSmsDealEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractTaskEntity;
import com.sankuai.meituan.waimai.econtract.server.exception.EcontractException;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractCertifyService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractRealNameAuthService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractRecordService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractTaskService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.TairService;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.TaskManager;
import com.sankuai.meituan.waimai.econtract.server.service.engine.template.processor.TemplateProcessor;
import com.sankuai.meituan.waimai.econtract.server.service.mq.TaskMsg;
import com.sankuai.meituan.waimai.econtrct.client.constants.CallbackConstant;
import com.sankuai.meituan.waimai.econtrct.client.constants.RealNameAuthFailCode;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.SignH5InfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.SignerInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageInfoBo;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.sankuai.meituan.waimai.econtract.server.constants.MccConstant.REAL_NAME_JUMP;

/**
 * <AUTHOR> Hou
 * @date 2017/10/21
 * @time 下午1:25
 */

@Deprecated
@Service
public class WmExclusiveProcessor extends AbstractProcessor implements TemplateProcessor {

    private static final Logger LOGGER = LoggerFactory.getLogger(WmExclusiveProcessor.class);

    @Autowired
    private CaptchaTService.Iface captchaTService;
    @Autowired
    private EcontractRecordService econtractRecordService;
    @Autowired
    private EcontractTaskService econtractTaskService;
    @Autowired
    private TaskManager taskManager;
    @Autowired
    private EcontractCertifyService econtractCertifyService;
    @Autowired
    private EcontractRealNameAuthService realNameAuthService;
    @Autowired
    private TairService tairService;
    @Autowired
    private EcontractSmsDealEntityMapper smsDealEntityMapper;
    @Autowired
    private SmsExecutor smsExecutor;


    @Override
    @Transactional
    public String applyEcontractByTemplate(EcontractContext context) {
        //1.创建操作流水
        EcontractRecordEntity econtractRecordEntity = new EcontractRecordEntity();
        String[] recordKeyArray = context.getEcontractEntity().getEcontractType().split("_");
        String recordKey = DealVersionUtils.getRecordKeyDealVersion(recordKeyArray[recordKeyArray.length - 1]);
        econtractRecordEntity.setRecordKey(recordKey);
        econtractRecordEntity.setEcontractUserId(context.getEcontractUserEntity().getId());
        econtractRecordEntity.setEcontractId(context.getEcontractEntity().getId());
        econtractRecordEntity.setEcontractType(context.getEcontractEntity().getEcontractType());
        econtractRecordEntity.setEcontractState(EcontractRecordConstant.INIT);
        econtractRecordEntity.setEcontractStage(TaskConstant.APPLY_ECONTRACT);
        econtractRecordEntity.setValid(EcontractRecordConstant.valid);
        econtractRecordEntity.setUtime(TimeUtils.getUTime());
        econtractRecordEntity.setCtime(TimeUtils.getCTime());
        econtractRecordEntity.setVersion(0);
        econtractRecordEntity.setRecordBizKey(context.getRecordBizKey());
        econtractRecordEntity.setEcontractRecordContext(JSON.toJSONString(context));
        econtractRecordService.insertSelective(econtractRecordEntity);
        context = JSON.parseObject(econtractRecordEntity.getEcontractRecordContext(), EcontractContext.class);

        //2.操作流水设置入上下文
        context.setEcontractRecordEntity(econtractRecordEntity);
        econtractRecordEntity.setEcontractRecordContext(JSON.toJSONString(context));
        econtractRecordService.updateByPrimaryKeySelective(econtractRecordEntity);

        //3.创建父任务
        int parentTaskId = createTask(context, 0, TaskConstant.PARENT_TASK);
        //4.创建子任务
        createTask(context, parentTaskId, TaskConstant.APPLY_ECONTRACT);
        createTask(context, parentTaskId, TaskConstant.CREATE_PDF);
        createTask(context, parentTaskId, TaskConstant.UPLOAD_PDF);
        createTask(context, parentTaskId, TaskConstant.CA_CERTIFY_PART_A);
        createTask(context, parentTaskId, TaskConstant.SMS_SIGNER_A);
        createTask(context, parentTaskId, TaskConstant.REAL_NAME_AUTH_A);
        createTask(context, parentTaskId, TaskConstant.CONFIRM_STAMP_A);
        createTask(context, parentTaskId, TaskConstant.ECONTRACT_STAMP_A);
        createTask(context, parentTaskId, TaskConstant.ECONTRACT_FILING);
        createTask(context, parentTaskId, TaskConstant.ECONTRACT_FINISH);


        //4.提交任务
        context.setContextState(ContextConstant.CONTEXT_FINISH);
        TaskContext taskContext = new TaskContext();
        taskContext.setState(TaskConstant.TASK_SUCCESS);
        context.setTaskContext(taskContext);

        String contextStr = JSON.toJSONString(context);
        if (context.getUseMafka() && contextStr.getBytes().length > MccConfig.MESSAGE_SIZE) {
            context.setContextId(messageProducerHandler.getEntityId(contextStr));
        }

        return recordKey;
    }


    /**
     * @param context
     * @param callType
     */
    @Override
    @Transactional
    public void callBackCommitTemplateStage(EcontractContext context, String callType, Map<String, String> paramMap) {
        LOGGER.info(" WmExclusiveHandler callBackCommitTemplateStage recordKey is:{},callType is:{},param is :{}", context.getEcontractRecordEntity().getRecordKey(), callType, JSON.toJSONString(paramMap));
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(context.getEcontractRecordEntity().getRecordKey());
        replaceEcontractBo(context, paramMap);

        if (TaskConstant.CREATE_PDF.equals(recordEntity.getEcontractStage())
                && CallbackConstant.CREATE_PDF_COMMIT_CALL_BACK.equals(callType)
                && EcontractRecordConstant.WAIT_CALL_BACK.equals(recordEntity.getEcontractState())) {
            TaskContext taskContext = new TaskContext();
            taskContext.setEcontractStage(TaskConstant.CREATE_PDF);
            taskContext.setState(TaskConstant.TASK_SUCCESS);
            context.setTaskContext(taskContext);
            context.setContextState(ContextConstant.CONTEXT_CALLBACK);
            controlTemplate(context, CallbackConstant.TYPE_GATE_WAY);
        } else if (TaskConstant.UPLOAD_PDF.equals(recordEntity.getEcontractStage())
                && CallbackConstant.UPLOAD_PDF_CALL_BACK.equals(callType)
                && EcontractRecordConstant.WAIT_CALL_BACK.equals(recordEntity.getEcontractState())) {
            //上传签章平台成功 1000是成功，2002是重复上传,现在改为2002重复上传改为2004
            TaskContext taskContext = new TaskContext();
            taskContext.setEcontractStage(TaskConstant.UPLOAD_PDF);
            context.setTaskContext(taskContext);
            if ("1000".equals(paramMap.get(CallbackConstant.UPLOAD_RESULT_CODE))
                    || "2004".equals(paramMap.get(CallbackConstant.UPLOAD_RESULT_CODE))) {
                context.setContextState(ContextConstant.CONTEXT_CALLBACK);
                taskContext.setState(TaskConstant.TASK_SUCCESS);
                controlTemplate(context, CallbackConstant.TYPE_GATE_WAY);
            } else {
                String msg = JSON.toJSONString(paramMap);
                taskContext.setState(TaskConstant.TASK_FAIL);
                taskContext.setFailMessage(msg);
                taskContext.setErrorCode(EcontractException.ESTAMP_UPLOAD_ERROR);
                LOGGER.warn("fail to call back upload pdf , recordKey is:{},callType is:{},param is :{} ", context.getEcontractRecordEntity().getRecordKey(), callType, JSON.toJSONString(paramMap));
                dropTemplateByCallBack(context, msg);
            }
        } else if (TaskConstant.REAL_NAME_AUTH_A.equals(recordEntity.getEcontractStage())
                && CallbackConstant.REAL_NAME_AUTH_CALL_BACK.equals(callType)
                && EcontractRecordConstant.WAIT_CALL_BACK.equals(recordEntity.getEcontractState())) {
            TaskContext taskContext = new TaskContext();
            taskContext.setEcontractStage(TaskConstant.REAL_NAME_AUTH_A);
            context.setTaskContext(taskContext);
            if ("true".equals(paramMap.get(CallbackConstant.REAL_NAME_AUTH_RESULT))) {
                //创建一个成功的回调任务
                taskContext.setExecutorResult(paramMap);
                taskContext.setState(TaskConstant.TASK_SUCCESS);
                context.setContextState(ContextConstant.CONTEXT_CALLBACK);
                controlTemplate(context, CallbackConstant.TYPE_GATE_WAY);
            } else {
                String msg = RealNameAuthFailCode.getByKey(Integer.parseInt(paramMap.get(CallbackConstant.REAL_NAME_AUTH_FAIL_CODE)));
                taskContext.setState(TaskConstant.TASK_FAIL);
                taskContext.setFailMessage(msg);
                taskContext.setErrorCode(EcontractException.REAL_NAME_ERROR);
                LOGGER.warn("fail to realNameAuth  , recordKey is:{},msg is :{} ", context.getEcontractRecordEntity().getRecordKey(), msg);
                dropTemplateByCallBack(context, msg);
            }
        } else if (TaskConstant.ECONTRACT_STAMP_A.equals(recordEntity.getEcontractStage())
                && CallbackConstant.ESTAMP_CALL_BACK.equals(callType)
                && EcontractRecordConstant.WAIT_CALL_BACK.equals(recordEntity.getEcontractState())) {
            //签章成功
            TaskContext taskContext = new TaskContext();
            taskContext.setEcontractStage(TaskConstant.ECONTRACT_STAMP_A);
            context.setTaskContext(taskContext);
            if ("1000".equals(paramMap.get(CallbackConstant.ESTAMP_RESULT_CODE))) {
                Thread thread = new Thread(new Runnable() {
                    @Override
                    public void run() {
                        taskContext.setExecutorResult(paramMap);
                        taskContext.setState(TaskConstant.TASK_SUCCESS);
                        recordEntity.setSaveUrl(UrlConvetUtil.changeEstampUrl(paramMap.get(CallbackConstant.ESTAMP_DOWNLOAD_URL)));
                        econtractRecordService.updateByPrimaryKeySelective(recordEntity);
                        LOGGER.info("update changeUrl, recordkey is : {} , recordVersion is :{} , change URL is {}", recordEntity.getRecordKey(), recordEntity.getVersion(), recordEntity.getSaveUrl());
                        context.setContextState(ContextConstant.CONTEXT_CALLBACK);
                        controlTemplate(context, CallbackConstant.TYPE_GATE_WAY);
                    }
                });
                thread.start();
            } else {
                String msg = paramMap.get(CallbackConstant.ESTAMP_RESULT_MSG);
                taskContext.setState(TaskConstant.TASK_FAIL);
                taskContext.setFailMessage(msg);
                taskContext.setErrorCode(EcontractException.ESTAMP_UPLOAD_ERROR);
                LOGGER.warn("fail to call estamp pdf , recordKey is:{},error is :{} ", context.getEcontractRecordEntity().getRecordKey(), msg);
                dropTemplateByCallBack(context, msg);
            }
        }
    }

    /**
     * 处理类
     *
     * @param context
     * @param callBackType
     * @return 1=不需要发消息 2=commit 3=submit
     */
    @Transactional(rollbackFor = Exception.class)
    public int handleTemplate(EcontractContext context, String callBackType) {
        EcontractRecordEntity oldRecord = econtractRecordService.selectByPrimaryKey(context.getEcontractRecordEntity().getId());
        context.setEcontractRecordEntity(oldRecord);
        int res = 1;
        //处理失败
        if (TaskConstant.TASK_FAIL.equals(context.getTaskContext().getState())) {
            LOGGER.info("fail to execute task , recordKey is {} , fail reason is : {} ", context.getEcontractRecordEntity().getRecordKey(), context.getTaskContext().getFailMessage());
            failTask(context);
            res = 3;
        } else if (ContextConstant.CONTEXT_FINISH.equals(context.getContextState())
                && TaskConstant.APPLY_ECONTRACT.equals(oldRecord.getEcontractStage())) {
            turnCreatePdf(context);
            res = 3;
        } else if (ContextConstant.CONTEXT_FINISH.equals(context.getContextState())
                && TaskConstant.CREATE_PDF.equals(oldRecord.getEcontractStage())
                && EcontractRecordConstant.RUNNING.equals(oldRecord.getEcontractState())) {
            commitCreatePdf(context);
            res = 1;
        } else if (ContextConstant.CONTEXT_CALLBACK.equals(context.getContextState())
                && TaskConstant.CREATE_PDF.equals(oldRecord.getEcontractStage())
                && EcontractRecordConstant.WAIT_CALL_BACK.equals(oldRecord.getEcontractState())) {
            turnUploadPdf(context);
            res = 3;
        } else if (ContextConstant.CONTEXT_CALLBACK.equals(context.getContextState())
                && TaskConstant.UPLOAD_PDF.equals(oldRecord.getEcontractStage())
                && EcontractRecordConstant.WAIT_CALL_BACK.equals(oldRecord.getEcontractState())) {
            commitUploadPdf(context);
            res = 2;
        } else if (ContextConstant.CONTEXT_TO_EXECUTE.equals(context.getContextState())
                && TaskConstant.CA_CERTIFY_PART_A.equals(oldRecord.getEcontractStage())
                && EcontractRecordConstant.RUNNING.equals(oldRecord.getEcontractState())) {
            turnCACertifyPartA(context);
            res = 3;
        } else if (ContextConstant.CONTEXT_FINISH.equals(context.getContextState())
                && TaskConstant.CA_CERTIFY_PART_A.equals(oldRecord.getEcontractStage())
                && EcontractRecordConstant.RUNNING.equals(oldRecord.getEcontractState())) {
            commitCACertifyPartA(context);
            res = 2;
        } else if (ContextConstant.CONTEXT_TO_EXECUTE.equals(context.getContextState())
                && TaskConstant.SMS_SIGNER_A.equals(oldRecord.getEcontractStage())
                && EcontractRecordConstant.RUNNING.equals(oldRecord.getEcontractState())) {
            turnSmsSigner_A(context);
            res = 3;
        } else if (ContextConstant.CONTEXT_FINISH.equals(context.getContextState())
                && TaskConstant.SMS_SIGNER_A.equals(oldRecord.getEcontractStage())
                && EcontractRecordConstant.RUNNING.equals(oldRecord.getEcontractState())) {
            commitSmsSigner_A(context);
            res = 1;
        } else if (ContextConstant.CONTEXT_CALLBACK.equals(context.getContextState())
                && TaskConstant.REAL_NAME_AUTH_A.equals(oldRecord.getEcontractStage())
                && EcontractRecordConstant.WAIT_CALL_BACK.equals(oldRecord.getEcontractState())) {
            commitRealNameAuth_A(context);
            res = 1;
        } else if (ContextConstant.CONTEXT_CALLBACK.equals(context.getContextState())
                && TaskConstant.CONFIRM_STAMP_A.equals(oldRecord.getEcontractStage())
                && EcontractRecordConstant.WAIT_CALL_BACK.equals(oldRecord.getEcontractState())) {
            commitConfirmStampA(context);
            res = 2;
        } else if (ContextConstant.CONTEXT_TO_EXECUTE.equals(context.getContextState())
                && TaskConstant.ECONTRACT_STAMP_A.equals(oldRecord.getEcontractStage())
                && EcontractRecordConstant.RUNNING.equals(oldRecord.getEcontractState())) {
            turnEcontractStampA(context);
            res = 3;
        } else if (ContextConstant.CONTEXT_CALLBACK.equals(context.getContextState())
                && TaskConstant.ECONTRACT_STAMP_A.equals(oldRecord.getEcontractStage())
                && EcontractRecordConstant.WAIT_CALL_BACK.equals(oldRecord.getEcontractState())) {
            commitEcontractStampA(context);
            res = 2;
        } else if (ContextConstant.CONTEXT_TO_EXECUTE.equals(context.getContextState())
                && TaskConstant.ECONTRACT_FILING.equals(oldRecord.getEcontractStage())
                && EcontractRecordConstant.RUNNING.equals(oldRecord.getEcontractState())) {
            turnFilingEcontract(context);
            res = 3;
        } else if (ContextConstant.CONTEXT_FINISH.equals(context.getContextState())
                && TaskConstant.ECONTRACT_FILING.equals(oldRecord.getEcontractStage())
                && EcontractRecordConstant.RUNNING.equals(oldRecord.getEcontractState())) {
            commitFilingEcontract(context);
            res = 3;
        } else if (ContextConstant.CONTEXT_FINISH.equals(context.getContextState())
                && TaskConstant.ECONTRACT_FINISH.equals(oldRecord.getEcontractStage())
                && EcontractRecordConstant.RUNNING.equals(oldRecord.getEcontractState())) {
            commitApplyEcontract(context);
            res = 1;
        }
        //对大消息体做处理
        String contextStr = JSON.toJSONString(context);
        if (context.getUseMafka() && contextStr.getBytes().length > MccConfig.MESSAGE_SIZE) {
            context.setContextId(messageProducerHandler.getEntityId(contextStr));
        }
        return res;
    }


    @Override
    public void controlTemplate(EcontractContext context, String callBackType) {
        boolean isMafka = taskManager.mafkaMigrateSwitch();
        context.setUseMafka(isMafka);
        int res = handleTemplate(context, callBackType);
        if (res == 1) {
            return;
        }
        TaskMsg taskMsg = handleMessage(context, isMafka);
        if (res == 2) {
            taskManager.commitTask(taskMsg, isMafka);
        } else {
            taskManager.submitTask(taskMsg, isMafka);
        }

    }


    @Override
    public String queryRealNameRedirectUrl(String recordKey) {
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(recordKey);
        EcontractContext context = JSON.parseObject(recordEntity.getEcontractRecordContext(), EcontractContext.class);
        if (TaskConstant.REAL_NAME_AUTH_A.equals(recordEntity.getEcontractStage())) {
            StageInfoBo stageInfoBo = ContextUtils.getStageInfoBoFromList(context, TaskConstant.REAL_NAME_AUTH_A);
            SignerInfoBo signerInfoBo = stageInfoBo.getSignerInfoBo();
            EcontractTaskEntity taskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.SMS_SIGNER_A);
            String realNameDealVersion = DealVersionUtils.getRealNameDealVersion(taskEntity.getId());
            return realNameAuthService.queryRealNameRedirectUrl(realNameDealVersion, signerInfoBo, taskEntity);
        } else {
            return "";
        }
    }


    @Override
    public boolean sendPhoneCap(String recordKey) {
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(recordKey);
        EcontractContext context = JSON.parseObject(recordEntity.getEcontractRecordContext(), EcontractContext.class);
        EcontractTaskEntity taskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.CONFIRM_STAMP_A);
        String snedPhone = "";
        try {
            if (TaskConstant.CONFIRM_STAMP_A.equals(recordEntity.getEcontractStage()) && EcontractRecordConstant.WAIT_CALL_BACK.equals(recordEntity.getEcontractState())) {
                StageInfoBo stageInfoBo = ContextUtils.getStageInfoBoFromList(context, TaskConstant.REAL_NAME_AUTH_A);
                SignerInfoBo signerInfoBo = stageInfoBo.getSignerInfoBo();
                //60秒失效校验
                String phoneCaptchaKey = DealVersionUtils.getCaptchaVersion(taskEntity.getId());
                String turlingId = tairService.getStrValue(phoneCaptchaKey);
                if (null != turlingId) {
                    LOGGER.info("fail to ReSendPhoneCap in 60 seconds , recordKey {}", recordKey);
                    return false;
                }

                PhoneCaptcha phoneCaptcha = null;
                String phone = ConfigUtilAdapter.getString(MccConstant.TEST_PHONE_CAP);
                if (StringUtils.isNotEmpty(phone) && (StringUtils.isNotEmpty(phone.split(",")[0]))) {
                    phone = phone.split(",")[0];
                    snedPhone = phone;
                    phoneCaptcha = captchaTService.getPhoneCaptcha(phone, Source.WAIMAI_M_CONTRACT);
                } else {
                    snedPhone = signerInfoBo.getPhone();
                    phoneCaptcha = captchaTService.getPhoneCaptcha(signerInfoBo.getPhone(), Source.WAIMAI_M_CONTRACT);
                }
                tairService.setStrValue(phoneCaptchaKey, phoneCaptcha.getTurlingTestId(), 60);
            }
            if (ConfigUtilAdapter.getBoolean(REAL_NAME_JUMP, false)) {
                if (TaskConstant.REAL_NAME_AUTH_A.equals(recordEntity.getEcontractStage())
                        || TaskConstant.REAL_NAME_AUTH_B.equals(recordEntity.getEcontractStage())
                        || TaskConstant.REAL_NAME_AUTH_C.equals(recordEntity.getEcontractStage())
                        || TaskConstant.REAL_NAME_AUTH_D.equals(recordEntity.getEcontractStage())
                        || TaskConstant.REAL_NAME_AUTH_E.equals(recordEntity.getEcontractStage())
                        || TaskConstant.REAL_NAME_AUTH_F.equals(recordEntity.getEcontractStage())
                        || TaskConstant.REAL_NAME_AUTH_G.equals(recordEntity.getEcontractStage())
                        || TaskConstant.REAL_NAME_AUTH_H.equals(recordEntity.getEcontractStage())) {
                    autoRealName(recordKey);
                }
            }
            return true;
        } catch (BizssoServerException bz) {
            LOGGER.warn("fail to sendPhoneCap , recordKey :{} , phone is :{}, error code is :{} , error msg is :{}", recordKey, snedPhone, bz.getCode(), bz.getMsg());
        } catch (Exception e) {
            LOGGER.error(String.format("fail to sendPhoneCap , recordKey %s , error is %s", recordKey, e.getMessage()), e);
        }
        return false;
    }

    @Override
    public boolean verifyPhoneCaptcha(String recordKey, String code) {
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(recordKey);
        EcontractContext context = JSON.parseObject(recordEntity.getEcontractRecordContext(), EcontractContext.class);
        EcontractTaskEntity taskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.CONFIRM_STAMP_A);
        try {
            if (TaskConstant.CONFIRM_STAMP_A.equals(recordEntity.getEcontractStage()) && EcontractRecordConstant.WAIT_CALL_BACK.equals(recordEntity.getEcontractState())) {
                StageInfoBo stageInfoBo = ContextUtils.getStageInfoBoFromList(context, TaskConstant.REAL_NAME_AUTH_A);
                SignerInfoBo signerInfoBo = stageInfoBo.getSignerInfoBo();
                String turlingId = tairService.getStrValue(DealVersionUtils.getCaptchaVersion(taskEntity.getId()));
                return toVerify(turlingId, signerInfoBo, code);
            }
        } catch (Exception e) {
            LOGGER.error(String.format("fail to verifyPhoneCaptcha , recordKey %s", recordKey), e);
        }
        return false;
    }

    @Override
    @Transactional
    public void cancelSignEContract(String recordKey, String type) {
        super.cancelSignEContract(recordKey, type);
    }


    @Override
    @Transactional
    public void confirmSignEContract(String recordKey)
            throws com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException {
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(recordKey);
        EcontractContext context = JSON.parseObject(recordEntity.getEcontractRecordContext(), EcontractContext.class);
        if (TaskConstant.CONFIRM_STAMP_A.equals(recordEntity.getEcontractStage()) && EcontractRecordConstant.WAIT_CALL_BACK.equals(recordEntity.getEcontractState())) {
            context.setContextState(ContextConstant.CONTEXT_CALLBACK);
            context.setTaskContext(new TaskContext());
            controlTemplate(context, recordKey);
        } else {
            com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException.STATUS_ERROR_EXCEPTION.newInstance("电子合同状态异常");
        }
    }

    @Override
    public List<String> doRetrySms(String recordKey) {
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(recordKey);
        List<EcontractSmsDealEntity> smsDealEntityList = smsDealEntityMapper.queryDealByRecordId(recordEntity.getId());
        EcontractSmsDealEntity smsDealEntity = CollectionUtils.isNotEmpty(smsDealEntityList) ? smsDealEntityList.get(0) : null;
        String retry = ConfigUtilAdapter.getString(MccConstant.SIGN_URL_RETRY_TIME, String.valueOf(10 * 60));
        if (ConfigUtilAdapter.getBoolean(REAL_NAME_JUMP, false)) {
            if (TaskConstant.REAL_NAME_AUTH_A.equals(recordEntity.getEcontractStage())
                    || TaskConstant.REAL_NAME_AUTH_B.equals(recordEntity.getEcontractStage())
                    || TaskConstant.REAL_NAME_AUTH_C.equals(recordEntity.getEcontractStage())
                    || TaskConstant.REAL_NAME_AUTH_D.equals(recordEntity.getEcontractStage())
                    || TaskConstant.REAL_NAME_AUTH_E.equals(recordEntity.getEcontractStage())
                    || TaskConstant.REAL_NAME_AUTH_F.equals(recordEntity.getEcontractStage())
                    || TaskConstant.REAL_NAME_AUTH_G.equals(recordEntity.getEcontractStage())
                    || TaskConstant.REAL_NAME_AUTH_H.equals(recordEntity.getEcontractStage())) {
                autoRealName(recordKey);
            }
        }
        if (TaskConstant.REAL_NAME_AUTH_A.equals(recordEntity.getEcontractStage()) ||
                TaskConstant.CONFIRM_STAMP_A.equals(recordEntity.getEcontractStage())
        ) {
            if (smsDealEntity == null || TimeCompareUtils.isMoreThan(smsDealEntity.getCtime(), new Date(), Integer.valueOf(retry))) {

                EcontractContext context = JSON.parseObject(recordEntity.getEcontractRecordContext(), EcontractContext.class);
                EcontractTaskEntity newTaskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.SMS_SIGNER_A);
                TaskContext taskContext = new TaskContext();
                taskContext.setTaskId(newTaskEntity.getId());
                taskContext.setStageInfoBo(ContextUtils.getStageInfoBoFromList(context, TaskConstant.REAL_NAME_AUTH_A));
                taskContext.setExecutorType(TaskConstant.SMS_EXECUTOR);
                taskContext.setEcontractStage(TaskConstant.REAL_NAME_AUTH_A);
                taskContext.setState(TaskConstant.TASK_RUNNIG);
                context.setTaskContext(taskContext);
                context.setContextState(ContextConstant.CONTEXT_TO_EXECUTE);
                newTaskEntity.setEcontractTaskContext(JSON.toJSONString(taskContext));
                doNotifier(context, taskContext);

                boolean isMafka = taskManager.mafkaMigrateSwitch();
                TaskMsg taskMsg = handleMessage(context, isMafka);
                taskManager.submitTask(taskMsg, isMafka);
                return context.getTaskContext().getStageInfoBo().getSignerInfoBo().getMobileList();
            } else {
                EcontractException.SMS_RETRY_NAME_EXCEPTION.newInstance("发送短信频繁");
                return Lists.newArrayList();
            }
        } else {
            EcontractException.SMS_RETRY_NAME_EXCEPTION.newInstance("电子合同状态错误");
            return Lists.newArrayList();
        }

    }

    @Override
    public SignH5InfoBo querySignH5InoByRecordIdAndTaskId(Integer recordId, Integer taskId) {
        SignH5InfoBo signH5InfoBo = new SignH5InfoBo();
        String viewStage;
        EcontractRecordEntity recordEntity = econtractRecordService.selectByPrimaryKey(recordId);
        EcontractContext context = JSON.parseObject(recordEntity.getEcontractRecordContext(), EcontractContext.class);
        if (TaskConstant.REAL_NAME_AUTH_A.equals(recordEntity.getEcontractStage())) {
            viewStage = WebViewConstant.STAGE_WAIT_REAL_NAME;
        } else if (TaskConstant.CONFIRM_STAMP_A.equals(recordEntity.getEcontractStage())) {
            viewStage = WebViewConstant.STAGE_WAIT_SIGN;
        } else if (TaskConstant.ECONTRACT_FINISH.equals(recordEntity.getEcontractStage())
                && EcontractRecordConstant.SUCCESS.equals(recordEntity.getEcontractState())) {
            // 是否等到上游状态同步结束再返回
            if (MccConfig.isWaitForUpstreamStatusSwitch() && context.getIsWaitForUpstream()) {
                if (recordEntity.getUpstreamStatus() == UpstreamStatusEnum.SUCCESS.getCode()) { // 上游已同步成功，则返回成功
                    viewStage = WebViewConstant.STAGE_SUCCESS;
                } else if (recordEntity.getUpstreamStatus() == UpstreamStatusEnum.FAIL.getCode()) {// 上游同步为失败，则返回失败
                    viewStage = WebViewConstant.STAGE_FAIL;
                } else {// 上游未返回，则返回进行中
                    viewStage = WebViewConstant.STAGE_WAIT_ASY_EXECUTE;
                }
            } else {
                viewStage = WebViewConstant.STAGE_SUCCESS;
            }
        } else if (TaskConstant.ECONTRACT_FINISH.equals(recordEntity.getEcontractStage())
                && EcontractRecordConstant.FAIL.equals(recordEntity.getEcontractState())) {
            if (TaskConstant.SERVER_CANCEL.equals(recordEntity.getFailMessage()) || TaskConstant.H5_CANCEL.equals(recordEntity.getFailMessage())) {
                viewStage = WebViewConstant.STAGE_CANCEL_SIGN;
            } else {
                viewStage = WebViewConstant.STAGE_FAIL;
            }
        } else {
            viewStage = WebViewConstant.STAGE_WAIT_ASY_EXECUTE;
        }
        StageInfoBo stageInfoBo = ContextUtils.getStageInfoBoFromList(context, TaskConstant.REAL_NAME_AUTH_A);
        signH5InfoBo.setSignerName(stageInfoBo.getSignerInfoBo().getName());
        signH5InfoBo.setRecordKey(recordEntity.getRecordKey());
        signH5InfoBo.setViewStage(viewStage);
        signH5InfoBo.setSignerPhone(stageInfoBo.getSignerInfoBo().getPhone());
        return signH5InfoBo;
    }

    /**
     * 状态流转----创建PDF
     *
     * @param context
     */
    private void turnCreatePdf(EcontractContext context) {
        LOGGER.info("econtract context record : {} turn create PDF", context.getEcontractRecordEntity().getRecordKey());
        //1.修改record状态;
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(context.getEcontractRecordEntity().getRecordKey());
        recordEntity.setEcontractStage(TaskConstant.CREATE_PDF);
        recordEntity.setEcontractState(EcontractRecordConstant.RUNNING);
        recordEntity.setUtime(TimeUtils.getUTime());
        context.setEcontractRecordEntity(recordEntity);
        econtractRecordService.updateByPrimaryKeySelective(recordEntity);

        //2.修改老的task数据;
        EcontractTaskEntity parentTask = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.PARENT_TASK);
        EcontractTaskEntity applyTask = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.APPLY_ECONTRACT);
        parentTask.setTaskState(TaskConstant.TASK_RUNNIG);
        parentTask.setUtime(TimeUtils.getUTime());
        applyTask.setTaskState(TaskConstant.TASK_SUCCESS);
        applyTask.setUtime(TimeUtils.getUTime());
        econtractTaskService.updateByPrimaryKeySelective(parentTask);
        econtractTaskService.updateByPrimaryKeySelective(applyTask);

        //3.修改新的task数据
        EcontractTaskEntity taskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.CREATE_PDF);
        TaskContext taskContext = new TaskContext();
        taskContext.setTaskId(taskEntity.getId());
        taskContext.setStageInfoBo(ContextUtils.getStageInfoBoFromList(context, TaskConstant.CREATE_PDF));
        taskContext.setExecutorType(TaskConstant.CREATE_PDF_EXECUTOR);
        taskContext.setEcontractStage(TaskConstant.CREATE_PDF);
        taskContext.setState(TaskConstant.TASK_RUNNIG);
        context.setTaskContext(taskContext);
        context.setContextState(ContextConstant.CONTEXT_TO_EXECUTE);
        taskEntity.setEcontractTaskContext(JSON.toJSONString(taskContext));
        econtractTaskService.updateByPrimaryKeySelective(taskEntity);
    }

    /**
     * 状态流转----上传PDF
     *
     * @param context
     */
    private void commitCreatePdf(EcontractContext context) {
        LOGGER.info("econtract context record : {} commit create PDF", context.getEcontractRecordEntity().getRecordKey());
        //1.更新record状态，存储URL
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(context.getEcontractRecordEntity().getRecordKey());
        recordEntity.setEcontractStage(TaskConstant.CREATE_PDF);
        recordEntity.setEcontractState(EcontractRecordConstant.WAIT_CALL_BACK);
        recordEntity.setSaveUrl(context.getTaskContext().getExecutorResult().get(TaskConstant.PDF_URL));
        recordEntity.setUtime(TimeUtils.getUTime());
        econtractRecordService.updateByPrimaryKeySelective(recordEntity);

        //2.更新老task
        EcontractTaskEntity oldTaskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.CREATE_PDF);
        oldTaskEntity.setTaskState(TaskConstant.TASK_SUCCESS);
        oldTaskEntity.setUtime(TimeUtils.getUTime());
        econtractTaskService.updateByPrimaryKeySelective(oldTaskEntity);

        TaskContext taskContext = new TaskContext();
        taskContext.setEcontractStage(oldTaskEntity.getTaskType());
        taskContext.setState(oldTaskEntity.getTaskState());


        doNotifier(context, taskContext);
    }


    /**
     * 状态流转----上传PDF
     *
     * @param context
     */
    private void turnUploadPdf(EcontractContext context) {
        LOGGER.info("econtract context record : {} turn upload PDF", context.getEcontractRecordEntity().getRecordKey());
        EcontractRecordEntity recordEntity = context.getEcontractRecordEntity();
        recordEntity.setEcontractState(EcontractRecordConstant.WAIT_CALL_BACK);
        recordEntity.setEcontractStage(TaskConstant.UPLOAD_PDF);
        recordEntity.setUtime(TimeUtils.getUTime());
        econtractRecordService.updateByPrimaryKeySelective(recordEntity);

        EcontractTaskEntity newTaskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.UPLOAD_PDF);
        TaskContext taskContext = new TaskContext();
        taskContext.setTaskId(newTaskEntity.getId());
        taskContext.setExecutorType(TaskConstant.UPLOAD_EXECUTOR);
        taskContext.setEcontractStage(TaskConstant.UPLOAD_PDF);
        taskContext.setState(TaskConstant.TASK_RUNNIG);
        context.setTaskContext(taskContext);
        context.setContextState(ContextConstant.CONTEXT_TO_EXECUTE);
        newTaskEntity.setEcontractTaskContext(JSON.toJSONString(taskContext));
        econtractTaskService.updateByPrimaryKeySelective(newTaskEntity);

        doNotifier(context, taskContext);
    }


    private void commitUploadPdf(EcontractContext context) {
        LOGGER.info("econtract context record : {} commitUploadPdf ", context.getEcontractRecordEntity().getRecordKey());
        //1.修改record状态;
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(context.getEcontractRecordEntity().getRecordKey());
        recordEntity.setEcontractStage(TaskConstant.CA_CERTIFY_PART_A);
        recordEntity.setEcontractState(EcontractRecordConstant.RUNNING);
        recordEntity.setUtime(TimeUtils.getUTime());
        econtractRecordService.updateByPrimaryKeySelective(recordEntity);


        //2.更新老task
        EcontractTaskEntity oldTaskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.UPLOAD_PDF);
        oldTaskEntity.setTaskState(TaskConstant.TASK_SUCCESS);
        oldTaskEntity.setUtime(TimeUtils.getUTime());
        econtractTaskService.updateByPrimaryKeySelective(oldTaskEntity);

        context.setContextState(ContextConstant.CONTEXT_TO_EXECUTE);


        TaskContext taskContext = new TaskContext();
        taskContext.setEcontractStage(oldTaskEntity.getTaskType());
        taskContext.setState(EcontractRecordConstant.WAIT_CALL_BACK);
        doNotifier(context, taskContext);

    }


    /**
     * 状态流转 CAPartA 认证
     *
     * @param context
     */
    private void turnCACertifyPartA(EcontractContext context) {
        LOGGER.info("econtract context record : {} turnCACertifyPartA ", context.getEcontractRecordEntity().getRecordKey());

        //1.修改record状态;
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(context.getEcontractRecordEntity().getRecordKey());
        recordEntity.setEcontractStage(TaskConstant.CA_CERTIFY_PART_A);
        recordEntity.setEcontractState(EcontractRecordConstant.RUNNING);
        recordEntity.setUtime(TimeUtils.getUTime());
        econtractRecordService.updateByPrimaryKeySelective(recordEntity);


        //2.更新老task
        EcontractTaskEntity oldTaskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.UPLOAD_PDF);
        oldTaskEntity.setTaskState(TaskConstant.TASK_SUCCESS);
        oldTaskEntity.setUtime(TimeUtils.getUTime());
        econtractTaskService.updateByPrimaryKeySelective(oldTaskEntity);

        TaskContext taskContext = new TaskContext();
        taskContext.setEcontractStage(oldTaskEntity.getTaskType());
        taskContext.setState(oldTaskEntity.getTaskState());
        doNotifier(context, taskContext);


        EcontractTaskEntity newTaskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.CA_CERTIFY_PART_A);
        taskContext = new TaskContext();
        taskContext.setTaskId(newTaskEntity.getId());
        taskContext.setStageInfoBo(ContextUtils.getStageInfoBoFromList(context, TaskConstant.CA_CERTIFY_PART_A));
        taskContext.setExecutorType(TaskConstant.CA_CERTIFY_EXECUTOR);
        taskContext.setEcontractStage(TaskConstant.CA_CERTIFY_PART_A);
        taskContext.setState(TaskConstant.TASK_RUNNIG);
        context.setTaskContext(taskContext);
        context.setContextState(ContextConstant.CONTEXT_TO_EXECUTE);
        newTaskEntity.setEcontractTaskContext(JSON.toJSONString(taskContext));
        econtractTaskService.updateByPrimaryKeySelective(newTaskEntity);

        doNotifier(context, taskContext);

    }


    private void commitCACertifyPartA(EcontractContext context) {
        LOGGER.info("econtract context record : {} commitCACertifyPartA ", context.getEcontractRecordEntity().getRecordKey());

        //1.修改record状态;
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(context.getEcontractRecordEntity().getRecordKey());
        recordEntity.setEcontractStage(TaskConstant.SMS_SIGNER_A);
        recordEntity.setUtime(TimeUtils.getUTime());
        econtractRecordService.updateByPrimaryKeySelective(recordEntity);


        //2.更新老task
        EcontractTaskEntity oldTaskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.CA_CERTIFY_PART_A);
        oldTaskEntity.setTaskState(TaskConstant.TASK_SUCCESS);
        oldTaskEntity.setUtime(TimeUtils.getUTime());
        econtractTaskService.updateByPrimaryKeySelective(oldTaskEntity);
        context.setContextState(ContextConstant.CONTEXT_TO_EXECUTE);

        TaskContext taskContext = new TaskContext();
        taskContext.setEcontractStage(oldTaskEntity.getTaskType());
        taskContext.setState(oldTaskEntity.getTaskState());
        doNotifier(context, taskContext);
    }


    /**
     * 状态流转--发送短信
     *
     * @param context
     */
    private void turnSmsSigner_A(EcontractContext context) {
        LOGGER.info("econtract context record : {} turnSmsSigner_A ", context.getEcontractRecordEntity().getRecordKey());

        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(context.getEcontractRecordEntity().getRecordKey());
        recordEntity.setEcontractState(EcontractRecordConstant.RUNNING);
        recordEntity.setUtime(TimeUtils.getUTime());
        econtractRecordService.updateByPrimaryKeySelective(recordEntity);


        EcontractTaskEntity newTaskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.SMS_SIGNER_A);
        TaskContext taskContext = new TaskContext();
        taskContext.setTaskId(newTaskEntity.getId());
        taskContext.setStageInfoBo(ContextUtils.getStageInfoBoFromList(context, TaskConstant.REAL_NAME_AUTH_A));
        taskContext.setExecutorType(TaskConstant.SMS_EXECUTOR);
        taskContext.setEcontractStage(TaskConstant.SMS_SIGNER_A);
        taskContext.setState(TaskConstant.TASK_RUNNIG);
        context.setTaskContext(taskContext);
        context.setContextState(ContextConstant.CONTEXT_TO_EXECUTE);
        newTaskEntity.setEcontractTaskContext(JSON.toJSONString(taskContext));
        econtractTaskService.updateByPrimaryKeySelective(newTaskEntity);
        doNotifier(context, taskContext);
    }

    /**
     * 完成短信下发
     *
     * @param context
     */
    private void commitSmsSigner_A(EcontractContext context) {
        LOGGER.info("econtract context record  : {} commitSmsSigner_A ", context.getEcontractRecordEntity().getRecordKey());

        //1.修改record状态;
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(context.getEcontractRecordEntity().getRecordKey());
        recordEntity.setEcontractStage(TaskConstant.REAL_NAME_AUTH_A);
        recordEntity.setEcontractState(EcontractRecordConstant.WAIT_CALL_BACK);
        recordEntity.setUtime(TimeUtils.getUTime());
        econtractRecordService.updateByPrimaryKeySelective(recordEntity);

        //2.更新老task
        EcontractTaskEntity oldTaskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.SMS_SIGNER_A);
        oldTaskEntity.setTaskState(TaskConstant.TASK_SUCCESS);
        oldTaskEntity.setUtime(TimeUtils.getUTime());
        econtractTaskService.updateByPrimaryKeySelective(oldTaskEntity);


        TaskContext taskContext = new TaskContext();
        taskContext.setEcontractStage(oldTaskEntity.getTaskType());
        taskContext.setState(oldTaskEntity.getTaskState());
        doNotifier(context, taskContext);


        //3.已经实名认证成功，直接提交commit实名任务
        SignerInfoBo signerInfoBo = ContextUtils.getStageInfoBoFromList(context, TaskConstant.REAL_NAME_AUTH_A).getSignerInfoBo();
        EcontractRealNameAuthEntity realNameAuthEntity = realNameAuthService.querySuccessRealNameAuthBySignerInfoBo(signerInfoBo);

        if (ConfigUtilAdapter.getBoolean("wmExclusiveProcessor_auto_pass_real_name_auth_open", false)) {
            realNameAuthEntity = new EcontractRealNameAuthEntity();
        }

        if (null != realNameAuthEntity) {
            //修改record状态;
            recordEntity = econtractRecordService.queryRecordByRecordKey(context.getEcontractRecordEntity().getRecordKey());
            recordEntity.setEcontractStage(TaskConstant.CONFIRM_STAMP_A);
            recordEntity.setEcontractState(EcontractRecordConstant.WAIT_CALL_BACK);
            recordEntity.setUtime(TimeUtils.getUTime());
            econtractRecordService.updateByPrimaryKeySelective(recordEntity);

            //更新实名认证task
            EcontractTaskEntity realNameTaskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.REAL_NAME_AUTH_A);
            realNameTaskEntity.setTaskState(TaskConstant.TASK_SUCCESS);
            realNameTaskEntity.setUtime(TimeUtils.getUTime());
            econtractTaskService.updateByPrimaryKeySelective(realNameTaskEntity);

            taskContext = new TaskContext();
            taskContext.setEcontractStage(realNameTaskEntity.getTaskType());
            taskContext.setState(realNameTaskEntity.getTaskState());

            doNotifier(context, taskContext);
        }


    }


    /**
     * 完成实名认证
     *
     * @param context
     */
    private void commitConfirmStampA(EcontractContext context) {
        LOGGER.info("econtract context record  : {} commitConfirmStampA ", context.getEcontractRecordEntity().getRecordKey());

        //1.修改record状态;
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(context.getEcontractRecordEntity().getRecordKey());
        recordEntity.setEcontractStage(TaskConstant.ECONTRACT_STAMP_A);
        recordEntity.setEcontractState(EcontractRecordConstant.RUNNING);
        recordEntity.setUtime(TimeUtils.getUTime());
        econtractRecordService.updateByPrimaryKeySelective(recordEntity);

        //2.更新老task
        EcontractTaskEntity oldTaskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.CONFIRM_STAMP_A);
        oldTaskEntity.setTaskState(TaskConstant.TASK_SUCCESS);
        oldTaskEntity.setUtime(TimeUtils.getUTime());
        econtractTaskService.updateByPrimaryKeySelective(oldTaskEntity);

        context.setContextState(ContextConstant.CONTEXT_TO_EXECUTE);

        TaskContext taskContext = new TaskContext();
        taskContext.setEcontractStage(oldTaskEntity.getTaskType());
        taskContext.setState(oldTaskEntity.getTaskState());
        doNotifier(context, taskContext);
    }


    /**
     * 完成实名认证
     *
     * @param context
     */
    private void commitRealNameAuth_A(EcontractContext context) {
        LOGGER.info("econtract context record  : {} commitRealNameAuth_A ", context.getEcontractRecordEntity().getRecordKey());

        //1.修改record状态;
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(context.getEcontractRecordEntity().getRecordKey());
        recordEntity.setEcontractStage(TaskConstant.CONFIRM_STAMP_A);
        recordEntity.setEcontractState(EcontractRecordConstant.WAIT_CALL_BACK);
        recordEntity.setUtime(TimeUtils.getUTime());
        econtractRecordService.updateByPrimaryKeySelective(recordEntity);


        //2.更新老task
        EcontractTaskEntity oldTaskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.REAL_NAME_AUTH_A);
        oldTaskEntity.setTaskState(TaskConstant.TASK_SUCCESS);
        oldTaskEntity.setUtime(TimeUtils.getUTime());
        econtractTaskService.updateByPrimaryKeySelective(oldTaskEntity);

        //3.更新实名认证池
        SignerInfoBo signerInfoBo = ContextUtils.getStageInfoBoFromList(context, TaskConstant.REAL_NAME_AUTH_A).getSignerInfoBo();
        String dealVersion = context.getTaskContext().getExecutorResult().get(CallbackConstant.REAL_NAME_DEAL_VERSION);
        if ("true".equals(context.getTaskContext().getExecutorResult().get(CallbackConstant.REAL_NAME_AUTH_RESULT))) {
            realNameAuthService.refreshRealNameAuthPool(signerInfoBo, context.getEcontractUserEntity().getId(), RealNameConstanst.SUCCESS, dealVersion);
        }

        //4.通知业务方系统
        TaskContext taskContext = new TaskContext();
        taskContext.setEcontractStage(oldTaskEntity.getTaskType());
        taskContext.setState(oldTaskEntity.getTaskState());
        doNotifier(context, taskContext);

    }


    /**
     * @param context
     */
    private void turnEcontractStampA(EcontractContext context) {
        LOGGER.info("econtract context record : {} turnEcontractStampA ", context.getEcontractRecordEntity().getRecordKey());

        //1.修改record状态;
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(context.getEcontractRecordEntity().getRecordKey());
        recordEntity.setEcontractStage(TaskConstant.ECONTRACT_STAMP_A);
        recordEntity.setEcontractState(EcontractRecordConstant.WAIT_CALL_BACK);
        recordEntity.setUtime(TimeUtils.getUTime());
        econtractRecordService.updateByPrimaryKeySelective(recordEntity);

        //2.更新老task
        EcontractTaskEntity oldTaskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.CONFIRM_STAMP_A);
        oldTaskEntity.setTaskState(TaskConstant.TASK_SUCCESS);
        oldTaskEntity.setUtime(TimeUtils.getUTime());
        econtractTaskService.updateByPrimaryKeySelective(oldTaskEntity);

        TaskContext taskContext = new TaskContext();
        taskContext.setEcontractStage(oldTaskEntity.getTaskType());
        taskContext.setState(oldTaskEntity.getTaskState());
        doNotifier(context, taskContext);


        StageInfoBo stageInfoBo = ContextUtils.getStageInfoBoFromList(context, TaskConstant.CA_CERTIFY_PART_A);
        stageInfoBo.getCertifyInfoBo().setStampKey(context.getStampKey());
        String customerId = econtractCertifyService.getCustomerId(stageInfoBo.getCertifyInfoBo());

        EcontractTaskEntity newTaskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.ECONTRACT_STAMP_A);
        taskContext = new TaskContext();
        taskContext.setTaskId(newTaskEntity.getId());
        taskContext.setExecutorType(TaskConstant.ESTAMP_EXECUTOR);
        taskContext.setEcontractStage(TaskConstant.ECONTRACT_STAMP_A);
        taskContext.setCustomerId(customerId);
        taskContext.setState(TaskConstant.TASK_RUNNIG);
        taskContext.setSignKeyWord(PdfConstant.WM_EXCLUSIVE_SIGNKEY);
        context.setTaskContext(taskContext);
        context.setContextState(ContextConstant.CONTEXT_TO_EXECUTE);
        newTaskEntity.setEcontractTaskContext(JSON.toJSONString(taskContext));
        econtractTaskService.updateByPrimaryKeySelective(newTaskEntity);

        doNotifier(context, taskContext);
    }


    /**
     * 完成甲方签章
     *
     * @param context
     */
    private void commitEcontractStampA(EcontractContext context) {
        LOGGER.info("econtract context record : {} commitEcontractStampA ", context.getEcontractRecordEntity().getRecordKey());

        //1.修改record状态;
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(context.getEcontractRecordEntity().getRecordKey());
        recordEntity.setUtime(TimeUtils.getUTime());
        recordEntity.setEcontractStage(TaskConstant.ECONTRACT_FILING);
        recordEntity.setEcontractState(EcontractRecordConstant.RUNNING);
        recordEntity.setSaveUrl(context.getTaskContext().getExecutorResult().get(TaskConstant.PDF_URL));
        context.setEcontractRecordEntity(recordEntity);
        econtractRecordService.updateByPrimaryKeySelective(recordEntity);


        //2.更新老task
        EcontractTaskEntity oldTaskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.ECONTRACT_STAMP_A);
        oldTaskEntity.setTaskState(TaskConstant.TASK_SUCCESS);
        oldTaskEntity.setUtime(TimeUtils.getUTime());
        econtractTaskService.updateByPrimaryKeySelective(oldTaskEntity);

        context.setContextState(ContextConstant.CONTEXT_TO_EXECUTE);

        TaskContext taskContext = new TaskContext();
        taskContext.setEcontractStage(oldTaskEntity.getTaskType());
        taskContext.setState(oldTaskEntity.getTaskType());
        doNotifier(context, taskContext);
    }

    /**
     * 完成甲方签章
     *
     * @param context
     */
    private void turnFilingEcontract(EcontractContext context) {
        LOGGER.info("econtract context record : {} turnFilingEcontract ", context.getEcontractRecordEntity().getRecordKey());

        //1.修改record状态;
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(context.getEcontractRecordEntity().getRecordKey());


        //2.发起新任务
        EcontractTaskEntity newTaskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.ECONTRACT_FILING);
        TaskContext taskContext = new TaskContext();
        taskContext.setTaskId(newTaskEntity.getId());
        taskContext.setExecutorType(TaskConstant.FILING_ECONTRACT_EXECUTOR);
        taskContext.setEcontractStage(TaskConstant.ECONTRACT_FILING);
        taskContext.setState(TaskConstant.TASK_RUNNIG);
        context.setTaskContext(taskContext);
        newTaskEntity.setEcontractTaskContext(JSON.toJSONString(taskContext));
        econtractTaskService.updateByPrimaryKeySelective(newTaskEntity);


        doNotifier(context, taskContext);
    }

    /**
     * 完成甲方签章
     *
     * @param context
     */
    private void commitFilingEcontract(EcontractContext context) {
        LOGGER.info("econtract context record : {} commitFilingEcontract ", context.getEcontractRecordEntity().getRecordKey());

        //1.修改record状态;
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(context.getEcontractRecordEntity().getRecordKey());
        recordEntity.setUtime(TimeUtils.getUTime());
        recordEntity.setEcontractStage(TaskConstant.ECONTRACT_FINISH);
        recordEntity.setEcontractState(EcontractRecordConstant.RUNNING);
        recordEntity.setUtime(TimeUtils.getUTime());
        econtractRecordService.updateByPrimaryKeySelective(recordEntity);

        //2.更新老task
        EcontractTaskEntity oldTaskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.ECONTRACT_FILING);
        oldTaskEntity.setTaskState(TaskConstant.TASK_SUCCESS);
        oldTaskEntity.setUtime(TimeUtils.getUTime());
        econtractTaskService.updateByPrimaryKeySelective(oldTaskEntity);

        TaskContext taskContext = new TaskContext();
        taskContext.setEcontractStage(oldTaskEntity.getTaskType());
        taskContext.setState(oldTaskEntity.getTaskType());
        doNotifier(context, taskContext);


        //3.发起新任务
        EcontractTaskEntity newTaskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.ECONTRACT_FINISH);
        taskContext = new TaskContext();
        taskContext.setTaskId(newTaskEntity.getId());
        taskContext.setExecutorType(TaskConstant.FINISH_EXECUTOR);
        taskContext.setEcontractStage(TaskConstant.ECONTRACT_FINISH);
        taskContext.setState(TaskConstant.TASK_RUNNIG);
        context.setTaskContext(taskContext);
        newTaskEntity.setEcontractTaskContext(JSON.toJSONString(taskContext));
        econtractTaskService.updateByPrimaryKeySelective(newTaskEntity);

        doNotifier(context, taskContext);
    }


    /**
     * 完成合同申请流程
     *
     * @param context
     */
    private void commitApplyEcontract(EcontractContext context) {
        LOGGER.info("econtract context record : {} commitApplyEcontract ", context.getEcontractRecordEntity().getRecordKey());


        //1.修改record状态;
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(context.getEcontractRecordEntity().getRecordKey());
        recordEntity.setSaveUrl(context.getTaskContext().getExecutorResult().get(TaskConstant.PDF_URL));
        recordEntity.setUtime(TimeUtils.getUTime());
        recordEntity.setEcontractState(EcontractRecordConstant.SUCCESS);
        recordEntity.setEcontractStage(TaskConstant.ECONTRACT_FINISH);
        context.setEcontractRecordEntity(recordEntity);
        econtractRecordService.updateByPrimaryKeySelective(recordEntity);

        //2.更新任务
        EcontractTaskEntity oldTaskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.ECONTRACT_FINISH);
        oldTaskEntity.setTaskState(TaskConstant.TASK_SUCCESS);
        oldTaskEntity.setUtime(TimeUtils.getUTime());
        econtractTaskService.updateByPrimaryKeySelective(oldTaskEntity);

        TaskContext taskContext = new TaskContext();
        taskContext.setEcontractStage(oldTaskEntity.getTaskType());
        taskContext.setState(oldTaskEntity.getTaskState());
        doNotifier(context, taskContext);


        //3.更新父task
        EcontractTaskEntity parentTaskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.PARENT_TASK);
        parentTaskEntity.setTaskState(TaskConstant.TASK_SUCCESS);
        parentTaskEntity.setUtime(TimeUtils.getUTime());
        econtractTaskService.updateByPrimaryKeySelective(parentTaskEntity);


        taskContext = new TaskContext();
        taskContext.setEcontractStage(parentTaskEntity.getTaskType());
        taskContext.setState(parentTaskEntity.getTaskState());
        doNotifier(context, taskContext);
    }

    /**
     * 自动实名认证
     *
     * @param recordKey
     * @return
     */
    private void autoRealName(String recordKey) {
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(recordKey);
        EcontractContext econtractContext = JSON.parseObject(recordEntity.getEcontractRecordContext(), EcontractContext.class);

        EcontractRealNameDealEntity realNameDealEntity = realNameAuthService.queryRealNameDealEntityByRecordId(recordEntity.getId());
        if (realNameDealEntity == null) {
            this.queryRealNameRedirectUrl(recordKey);
            realNameDealEntity = realNameAuthService.queryRealNameDealEntityByRecordId(recordEntity.getId());
        }
        if (realNameDealEntity == null) {
            return;
        }

        Map<String, String> paramMap = Maps.newHashMap();
        paramMap.put(CallbackConstant.REAL_NAME_AUTH_RESULT, "true");
        paramMap.put(CallbackConstant.REAL_NAME_DEAL_VERSION, realNameDealEntity.getDealVersion());
        callBackCommitTemplateStage(econtractContext, CallbackConstant.REAL_NAME_AUTH_CALL_BACK, paramMap);
    }

    @Override
    public List<String> doBatchSms(String recordKey) {
        return Lists.newArrayList();
    }
}

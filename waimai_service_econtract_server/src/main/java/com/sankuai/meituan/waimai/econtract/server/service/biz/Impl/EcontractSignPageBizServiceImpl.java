package com.sankuai.meituan.waimai.econtract.server.service.biz.Impl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.*;
import com.meituan.mtrace.thread.pool.ExecutorServiceTraceWrapper;
import com.meituan.sankuai.bsi.esign.common.model.entity.result.ApplyCertResult;
import com.meituan.service.inf.kms.client.Kms;
import com.sankuai.meituan.mtcoop.thrift.dto.*;
import com.sankuai.meituan.mtcoop.thrift.enumtype.AccountType;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.bizsso.thrift.CaptchaTService;
import com.sankuai.meituan.waimai.bizsso.thrift.PhoneCaptcha;
import com.sankuai.meituan.waimai.bizsso.thrift.Source;
import com.sankuai.meituan.waimai.bizsso.thrift.VerifyCaptcha;
import com.sankuai.meituan.waimai.bizsso.thrift.exception.BizssoServerException;
import com.sankuai.meituan.waimai.econtract.server.adapter.CommonCoopServiceAdapter;
import com.sankuai.meituan.waimai.econtract.server.adapter.EsignClient;
import com.sankuai.meituan.waimai.econtract.server.adapter.WmPartnerCustomerContractThriftServiceAdapter;
import com.sankuai.meituan.waimai.econtract.server.bo.EcontractBatchOpExtBo;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import com.sankuai.meituan.waimai.econtract.server.constants.CertifyConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.*;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractBatchOpMapper;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractSmsDealEntityMapper;
import com.sankuai.meituan.waimai.econtract.server.entity.*;
import com.sankuai.meituan.waimai.econtract.server.service.SpringBeanUtil;
import com.sankuai.meituan.waimai.econtract.server.service.ability.dataanalysis.WmEcontractSpAreaContentWrapperService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.*;
import com.sankuai.meituan.waimai.econtract.server.service.biz.Impl.extrainfo.EcontractExtraInfoPacking;
import com.sankuai.meituan.waimai.econtract.server.service.cache.RedisKvService;
import com.sankuai.meituan.waimai.econtract.server.service.engine.template.manager.TemplateManager;
import com.sankuai.meituan.waimai.econtract.server.template.config.service.EcontractTemplateBaseService;
import com.sankuai.meituan.waimai.econtract.server.utils.*;
import com.sankuai.meituan.waimai.econtrct.client.constants.*;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.*;
import com.sankuai.meituan.waimai.econtrct.client.domain.applybiz.SignAdditionInfoMeta;
import com.sankuai.meituan.waimai.econtrct.client.domain.applybiz.SpAreaDataQueryRequestDTO;
import com.sankuai.meituan.waimai.econtrct.client.domain.applybiz.SpAreaDataQueryRespDTO;
import com.sankuai.meituan.waimai.econtrct.client.domain.applybiz.WmContractContentAggreBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.base.BoolResult;
import com.sankuai.meituan.waimai.econtrct.client.domain.biz.DcSignCompletePageInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.biz.EcontractRecordBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.common.BaseResponse;
import com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException;
import com.sankuai.meituan.waimai.econtrct.client.template.config.domain.EcontractTemplateBaseBo;
import com.sankuai.meituan.waimai.econtrct.client.util.EcontractContentUtil;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.partner.DaoCanContractContext;
import joptsimple.internal.Strings;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import javax.crypto.BadPaddingException;
import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException.ECONTRACT_SERVER_CANCEL_ERROR;

/**
 * 合同H5签约页操作
 * <AUTHOR>
 */
@Service
public class EcontractSignPageBizServiceImpl implements EcontractSignPageBizService {

    private static final Logger LOGGER = LoggerFactory.getLogger(EcontractSignPageBizServiceImpl.class);

    @Autowired
    private EcontractRecordService econtractRecordService;
    @Autowired
    private EcontractRelService econtractRelService;
    @Autowired
    private EcontractService econtractService;
    @Autowired
    private EcontractSignPageTemplateService signPageTemplateService;
    @Autowired
    private EcontractRealNameAuthService realNameAuthService;
    @Autowired
    private EcontractUserService userService;
    @Autowired
    private TemplateManager templateManager;
    @Autowired
    private CaptchaTService.Iface captchaTService;
    @Autowired
    private TairService tairService;
    @Autowired
    private EcontractSmsDealEntityMapper smsDealEntityMapper;
    @Autowired
    private EcontractCertifyService econtractCertifyService;
    @Autowired
    private EsignClient esignClient;
    @Autowired
    private EcontractSignRecordBatchEventService econtractSignRecordBatchEventService;
    @Autowired
    private EcontractBatchOpMapper econtractBatchOpMapper;
    @Autowired
    private EcontractSignRecordBatchService econtractSignRecordBatchService;
    @Autowired
    private RedisKvService redisKvService;

    @Resource
    private EcontractTemplateBaseService econtractTemplateBaseService;

    @Resource
    private CommonCoopServiceAdapter commonCoopServiceAdapter;

    @Resource
    private WmPartnerCustomerContractThriftServiceAdapter wmPartnerCustomerContractThriftServiceAdapter;

    @Resource
    private EcontractMetricService econtractMetricService;

    @Resource
    private WmEcontractSpAreaContentWrapperService wmEcontractSpAreaContentWrapperService;


    public static List<EcontractExtraInfoPacking> packingList = Lists.newArrayList();

    private static ListeningExecutorService toSignQueryExecutorService;

    private static ListeningExecutorService toSignOpExecutorService;

    public static final String DINE_IN_SERVICE_C2 = "dine_in_service_C2";

    public static final String PDF_URL_PREFIX = "https://econtract.meituan.com";

    private static final ExecutorServiceTraceWrapper confirmSignExecutor = new ExecutorServiceTraceWrapper(
            new ThreadPoolExecutor(10,
                    10,
                    3000,
                    TimeUnit.MILLISECONDS,
                    new ArrayBlockingQueue<>(1000),
                    new ThreadFactoryBuilder().setNameFormat("confirm-sign-executor").build(),
                    new ThreadPoolExecutor.DiscardPolicy())
    );

    private static final ImmutableSet<String> CONFIRM_STAMP_TASK_LIST = ImmutableSet
            .of(TaskConstant.CONFIRM_STAMP_A,
                    TaskConstant.CONFIRM_STAMP_B,
                    TaskConstant.CONFIRM_STAMP_C,
                    TaskConstant.CONFIRM_STAMP_D,
                    TaskConstant.CONFIRM_STAMP_E,
                    TaskConstant.CONFIRM_STAMP_F,
                    TaskConstant.CONFIRM_STAMP_G);


    @Override
    public void init(){
        packingList.add((EcontractExtraInfoPacking) SpringBeanUtil.getBean("deliveryAreaPacking"));
        packingList.add((EcontractExtraInfoPacking) SpringBeanUtil.getBean("medicineFeeInfoPacking"));

        //初始化线程池
        toSignQueryExecutorService = MoreExecutors.listeningDecorator(Executors.newFixedThreadPool(MccConfig.toSignQueryThreadPoolSize()));
        toSignOpExecutorService = MoreExecutors.listeningDecorator(Executors.newFixedThreadPool(MccConfig.toSignOpThreadPoolSize()));
    }

    @Override
    public String queryRealNameRedirectUrl(String recordKey) {
        return templateManager.queryRealNameRedirectUrl(recordKey);
    }

    @Override
    public SignH5InfoBo querySignH5InoBySecretParam(String param) {
        SignH5InfoBo signH5InfoBo = new SignH5InfoBo();
        try {
            String privateKey = Kms.getByName(MccConstant.APP_KEY, KmsConstant.REAL_NAME_PRIVATE_KEY);
            LOGGER.info("querySignH5InoBySecretParam param is : {} , privacy key is : {}", param, privateKey);

            String dealVersion = CaSignUtil.decrypt3desc(param, privateKey.substring(0, 24));

            LOGGER.info("querySignH5InoBySecretParam dealEntity is : {}", dealVersion);
            EcontractSmsDealEntity dealEntity = smsDealEntityMapper.queryByDealVersion(dealVersion);

            //未获取到对应短信记录
            if (dealEntity == null) {
                signH5InfoBo.setViewStage(WebViewConstant.STAGE_UN_EFFECTIVE);
                return signH5InfoBo;
            }
            LOGGER.info("querySignH5InoBySecretParam dealEntity:{}, dealVersion:{}", JSON.toJSONString(dealEntity), dealVersion);
            //对应短信未过期
            if (isEffectSmsDeal(dealEntity)) {
                signH5InfoBo = templateManager.querySignH5InoByRecordIdAndTaskId(dealEntity.getEcontractRecordId(), DealVersionUtils.getTaskId(dealVersion));
            } else {
                signH5InfoBo.setViewStage(WebViewConstant.STAGE_UN_EFFECTIVE);
                signH5InfoBo.setPdfUrlMap(templateManager.buildDownloadPdfUrl(dealEntity.getEcontractRecordId()));
            }
            return signH5InfoBo;
        } catch (BadPaddingException e) {
            LOGGER.warn("fail to get recordKey，解密param失败，param={}", param, e);
            return signH5InfoBo;
        } catch (Exception e) {
            LOGGER.error("fail to get recordKey", e);
            return signH5InfoBo;
        }
    }

    @Override
    public String getCertPhone(String param) {
        try {
            String privateKey = Kms.getByName(MccConstant.APP_KEY, KmsConstant.REAL_NAME_PRIVATE_KEY);
            String longUrlWithPhone = CaSignUtil.decrypt3desc(param, privateKey.substring(0, 24));
            return longUrlWithPhone.substring(longUrlWithPhone.lastIndexOf("=")+1);
        } catch (Exception e) {
            LOGGER.error("fail to get recordKey", e);
            return "";
        }
    }

    @Override
    public String getLongUrl(String param) {//规则维护在服务化内部
        try {
            String privateKey = Kms.getByName(MccConstant.APP_KEY, KmsConstant.REAL_NAME_PRIVATE_KEY);
            String longUrlWithPhone = CaSignUtil.decrypt3desc(param, privateKey.substring(0, 24));
            return longUrlWithPhone.substring(0, longUrlWithPhone.lastIndexOf("&"));
        } catch (Exception e) {
            LOGGER.error("fail to get recordKey", e);
            return "";
        }
    }


    @Override
    public String querySignerPageTypeBySecretParam(String param) {
        if (StringUtils.isBlank(param)) {
            LOGGER.info("querySignerPageTypeBySecretParam inputParam = {}", param);
            return StringUtils.EMPTY;
        }

        try {
            String privateKey = Kms.getByName(MccConstant.APP_KEY, KmsConstant.REAL_NAME_PRIVATE_KEY);
            String dealVersion = CaSignUtil.decrypt3desc(param, privateKey.substring(0, 24));
            EcontractSmsDealEntity dealEntity = smsDealEntityMapper.queryOneDealByVersion(dealVersion);
            ObjectAssertUtil.assertObjectNotNull(dealEntity, "参数错误，找不到对应的短信流水");

            EcontractRecordEntity recordEntity = econtractRecordService.selectByPrimaryKey(dealEntity.getEcontractRecordId());
            ObjectAssertUtil.assertObjectNotNull(recordEntity, "参数错误，找不到record记录信息");

            String h5Type = econtractService.queryH5TypeByEcontractId(recordEntity.getEcontractId());
            LOGGER.info("querySignerPageTypeBySecretParam h5Type = {}", h5Type);
            return h5Type;
        } catch (EcontractException e) {
            LOGGER.warn("fail to get h5Type", e);
            return StringUtils.EMPTY;
        } catch (BadPaddingException e) {
            LOGGER.warn("fail to get h5Type，解密param失败，param={}", param, e);
            return StringUtils.EMPTY;
        } catch (Exception e) {
            LOGGER.error("fail to get h5Type", e);
            return StringUtils.EMPTY;
        }
    }

    @Override
    public SignPageInfoBo querySignPageInfoBySecretParam(String param) throws EcontractException {
        try {
            String privateKey = Kms.getByName(MccConstant.APP_KEY, KmsConstant.REAL_NAME_PRIVATE_KEY);
            String dealVersion = CaSignUtil.decrypt3desc(param, privateKey.substring(0, 24));
            EcontractSmsDealEntity dealEntity = smsDealEntityMapper.queryOneDealByVersion(dealVersion);
            EcontractRecordEntity recordEntity = econtractRecordService.selectByPrimaryKey(dealEntity.getEcontractRecordId());
            LOGGER.info("EcontractSignPageBizServiceImpl#querySignPageInfoBySecretParam, recordEntity: {}", JacksonUtil.writeAsJsonStr(recordEntity));
            EcontractSignPageTemplateEntity entity;
            // 拼好饭相关的合同类型
            Set<String> phfTypeSet = Sets.newHashSet(Splitter.on(",").trimResults().splitToList(MccConfig.getPHFSignTempletTypeList()));
            // 判断是否批量发起签约的任务 && 非拼好饭签约
            if (recordEntity.getRecordBatchId() > 0 && !phfTypeSet.contains(recordEntity.getEcontractType())) {
                List<EcontractRecordEntity> recordEntityList = econtractRecordService.queryEcontractRecordByBatchId(recordEntity.getRecordBatchId());
                // 如果打包的任务数大于1，则用打包模板
                if (CollectionUtils.isNotEmpty(recordEntityList) && recordEntityList.size() > 1) {
                    entity = signPageTemplateService.selectByNameCompatible(MccConfig.batchSignpageTemplateName());
                } else {
                    entity = querySignPageInfoByContentTemplateId(recordEntity);
                    if (entity == null) {
                        EcontractRelEntity relEntity = econtractRelService.selectBytemplateName(recordEntity.getEcontractType());
                        entity = signPageTemplateService.selectByNameCompatible(relEntity.getSignPageName());
                    }
                }
            } else {
                entity = querySignPageInfoByContentTemplateId(recordEntity);
                if (entity == null) {
                    EcontractRelEntity relEntity = econtractRelService.selectBytemplateName(recordEntity.getEcontractType());
                    entity = signPageTemplateService.selectByNameCompatible(relEntity.getSignPageName());
                }
            }
            return JSON.parseObject(entity.getContext(), SignPageInfoBo.class);
        } catch (BadPaddingException e) {
            LOGGER.warn("fail to get querySignPageInfoBySecretParam，解密param失败，param={}", param, e);
            throw new EcontractException(EcontractErrorCode.BUSINESS_ERROR, "查询异常");
        } catch (Exception e) {
            LOGGER.error("fail to get querySignPageInfoBySecretParam", e);
            throw new EcontractException(EcontractErrorCode.BUSINESS_ERROR, "查询异常");
        }
    }

    private EcontractSignPageTemplateEntity querySignPageInfoByContentTemplateId(EcontractRecordEntity recordEntity) {
        try {
            if (recordEntity == null || recordEntity.getEcontractRecordContext() == null) {
                return null;
            }
            EcontractContext econtractContext = JacksonUtil.readValue(recordEntity.getEcontractRecordContext(), EcontractContext.class);
            Integer contentTemplateId = ContractContentTemplateUtil.extractTemplateIdFromEcontractContext(econtractContext);
            if (contentTemplateId == null || contentTemplateId <= 0) {
                return null;
            }
            EcontractTemplateBaseBo econtractTemplate = econtractTemplateBaseService.getValidEcontractTemplate(contentTemplateId);
            return signPageTemplateService.selectByPrimaryKey(econtractTemplate.getSignPageTemplateId());
        } catch (Exception e) {
            LOGGER.error("EcontractSignPageBizServiceImpl#querySignPageInfoByContentTemplateId, error", e);
            return null;
        }
    }

    private Integer extractContentTemplateId(EcontractContext econtractContext) {
        if (CollectionUtils.isNotEmpty(econtractContext.getStageBatchInfoBoList())) {
            return extractContentTemplateIdFromStageBatchInfoBoList(econtractContext.getStageBatchInfoBoList());
        } else {
            return extractContentTemplateIdFromStageInfoBoList(econtractContext.getStageInfoBoList());
        }
    }

    private Integer extractContentTemplateIdFromStageBatchInfoBoList(List<StageBatchInfoBo> stageBatchInfoBoList) {
        Optional<StageBatchInfoBo> optional = stageBatchInfoBoList.stream()
                .filter(v -> TaskConstant.CREATE_PDF.equals(v.getStageName()))
                .findFirst();
        if (!optional.isPresent()) {
            return 0;
        }
        StageBatchInfoBo stageBatchInfoBo = optional.get();
        return ContractContentTemplateUtil.extractTemplateIdFromStageBatchInfoBo(stageBatchInfoBo);
    }

    private Integer extractContentTemplateIdFromStageInfoBoList(List<StageInfoBo> stageInfoBoList) {
        return stageInfoBoList.stream()
                .flatMap(v -> v.getPdfContentInfoBoList().stream())
                .filter(pdfContentInfoBo -> pdfContentInfoBo.getPdfTemplateId() != null)
                .findFirst()
                .map(PdfContentInfoBo::getPdfTemplateId)
                .orElse(0);
    }

    @Override
    public BoolResult sendPhoneCap(String recordKey) throws EcontractException {
        return new BoolResult(templateManager.sendPhoneCap(recordKey));
    }

    @Override
    public BoolResult sendPhoneCapAtMid(String certPhone) throws EcontractException {
        LOGGER.info("start to send certPhone:{}", certPhone);
        try {
            //60秒失效校验
            String phoneCaptchaKey = DealVersionUtils.getCertCode(certPhone);
            String turlingId = tairService.getStrValue(phoneCaptchaKey);

            LOGGER.info("sendPhoneCapAtMid:从缓存获取turlingId:{}",turlingId);

            if (null != turlingId) {
                LOGGER.info("fail to ReSendCertPhoneCap in 60 seconds , certPhone {}", certPhone);
                return new BoolResult(false);
            }

            PhoneCaptcha phoneCaptcha = null;
            String phone = ConfigUtilAdapter.getString(MccConstant.TEST_PHONE_CAP);
            if (StringUtils.isNotEmpty(phone) && (StringUtils.isNotEmpty(phone.split(",")[0]))) {
                phone = phone.split(",")[0];
                phoneCaptcha = captchaTService.getPhoneCaptcha(phone, Source.WAIMAI_M_CONTRACT_CERT);
            } else {
                phoneCaptcha = captchaTService.getPhoneCaptcha(certPhone, Source.WAIMAI_M_CONTRACT_CERT);
            }
            LOGGER.info("sendPhoneCapAtMid:设置缓存turlingId:{}",phoneCaptcha.getTurlingTestId());

            tairService.setStrValue(phoneCaptchaKey, phoneCaptcha.getTurlingTestId(), 60);
            return new BoolResult(true);
        } catch (BizssoServerException e) {
            LOGGER.error(String.format("fail to sendPhoneCapAtMid , certPhone %s , error is %s", certPhone, e.getMsg()), e);
            throw new com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException(e.getCode(), e.getMessage());
        } catch (Exception e) {
            LOGGER.error(String.format("fail to sendPhoneCapAtMid , certPhone %s , error is %s", certPhone, e.getMessage()), e);
            throw new com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException(WmContractErrorCodeConstant.SYSTEM_ERROR, e.getMessage());
        }
    }

    @Override
    public BoolResult verifyPhoneCaptcha(String recordKey, String code) {
        return new BoolResult(templateManager.verifyPhoneCaptcha(recordKey, code));
    }

    @Override
    public BoolResult verifyPhoneCaptchaAtMid(String certPhone, String code) throws EcontractException {
        LOGGER.info("start to verify certPhone:{}, code:{}", certPhone, code);

        String turlingId = tairService.getStrValue(DealVersionUtils.getCertCode(certPhone));

        LOGGER.info("verifyPhoneCaptcha:从缓存获取turlingId:{}",turlingId);

        if (StringUtils.isEmpty(turlingId)) {
            throw new EcontractException( EcontractException.VERIFICATION_CODE_EXPIRED, "验证码已过期，请重新获取");
        } else {
            //测试后门,发给第一个手机号
            String phone = ConfigUtilAdapter.getString(MccConstant.TEST_PHONE_CAP);
            if (StringUtils.isNotEmpty(phone) && (StringUtils.isNotEmpty(phone.split(",")[0]))) {
                phone = phone.split(",")[0];
            } else {
                phone = certPhone;
            }
            try {
                VerifyCaptcha verifyCaptcha = captchaTService.verifyPhoneCaptcha(turlingId, phone, code, false, Source.WAIMAI_M_CONTRACT_CERT);
                LOGGER.info("verifyCaptcha.isVerify : {} ,phone : {},code : {}", verifyCaptcha.isVerify(), phone, code);
                return new BoolResult(verifyCaptcha.isVerify());
            } catch (BizssoServerException e){
                LOGGER.warn(String.format("fail to verifyPhoneCaptchaAtMid , certPhone %s , error is %s", certPhone, e.getMsg()), e);
                return new BoolResult(false);
            } catch (Exception e){
                LOGGER.error(String.format("fail to verifyPhoneCaptchaAtMid , certPhone %s", certPhone), e);
                return new BoolResult(false);
            }
        }
    }

    @Override
    public BoolResult confirmSignEContract(String recordKey) throws EcontractException {
        templateManager.confirmSignEContract(recordKey);
        return new BoolResult(true);
    }

    @Override
    public BoolResult cancelSignEContract(String recordKey, String msg) throws EcontractException {
        try {
            templateManager.cancelSignEContract(recordKey, msg);
        } catch (com.sankuai.meituan.waimai.econtract.server.exception.EcontractException e) {
            throw new com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException(e.getErrorCode(), e.getMessage());
        }
        return new BoolResult(true);
    }


    @Override
    public String queryEcontractSaveUrl(String recordKey) throws EcontractException {
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(recordKey);
        if (null != recordEntity) {
            return recordEntity.getSaveUrl();
        } else {
            com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException.PARAM_ERROR_EXCEPTION.newInstance("can not find " + recordKey + " econtract");
            return "";
        }
    }


    @Override
    public BoolResult writeRealName(String userToken, SignerInfoBo signerInfoBo) {
        EcontractUserEntity econtractUserEntity = userService.queryUserByToken(userToken);
        if (null == econtractUserEntity) {
            com.sankuai.meituan.waimai.econtract.server.exception.EcontractException.PARAM_ERROR_EXCEPTION.newInstance("没有该用户");
        }
        EcontractRealNameAuthEntity
            realNameAuthEntity = realNameAuthService.querySuccessRealNameAuthBySignerInfoBo(signerInfoBo);
        if (null == realNameAuthEntity) {
            String md5 = RealNameAuthUtil.genRealNameAuthMD5(signerInfoBo);
            realNameAuthEntity = new EcontractRealNameAuthEntity();
            realNameAuthEntity.setEcontractUserId(econtractUserEntity.getId());

            realNameAuthEntity.setBankName(signerInfoBo.getBankName());
            realNameAuthEntity.setPhone(signerInfoBo.getPhone());
            realNameAuthEntity.setName(signerInfoBo.getName());
            realNameAuthEntity.setBankCardNo(signerInfoBo.getBankCardNo());
            realNameAuthEntity.setIdCardNo(signerInfoBo.getIdCardNo());

            realNameAuthEntity.setAuthTime(new Date());
            realNameAuthEntity.setAuthResult(RealNameConstanst.SUCCESS);
            realNameAuthEntity.setMd5(md5);

            realNameAuthEntity.setCtime(new Date());
            realNameAuthEntity.setValid(RealNameConstanst.VALID);
            realNameAuthService.insertSelective(realNameAuthEntity);
            LOGGER.info("user :{}, writeRealName pool , data is :{}", econtractUserEntity.getName(), JSON.toJSONString(realNameAuthEntity));
        }
        return new BoolResult(true);
    }

    @Override
    public BoolResult writeCaPool(String userToken, CertifyInfoBo certifyInfoBo) {
        EcontractUserEntity econtractUserEntity = userService.queryUserByToken(userToken);
        if (null == econtractUserEntity) {
            com.sankuai.meituan.waimai.econtract.server.exception.EcontractException.PARAM_ERROR_EXCEPTION.newInstance("没有该用户");
        }
        if (!StageInfoCheckUtils.checkForInsertCaInfo(certifyInfoBo)) {
            com.sankuai.meituan.waimai.econtract.server.exception.EcontractException.CA_PARAM_EXCEPTION.newInstance("CA信息不完整");
        }

        try {
            EcontractCertifyEntity econtractCertifyEntity = econtractCertifyService.queryByCertifyInfoBo(certifyInfoBo);
            if (null == econtractCertifyEntity) {
                econtractCertifyEntity = new EcontractCertifyEntity();
                econtractCertifyEntity.setEcontractUserId(econtractUserEntity.getId());
                econtractCertifyEntity.setType(certifyInfoBo.getCaType().getValue());
                econtractCertifyEntity.setDetail(JSON.toJSONString(certifyInfoBo));
                String key = econtractCertifyService.wrapEstampCaKey(certifyInfoBo);
                econtractCertifyEntity.setCertifyKey(key);
                econtractCertifyEntity.setResult(1000);
                econtractCertifyEntity.setValid(CertifyConstant.VALID);
                econtractCertifyEntity.setCtime(new Date());
                econtractCertifyEntity.setUtime(new Date());
                econtractCertifyEntity.setCustomerId(certifyInfoBo.getCustomerId());
                econtractCertifyService.insertSelective(econtractCertifyEntity);
            }
        } catch (NoSuchAlgorithmException e) {
            LOGGER.error("查询CA认证customerId失败, 客户名称:{}", certifyInfoBo.getCustomerName());
        }
        return new BoolResult(true);
    }

    @Override
    public String queryCaCustomerId(CertifyInfoBo certifyInfoBo) {
        try {
            EcontractCertifyEntity econtractCertifyEntity = econtractCertifyService.queryByCertifyInfoBo(certifyInfoBo);
            return econtractCertifyEntity == null?StringUtils.EMPTY:econtractCertifyEntity.getCustomerId();
        } catch (NoSuchAlgorithmException e) {
            LOGGER.error("查询CA认证customerId失败, 客户名称:{}", certifyInfoBo.getCustomerName());
            return StringUtils.EMPTY;
        }
    }

    private String wrapEstampCaKey(String email, String mobile) {
        return StringUtils.isNotBlank(email) ? email : mobile;
    }


    @Override
    public BoolResult hasRealName(SignerInfoBo signerInfoBo) {
        return new BoolResult(realNameAuthService.querySuccessRealNameAuthBySignerInfoBo(signerInfoBo) != null);
    }

    private static boolean refresh = true;

    @Override
    public BoolResult doRefreshCaPool() {
        synchronized (this) {
            if (refresh) {
                refresh = false;
                List<EcontractCertifyEntity> econtractEntityList = econtractCertifyService.queryAgentRecord();
                if (CollectionUtils.isNotEmpty(econtractEntityList)) {
                    for (EcontractCertifyEntity econtractCertifyEntity : econtractEntityList) {
                        String key = econtractCertifyEntity.getCertifyKey();
                        key = key.split(":")[1];
                        econtractCertifyEntity.setCertifyKey(key);
                        try {
                            econtractCertifyService.updateByPrimaryKeySelective(econtractCertifyEntity);
                        } catch (Exception e) {
                            LOGGER.error("fail to refresh " + JSON.toJSONString(econtractCertifyEntity));
                        }
                    }
                }
                refresh = true;
                return new BoolResult(true);
            }
        }
        return new BoolResult(false);
    }

    @Override
    public String queryRecordKeyBySecretParam(String param)
        throws com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException, TException {
        try {
            String privateKey = Kms.getByName(MccConstant.APP_KEY, KmsConstant.REAL_NAME_PRIVATE_KEY);
            String dealVersion = CaSignUtil.decrypt3desc(param, privateKey.substring(0, 24));
            EcontractSmsDealEntity dealEntity = smsDealEntityMapper.queryOneDealByVersion(dealVersion);
            if (dealEntity == null) {
                return StringUtils.EMPTY;
            }
            EcontractRecordEntity recordEntity = econtractRecordService.selectByPrimaryKey(dealEntity.getEcontractRecordId());
            return recordEntity != null ? recordEntity.getRecordKey() : StringUtils.EMPTY;
        } catch (Exception e) {
            LOGGER.error("fail to get queryRecordKeyBySecretParam", e);
            throw new com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException(EcontractErrorCode.BUSINESS_ERROR, "查询异常");
        }
    }

    @Override
    public Long confirmSign(DaocanContractRequestDTO request) throws EcontractException, TException {
        Integer econtractRecordId = getEcontractRecordIdFromParam(request.getParam());
        EcontractRecordEntity recordEntity = econtractRecordService.selectByPrimaryKey(econtractRecordId);
        if (recordEntity == null) {
            throw new EcontractException(EcontractErrorCode.BUSINESS_ERROR, "查询异常");
        }
        List<String> recordKeyList = queryRecurdKeyList(recordEntity);
        Long batchOpId = this.initBatchOpInfo(recordKeyList, EcontractBatchOpTypeConstant.CONFIRM);
        try {
            CompletableFuture<Object> firstCompletedFuture = CompletableFuture.anyOf(recordKeyList.stream()
                    .map(recordKey -> CompletableFuture.runAsync(() -> confirmSignSingleRecordKey(recordKey), confirmSignExecutor))
                    .toArray(CompletableFuture[]::new));
            firstCompletedFuture.get();
        } catch (Exception e) {
            LOGGER.error("EcontractSignPageBizServiceImpl#confirmSign, error", e);
            econtractMetricService.metricConfirmSignResult("fail");
        }
        return batchOpId;
    }

    private void confirmSignSingleRecordKey(String recordKey) {
        try {
            templateManager.confirmSignEContract(recordKey);
            econtractMetricService.metricConfirmSignResult("success");
        } catch (Exception e) {
            LOGGER.error("EcontractSignPageBizServiceImpl#confirmSignSingleRecordKey, error", e);
            econtractMetricService.metricConfirmSignResult("fail");
        }
    }

    private Integer getEcontractRecordIdFromParam(String param) {
        try {
            String privateKey = Kms.getByName(MccConstant.APP_KEY, KmsConstant.REAL_NAME_PRIVATE_KEY);
            String dealVersion = CaSignUtil.decrypt3desc(param, privateKey.substring(0, 24));
            EcontractSmsDealEntity dealEntity = smsDealEntityMapper.queryOneDealByVersion(dealVersion);
            LOGGER.info("EcontractSignPageBizServiceImpl#getEcontractRecordIdFromParam, dealEntity: {}", JacksonUtil.writeAsJsonStr(dealEntity));
            if (dealEntity == null) {
                return 0;
            } else {
                return dealEntity.getEcontractRecordId();
            }
        } catch (Exception e) {
            LOGGER.error("EcontractSignPageBizServiceImpl#getEcontractRecordIdFromParam, error", e);
            return 0;
        }
    }

    /**
     * 用于电子合同签约页强化数据tab展示包括PDF，地图信息
     */
    @Override
    public String queryEcontractData(String recordKey, String type) throws EcontractException, TException {
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(recordKey);
        if (recordEntity == null) {
            return StringUtils.EMPTY;
        }
        EcontractContext context = JSON.parseObject(recordEntity.getEcontractRecordContext(), EcontractContext.class);
        //只有pdf是单独处理的，pdf信息由平台解析
        if (EcontractContentTypeEnum.PDF.getName().equals(type)) {
            return this.queryEcontractSaveUrl(recordKey);
        }
        //若电子合同平台已定义的class，则直接转换为对应的class
        StageInfoBo stageInfoBo = ContextUtils.getStageInfoBoFromList(context, TaskConstant.ECONTRACT_VIEW_CONTENT);
        if (EcontractContentUtil.getClassByContentType(type) != null) {
            return stageInfoBo.getViewContentMap().get(type);
        }
        //否则，让业务自传json进来
        return stageInfoBo.getViewContentMap().get(type);
    }

    /**
     * 查询页面跳转数据(只查询，不做数据解析)
     */
    @Override
    public String queryEcontractExternalJumpData(String recordKey, String type) throws EcontractException, TException {
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(recordKey);
        if (recordEntity == null) {
            return StringUtils.EMPTY;
        }
        EcontractContext context = JSON.parseObject(recordEntity.getEcontractRecordContext(), EcontractContext.class);
        StageInfoBo stageInfoBo = ContextUtils.getStageInfoBoFromList(context, TaskConstant.ECONTRACT_EXTERNAL_JUMP_CONTENT);
        if (stageInfoBo == null || MapUtils.isEmpty(stageInfoBo.getViewContentMap())) {
            return StringUtils.EMPTY;
        }
        return stageInfoBo.getViewContentMap().get(type);
    }

    /**
     * 用于testController，生成需要的CA customerId
     */
    @Override
    public String postForCA(CertifyInfoBo certifyInfoBo) throws EcontractException, TException {
        ApplyCertResult applyCertResult = esignClient.applyCert(certifyInfoBo);
        return applyCertResult.getCustomerId();
    }


    @Override
    public Map<String, String> queryEcontractSpAreaData(String recordKey) throws EcontractException {
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(recordKey);
        if (recordEntity == null) {
            return Maps.newHashMap();
        }
        EcontractContext context = JSON.parseObject(recordEntity.getEcontractRecordContext(), EcontractContext.class);
        return ContextUtils.getSpAreaMap(context, TaskConstant.ECONTRACT_VIEW_CONTENT);
    }

    @Override
    public WmContractContentAggreBo queryEcontractContentAggre(String recordKey) throws EcontractException, TException {
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(recordKey);
        //记录为空或任务失效
        if (recordEntity == null) {
            return null;
        }
        WmContractContentAggreBo aggreBo = new WmContractContentAggreBo();
        EcontractContext context = JSON.parseObject(recordEntity.getEcontractRecordContext(), EcontractContext.class);
        aggreBo.setContractDesc(ContextUtils.getContractDesc(context, TaskConstant.ECONTRACT_VIEW_CONTENT));
        aggreBo.setContractName(ContextUtils.getContractName(context, TaskConstant.ECONTRACT_VIEW_CONTENT));
        aggreBo.setPdfUrl(recordEntity.getSaveUrl());
        return aggreBo;
    }

    @Override
    public List<WmContractContentAggreBo> queryEcontractContentAggreList(String recordKey) throws EcontractException, TException {
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(recordKey);
        boolean isForceAllOp = isForceAllOp(recordEntity.getRecordBatchId());
        // 记录为空或任务失效
        if (recordEntity == null) {
            return null;
        }
        List<WmContractContentAggreBo> aggreBoList = Lists.newArrayList();
        EcontractContext context = JSON.parseObject(recordEntity.getEcontractRecordContext(), EcontractContext.class);
        List<StageBatchInfoBo> stageBatchInfoBoList = context.getStageBatchInfoBoList();
        if (null != stageBatchInfoBoList) {
            List<StageBatchInfoBo> pdfStageInfoBoList = stageBatchInfoBoList.stream()
                    .filter(stage -> stage.getStageName().equals("create_pdf")).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(pdfStageInfoBoList)) {
                StageBatchInfoBo pdfStageInfoBo = pdfStageInfoBoList.get(0);
                // 获取pdf链接
                Map<String, String> pdfUrlMap = econtractRecordService.parseMultiPdfUrl(recordEntity.getSaveUrl());
                Map<String, List<PdfContentInfoBo>> pdfMap = pdfStageInfoBo.getPdfContentInfoBoMap();

                SignH5InfoBo signH5InfoBo = querySignH5InoByRecordKey(recordKey);
                for (Map.Entry<String, List<PdfContentInfoBo>> entry : pdfMap.entrySet()) {
                    StringBuilder prefixText = new StringBuilder("适用于 ");
                    StringBuilder poiNameText = new StringBuilder();
                    StringBuilder mergeText = new StringBuilder();

                    if (CollectionUtils.isNotEmpty(entry.getValue()) && StringUtils.isNotEmpty(entry.getValue().get(0).getContractName())) {
                        WmContractContentAggreBo aggreBo = buildWmContractContentAggreBo(entry, signH5InfoBo, pdfUrlMap, recordEntity, isForceAllOp, recordKey);
                        aggreBoList.add(aggreBo);
                        continue;
                    }

                    if(entry.getKey().equals(EcontractPdfTypeEnum.FEE_CONTRACT.getName())
                            || entry.getKey().equals(EcontractPdfTypeEnum.FEE_CONTRACT_LOWERCASE.getName())
                            || entry.getKey().equals(EcontractPdfTypeEnum.PHF_ACCOUNT_CONTRACT.getName())
                            || entry.getKey().equals(EcontractPdfTypeEnum.PHF_AGENT_SPECIAL_PRICE_CONTRACT.getName())
                            || entry.getKey().equals(EcontractPdfTypeEnum.BANMA_PHF_CONTRACT.getName())){
                        // 拼好饭，约定将页面展示信息放到econtract_view_content中
                        WmContractContentAggreBo aggreBo = new WmContractContentAggreBo();
                        aggreBo.setContractDesc(ContextUtils.getContractDesc(context, TaskConstant.ECONTRACT_VIEW_CONTENT));
                        aggreBo.setContractName(ContextUtils.getContractName(context, TaskConstant.ECONTRACT_VIEW_CONTENT));
                        aggreBo.setContractTaskType(entry.getKey());
                        aggreBo.setRecordKey(recordKey);
                        String pdfUrl = StringUtils.isEmpty(pdfUrlMap.get(entry.getKey())) ?
                                pdfUrlMap.get("default") : pdfUrlMap.get(entry.getKey());
                        aggreBo.setViewStage(signH5InfoBo.getViewStage());
                        aggreBo.setPdfUrl(pdfUrl);
                        aggreBo.setRecordBatchId(recordEntity.getRecordBatchId());
                        aggreBo.setForceAllOp(isForceAllOp);
                        aggreBoList.add(aggreBo);
                    } else if (entry.getKey().equals(EcontractPdfTypeEnum.C1CONTRACT.getName())) {
                        WmContractContentAggreBo aggreBo = new WmContractContentAggreBo();
                        aggreBo.setContractName(EcontractPdfTypeEnum.C1CONTRACT.getDesc());
                        aggreBo.setContractTaskType(entry.getKey());
                        aggreBo.setContractDesc("适用于 全部门店");
                        aggreBo.setRecordKey(recordKey);
                        String pdfUrl = StringUtils.isEmpty(pdfUrlMap.get(entry.getKey())) ?
                                pdfUrlMap.get("default") : pdfUrlMap.get(entry.getKey());
                        aggreBo.setPdfUrl(pdfUrl);
                        aggreBo.setViewStage(signH5InfoBo.getViewStage());
                        aggreBo.setRecordBatchId(recordEntity.getRecordBatchId());
                        aggreBo.setForceAllOp(isForceAllOp);
                        aggreBoList.add(aggreBo);
                    } else if (entry.getKey().equals(EcontractPdfTypeEnum.C2CONTRACT.getName())) {
                        WmContractContentAggreBo aggreBo = new WmContractContentAggreBo();
                        aggreBo.setContractName(EcontractPdfTypeEnum.C2CONTRACT.getDesc());
                        aggreBo.setContractTaskType(entry.getKey());
                        String desc = assemblyC2Desc(recordKey, entry.getValue());
                        aggreBo.setContractDesc(StringUtils.isNotEmpty(desc) ?  desc : "适用于 全部代理门店");
                        aggreBo.setRecordKey(recordKey);
                        aggreBo.setPdfUrl(pdfUrlMap.get(entry.getKey()));
                        String pdfUrl = StringUtils.isEmpty(pdfUrlMap.get(entry.getKey())) ?
                                pdfUrlMap.get("default") : pdfUrlMap.get(entry.getKey());
                        aggreBo.setPdfUrl(pdfUrl);
                        aggreBo.setViewStage(signH5InfoBo.getViewStage());
                        aggreBo.setRecordBatchId(recordEntity.getRecordBatchId());
                        aggreBo.setForceAllOp(isForceAllOp);
                        aggreBoList.add(aggreBo);
                    } else if (entry.getKey().equals(EcontractPdfTypeEnum.QUA_REAL_LETTER.getName())
                            || entry.getKey().equals(EcontractPdfTypeEnum.POI_PROMOTION_SERVICE.getName())
                            || entry.getKey().equals(EcontractPdfTypeEnum.BUSINESS_CUSTOMER_CONTRACT.getName())
                            || entry.getKey().equals(EcontractPdfTypeEnum.GROUP_MEAL.getName())
                            || entry.getKey().equals(EcontractPdfTypeEnum.BAG_SERVICE.getName())
                            || entry.getKey().equals(EcontractPdfTypeEnum.MED_DEPOSIT.getName())
                            || entry.getKey().equals(EcontractPdfTypeEnum.FOODCITY_STATEMENT.getName())
                            || entry.getKey().equals(EcontractPdfTypeEnum.FOODCITY_POI_TABLE.getName())
                            || entry.getKey().equals(EcontractPdfTypeEnum.INTERIM_SELF.getName())
                            || entry.getKey().equals(EcontractPdfTypeEnum.MEDIC_ORDER_SPLIT.getName())
                            || entry.getKey().equals(EcontractPdfTypeEnum.SUBJECT_CHANGE_SUPPLEMENT.getName())
                            || entry.getKey().equals(EcontractPdfTypeEnum.AGENT_SQS_STANDARD.getName())
                            || entry.getKey().equals(EcontractPdfTypeEnum.FOUR_WHEEL_PERFORMANCE_SUPPLEMENT_AGREEMENT.getName())
                            || entry.getKey().equals(EcontractPdfTypeEnum.SPEEDY_DELIVERY_COOPERATION_AGREEMENT.getName())
                            || entry.getKey().equals(EcontractPdfTypeEnum.NATIONAL_SUBSIDY_PURCHASE.getName())
                    ) {
                        WmContractContentAggreBo aggreBo = new WmContractContentAggreBo();
                        aggreBo.setContractName(EcontractPdfTypeEnum.getEnumByName(entry.getKey()).getDesc());
                        aggreBo.setContractTaskType(entry.getKey());
                        aggreBo.setRecordKey(recordKey);
                        aggreBo.setContractDesc("");
                        // 单PDF的情况对于的key为default
                        String pdfUrl = StringUtils.isEmpty(pdfUrlMap.get(entry.getKey())) ?
                                pdfUrlMap.get("default") : pdfUrlMap.get(entry.getKey());
                        aggreBo.setPdfUrl(pdfUrl);
                        aggreBo.setViewStage(signH5InfoBo.getViewStage());
                        aggreBo.setRecordBatchId(recordEntity.getRecordBatchId());
                        aggreBo.setForceAllOp(isForceAllOp);
                        aggreBoList.add(aggreBo);
                    } else if (entry.getKey().equals(EcontractPdfTypeEnum.COMMON_CONFIG_FRAME_CONTRACT_AGREEMENT.getName())) {
                        WmContractContentAggreBo aggreBo = buildAggreBo4Config(entry, signH5InfoBo, pdfUrlMap, recordEntity, isForceAllOp, recordKey);
                        aggreBoList.add(aggreBo);
                    } else {
                        WmContractContentAggreBo aggreBo = new WmContractContentAggreBo();
                        aggreBo.setContractName(EcontractPdfTypeEnum.getEnumByName(entry.getKey()).getDesc());
                        aggreBo.setContractTaskType(entry.getKey());
                        List<PdfContentInfoBo> pdfContents = entry.getValue();
                        // 当前配送&结算协议没有pdf拼接的情况，因此取第一个值即可，如果后续有pdf拼接，此处需要重新评估
                        if (CollectionUtils.isNotEmpty(pdfContents)) {
                            for (PdfContentInfoBo pdfContent : pdfContents) {
                                Map<String, String> pdfMetaContent = pdfContent.getPdfMetaContent();
                                if (org.apache.commons.collections4.MapUtils.isNotEmpty(pdfMetaContent)
                                        && StringUtils.isNotEmpty(pdfMetaContent.get("poiName"))) {
                                    poiNameText.append(pdfMetaContent.get("poiName"));
                                    poiNameText.append("、");
                                } else {
                                    List<Map<String, String>> pdfBizContent = pdfContent.getPdfBizContent();
                                    if (CollectionUtils.isNotEmpty(pdfBizContent)) {
                                        pdfBizContent.stream().forEach(content -> {
                                            if (StringUtils.isNotEmpty(content.get("poiName"))) {
                                                poiNameText.append(content.get("poiName"));
                                                poiNameText.append("、");
                                            }
                                        });
                                    }
                                }
                            }
                        }
                        if (StringUtils.isNotEmpty(poiNameText.toString())) {
                            String contractDesc = buildLimitedPoiContractDesc(poiNameText.toString());
                            mergeText = prefixText.append(contractDesc);
                            mergeText.deleteCharAt(mergeText.length() - 1);
                            aggreBo.setContractDesc(mergeText.toString());
                        }
                        aggreBo.setRecordKey(recordKey);
                        String pdfUrl = StringUtils.isEmpty(pdfUrlMap.get(entry.getKey())) ?
                                pdfUrlMap.get("default") : pdfUrlMap.get(entry.getKey());
                        aggreBo.setPdfUrl(pdfUrl);
                        aggreBo.setViewStage(signH5InfoBo.getViewStage());
                        aggreBo.setRecordBatchId(recordEntity.getRecordBatchId());
                        aggreBo.setForceAllOp(isForceAllOp);
                        aggreBoList.add(aggreBo);
                    }
                }
            }
        }
        return aggreBoList;
    }

    private WmContractContentAggreBo buildAggreBo4Config(Map.Entry<String, List<PdfContentInfoBo>> entry, SignH5InfoBo signH5InfoBo,
                                                         Map<String, String> pdfUrlMap, EcontractRecordEntity recordEntity, boolean isForceAllOp, String recordKey) {
        WmContractContentAggreBo aggreBo = new WmContractContentAggreBo();
        aggreBo.setContractName(extractContractName4Config(entry.getValue()));
        aggreBo.setContractTaskType(entry.getKey());
        aggreBo.setRecordKey(recordKey);
        aggreBo.setContractDesc("");
        // 单PDF的情况对于的key为default
        String pdfUrl = StringUtils.isEmpty(pdfUrlMap.get(entry.getKey())) ?
                pdfUrlMap.get("default") : pdfUrlMap.get(entry.getKey());
        aggreBo.setPdfUrl(pdfUrl);
        aggreBo.setViewStage(signH5InfoBo.getViewStage());
        aggreBo.setRecordBatchId(recordEntity.getRecordBatchId());
        aggreBo.setForceAllOp(isForceAllOp);
        return aggreBo;
    }

    private String extractContractName4Config(List<PdfContentInfoBo> pdfContentInfoBoList) {
        return pdfContentInfoBoList.stream()
                .map(PdfContentInfoBo::getContractName)
                .filter(Objects::nonNull)
                .findFirst()
                .orElse("合同");
    }

    private WmContractContentAggreBo buildWmContractContentAggreBo(Map.Entry<String, List<PdfContentInfoBo>> entry, SignH5InfoBo signH5InfoBo,
                                                                   Map<String, String> pdfUrlMap, EcontractRecordEntity recordEntity, boolean isForceAllOp, String recordKey) {
        PdfContentInfoBo pdfContentInfoBo = entry.getValue().get(0);

        WmContractContentAggreBo aggreBo = new WmContractContentAggreBo();
        aggreBo.setContractDesc(pdfContentInfoBo.getContractDesc());
        aggreBo.setContractName(pdfContentInfoBo.getContractName());
        aggreBo.setContractTaskType(entry.getKey());
        aggreBo.setRecordKey(recordKey);
        String pdfUrl = StringUtils.isEmpty(pdfUrlMap.get(entry.getKey())) ? pdfUrlMap.get("default") : pdfUrlMap.get(entry.getKey());
        aggreBo.setViewStage(signH5InfoBo.getViewStage());
        aggreBo.setPdfUrl(pdfUrl);
        aggreBo.setRecordBatchId(recordEntity.getRecordBatchId());
        aggreBo.setForceAllOp(isForceAllOp);
        return aggreBo;
    }

    private String removeDuplicatePoiName(String poiName){
        TreeSet poiNameSet = new TreeSet();
        String[] poiNameArray = poiName.split("、");
        for(String name : poiNameArray){
            poiNameSet.add(name);
        }
        StringBuilder sb = new StringBuilder();
        Iterator iterator = poiNameSet.iterator();
        while(iterator.hasNext()){
            sb.append(iterator.next());
            sb.append("、");
        }
        return sb.toString();
    }

    private String buildLimitedPoiContractDesc(String poiNameConcat) {
        int limitToShow = MccConfig.getPoiNameLimitToShow();
        String contractDesc = removeDuplicatePoiName(poiNameConcat);
        String[] poiNames = contractDesc.split("、");
        int totalPoiCount = 0;
        for (String name : poiNames) {
            if (StringUtils.isNotEmpty(name)) {
                totalPoiCount++;
            }
        }
        if (totalPoiCount <= limitToShow) {
            return contractDesc;
        }
        StringBuilder limitedDesc = new StringBuilder();
        int added = 0;
        for (String name : poiNames) {
            if (StringUtils.isNotEmpty(name)) {
                limitedDesc.append(name).append("、");
                added++;
                if (added >= limitToShow) {
                    break;
                }
            }
        }
        limitedDesc.append("...").append("、");
        return limitedDesc.toString();
    }

    @Override
    public CertifyH5InfoBo queryH5CertifyInfoBySecretParam(String param) throws EcontractException, TException {
        try {
            if (StringUtils.isNotEmpty(param)) {
                String privateKey = Kms.getByName(MccConstant.APP_KEY, KmsConstant.REAL_NAME_PRIVATE_KEY);
                String dealVersion = CaSignUtil.decrypt3desc(param, privateKey.substring(0, 24));
                EcontractSmsDealEntity dealEntity = smsDealEntityMapper.queryOneDealByVersion(dealVersion);
                EcontractRecordEntity recordEntity = econtractRecordService.selectByPrimaryKey(dealEntity.getEcontractRecordId());
                EcontractContext context = JSON.parseObject(recordEntity.getEcontractRecordContext(), EcontractContext.class);
                StageInfoBo stageInfoBo = ContextUtils.getStageInfoBoFromList(context, TaskConstant.REAL_NAME_AUTH_D);
                return stageInfoBo.getSignerInfoBo().getCertifyH5InfoBo();
            }
        } catch (BadPaddingException e) {
            LOGGER.warn("fail to get queryH5CertifyInfoBySecretParam，解密param失败，param={}", param, e);
        } catch (Exception e) {
            LOGGER.error("fail to get queryH5CertifyInfoBySecretParam", e);
        }
        return null;
    }

    @Override
    public List<SignAdditionInfoMeta> queryEcontractListExtraInfo(String recordKey, WmContractContentAggreBo aggreBo) throws EcontractException, TException {
        List<SignAdditionInfoMeta> result = Lists.newArrayList();
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(recordKey);
        if (recordEntity == null) {
            return result;
        }
        List<SignAdditionInfoMeta> additionInfoMetaList = Lists.newArrayList();
        for (EcontractExtraInfoPacking executor : packingList) {
            try{
                executor.packing(recordEntity, additionInfoMetaList, aggreBo);
            }catch(Exception e){
                LOGGER.error("queryEcontractListExtraInfo#签约页面附加信息包装器执行异常:{}",executor.getClass().getSimpleName(), e);
            }
        }
        return additionInfoMetaList;
    }

    @Override
    public Map<String, List<WmContractContentAggreBo>> queryEcontractContentAggreMap(List<String> recordKeys)
            throws EcontractException, TException {
        try {
            // 空列表则直接返回
            if (CollectionUtils.isEmpty(recordKeys)) {
                return Maps.newHashMap();
            }
            Map<String, List<WmContractContentAggreBo>> aggreMap = new HashMap<>();
            CountDownLatch latch = new CountDownLatch(recordKeys.size());
            for (String recordKey : recordKeys) {
                ListenableFuture<List<WmContractContentAggreBo>> future = toSignQueryExecutorService.submit(new Callable() {
                            @Override
                            public List<WmContractContentAggreBo> call() throws Exception {
                                return queryEcontractContentAggreList(recordKey);
                            }
                        });
                Futures.addCallback(future, new FutureCallback<List<WmContractContentAggreBo>>() {
                    @Override
                    public void onSuccess(@Nullable List<WmContractContentAggreBo> result) {
                        aggreMap.put(recordKey, result);
                        latch.countDown();
                    }

                    @Override
                    public void onFailure(Throwable t) {
                        LOGGER.error("获取recordKey:{}的签约信息失败", recordKey, t);
                        latch.countDown();
                    }
                });
            }
            latch.await();
            return aggreMap;
        } catch (Exception e) {
            LOGGER.error("queryEcontractContentAggreMap#根据recordKey列表获取签约信息异常 recordKeys:{}", recordKeys, e);
        }
        return Maps.newHashMap();
    }

    @Override
    public Long initBatchOpInfo(List<String> recordKeys, Integer opType) throws EcontractException, TException {
        try {
            // 生成批次激励
            EcontractBatchOpEntity batchOpEntity = new EcontractBatchOpEntity();
            batchOpEntity.setRecordKeys(StringUtils.join(recordKeys, ","));
            batchOpEntity.setOpType(opType);
            EcontractBatchOpExtBo batchOpExtBo = new EcontractBatchOpExtBo();
            batchOpExtBo.setTotalNum(recordKeys.size());
            batchOpEntity.setExt(JSON.toJSONString(batchOpExtBo));
            batchOpEntity.setStatus(EcontractBatchOpStatusConstant.RUNING);
            batchOpEntity.setVersion(0);
            econtractBatchOpMapper.insert(batchOpEntity);
            // 更新record表
            econtractRecordService.batchUpdateBatchOpIdByRecordKeyList(recordKeys, batchOpEntity.getId());
            return batchOpEntity.getId();
        } catch (Exception e) {
            LOGGER.error("initBatchOpInfo#初始化批量操作信息异常 recordKeys:{}, opType:{}", recordKeys, opType, e);
        }
        return null;
    }

    @Override
    public void doBatchOpByRecordKeys(List<String> recordKeys, Integer opType) {
        for (String recordKey : recordKeys) {
            toSignOpExecutorService.submit(() -> {
                try {
                    if (EcontractBatchOpTypeConstant.CONFIRM == opType) {
                        templateManager.confirmSignEContract(recordKey);
                    }
                    if (EcontractBatchOpTypeConstant.CANCEL == opType) {
                        templateManager.cancelSignEContract(recordKey, "批量取消");
                    }
                } catch (Exception e) {
                    LOGGER.error("批量操作合同异常 recordKey:{}, opType:{}", recordKey, opType, e);
                }
            });
        }
    }

    @Override
    public EcontractBatchOpEntity queryBatchOpEntityById(Long batchOpId) throws EcontractException, TException {
        try {
            return econtractBatchOpMapper.selectByPrimaryKey(batchOpId);
        } catch (Exception e) {
            LOGGER.error("queryBatchOpEntityById#查询 batchOpId:{}", batchOpId, e);
        }
        return null;
    }

    private boolean isEffectSmsDeal(EcontractSmsDealEntity dealEntity) {
        Date nowDate = new Date();
        //对应的失效时间不为空且大于0，说明业务方传入了短信失效时间
        if (dealEntity.getExpireTime() != null && dealEntity.getExpireTime() > 0) {
            return dealEntity.getExpireTime() > nowDate.getTime() / 1000;
        }
        //使用默认判断逻辑
        String effectiveTime = ConfigUtilAdapter.getString(MccConstant.SIGN_URL_EFFECTIVE_TIME, String.valueOf(24 * 60 * 60));
        return !TimeCompareUtils.isMoreThan(dealEntity.getCtime(), new Date(), Integer.valueOf(effectiveTime));
    }

    @Override
    public List<EcontractRecordBo> queryEcontractRecordByBatchId(Integer batchId) throws EcontractException, TException {
        List<EcontractRecordBo> recordBoList = Lists.newArrayList();
        List<EcontractRecordEntity> recordEntities = econtractRecordService.queryEcontractRecordByBatchId(batchId);
        if (CollectionUtils.isNotEmpty(recordEntities)) {
            recordEntities.stream().forEach(econtractRecordEntity -> {
                recordBoList.add(econtractRecordEntityToBo(econtractRecordEntity));
            });
        }
        return recordBoList;
    }

    @Override
    public EcontractRecordBo queryEcontractRecordByRecordKey(String recordKey) throws EcontractException, TException {
        EcontractRecordEntity econtractRecordEntity = econtractRecordService.queryRecordByRecordKey(recordKey);
        return econtractRecordEntityToBo(econtractRecordEntity);
    }

    private EcontractRecordBo econtractRecordEntityToBo(EcontractRecordEntity entity) {
        if (entity == null) {
            return null;
        }
        EcontractRecordBo recordBo = new EcontractRecordBo();
        recordBo.setRecordKey(entity.getRecordKey());
        recordBo.setEcontractType(entity.getEcontractType());
        recordBo.setEcontractStage(entity.getEcontractStage());
        recordBo.setEcontractState(entity.getEcontractState());
        recordBo.setFailMessage(entity.getFailMessage());
        recordBo.setSaveUrl(entity.getSaveUrl());
        recordBo.setRecordBatchId(entity.getRecordBatchId());
        return recordBo;
    }

    @Override
    public String queryBatchSmsRecordKeyByBatchId(Integer batchId) throws EcontractException, TException {
        EcontractSignBatchEventEntity eventEntity = econtractSignRecordBatchEventService.getSignBatchEventByBatchId(batchId);
        return eventEntity == null ? Strings.EMPTY : eventEntity.getRecordKey();
    }

    @Override
    public SignH5InfoBo querySignH5InoByRecordKey(String recordKey) throws EcontractException, TException {
        SignH5InfoBo signH5InfoBo = new SignH5InfoBo();
        try {
            LOGGER.info("querySignH5InoByRecordKey recordKey is : {}", recordKey);
            String effectiveTime = ConfigUtilAdapter.getString(MccConstant.SIGN_URL_EFFECTIVE_TIME, String.valueOf(24 * 60 * 60));
            EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(recordKey);
            if (recordEntity == null) {
                signH5InfoBo.setViewStage(WebViewConstant.STAGE_UN_EFFECTIVE);
                return signH5InfoBo;
            }

            List<EcontractSmsDealEntity> dealEntityList = smsDealEntityMapper.queryDealByRecordId(recordEntity.getId());
            if (CollectionUtils.isEmpty(dealEntityList)) {
                signH5InfoBo.setViewStage(WebViewConstant.STAGE_UN_EFFECTIVE);
                return signH5InfoBo;
            }

            EcontractSmsDealEntity dealEntity = dealEntityList.get(0);

            //对应短信未过期
            if (isEffectSmsDeal(dealEntity)) {
                signH5InfoBo = templateManager.querySignH5InoByRecordIdAndTaskId(dealEntity.getEcontractRecordId(), DealVersionUtils
                        .getTaskId(dealEntity.getDealVersion()));
            } else {
                signH5InfoBo.setViewStage(WebViewConstant.STAGE_UN_EFFECTIVE);
                signH5InfoBo.setPdfUrlMap(templateManager.buildDownloadPdfUrl(dealEntity.getEcontractRecordId()));
            }
            return signH5InfoBo;
        } catch (Exception e) {
            LOGGER.error("fail to get recordKey", e);
            return signH5InfoBo;
        }
    }

    @Override
    public Boolean isForceAllOp(Integer batchId) throws EcontractException, TException {
        if (batchId == null || batchId <= 0) {
            return false;
        }
        EcontractSignRecordBatchEntity entity = econtractSignRecordBatchService.selectByPrimaryKey(batchId);
        return entity != null && entity.getForceAllOp() == 1;
    }

    @Override
    public Boolean checkRecordKeys(List<String> recordKeys) throws EcontractException {
        // 根据recordKeys获取record信息
        List<EcontractRecordEntity> recordEntityListByRecordKeys = econtractRecordService.queryRecordListByRecordKeyList(recordKeys);
        // 过滤出批量发起签约的record信息
        recordEntityListByRecordKeys = recordEntityListByRecordKeys.stream().filter(item -> item.getRecordBatchId() > 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(recordEntityListByRecordKeys)) {
            return true;
        }
        // 获取批次Id列表
        List<Integer> batchIdList = recordEntityListByRecordKeys.stream().map(EcontractRecordEntity::getRecordBatchId).collect(Collectors.toList());
        // 根据批次Id列表获取批次信息
        List<EcontractSignRecordBatchEntity> batchEntityList = econtractSignRecordBatchService.queryBatchEntityListByIds(batchIdList);
        // 过滤需要全部操作的批次Id
        List<Integer> forceAllOpBatchIds = new ArrayList<>();
        for (EcontractSignRecordBatchEntity batchEntity : batchEntityList) {
            if (batchEntity.getForceAllOp() == 1) {
                forceAllOpBatchIds.add(Integer.parseInt(batchEntity.getId() + ""));
            }
        }
        if (CollectionUtils.isEmpty(forceAllOpBatchIds)) {
            return true;
        }
        // 根据需要全部操作的批次Id查询record信息
        List<EcontractRecordEntity> recordEntityListByForceAllOpBatchIds = econtractRecordService.queryEcontractRecordByBatchIds(forceAllOpBatchIds);
        // Map转换
        Map<Integer, List<EcontractRecordEntity>> recordEntityMapByRecordKeys = recordEntityListByRecordKeys.stream()
                .collect(Collectors.groupingBy(EcontractRecordEntity::getRecordBatchId));
        Map<Integer, List<EcontractRecordEntity>> recordEntityMapByForceAllOpBatchIds = recordEntityListByForceAllOpBatchIds
                .stream().collect(Collectors.groupingBy(EcontractRecordEntity::getRecordBatchId));
        // 针对需要全部操作的发起签约批次，判断传进来的recordKey和查出来的recordKey是否全部匹配
        for (Map.Entry<Integer, List<EcontractRecordEntity>> entry : recordEntityMapByForceAllOpBatchIds.entrySet()) {
            List<String> recordKeysToCheck = recordEntityMapByRecordKeys.get(entry.getKey())
                .stream()
                .map(EcontractRecordEntity::getRecordKey)
                .collect(Collectors.toList());
            List<String> recordKeysToBeChecked = entry.getValue()
                .stream()
                .map(EcontractRecordEntity::getRecordKey)
                .collect(Collectors.toList());
            if (!recordKeysToCheck.containsAll(recordKeysToBeChecked)) {
                LOGGER.info("checkRecordKeys 需要全部操作的批次未同步所有record记录 batchId:{}", entry.getKey());
                throw new EcontractException(WmContractErrorCodeConstant.SYSTEM_ERROR, "需要全部操作的批次未同步所有record记录");
            }
        }
        return true;
    }

    @Override
    public List<EcontractRecordBo> queryEcontractRecordList(List<String> recordKeyList)
        throws EcontractException, TException {

        // 根据recordKeys获取record信息
        List<EcontractRecordEntity> recordEntityListByRecordKeys = econtractRecordService.queryRecordList(
            recordKeyList);
        List<EcontractRecordBo> econtractRecordBoList =
            org.apache.commons.collections4.CollectionUtils.emptyIfNull(recordEntityListByRecordKeys)
                .stream()
                .map(m -> econtractRecordEntityToBo(m))
                .collect(
                    Collectors.toList());
        LOGGER.info("queryEcontractRecordList返回", JSON.toJSONString(econtractRecordBoList));
        return econtractRecordBoList;
    }

    @Override
    public SignEcontractInfoBo queryEcontractDataInfoWD(DaocanContractRequestDTO request) throws EcontractException, TException {
        LOGGER.info("EcontractSignPageBizServiceImpl#queryEcontractDataInfoWD, DaocanContractRequestDTO = {}", JSON.toJSONString(request));
        String recordKey = this.queryRecordKeyBySecretParam(request.getParam());
        if (StringUtils.isEmpty(recordKey)) {
            throw new EcontractException(EcontractErrorCode.BUSINESS_ERROR, "查询失败");
        }
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(recordKey);

        SignEcontractInfoBo signEcontractInfoBo = new SignEcontractInfoBo();
        signEcontractInfoBo.setC1EcontractInfo(new ArrayList<>());
        signEcontractInfoBo.setC1ResignInfo(new ArrayList<>());
        signEcontractInfoBo.setSignEcontractPdfUrl(new ArrayList<>());


        List<String> recordKeyList = queryRecurdKeyList(recordEntity);
        List<EcontractRecordEntity> recordEntityList = queryAllEcontractRecordByRecordKeyList(recordKeyList);

        // 组装c1合同信息
        List<CoopSignBasicInfo> coopSignBasicInfos = queryCoopInfoByRecordEntity(recordKeyList);
        for (CoopSignBasicInfo coopSignBasicInfo : coopSignBasicInfos) {
            initCoopSignInfo(coopSignBasicInfo, signEcontractInfoBo);
        }

        // c2
        List<PdfContentInfoBo> dineInServiceC2 = querydineInServiceC2Info(recordEntityList);
        initC2SignInfo(dineInServiceC2, signEcontractInfoBo);

        // pdf信息
        signEcontractInfoBo.setSignEcontractPdfUrl(buildSignEcontractPdfUrl(coopSignBasicInfos, dineInServiceC2));

        LOGGER.info("EcontractSignPageBizServiceImpl#queryEcontractDataInfoWD, signEcontractInfoBo = {}", JSON.toJSONString(signEcontractInfoBo));
        return signEcontractInfoBo;
    }

    private List<PdfContentInfoBo> querydineInServiceC2Info(List<EcontractRecordEntity> recordEntityList) {
        if (CollectionUtils.isEmpty(recordEntityList)) {
            return Collections.emptyList();
        }

        List<EcontractRecordEntity> dcC2RecordEntityList = recordEntityList.stream()
                .filter(recordKey -> DINE_IN_SERVICE_C2.equals(recordKey.getEcontractType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dcC2RecordEntityList)) {
            return Collections.emptyList();
        }
        return dcC2RecordEntityList.stream().map(this::extractPdfContentInfoBo).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private PdfContentInfoBo extractPdfContentInfoBo(EcontractRecordEntity recordEntity) {
        EcontractContext context = JSON.parseObject(recordEntity.getEcontractRecordContext(), EcontractContext.class);
        Optional<StageBatchInfoBo> stageBatchInfoBo = context.getStageBatchInfoBoList().stream()
                .filter(v -> TaskConstant.CREATE_PDF.equals(v.getStageName()))
                .findFirst();
        if (!stageBatchInfoBo.isPresent()) {
            return null;
        }
        for (List<PdfContentInfoBo> pdfContentInfoBoList : stageBatchInfoBo.get().getPdfContentInfoBoMap().values()) {
            if (CollectionUtils.isEmpty(pdfContentInfoBoList)) {
                continue;
            }
            pdfContentInfoBoList.get(0).setPdfUrl(PDF_URL_PREFIX + recordEntity.getSaveUrl());
            return pdfContentInfoBoList.get(0);
        }
        return null;
    }

    private List<EcontractRecordEntity> queryAllEcontractRecordByRecordKeyList(List<String> recordKeyList) {
        LOGGER.info("EcontractSignPageBizServiceImpl#queryAllEcontractRecordByRecordKeyList, recordKeyList = {}", JSON.toJSONString(recordKeyList));
        if (CollectionUtils.isEmpty(recordKeyList)) {
            return Collections.emptyList();
        }
        return recordKeyList.stream().map(econtractRecordService::queryRecordByRecordKey).collect(Collectors.toList());
    }

    private List<String> queryRecurdKeyList(EcontractRecordEntity recordEntity) {
        List<String> recordKeyList = new ArrayList<>();
        // 非打包
        if (recordEntity.getRecordBatchId() == null || recordEntity.getRecordBatchId() <= 0) {
            recordKeyList.add(recordEntity.getRecordKey());
        } else {
            // 打包，查询对应的打包批次id下的所有record
            List<EcontractRecordEntity> recordEntities = econtractRecordService.queryEcontractRecordByBatchId(recordEntity.getRecordBatchId());
            if (CollectionUtils.isNotEmpty(recordEntities)) {
                recordKeyList = recordEntities.stream().map(EcontractRecordEntity::getRecordKey).collect(Collectors.toList());
            }
        }
        LOGGER.info("EcontractSignPageBizServiceImpl#queryRecurdKeyList, recordKeyList: {}", JacksonUtil.writeAsJsonStr(recordKeyList));
        return recordKeyList;
    }

    private void initC2SignInfo(List<PdfContentInfoBo> pdfContentInfoBoList, SignEcontractInfoBo signEcontractInfoBo) {
        if (CollectionUtils.isEmpty(pdfContentInfoBoList)) {
            signEcontractInfoBo.setC2EcontractInfo(Collections.emptyList());
            return;
        }
        signEcontractInfoBo.setC2EcontractInfo(pdfContentInfoBoList.stream().map(this::buildC2EcontractInfoBo).collect(Collectors.toList()));
    }

    private SignEcontractPdfUrlBo buildC2SignEcontractPdfUrlBo(PdfContentInfoBo pdfContent) {
        return SignEcontractPdfUrlBo.builder()
                .contractName(buildPdfContractName(pdfContent.getContractName(), pdfContent.getContractNumber(), true))
                .businessLine(EcontractBusinessTypeEnum.DAOJIA.getType())
                .pdfUrl(pdfContent.getPdfUrl())
                .build();
    }

    private EcontractInfoBo buildC2EcontractInfoBo(PdfContentInfoBo pdfContentInfoBo) {
        return EcontractInfoBo.builder()
                .contractName(pdfContentInfoBo.getContractName())
                .contractNumber(pdfContentInfoBo.getContractNumber())
                .pdfUrl(pdfContentInfoBo.getPdfUrl())
                .build();
    }

    private void initCoopSignInfo(CoopSignBasicInfo coopSignBasicInfo, SignEcontractInfoBo signEcontractInfoBo) throws EcontractException {
        LOGGER.info("EcontractSignPageBizServiceImpl#initCoopSignInfo, coopSignBasicInfo = {}", JSON.toJSONString(coopSignBasicInfo));
        boolean isNewSign = coopSignBasicInfo.isIsNewSign();
        if (isNewSign) {
            CoopNewSignInfo newSignCoopInfo = coopSignBasicInfo.getNewSignCoopInfo();
            TFrameCoop frameCoop = newSignCoopInfo.getFrameCoop();

            C1EcontractInfoBo c1EcontractInfoBo = new C1EcontractInfoBo();
            c1EcontractInfoBo.setCoopBaseInfo(buildCoopBaseInfo(newSignCoopInfo));
            c1EcontractInfoBo.setCoopBusinessInfo(buildCoopBusinessInfoList(frameCoop));
            c1EcontractInfoBo.setCoopBankInfo(buildCoopBankInfo(frameCoop));
            c1EcontractInfoBo.setNewSignC1(isNewSign);

            signEcontractInfoBo.getC1EcontractInfo().add(c1EcontractInfoBo);
        } else {
            // 拼装换签信息
            TBasicCoopInfo reSignCoopInfo = coopSignBasicInfo.getReSignCoopInfo();

            C1ResignInfoBo c1ResignInfoBo = C1ResignInfoBo.builder()
                    .oriEcontractInfo(EcontractInfoBo.builder().contractName(reSignCoopInfo.getCommonCoopName()).contractNumber(reSignCoopInfo.getCommonCoopNum()).build())
                    // 主体换签不修改合同名称和合同编号
                    .newEcontractInfo(EcontractInfoBo.builder().contractName(reSignCoopInfo.getCommonCoopName()).contractNumber(reSignCoopInfo.getCommonCoopNum()).build())
                    .build();
            signEcontractInfoBo.getC1ResignInfo().add(c1ResignInfoBo);
        }
    }

    private SignEcontractPdfUrlBo buildSignEcontractPdfUrlBo(CoopSignBasicInfo coopSignBasicInfo, String contractName, String contractNumber) throws EcontractException {
        return SignEcontractPdfUrlBo.builder()
                .coopKey(buildCoopKey(coopSignBasicInfo))
                .businessLine(EcontractBusinessTypeEnum.DAOCAN.getType())
                .contractName(buildPdfContractName(contractName, contractNumber, coopSignBasicInfo.isIsNewSign()))
                .build();
    }

    private CoopBankInfoBo buildCoopBankInfo(TFrameCoop frameCoop) {
        TFrameBankInfo frameBankInfo = frameCoop.getTFrameBankInfo();
        if (frameBankInfo == null) {
            return null;
        }
        return CoopBankInfoBo.builder()
                .accountNum(frameBankInfo.getAccountNum())
                .accountName(frameBankInfo.getAccountName())
                .idCardNum(frameBankInfo.getAccountType() == AccountType.PUBLIC.getValue() ? frameBankInfo.getLegalPersonIdCardNum() : frameBankInfo.getAccountIdCardNum())
                .financeName(frameBankInfo.getFinanceName())
                .financeMobile(frameBankInfo.getFinanceMobile())
                .build();
    }

    private List<CoopBusinessInfoBo> buildCoopBusinessInfoList(TFrameCoop frameCoop) {
        List<TBusiness> businesses = frameCoop.getTBusinesses();
        List<CoopBusinessInfoBo> coopBusinessInfoBos = Collections.emptyList();
        if (CollectionUtils.isNotEmpty(businesses)) {
            coopBusinessInfoBos = businesses.stream().map(item -> {
                return CoopBusinessInfoBo.builder()
                        .businessId(item.getBusinessId())
                        .businessName(item.getBusinessName())
                        .autoPayPlanDetail(item.getAutoPayPlanDetail())
                        .contactMobile(item.getContactMobile())
                        .contactEmail(item.getContactEmail())
                        .build();
            }).collect(Collectors.toList());
        }
        return coopBusinessInfoBos;
    }

    private CoopBaseInfoBo buildCoopBaseInfo(CoopNewSignInfo coopNewSignInfo) {
        if (coopNewSignInfo == null) {
            return CoopBaseInfoBo.builder().build();
        }
        return CoopBaseInfoBo.builder()
                .qualificationName(coopNewSignInfo.getQualificationName())
                .qualificationCode(coopNewSignInfo.getQualificationCode())
                .signPersonName(coopNewSignInfo.getSignPersonName())
                .signPersonPhone(coopNewSignInfo.getSignPersonPhone())
                .build();
    }

    private String buildPdfContractName(String contractName, String contractNumber, boolean isNewSign) {
        return new StringBuilder().append(contractName)
                .append("(")
                .append(isNewSign ? "":"换签后，")
                .append("合同编号：")
                .append(contractNumber)
                .append(")")
                .toString();
    }

    private String buildCoopKey(CoopSignBasicInfo coopSignBasicInfo) throws EcontractException {
        try {
            boolean isNewSign = coopSignBasicInfo.isIsNewSign();
            String frameId = "";
            int coopType = -1;
            if (isNewSign) {
                frameId = coopSignBasicInfo.getNewSignCoopInfo().getFrameCoop().getTFrame().getFrameId();
            } else {
                frameId = coopSignBasicInfo.getReSignCoopInfo().getCommonCoopId();
                coopType = coopSignBasicInfo.getReSignCoopInfo().getCoopType();
            }
            // 格式为：coopId_isNewSign_coopType
            // 其中 coopType仅在换签时需要，新签时设置为-1
            StringBuilder coopKey = new StringBuilder()
                    .append(frameId).append("_")
                    .append(isNewSign).append("_")
                    .append(coopType);

            String privateKey = Kms.getByName(MccConstant.APP_KEY, KmsConstant.REAL_NAME_PRIVATE_KEY);
            return CaSignUtil.encrypt3desc(coopKey.toString(), privateKey.substring(0, 24));
        } catch (Exception e) {
            LOGGER.error("EcontractSignPageBizServiceImpl#buildCoopKey, 加密合同信息失败, error", e);
            throw new EcontractException(EcontractErrorCode.BUSINESS_ERROR, "失败");
        }
    }

    @Override
    public String queryCoopPreviewHtml(CoopPreviewHtmlRequestDTO coopPreviewHtmlRequestDTO) throws EcontractException {
        LOGGER.info("EcontractSignPageBizServiceImpl#queryCoopPreviewHtml, coopPreviewHtmlRequestDTO = {}", JSON.toJSONString(coopPreviewHtmlRequestDTO));
        try {
            if (StringUtils.isEmpty(coopPreviewHtmlRequestDTO.getCoopKey())) {
                throw new EcontractException(EcontractErrorCode.BUSINESS_ERROR, "参数错误");
            }

            String coopKey = decrypt3desc(coopPreviewHtmlRequestDTO.getCoopKey());
            if (StringUtils.isEmpty(coopKey)) {
                throw new EcontractException(EcontractErrorCode.BUSINESS_ERROR, "查询失败");
            }

            return commonCoopServiceAdapter.queryCoopPreviewHtml(buildCoopPreviewHtmlBo(coopKey));
        } catch (EcontractException e) {
            LOGGER.error("EcontractSignPageBizServiceImpl#queryCoopPreviewHtml ", e);
            throw e;
        } catch (Exception e) {
            throw new EcontractException(EcontractErrorCode.BUSINESS_ERROR, "查询失败");
        }
    }

    private String decrypt3desc(String encryptStr) throws EcontractException {
        LOGGER.info("EcontractSignPageBizServiceImpl#decrypt3desc, encryptInfo = {}", encryptStr);
        try {
            String privateKey = Kms.getByName(MccConstant.APP_KEY, KmsConstant.REAL_NAME_PRIVATE_KEY);
            return CaSignUtil.decrypt3desc(encryptStr, privateKey.substring(0, 24));
        } catch (Exception e) {
            LOGGER.info("EcontractSignPageBizServiceImpl#decrypt3desc 解密失败, encryptInfo = {}", encryptStr);
            throw new EcontractException(EcontractErrorCode.BUSINESS_ERROR, "失败");
        }
    }

    private CoopPreviewHtmlBo buildCoopPreviewHtmlBo(String coopKey) {
        // coopId_isNewSign_coopType
        String[] coopInfoArray = coopKey.split("_");
        String coopId = coopInfoArray[0];
        boolean isNewSign = Boolean.parseBoolean(coopInfoArray[1]);
        Integer coopType = Integer.parseInt(coopInfoArray[2]);
        return CoopPreviewHtmlBo.builder()
                .coopId(coopId)
                .newSign(isNewSign)
                .coopType(isNewSign ? null : coopType)
                .build();
    }

    @Override
    public SignEcontractCompleteInfoBo getEcontractCompleteInfo(DaocanContractRequestDTO request) throws EcontractException, TException {
        LOGGER.info("EcontractSignPageBizServiceImpl#getEcontractCompleteInfo, recordKey = {}", JSON.toJSONString(request));
        String recordKey = this.queryRecordKeyBySecretParam(request.getParam());
        if (StringUtils.isEmpty(recordKey)) {
            throw new EcontractException(EcontractErrorCode.BUSINESS_ERROR, "查询失败");
        }
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(recordKey);

        SignEcontractCompleteInfoBo signEcontractCompleteInfoBo = buildSignEcontractCompleteInfoBo();

        // PDF 信息
        List<String> recordKeyList = queryRecurdKeyList(recordEntity);
        List<EcontractRecordEntity> recordEntityList = queryAllEcontractRecordByRecordKeyList(recordKeyList);
        List<PdfContentInfoBo> dineInServiceC2 = querydineInServiceC2Info(recordEntityList);
        List<CoopSignBasicInfo> coopSignBasicInfos = queryCoopInfoByRecordEntity(recordKeyList);
        signEcontractCompleteInfoBo.setSignEcontractPdfUrl(buildSignEcontractPdfUrl(coopSignBasicInfos, dineInServiceC2));

        LOGGER.info("EcontractSignPageBizServiceImpl#getEcontractCompleteInfo, signEcontractCompleteInfoBo = {}", JSON.toJSONString(signEcontractCompleteInfoBo));
        return signEcontractCompleteInfoBo;
    }

    @Override
    public SignStatusViewVo querySignStatus(DaocanContractRequestDTO request) throws TException, EcontractException {
        WebViewEnum webViewEnum = getWebView(request.getParam());
        SignStatusViewVo statusViewVo = new SignStatusViewVo();
        statusViewVo.setCode(webViewEnum.getCode());
        statusViewVo.setDesc(webViewEnum.getDesc());
        return statusViewVo;
    }

    /**
     * 将合同pdfUrl转换为图片
     * @param url
     * @return
     * @throws EcontractException
     */
    @Override
    public PdfImageBo queryPdfImageByUrl(String url) throws EcontractException {
        try {
            PdfImageBo pdfImageBo = new PdfImageBo();
            pdfImageBo.setUrl(url);
            byte[] bytesOfPdf = ImageUtil.getCloudPicBytes(url);
            int totalPages = PdfConvertImageUtil.getTotalPages(bytesOfPdf);
            pdfImageBo.setTotalPage(totalPages);

            byte[] bytesOfImage;
            //进行转换PDF->base64
            List<String> pdfImages = Lists.newArrayList();
            for (int i = 1; i <= totalPages; i++) {
                bytesOfImage = PdfConvertImageUtil.transfer(bytesOfPdf, i);
                pdfImages.add(Base64.encodeBase64String(bytesOfImage));
            }
            pdfImageBo.setImages(pdfImages);
            return pdfImageBo;
        } catch (Exception e) {
            LOGGER.error("EcontractSignPageBizServiceImpl#transPdfUrlToImage, pdfUrl = {}", url, e);
            throw new EcontractException(EcontractErrorCode.BUSINESS_ERROR, "转换失败");
        }
    }

    @Override
    public List<SpAreaDataQueryRespDTO> querySpAreaData(SpAreaDataQueryRequestDTO request) throws EcontractException  {
        checkSpAreaDataQueryParam(request);
        Map<String, String> spAreaMap = this.queryEcontractSpAreaData(request.getRecordKey());
        return wmEcontractSpAreaContentWrapperService.analysisSpAreaData(spAreaMap, request.getAreaModule());
    }

    private void checkSpAreaDataQueryParam(SpAreaDataQueryRequestDTO request) throws EcontractException {
        if (request == null) {
            throw new EcontractException(EcontractErrorCode.BUSINESS_ERROR, "参数错误");
        }
        checkEncryptedParams(request.getAreaModule(), request.getRecordKey(), request.getEncryptKey());
    }

    private void checkEncryptedParams(String areaModule, String recordKey, String encryptKey) throws EcontractException {
        try {
            String privateKey = Kms.getByName(MccConstant.APP_KEY, KmsConstant.REAL_NAME_PRIVATE_KEY);
            String decrypted = CaSignUtil.decrypt3desc(encryptKey, privateKey.substring(0, 24));
            String[] params = decrypted.split("\\|");
            if (params.length != 2 || !params[0].equals(recordKey) || !params[1].equals(areaModule)) {
                LOGGER.error("EcontractSignPageBizServiceImpl#checkEncryptedParams, error, params: {}", JacksonUtil.writeAsJsonStr(params));
                throw new EcontractException(EcontractException.AUTHORITY_ERROR, "无权限查看");
            }
        } catch (EcontractException e) {
            throw e;
        } catch (Exception e) {
            throw new EcontractException(EcontractException.AUTHORITY_ERROR, "系统异常, 请稍后重试");
        }
    }

    private WebViewEnum getWebView(String param) throws EcontractException, TException {
        String dealVersion = decrypt3desc(param);
        EcontractSmsDealEntity dealEntity = smsDealEntityMapper.queryOneDealByVersion(dealVersion);
        if (dealEntity == null || !isEffectSmsDeal(dealEntity)) {
            return WebViewEnum.STAGE_UN_EFFECTIVE;
        }

        EcontractRecordEntity recordEntity = econtractRecordService.selectByPrimaryKey(dealEntity.getEcontractRecordId());
        if (recordEntity == null) {
            throw new EcontractException(EcontractErrorCode.BUSINESS_ERROR, "数据解密失败");
        }

        List<EcontractRecordBo> econtractRecordBoList;
        if (recordEntity.getRecordBatchId() != null && recordEntity.getRecordBatchId() > 0) {
            econtractRecordBoList = queryEcontractRecordByBatchId(recordEntity.getRecordBatchId());
        } else {
            econtractRecordBoList = Collections.singletonList(econtractRecordEntityToBo(recordEntity));
        }

        int successCount = 0;
        int signingCount = 0;
        int totalCount = econtractRecordBoList.size();
        for (EcontractRecordBo econtractRecordBo : econtractRecordBoList) {
            if (isEcontractRecordFail(econtractRecordBo)) {
                return WebViewEnum.STAGE_FAIL;
            }
            if (isEcontractRecordSuccess(econtractRecordBo)) {
                successCount++;
            }
            if (isEcontractRecordSigning(econtractRecordBo)) {
                signingCount++;
            }
        }
        if (successCount == totalCount) {
            return WebViewEnum.STAGE_SUCCESS;
        }
        if (signingCount == totalCount) {
            return WebViewEnum.STAGE_WAIT_SIGN;
        }
        return WebViewEnum.STAGE_WAIT_ASY_EXECUTE;
    }

    private List<SignEcontractPdfUrlBo> buildSignEcontractPdfUrl(List<CoopSignBasicInfo> coopSignBasicInfoList, List<PdfContentInfoBo> dineInServiceC2PdfInfo) throws EcontractException {
        List<SignEcontractPdfUrlBo> signEcontractInfoBoList = new ArrayList<>();

        // C1合同PDF
        if (CollectionUtils.isNotEmpty(coopSignBasicInfoList)) {
            for (CoopSignBasicInfo coopSignBasicInfo : coopSignBasicInfoList) {
                // PDF信息
                String contractName = coopSignBasicInfo.isIsNewSign() ? coopSignBasicInfo.getNewSignCoopInfo().getFrameCoop().getTFrame().getFrameName() : coopSignBasicInfo.getReSignCoopInfo().getCommonCoopName();
                String contractNumber = coopSignBasicInfo.isIsNewSign() ? coopSignBasicInfo.getNewSignCoopInfo().getFrameCoop().getTFrame().getFrameNum() : coopSignBasicInfo.getReSignCoopInfo().getCommonCoopNum();
                signEcontractInfoBoList.add(buildSignEcontractPdfUrlBo(coopSignBasicInfo, contractName, contractNumber));
            }
        }

        // C2合同PDF
        if (CollectionUtils.isNotEmpty(dineInServiceC2PdfInfo)) {
            dineInServiceC2PdfInfo.forEach(pdfContent -> signEcontractInfoBoList.add(buildC2SignEcontractPdfUrlBo(pdfContent)));
        }

        return signEcontractInfoBoList;
    }

    private SignEcontractCompleteInfoBo buildSignEcontractCompleteInfoBo() {
        String dcSignCompletePageInfoJson = MccConfig.getDcSignCompletePageInfo();
        DcSignCompletePageInfoBo dcSignCompletePageInfoBo = JSON.parseObject(dcSignCompletePageInfoJson, DcSignCompletePageInfoBo.class);
        SignEcontractCompleteInfoBo signEcontractCompleteInfoBo = new SignEcontractCompleteInfoBo();
        signEcontractCompleteInfoBo.setAdditionalInfo(dcSignCompletePageInfoBo.getAdditionalInfo());
        signEcontractCompleteInfoBo.setDetailUrlText(dcSignCompletePageInfoBo.getDetailUrlText());
        signEcontractCompleteInfoBo.setDetailUrl(dcSignCompletePageInfoBo.getDetailUrl());
        signEcontractCompleteInfoBo.setSignEcontractPdfUrl(new ArrayList<>());
        return signEcontractCompleteInfoBo;
    }

    private List<CoopSignBasicInfo> queryCoopInfoByRecordEntity(List<String> recordKeyList) throws TException, EcontractException {
        LOGGER.info("EcontractSignPageBizServiceImpl#queryCoopInfoByRecordEntity, recordEntity: {}", JacksonUtil.writeAsJsonStr(recordKeyList));
        // EcontractSignRecordBatchEntity recordBatchEntity = econtractSignRecordBatchService.selectByPrimaryKey(recordEntity.getRecordBatchId());
        List<DaoCanContractContext> dcContractList = wmPartnerCustomerContractThriftServiceAdapter.queryDaoCanContractInfo(recordKeyList);
        if (CollectionUtils.isEmpty(dcContractList)) {
            return Collections.emptyList();
        }

        List<CoopSignBasicReqInfo> coopSignBasicReqInfos = dcContractList.stream()
                .map(this::buildCoopSignBasicReqInfo)
                .filter(Objects::nonNull).collect(Collectors.toList());
        return commonCoopServiceAdapter.querySignCoopList(coopSignBasicReqInfos);
    }

    private CoopSignBasicReqInfo buildCoopSignBasicReqInfo(DaoCanContractContext daoCanContract) {
        if (StringUtils.isEmpty(daoCanContract.getContractProof())) {
            return null;
        }
        CoopSignBasicReqInfo coopSignBasicReqInfo = new CoopSignBasicReqInfo();
        coopSignBasicReqInfo.setCoopId(daoCanContract.getContractProof());
        coopSignBasicReqInfo.setIsNewSign(daoCanContract.isNewSignContract());
        coopSignBasicReqInfo.setCoopType(daoCanContract.getCoopType());
        return coopSignBasicReqInfo;
    }

    /**
     * 从PDF参数解析C2合同描述
     *
     * @param recordKey
     * @param pdfContentInfoBoList
     * @return
     */
    private String assemblyC2Desc(String recordKey, List<PdfContentInfoBo> pdfContentInfoBoList) {
        try {
            // PDF参数为空
            if (CollectionUtils.isEmpty(pdfContentInfoBoList)) {
                return "";
            }
            // C2的列表大小只有1
            PdfContentInfoBo pdfContentInfoBo = pdfContentInfoBoList.get(0);
            // 获取PDF参数
            Map<String, String> pdfMetaContent = pdfContentInfoBo.getPdfMetaContent();
            // 无乙方信息(即无代理商展示名称)
            if (StringUtils.isEmpty(pdfMetaContent.get("agentShowName")) || StringUtils.isEmpty(pdfMetaContent.get("agentShowId"))) {
                return "";
            }

            StringBuffer sb = new StringBuffer();
            sb.append("乙方：").append(pdfMetaContent.get("agentShowName")).append("(").append(pdfMetaContent.get("agentShowId")).append(")");

            return sb.toString();
        } catch (Exception e) {
            LOGGER.error("assemblyC2Desc 异常 recordKey:{}", recordKey, e);
        }
        return "";
    }

    private boolean isEcontractRecordSuccess(EcontractRecordBo recordEntity) {
        return TaskConstant.ECONTRACT_FINISH.equals(recordEntity.getEcontractStage())
                && EcontractRecordConstant.SUCCESS.equals(recordEntity.getEcontractState());
    }

    private boolean isEcontractRecordFail(EcontractRecordBo recordEntity) {
        return TaskConstant.ECONTRACT_FINISH.equals(recordEntity.getEcontractStage())
                && EcontractRecordConstant.FAIL.equals(recordEntity.getEcontractState());
    }

    private boolean isEcontractRecordSigning(EcontractRecordBo recordEntity) {
        return CONFIRM_STAMP_TASK_LIST.contains(recordEntity.getEcontractStage());
    }

}

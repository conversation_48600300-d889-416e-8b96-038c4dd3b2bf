package com.sankuai.meituan.waimai.econtract.server.service.mq;

import com.alibaba.fastjson.JSONObject;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.IMessageListener;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractAsyncRecordEntity;
import com.sankuai.meituan.waimai.econtract.server.exception.EcontractException;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.util.monitor.AsyncEstampDelayMonitor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2022-01-25 17:09
 * Email: <EMAIL>
 * Desc:
 */
@Service
@Slf4j
public class EsignAsyncDelayListener implements IMessageListener {

    @Autowired
    private AsyncEstampDelayMonitor asyncEstampDelayMonitor;

    @Override
    public ConsumeStatus recvMessage(MafkaMessage mafkaMessage, MessagetContext messagetContext) {
        try {
            EcontractAsyncRecordEntity asyncRecord = JSONObject.parseObject(mafkaMessage.getBody().toString(), EcontractAsyncRecordEntity.class);
            if (null == asyncRecord) {
                log.error("延迟消息为空，msg:{}", mafkaMessage.getBody().toString());
                throw new EcontractException(EcontractException.ASYNC_DELAY_ERROR, "异步处理流水信息为空");
            }
            asyncEstampDelayMonitor.checkDelayTask(asyncRecord);
        } catch (Exception e) {
            log.error("延迟消息消费失败，msg:{}", mafkaMessage.getBody().toString(), e);
            return ConsumeStatus.RECONSUME_LATER;
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}

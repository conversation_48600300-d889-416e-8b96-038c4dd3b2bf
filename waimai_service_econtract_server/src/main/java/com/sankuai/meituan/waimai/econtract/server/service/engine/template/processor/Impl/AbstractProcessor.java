package com.sankuai.meituan.waimai.econtract.server.service.engine.template.processor.Impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Preconditions;
import com.google.common.base.Predicates;
import com.meituan.mtrace.Tracer;
import com.meituan.mtrace.thread.pool.TraceExecutors;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.bizsso.thrift.CaptchaTService;
import com.sankuai.meituan.waimai.bizsso.thrift.Source;
import com.sankuai.meituan.waimai.bizsso.thrift.VerifyCaptcha;
import com.sankuai.meituan.waimai.bizsso.thrift.exception.BizssoServerException;
import com.sankuai.meituan.waimai.econtract.server.bo.EcontractBatchOpExtBo;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.bo.task.TaskContext;
import com.sankuai.meituan.waimai.econtract.server.bo.task.TaskNodeBo;
import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import com.sankuai.meituan.waimai.econtract.server.constants.EcontractRecordConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.MccConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractBatchOpMapper;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractSmsDealEntityMapper;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractBatchOpEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractTaskEntity;
import com.sankuai.meituan.waimai.econtract.server.exception.EcontractException;
import com.sankuai.meituan.waimai.econtract.server.service.PirateService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.*;
import com.sankuai.meituan.waimai.econtract.server.service.engine.notify.Notifier;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.Impl.SmsExecutor;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.TaskManager;
import com.sankuai.meituan.waimai.econtract.server.service.mq.TaskMsg;
import com.sankuai.meituan.waimai.econtract.server.service.mq.mafka.handler.MessageProducerHandler;
import com.sankuai.meituan.waimai.econtract.server.service.mq.mafka.producer.RecordStatusUpdateProducer;
import com.sankuai.meituan.waimai.econtract.server.utils.*;
import com.sankuai.meituan.waimai.econtrct.client.constants.CallbackConstant;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractBatchOpStatusConstant;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractBatchOpTypeConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractNotifyInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.SignerInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageInfoBo;
import com.sankuai.meituan.waimai.util.DaxiangUtilV2;
import joptsimple.internal.Strings;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import com.github.rholder.retry.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Hou
 * @date 2017/11/26
 * @time 下午12:16
 */
public class AbstractProcessor {

    private static final Logger LOGGER = LoggerFactory.getLogger(AbstractProcessor.class);

    private static final ScheduledExecutorService scheduledthreadpool = Executors.newScheduledThreadPool(10);

    private static final ExecutorService executorService = TraceExecutors
            .getTraceExecutorService(new ThreadPoolExecutor(10, 100, 0L, TimeUnit.MILLISECONDS,
                    new LinkedBlockingDeque<>(500), new ThreadPoolExecutor.CallerRunsPolicy()));

    private static final Integer MAX_REFRESH_BATCH_OP_INFO_RETRY_TIMES = 20;

    @Autowired
    private CaptchaTService.Iface captchaTService;
    @Autowired
    private EcontractRecordService econtractRecordService;
    @Autowired
    private EcontractTaskService econtractTaskService;
    @Autowired
    private TaskManager taskManager;
    @Autowired
    private EcontractCertifyService econtractCertifyService;
    @Autowired
    private Notifier notifier;
    @Autowired
    private EcontractRealNameAuthService realNameAuthService;
    @Autowired
    private TairService tairService;
    @Autowired
    private EcontractSmsDealEntityMapper smsDealEntityMapper;
    @Autowired
    private SmsExecutor smsExecutor;
    @Resource
    protected MessageProducerHandler messageProducerHandler;
    @Autowired
    private EcontractBatchOpMapper econtractBatchOpMapper;
    @Autowired
    private RecordStatusUpdateProducer recordStatusUpdateProducer;
    @Autowired
    private PirateService pirateService;


    public void applyCheck(EcontractContext econtractContext, EcontractBo econtractBo) {
        //TODO:调用前校验检查
    }


    public int createTask(EcontractContext context, int parentTaskId, String stageName) {
        EcontractTaskEntity taskEntity = new EcontractTaskEntity();
        taskEntity.setEcontractUserId(context.getEcontractUserEntity().getId());
        taskEntity.setEcontractRecordId(context.getEcontractRecordEntity().getId());
        taskEntity.setParentTaskId(parentTaskId);
        taskEntity.setTaskType(stageName);
        taskEntity.setTaskState(TaskConstant.TASK_INIT);
        taskEntity.setValid(TaskConstant.VALID);
        taskEntity.setUtime(TimeUtils.getUTime());
        taskEntity.setCtime(TimeUtils.getCTime());
        taskEntity.setEcontractTaskContext(JSON.toJSONString(context));
        econtractTaskService.insertSelective(taskEntity);
        return taskEntity.getId();
    }

    public int createTask(EcontractTaskEntity taskEntity) {
        econtractTaskService.insertSelective(taskEntity);
        return taskEntity.getId();
    }


    /**
     * 创建通知上下文
     */
    public TaskContext createNotifierTaskContext(EcontractContext context, String stageName, String taskState) {
        TaskContext taskContext = new TaskContext();
        taskContext.setEcontractStage(stageName);
        taskContext.setState(taskState);
        return taskContext;
    }

    /**
     * 发送通知任务
     */
    public void doNotifier(EcontractContext context, TaskContext taskContext) {
        doNotifier(context, taskContext, StringUtils.EMPTY);
    }

    /**
     * 发送通知任务
     */
    public void doNotifier(EcontractContext context, TaskContext taskContext, String executeName) {
        LOGGER.info("AbstractProcessor#doNotifier recordKey start: {}", context.getEcontractRecordEntity().getRecordKey());
        // 封装通知参数
        EcontractNotifyInfoBo notifyInfoBo = new EcontractNotifyInfoBo();
        notifyInfoBo.setNotifyQueueId(context.getEcontractUserEntity().getId());
        notifyInfoBo.setNotifyUrl(context.getCallBackUrl());
        notifyInfoBo.setRecordKey(context.getEcontractRecordEntity().getRecordKey());
        notifyInfoBo.setBizKey(context.getRecordBizKey());
        notifyInfoBo.setStageName(taskContext.getEcontractStage());
        notifyInfoBo.setExecuteName(executeName);
        notifyInfoBo.setState(taskContext.getState());
        notifyInfoBo.setEcontractType(context.getEcontractType());
        JSONObject extMsg = new JSONObject();
        if (!TaskConstant.TASK_FAIL.equals(context.getTaskContext().getState())) {
            notifyInfoBo.setCode(EcontractException.SUCCESS_CODE);
            notifyInfoBo.setMsg(TaskConstant.TASK_SUCCESS);
        } else {
            notifyInfoBo.setCode(context.getTaskContext().getErrorCode());
            notifyInfoBo.setMsg(context.getTaskContext().getFailMessage());
        }
        if (null != context.getTaskContext().getExecutorResult()) {
            Map<String, String> executorResult = context.getTaskContext().getExecutorResult();
            notifyInfoBo.setDownLoadUrl(executorResult.get(TaskConstant.PDF_URL));

            if (StringUtils.isNotEmpty(executorResult.get(TaskConstant.PDF_URL))) {
                extMsg.put(TaskConstant.PDF_URL, executorResult.get(TaskConstant.PDF_URL));
            }
            if (StringUtils.isNotEmpty(executorResult.get(TaskConstant.CUSTOMER_ID))) {
                extMsg.put(TaskConstant.CUSTOMER_ID, executorResult.get(TaskConstant.CUSTOMER_ID));
            }
            JsonUtil.mergeMap2Json(extMsg, executorResult);
            LOGGER.info("set notifyInfoBo downloadUrl taskContext : {}", notifyInfoBo.getDownLoadUrl());
        }
        if (null != context.getEcontractRecordEntity() && StringUtils
            .isNotEmpty(context.getEcontractRecordEntity().getSaveUrl())) {
            notifyInfoBo.setDownLoadUrl(context.getEcontractRecordEntity().getSaveUrl());
            LOGGER.info("set notifyInfoBo downloadUrl recordEntity : {}", notifyInfoBo.getDownLoadUrl());
        }

        notifyInfoBo.setExtMsg(JSON.toJSONString(extMsg));
        LOGGER.info("set notifyInfoBo downloadUrl : {}", notifyInfoBo.getDownLoadUrl());

        // 进行通知
        if (context.getIsCallBackByMQ()) {
            boolean isSend = recordStatusUpdateProducer.sendMessage(notifyInfoBo);
            if (!isSend) {//发送小时失败，发送告警
                pushByMQFailMsgSend(context.getEcontractRecordEntity().getRecordKey());
            }
        } else {
            notifier.submitNotify(notifyInfoBo);
        }

        // 对于签约成功且需要感知上游状态的记录
        if (context.getIsWaitForUpstream() && TaskConstant.ECONTRACT_FINISH.equals(notifyInfoBo.getStageName())
                && TaskConstant.TASK_SUCCESS.equals(notifyInfoBo.getState())) {
            queryUpstreamStatus(context, MccConfig.queryUpstreamStatusDelayTime());
        }
        LOGGER.info("AbstractProcessor#doNotifier recordKey end: {}", context.getEcontractRecordEntity().getRecordKey());
    }


    public void cancelEcontractNotify(EcontractContext context, String message) {
        LOGGER.info("AbstractProcessor#cancelEcontractNotify recordKey: {},message:{}", context.getEcontractRecordEntity().getRecordKey(),message);
        TaskContext taskContext = context.getTaskContext() == null ? new TaskContext() : context.getTaskContext();
        taskContext.setState(TaskConstant.TASK_FAIL);
        taskContext.setEcontractStage(TaskConstant.ECONTRACT_FINISH);

        if (TaskConstant.H5_CANCEL.equals(message)) {
            taskContext.setErrorCode(EcontractException.ECONTRACT_H5_CANCEL_ERROR);
        } else if (TaskConstant.SERVER_CANCEL.equals(message)) {
            taskContext.setErrorCode(EcontractException.ECONTRACT_SERVER_CANCEL_ERROR);
        }
        taskContext.setFailMessage(message);
        context.setTaskContext(taskContext);
        doNotifier(context, taskContext);
    }

    /**
     * 由于任务失败取消合同
     */
    public void dropTemplateByTask(EcontractContext context) {
        LOGGER.info("econtract record : {} dropTemplateByTask", context.getEcontractRecordEntity().getRecordKey());
        //更新老记录状态
        EcontractRecordEntity recordEntity =
            econtractRecordService.queryRecordByRecordKey(context.getEcontractRecordEntity().getRecordKey());
        recordEntity.setEcontractStage(TaskConstant.ECONTRACT_FINISH);
        recordEntity.setEcontractState(EcontractRecordConstant.FAIL);
        recordEntity.setFailMessage(context.getTaskContext().getFailMessage());
        recordEntity.setUtime(TimeUtils.getUTime());
        econtractRecordService.updateByPrimaryKeySelective(recordEntity);

        //未执行的任务失败，父任务失败，当前任务失败
        List<EcontractTaskEntity> taskEntityList = econtractTaskService.qeuryTaskByRecordId(recordEntity.getId());
        taskEntityList.stream().forEach(taskEntity -> {
            if (!TaskConstant.TASK_SUCCESS.equals(taskEntity.getTaskState())) {
                taskEntity.setTaskState(TaskConstant.TASK_FAIL);
            }

            if (context.getTaskContext().getTaskId() == taskEntity.getId() || taskEntity.getParentTaskId() == 0) {
                taskEntity.setFailMessage(context.getTaskContext().getFailMessage()+",traceId:"+ Tracer.id());
            } else if (!TaskConstant.TASK_SUCCESS.equals(taskEntity.getTaskState())) {
                taskEntity.setFailMessage(TaskConstant.NOT_RUNNING+",traceId:"+ Tracer.id());
            }

            taskEntity.setUtime(TimeUtils.getUTime());
            econtractTaskService.updateByPrimaryKeySelective(taskEntity);
        });

        // 更新批次表状态
        doRefreshBatchOpInfo(recordEntity);

        // 通知
        doNotifier(context, context.getTaskContext());
    }


    /**
     * 由于回调信息为失败取消的合同
     */

    public void dropTemplateByCallBack(EcontractContext context, String msg) {
        LOGGER.info("AbstractProcessor#dropTemplateByCallBack recordKey: {}，msg:{}", context.getEcontractRecordEntity().getRecordKey(),msg);
        //更新老记录状态
        EcontractRecordEntity recordEntity =
            econtractRecordService.queryRecordByRecordKey(context.getEcontractRecordEntity().getRecordKey());

        recordEntity.setEcontractStage(TaskConstant.ECONTRACT_FINISH);
        recordEntity.setEcontractState(EcontractRecordConstant.FAIL);
        recordEntity.setFailMessage(msg);
        recordEntity.setUtime(TimeUtils.getUTime());
        econtractRecordService.updateByPrimaryKeySelective(recordEntity);

        // 未执行的任务失败，父任务失败，当前任务失败
        failTaskListUnExecute(recordEntity.getId(), msg);

        // 更新批次表状态
        doRefreshBatchOpInfo(recordEntity);

        // 通知
        doNotifier(context, context.getTaskContext());
    }

    public void cancelSignEContract(String recordKey, String msg) {
        LOGGER.info("AbstractProcessor#recordKey recordKey: {}，msg:{}", recordKey,msg);
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(recordKey);
        if (TaskConstant.ECONTRACT_FINISH.equals(recordEntity.getEcontractStage())) {
            LOGGER.info("fail to cancel , record key is : {} , record stage is :{}", recordEntity.getRecordKey(), recordEntity.getEcontractStage());
            EcontractException.CANCEL_ECONTRACT_EXCEPTION.newInstance("取消签约失败，当前状态无法取消");
        }
        EcontractContext context = JSON.parseObject(recordEntity.getEcontractRecordContext(), EcontractContext.class);
        LOGGER.info("context = {}", JSON.toJSONString(context));

        recordEntity.setEcontractStage(TaskConstant.ECONTRACT_FINISH);
        recordEntity.setEcontractState(EcontractRecordConstant.FAIL);
        recordEntity.setFailMessage(msg);
        recordEntity.setUtime(TimeUtils.getUTime());
        econtractRecordService.updateByPrimaryKeySelective(recordEntity);

        failTaskListUnExecute(recordEntity.getId(), msg);

        // 更新批次表状态
        doRefreshBatchOpInfo(recordEntity);

        // 通知
        cancelEcontractNotify(context, msg);
    }


    public void failTask(EcontractContext context) {
        if (context.getFailFast() != null && context.getFailFast()) {
            dropTemplateByTask(context);
        }
        //参数异常直接fail
        if (context.getTaskContext().getErrorCode() == EcontractException.PARAM_ERROR) {
            dropTemplateByTask(context);
        }
        //重试超过次数直接fail
        if (context.getTaskContext().getRetryTimes() >= 3) {
            dropTemplateByTask(context);
        } else {
            EcontractTaskEntity taskEntity =
                econtractTaskService.selectByPrimaryKey(context.getTaskContext().getTaskId());
            taskEntity.setRetryTimes(taskEntity.getRetryTimes() + 1);
            taskEntity.setUtime(TimeUtils.getUTime());
            econtractTaskService.updateByPrimaryKeySelective(taskEntity);

            context.getTaskContext().setRetryTimes(context.getTaskContext().getRetryTimes() + 1);
            LOGGER.info("retry task , recordKey is : {},task type is :{},fail message is :{}, retry time is :{}",
                        context.getEcontractRecordEntity().getRecordKey(), taskEntity.getTaskType(),
                        taskEntity.getFailMessage(), taskEntity.getRetryTimes());

        }
    }

    /**
     * 补全请求参数
     */
    public void replaceEcontractBo(EcontractContext context, Map<String, String> paramMap) {
        LOGGER.info("AbstractProcessor#replaceEcontractBo recordKey: {}", context.getEcontractRecordEntity().getRecordKey());
        EcontractRecordEntity
            recordEntity =
            econtractRecordService.queryRecordByRecordKey(context.getEcontractRecordEntity().getRecordKey());
        try {
            if (null != paramMap && StringUtils.isNotEmpty(paramMap.get(CallbackConstant.ECONTRACTBO_REPLACE))) {
                EcontractBo econtractBo =
                    JSON.parseObject(paramMap.get(CallbackConstant.ECONTRACTBO_REPLACE), EcontractBo.class);
                List<StageInfoBo> oldStageInfoBoList = context.getStageInfoBoList();
                List<StageInfoBo> newStageInfoBoList = econtractBo.getStageInfoBoList();
                for (int i = 0; i < oldStageInfoBoList.size(); i++) {
                    for (int j = 0; j < newStageInfoBoList.size(); j++) {
                        if (oldStageInfoBoList.get(i).getStageName().equals(newStageInfoBoList.get(j).getStageName())) {
                            LOGGER.info("replace stage , old stage is : {} , new stage is :{}",
                                        JSON.toJSONString(oldStageInfoBoList.get(i)),
                                        JSON.toJSONString(newStageInfoBoList.get(j)));
                            oldStageInfoBoList.set(i, newStageInfoBoList.get(j));
                            break;
                        }
                    }
                }

                context.getStageInfoBoList().addAll(econtractBo.getStageInfoBoList());
                recordEntity.setEcontractRecordContext(JSON.toJSONString(context));
                LOGGER.info("replaceEcontractBo is  {}", JSON.toJSONString(context.getStageInfoBoList()));
                econtractRecordService.updateByPrimaryKeySelective(recordEntity);
            }
        } catch (Exception e) {
            LOGGER.error("fail to appendEcontractBo ", e);
        }
    }


    public void failTaskListUnExecute(Integer recordId, String msg) {
        LOGGER.info("AbstractProcessor#failTaskListUnExecute recordId: {},msg:{}", recordId,msg);
        //未执行的任务失败，父任务失败，当前任务失败
        List<EcontractTaskEntity> taskEntityList = econtractTaskService.qeuryTaskByRecordId(recordId);
        taskEntityList.stream().forEach(taskEntity -> {
            if (!TaskConstant.TASK_SUCCESS.equals(taskEntity.getTaskState())) {
                taskEntity.setTaskState(TaskConstant.TASK_FAIL);
                taskEntity.setFailMessage(msg+",traceId:"+ Tracer.id());
            }
            taskEntity.setUtime(TimeUtils.getUTime());
            econtractTaskService.updateByPrimaryKeySelective(taskEntity);
        });
    }


    public boolean toVerify(String turlingId, SignerInfoBo signerInfoBo, String code)
        throws BizssoServerException, TException {
        LOGGER.info("AbstractProcessor#toVerify turlingId: {},code:{}", turlingId,code);
        if (StringUtils.isEmpty(turlingId)) {
            return false;
        } else {
            //测试后门,发给第一个手机号
            String phone = ConfigUtilAdapter.getString(MccConstant.TEST_PHONE_CAP);
            if (StringUtils.isNotEmpty(phone) && (StringUtils.isNotEmpty(phone.split(",")[0]))) {
                phone = phone.split(",")[0];
            } else {
                phone = signerInfoBo.getPhone();
            }
            VerifyCaptcha verifyCaptcha =
                captchaTService.verifyPhoneCaptcha(turlingId, phone, code, false, Source.WAIMAI_M_CONTRACT);
            if (verifyCaptcha.isVerify()) {
                return true;
            } else {
                return false;
            }
        }
    }

    /**
     * 当前执行节点是否全部执行结束
     */
    public boolean executeTaskFinish(TaskNodeBo currentTaskNode) {
        //校验当前是不是所有节点都处理结束
        List<EcontractTaskEntity> childTaskList =
            econtractTaskService.queryTaskByParentIdMaster(currentTaskNode.getTaskId());
        LOGGER.info("childTaskList = {}", JSON.toJSONString(childTaskList));
        List<EcontractTaskEntity> unFinishList =
            childTaskList.stream().filter(
                childTaskEntity -> (!TaskConstant.TASK_SUCCESS.equals(childTaskEntity.getTaskState()))
                                   && !TaskConstant.TASK_FAIL.equals(childTaskEntity.getTaskState()))
                .collect(Collectors.toList());
        LOGGER.info("unFinishList = {}", JSON.toJSONString(unFinishList));
        return CollectionUtils.isEmpty(unFinishList);
    }

    /**
     * 生成record中的saveUrl
     */
    public String parseUrl(EcontractRecordEntity recordEntity, TaskContext taskContext, EcontractTaskEntity executeTask) {
        String storeSaveUrl = recordEntity.getSaveUrl();
        LOGGER.info("AbstractProcessor#parseUrl recordKey: {} storeSaveUrl:{}", recordEntity.getRecordKey(),storeSaveUrl);
        JSONObject jo = StringUtils.isBlank(storeSaveUrl) ? new JSONObject() : JSONObject.parseObject(storeSaveUrl);
        jo.put(executeTask.getTaskType(), taskContext.getExecutorResult().get(TaskConstant.PDF_URL));
        return jo.toJSONString();
    }

    public String getShortLink(String recordKey) {
        LOGGER.info("AbstractProcessor#getShortLink recordKey: {}", recordKey);
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(recordKey);
        if (recordEntity == null) {
            EcontractException.SMS_RETRY_NAME_EXCEPTION.newInstance("获取短链接失败,请取消后重试");
        }
        if (recordEntity.getRecordBatchId() != null && recordEntity.getRecordBatchId() > 0) {
            List<EcontractRecordEntity> entityList = econtractRecordService.queryColdAndHotEcontractRecordByBatchId(recordEntity.getRecordBatchId());
            List<String> shortLinkList = new ArrayList<>(entityList.size());
            entityList.forEach(entity -> shortLinkList.add(getEcontractShortLink(entity)));
            LOGGER.info("AbstractProcessor#getShortLink, shortLinkList: {}", JacksonUtil.writeAsJsonStr(shortLinkList));
            return shortLinkList.stream().filter(StringUtils::isNotEmpty).findFirst().orElse(Strings.EMPTY);
        } else {
            return getEcontractShortLink(recordEntity);
        }
    }

    private String getEcontractShortLink(EcontractRecordEntity recordEntity) {
        EcontractTaskEntity taskEntity;
        if (TaskConstant.REAL_NAME_AUTH_A.equals(recordEntity.getEcontractStage()) ||
                TaskConstant.CONFIRM_STAMP_A.equals(recordEntity.getEcontractStage())) {
            taskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.SMS_SIGNER_A);
            return doGetShortLink(recordEntity,taskEntity.getId(),TaskConstant.REAL_NAME_AUTH_A);

        } else if (TaskConstant.REAL_NAME_AUTH_B.equals(recordEntity.getEcontractStage()) ||
                TaskConstant.CONFIRM_STAMP_B.equals(recordEntity.getEcontractStage())) {
            taskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.SMS_SIGNER_B);

            return doGetShortLink(recordEntity,taskEntity.getId(),TaskConstant.REAL_NAME_AUTH_B);

        } else if (TaskConstant.REAL_NAME_AUTH_C.equals(recordEntity.getEcontractStage()) ||
                TaskConstant.CONFIRM_STAMP_C.equals(recordEntity.getEcontractStage())) {
            taskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.SMS_SIGNER_C);

            return doGetShortLink(recordEntity,taskEntity.getId(),TaskConstant.REAL_NAME_AUTH_C);

        } else if (TaskConstant.REAL_NAME_AUTH_D.equals(recordEntity.getEcontractStage()) ||
                TaskConstant.CONFIRM_STAMP_D.equals(recordEntity.getEcontractStage())) {
            taskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.SMS_SIGNER_D);

            return doGetShortLink(recordEntity,taskEntity.getId(),TaskConstant.REAL_NAME_AUTH_D);

        } else if (TaskConstant.REAL_NAME_AUTH_E.equals(recordEntity.getEcontractStage()) ||
                TaskConstant.CONFIRM_STAMP_E.equals(recordEntity.getEcontractStage())) {
            taskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.SMS_SIGNER_E);

            return doGetShortLink(recordEntity,taskEntity.getId(),TaskConstant.REAL_NAME_AUTH_E);
        } else if (TaskConstant.REAL_NAME_AUTH_F.equals(recordEntity.getEcontractStage()) ||
                TaskConstant.CONFIRM_STAMP_F.equals(recordEntity.getEcontractStage())) {
            taskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.SMS_SIGNER_F);

            return doGetShortLink(recordEntity,taskEntity.getId(),TaskConstant.REAL_NAME_AUTH_F);
        } else if (TaskConstant.REAL_NAME_AUTH_G.equals(recordEntity.getEcontractStage()) ||
                TaskConstant.CONFIRM_STAMP_G.equals(recordEntity.getEcontractStage())) {
            taskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.SMS_SIGNER_G);

            return doGetShortLink(recordEntity,taskEntity.getId(),TaskConstant.REAL_NAME_AUTH_G);
        } else if (TaskConstant.REAL_NAME_AUTH_H.equals(recordEntity.getEcontractStage()) ||
                TaskConstant.CONFIRM_STAMP_H.equals(recordEntity.getEcontractStage())) {
            taskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.SMS_SIGNER_H);

            return doGetShortLink(recordEntity,taskEntity.getId(),TaskConstant.REAL_NAME_AUTH_H);
        } else {
            EcontractException.SMS_RETRY_NAME_EXCEPTION.newInstance("合同状态非签约中，无法查看签约链接");
            return Strings.EMPTY;
        }
    }

    /**
     * 当签约任务达到终态时更新对应批次任务信息（异步操作）
     * @param recordEntity
     */
    public void doRefreshBatchOpInfo(EcontractRecordEntity recordEntity) {
        executorService.execute(() -> {
            try {
                LOGGER.info("doRefreshBatchOpInfo#更新批次任务信息 recordKey:{}", recordEntity.getRecordKey());
                Long batchOpId = recordEntity.getBatchOpId();
                // 无对应的批次信息(即非批量操作的任务)直接返回
                if (batchOpId == null || batchOpId <= 0) {
                    return;
                }
                int updateCount = 0;
                for (int retryTime = MAX_REFRESH_BATCH_OP_INFO_RETRY_TIMES; retryTime > 0; retryTime--) {
                    if (updateCount > 0) {
                        return;
                    }
                    EcontractBatchOpEntity batchOpEntity = econtractBatchOpMapper.selectByPrimaryKey(batchOpId);

                    // 查询不到批次信息，或者该批次不是运行中状态，直接返回
                    if (batchOpEntity == null || batchOpEntity.getStatus() != EcontractBatchOpStatusConstant.RUNING) {
                        return;
                    }
                    EcontractBatchOpExtBo extBo = JSON.parseObject(batchOpEntity.getExt(), EcontractBatchOpExtBo.class);

                    // 防止重复调用
                    if (extBo.getSuccessRecordKeyList().contains(recordEntity.getRecordKey())
                            || extBo.getFailRecordKeyList().contains(recordEntity.getRecordKey())) {
                        return;
                    }

                    // 更新ext信息
                    if (batchOpEntity.getOpType() == EcontractBatchOpTypeConstant.CONFIRM) {// 该批次是确认操作
                        if (EcontractRecordConstant.SUCCESS.equals(recordEntity.getEcontractState())
                                &&  TaskConstant.ECONTRACT_FINISH.equals(recordEntity.getEcontractStage())) {// 正常流程
                            extBo.getSuccessRecordKeyList().add(recordEntity.getRecordKey());
                        } else {// 异常流程
                            extBo.getFailRecordKeyList().add(recordEntity.getRecordKey());
                        }
                    } else if (batchOpEntity.getOpType() == EcontractBatchOpTypeConstant.CANCEL) {// 该批次是取消操作
                        if (EcontractRecordConstant.FAIL.equals(recordEntity.getEcontractState())
                                && TaskConstant.ECONTRACT_FINISH.equals(recordEntity.getEcontractStage())
                                && (TaskConstant.H5_CANCEL.equals(recordEntity.getFailMessage())
                                || TaskConstant.SERVER_CANCEL.equals(recordEntity.getFailMessage()))) {
                            extBo.getSuccessRecordKeyList().add(recordEntity.getRecordKey());
                        } else {
                            extBo.getFailRecordKeyList().add(recordEntity.getRecordKey());
                        }
                    }
                    batchOpEntity.setExt(JSON.toJSONString(extBo));

                    // 判断是否所有record都到达终态
                    Integer successNum = extBo.getSuccessRecordKeyList().size();
                    Integer failNum = extBo.getFailRecordKeyList().size();
                    Integer doneNum = successNum + failNum;
                    if (extBo.getTotalNum() == doneNum) {
                        if (extBo.getTotalNum() == successNum) {// 全部成功
                            batchOpEntity.setStatus(EcontractBatchOpStatusConstant.ALL_SUCCESS);
                        } else if (extBo.getTotalNum() == failNum) {// 全部失败
                            batchOpEntity.setStatus(EcontractBatchOpStatusConstant.ALL_FAIL);
                        } else {// 部分成功部分失败
                            batchOpEntity.setStatus(EcontractBatchOpStatusConstant.PART_SUCCESS);
                        }
                    }
                    LOGGER.info("doRefreshBatchOpInfo#更新批次任务信息 batchOpEntity:{}", JSON.toJSONString(batchOpEntity));

                    // 更新批次信息
                    updateCount = econtractBatchOpMapper.updateOptimisticLock(batchOpEntity);
                }
            } catch (Exception e) {
                LOGGER.error("doRefreshBatchOpInfo#更新批次任务信息 recordKey:{}", recordEntity.getRecordKey(), e);
            }
        });
    }

    private String doGetShortLink(EcontractRecordEntity recordEntity,Integer taskId,String stageKeyName) {
        EcontractContext context = JSON.parseObject(recordEntity.getEcontractRecordContext(), EcontractContext.class);
        TaskContext taskContext = new TaskContext();
        taskContext.setTaskId(taskId);
        taskContext.setStageInfoBo(ContextUtils.getStageInfoBoFromList(context, stageKeyName));
        context.setTaskContext(taskContext);
        String shortLink = "";
        try {
            shortLink = smsExecutor.getShortLink(context);
        } catch (Exception e) {
            LOGGER.error("获取短信链接失败, recordKey:" + context.getEcontractRecordEntity().getRecordKey(), e);
            EcontractException.SMS_RETRY_NAME_EXCEPTION.newInstance("获取短信链接失败");
            return Strings.EMPTY;
        }
        LOGGER.info("AbstractProcessor#doGetShortLink recordKey: {},shortLink:{}", recordEntity.getRecordKey(),shortLink);
        return shortLink;
    }

    protected TaskMsg handleMessage(EcontractContext context, boolean isMafka){
        return messageProducerHandler.handler(context,isMafka);
    }

    private void queryUpstreamStatus(final EcontractContext context, final int defaultTime) {
        try {
            Preconditions.checkArgument(defaultTime > 0);
            scheduledthreadpool.schedule(new Runnable() {
                @Override
                public void run() {
                    //允许反查 && 上游状态未同步成功 && 最大延迟时间
                    if (MccConfig.isQueryUpstreamStatus()) {
                        boolean isQuerySuccess = pirateService.queryUpstreamStatus(context.getEcontractRecordEntity().getRecordKey());
                        if (isQuerySuccess) {//反查上游状态成功，则直接返回
                            return;
                        }
                        if (defaultTime > MccConfig.queryUpstreamStatusMaxDelayTime()) {// 反查延迟时间大于最大时间，则发送告警且返回
                            upstreamStatusTimeOutMsgSend(context.getEcontractRecordEntity().getRecordKey());
                            return;
                        }
                        queryUpstreamStatus(context, defaultTime * 2);
                    }
                }
            }, defaultTime, TimeUnit.SECONDS);
        } catch (Exception e) {
            LOGGER.error("根据recordKey获取上游状态异常 recordKey:{}" + context.getEcontractRecordEntity().getRecordKey(), e);
        }
    }

    /**
     * 报警推送
     *
     * @param
     */
    private void upstreamStatusTimeOutMsgSend(String recordKey) {
        try {
            //告警接收人获取
            String receivers = com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter.getString("upstream_status_timeout_receiver",
                    "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>");
            //告警消息拼接+发送
            StringBuilder sb = new StringBuilder("【获取上游状态超时告警】recordKey:");
            sb.append(recordKey);
            DaxiangUtilV2.push(sb.toString(), receivers.split(","));
            LOGGER.info("upstreamStatusTimeOutMsgSend#send success，receivers:{}, msg:{}", receivers, sb.toString());
        } catch (Exception e) {
            LOGGER.error("upstreamStatusTimeOutMsgSend#send error recordKey:{}", recordKey);
        }
    }

    /**
     * 报警推送
     *
     * @param
     */
    private void pushByMQFailMsgSend(String recordKey) {
        try {
            //告警接收人获取
            String receivers = com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter.getString("upstream_status_timeout_receiver",
                    "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>");
            //告警消息拼接+发送
            String env = ServiceEnvUtils.getEnv();
            StringBuilder sb = new StringBuilder(env);
            sb.append("【电子合同状态变更发送MQ消息失败】recordKey:");
            sb.append(recordKey);
            DaxiangUtilV2.push(sb.toString(), receivers.split(","));
            LOGGER.info("pushByMQFailMsgSend#send success，receivers:{}, msg:{}", receivers, sb.toString());
        } catch (Exception e) {
            LOGGER.error("pushByMQFailMsgSend#send error recordKey:{}", recordKey);
        }
    }
}

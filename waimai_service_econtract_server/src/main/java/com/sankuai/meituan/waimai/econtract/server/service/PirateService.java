package com.sankuai.meituan.waimai.econtract.server.service;


import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordEntity;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractRecordService;
import com.sankuai.meituan.waimai.econtrct.client.constants.UpstreamStatusEnum;
import com.sankuai.meituan.waimai.util.DaxiangUtilV2;
import org.apache.commons.lang.StringEscapeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.dianping.csc.pirate.client.executor.Response;
import com.dianping.csc.pirate.client.facade.PirateEngine;

/**
 * 海盗中间件使用
 * Created by lixuepeng on 2022/02/21.
 */
@Service
public class PirateService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PirateService.class);

    @Autowired
    private EcontractRecordService econtractRecordService;

    /**
     * 该方法为一个兜底方法
     * 根据recordKey反查上游状态，返回是否上游状态已同步成功
     * @param recordKey
     * @return
     */
    public boolean queryUpstreamStatus(String recordKey) {
        LOGGER.info("PirateService.queryUpstreamStatus recordKey={}", recordKey);
        try {
            EcontractRecordEntity entity = econtractRecordService.queryRecordByRecordKey(recordKey);
            // 如果对应record状态已更新为成功或者失败
            if (entity.getUpstreamStatus() != UpstreamStatusEnum.INIT.getCode()) {
                return true;
            }
            //默认入参
            String defaultParam = "{\"recordKey\":\"" + recordKey + "\"}";
            EcontractContext context = JSON.parseObject(entity.getEcontractRecordContext(), EcontractContext.class);
            Response response = PirateEngine.invoke(context.getCallBackDsl(), defaultParam);
            LOGGER.info("PirateService.queryUpstreamStatus recordKey={}, response:{}", recordKey, JSON.toJSONString(response));
            if (response.getCode() != 0) {
                LOGGER.error("PirateService.queryUpstreamStatus pirate.code!=0 recordKey={}", recordKey);
                Map<Object, Throwable> errors = response.getErrors();
                for (Map.Entry<Object, Throwable> entry : errors.entrySet()) {
                    LOGGER.error("PirateService.queryUpstreamStatus recordKey={}, Throwable={}", recordKey, entry.getValue());
                }
            } else {
                String data = StringEscapeUtils.unescapeJavaScript(response.getData());
                JSONObject jsonData = JSONObject.parseObject(data);
                Integer upstreamStatus = jsonData.getIntValue("upstreamStatus");
                if (upstreamStatus != UpstreamStatusEnum.INIT.getCode()) {
                    upstreamStatusNotSyncMsgSend(recordKey);
                    econtractRecordService.updateUpstreamStatusByRecordKey(recordKey, upstreamStatus);
                    return true;
                }
            }
        } catch (Exception e) {
            LOGGER.error("PirateService.receiveAwards error recordKey={}", recordKey, e);
        }
        return false;
    }

    /**
     * 报警推送
     *
     * @param
     */
    private void upstreamStatusNotSyncMsgSend(String recordKey) {
        try {
            //告警接收人获取
            String receivers = com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter.getString("upstream_status_timeout_receiver",
                    "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>");
            //告警消息拼接+发送
            StringBuilder sb = new StringBuilder("【上游状态未主动更新】recordKey:");
            sb.append(recordKey);
            DaxiangUtilV2.push(sb.toString(), receivers.split(","));
            LOGGER.info("upstreamStatusNotSyncMsgSend#send success，receivers:{}, msg:{}", receivers, sb.toString());
        } catch (Exception e) {
            LOGGER.error("upstreamStatusNotSyncMsgSend#send error recordKey:{}", recordKey);
        }
    }
}

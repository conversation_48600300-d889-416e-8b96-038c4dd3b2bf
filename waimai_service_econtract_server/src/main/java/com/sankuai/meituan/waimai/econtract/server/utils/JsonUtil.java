package com.sankuai.meituan.waimai.econtract.server.utils;

import com.alibaba.fastjson.JSONObject;

import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

public class JsonUtil {

    private final static Logger LOGGER = LoggerFactory.getLogger(JsonUtil.class);

    public static boolean isJsonFormat(String str) {
        try {
            JSONObject jo = JSONObject.parseObject(str);
            return true;
        } catch (Exception e) {
            LOGGER.warn("转JSON失败 str:{}", str, e);
            return false;
        }
    }

    public static JSONObject mergeMap2Json(JSONObject jo, Map<String, String> map) {
        if (MapUtils.isEmpty(map)) {
            return new JSONObject();
        }

        for (Map.Entry<String, String> entry:map.entrySet()) {
            jo.put(entry.getKey(), entry.getValue());
        }
        return jo;
    }



}

package com.sankuai.meituan.waimai.econtract.server.service.mq.mafka.producer;

import com.alibaba.fastjson.JSONObject;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.producer.ProducerResult;
import com.sankuai.meituan.waimai.econtract.server.constants.MqConstant;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractMetricService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.Impl.EcontractBigMessageServiceImpl;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractNotifyInfoBo;
import java.util.Properties;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 电子合同状态更新
 * <AUTHOR>
 */
@Slf4j
@Component
public class RecordStatusUpdateProducer extends AbstractMafkaProducer {

    @Resource
    private EcontractBigMessageServiceImpl eContractBigMessageService;

    @Autowired
    private EcontractMetricService econtractMetricService;

    @Override
    protected void doSetUpProducer() throws Exception {
        Properties properties = new Properties();
        // 设置业务所在BG的namespace
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, MqConstant.BG_NAME_SPACE);
        // 设置生产者appkey
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, MqConstant.APP_KEY);
        // 创建topic对应的producer对象（注意每次build调用会产生一个新的实例）
        producer = MafkaClient.buildProduceFactory(properties, MqConstant.MAFKA_RECORD_STATUS_UPDATE_TOPIC, false);
    }

    public boolean sendMessage(EcontractNotifyInfoBo notifyInfoBo) {
        try {
            log.info("RecordStatusUpdateProducer.sendMessage notifyInfoBo={},", JSONObject.toJSONString(notifyInfoBo));
            ProducerResult result = producer.sendMessage(JSONObject.toJSONString(notifyInfoBo), notifyInfoBo.getRecordKey());
            log.info("RecordStatusUpdateProducer！MsgId = {},recordKey:{}",result.getMessageID(),notifyInfoBo.getRecordKey());
            econtractMetricService.metricCallbackCount(notifyInfoBo, "MQ");
            return true;
        } catch (Exception e) {
            log.error("RecordStatusUpdateProducer.sendMessage exception, recordKey={}, e:", notifyInfoBo.getRecordKey(), e);
        }
        return false;
    }
}


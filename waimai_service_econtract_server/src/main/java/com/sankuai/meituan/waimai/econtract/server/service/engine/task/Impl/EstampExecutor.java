package com.sankuai.meituan.waimai.econtract.server.service.engine.task.Impl;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meituan.sankuai.bsi.esign.common.model.dict.NotifyType;
import com.meituan.sankuai.bsi.esign.common.model.entity.request.SignContractRequest;
import com.meituan.sankuai.bsi.esign.common.model.entity.result.GetContractResult;
import com.meituan.sankuai.bsi.esign.common.model.entity.result.SignContractAsyncResult;
import com.meituan.sankuai.bsi.esign.common.model.entity.result.SignContractResult;
import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.econtract.server.adapter.EsignClient;
import com.sankuai.meituan.waimai.econtract.server.annotation.EcontractIdempotent;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.constants.*;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractRecordEntityMapper;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractAsyncRecordEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordEntity;
import com.sankuai.meituan.waimai.econtract.server.exception.EcontractException;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractMetricService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractRecordService;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.TaskExecutor;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.util.monitor.AsyncEstampDelayMonitor;
import com.sankuai.meituan.waimai.econtract.server.utils.*;
import com.sankuai.meituan.waimai.econtrct.client.constants.CallbackConstant;

import com.sankuai.meituan.waimai.econtrct.client.constants.EstampSignKeyRelEnum;
import com.sankuai.meituan.waimai.econtrct.client.constants.EstampSubjectInfoEnum;
import deps.redis.clients.util.CollectionUtils;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;
import java.util.Random;

import javax.annotation.Resource;

/**
 * <AUTHOR> Hou
 * @date 2017/10/25
 * @time 上午10:58
 */
@Service
public class EstampExecutor extends AbstractExecutor implements TaskExecutor {

    private static final Logger LOGGER = LoggerFactory.getLogger(EstampExecutor.class);

    private static final Integer MAX_CAS_RETRY_TIMES = 5;

    @Resource
    private EsignClient esignClient;

    @Resource
    private EcontractRecordService econtractRecordService;

    @Autowired
    private AsyncEstampDelayMonitor asyncEstampDelayMonitor;

    @Autowired
    private EcontractRecordEntityMapper econtractRecordEntityMapper;

    @Autowired
    private EcontractMetricService econtractMetricService;

    private static final String E_STAMP = "e_stamp";

    @Override
    @EcontractIdempotent
    public void executeTask(EcontractContext context) {
        try {
            LOGGER.info("EstampExecutor executeTask,recordKey:{}",context.getEcontractRecordEntity().getRecordKey());
            if (EcontractStampRouteUtil.checkRoutingToSSQ(context.getStampKey())) {
                if(asyncEstampProportion(context) || asyncEstampEcontractType(context)){
                    LOGGER.info("recordKey:{}，开启异步签章",context.getEcontractRecordEntity().getRecordKey());
                    handleAsyncPostForStampV2(context);
                }else{
                    LOGGER.info("recordKey:{}，维持同步签章",context.getEcontractRecordEntity().getRecordKey());
                    handleSyncPostForStamp(context);
                    econtractMetricService.metricEstampStatus("签章成功","同步签章");
                }
            } else {
                handleAsyncPostForStamp(context);
            }
        } catch (Exception e) {
            LOGGER.error("fail to EstampExecutor :" + context.getEcontractRecordEntity().getRecordKey() + ", msg:" + e.getMessage(), e);
            executeFail(context, e);
            econtractMetricService.metricEstampStatus("签章失败","同步签章");
        }
    }

    private void handleAsyncPostForStamp(EcontractContext context)
        throws KmsResultNullException, NoSuchAlgorithmException, ParseException, IOException {
        if (asyncPostForStamp(context)) {
            context.setContextState(ContextConstant.CONTEXT_FINISH);
            executeSuccess(context);
            LOGGER.info("EstampExecutor success , recordkey : {} ",context.getEcontractRecordEntity().getRecordKey());
        }
    }

    private boolean asyncPostForStamp(EcontractContext context) throws IOException, ParseException, NoSuchAlgorithmException, KmsResultNullException {

        //存放请求key:val数据对
        Map<String, String> params = Maps.newHashMap();
        //获取个人ca请求url,配置于mcc上
        String asyncStampUrl = ConfigUtilAdapter.getString(MccConstant.CERTIFY_ESTAMP_URL, "");
        String callbackUrl = ConfigUtilAdapter.getString(MccConstant.CERTIFY_ESTAMP_CALL_BACK_URL,"");
        String app_id = Kms.getByName(MccConstant.APP_KEY, KmsConstant.CERTIFY_APP_ID);
        String app_secret = Kms.getByName(MccConstant.APP_KEY, KmsConstant.CERTIFY_APP_SECRET);
        Timestamp ts = new Timestamp(System.currentTimeMillis());
        DateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String timestamp = sdf.format(ts);
        String transactionId = DealVersionUtils.getEstampDealVersion(context.getTaskContext().getTaskId());
        String contractId = genContractId(context, context.getEcontractRecordEntity().getRecordKey());
        params.put("app_id", app_id);
        params.put("timestamp", timestamp);
        params.put("v", "2.0");
        params.put("transaction_id", transactionId);
        params.put("batch_id", "1");
        params.put("client_role", "1");
        params.put("customer_id", context.getTaskContext().getCustomerId());
        params.put("contract_id", contractId);
//        params.put("doc_title", context.getTaskContext().getStageInfoBo().getEstampInfoBo().getEstampMap().get("doc_title"));
        params.put("doc_title", "doc_title");
        params.put("doc_url", "");
        params.put("sign_keyword", context.getTaskContext().getSignKeyWord());
        params.put("msg_digest", CaSignUtil.getMsgDigestForStamp(timestamp, app_id, app_secret, transactionId, context.getTaskContext().getCustomerId()));
        params.put("notify_url", "");
        params.put("doc_type", ".pdf");
        params.put("callback", callbackUrl);
        //进行post请求
        String postResult = null;
        LOGGER.info("do estamp , param is : {}",JSON.toJSONString(params));
        postResult = HttpClientUtil.doPostRequest(params, asyncStampUrl);
        return true;
    }

    private void handleSyncPostForStamp(EcontractContext context) {
        try {
            String taskType = getTaskType(context);
            String contractId = context.getTaskTypeAndContractIdMap().get(taskType);
            String failReason = syncPostForStamp(context);
            if (Strings.isEmpty(failReason)) {
                GetContractResult getContractResult = esignClient.downloadContract(contractId);
                String stampUrl = UrlConvetUtil.changeEstampUrl(getContractResult.getContract());
                Map<String, String> paramMap = Maps.newHashMap();
                paramMap.put(CallbackConstant.ESTAMP_RESULT_CODE, "1000");
                paramMap.put(CallbackConstant.ESTAMP_DOWNLOAD_URL, stampUrl);
                paramMap.put(CallbackConstant.ESTAMP_TRANSCATION_ID,
                             DealVersionUtils.getEstampDealVersion(context.getTaskContext().getTaskId()));
                context.getTaskContext().setExecutorResult(paramMap);
                context.getTaskContext().setState(TaskConstant.TASK_SUCCESS);
                EcontractRecordEntity recordEntity =
                    econtractRecordService.queryRecordByRecordKey(context.getEcontractRecordEntity().getRecordKey());
                context.getTaskContext().setEcontractStage(recordEntity.getEcontractStage());
                context.setContextState(ContextConstant.CONTEXT_CALLBACK);
                context.setExecuteTaskId(context.getTaskContext().getTaskId());
                casRecordSaveUrl(recordEntity, stampUrl, taskType);
                metricExecutorResult(E_STAMP, true);
                executeCallback(context);
            } else {
                context.getTaskContext().setErrorCode(EcontractException.ESTAMP_UPLOAD_ERROR);
                context.getTaskContext().setState(TaskConstant.TASK_FAIL);
                context.getTaskContext().setFailMessage(failReason);
                LOGGER.info("handleSyncPostForStamp, {}, recordKey:{}", failReason, context.getEcontractRecordEntity().getRecordKey());
                metricExecutorResult(E_STAMP, false);
                executeFail(context, failReason);
            }
        } catch (Exception e) {
            metricExecutorResult(E_STAMP, false);
            LOGGER.error("签章异常, recordKey={}, msg:{}", context.getEcontractRecordEntity().getRecordKey(), e.getMessage(), e);
            executeFail(context, e);
        }
    }

    private void handleAsyncPostForStampV2(EcontractContext context) {
        LOGGER.info("handleAsyncPostForStampV2，recordKey:{}",context.getEcontractRecordEntity().getRecordKey());
        //调用签章异步接口
        SignContractAsyncResult asyncSignResult = esignClient.signContractAsync(context);
        //记录异步任务ID与taskId的关系
        EcontractAsyncRecordEntity asyncRecordEntity = saveAsyncTaskRel(context, asyncSignResult, NotifyType.SIGN_CONTRACT_ASYNC.getName());
        //监控接口调用情况
        asyncEstampDelayMonitor.addAsyncTask(asyncRecordEntity);
    }

    private String syncPostForStamp(EcontractContext context) {
        String taskType = getTaskType(context);
        SignContractRequest signContractRequest = new SignContractRequest();
        signContractRequest.setContractId(context.getTaskTypeAndContractIdMap().get(taskType));
        signContractRequest.setCustomerId(context.getTaskContext().getCustomerId());
        signContractRequest.setKeyWord(context.getTaskContext().getSignKeyWord());
        Map<String,String> resultMap = esignClient.warpSignContractCustomerNameAndIdCode(context);
        signContractRequest.setCustomerName(resultMap.get("customerName"));
        signContractRequest.setCustomerIdCode(resultMap.get("customerIdCode"));
        LOGGER.info("syncPostForStamp 发起签章请求,recordKey:{},signContractRequest:{}", context.getEcontractRecordEntity().getRecordKey(), JacksonUtil.writeAsJsonStr(signContractRequest));
        SignContractResult result = esignClient.signContract(signContractRequest);
        LOGGER.info("syncPostForStamp 发起签章结果,recordKey:{},result:{}", context.getEcontractRecordEntity().getRecordKey(), JacksonUtil.writeAsJsonStr(result));

        //CA认证成功
        if (StringUtils.isNotBlank(result.getContractId())) {
            return null;
        }
        //重复签章
        if(result.getCode()==300039){
            return null;
        }
        LOGGER.warn("签章失败，recordKey={}，result={}", context.getEcontractRecordEntity().getRecordKey(), JSON.toJSONString(result));
        return result.getMessage();
    }

    public void casRecordSaveUrl(EcontractRecordEntity recordEntity, String saveUrl, String executeTaskType) {
        int updateCount = 0;
        for (int retryTime = MAX_CAS_RETRY_TIMES; retryTime > 0; retryTime--) {
            if (updateCount > 0) {
                return;
            }

            recordEntity = econtractRecordService.queryRecordByRecordKey(recordEntity.getRecordKey());
            String toUrl = urlFormat(recordEntity.getSaveUrl(), saveUrl, executeTaskType);
            if (toUrl != null && toUrl.equals(recordEntity.getSaveUrl())) {
                continue;
            }
            recordEntity.setSaveUrl(toUrl);
            LOGGER.info("update changeUrl, recordkey is : {} , recordVersion is :{} , change URL is {}, executeTaskType is {}", recordEntity.getRecordKey(), recordEntity.getVersion(), recordEntity.getSaveUrl(), executeTaskType);
            //直接更新saveurl，省去加密数据的频繁更新
            updateCount += econtractRecordEntityMapper.updateSaveUrlById(recordEntity.getId(), toUrl, recordEntity.getVersion(), TimeUtils.getUTime());
        }
    }

    private String urlFormat(String storeUrl, String toUrl, String taskType) {
        if (StringUtils.isEmpty(storeUrl) || storeUrl.endsWith(".pdf")) {
            return toUrl;
        }

        JSONObject jo = JSON.parseObject(storeUrl);
        jo.put(taskType, toUrl);
        return jo.toJSONString();
    }

    private boolean asyncEstampProportion(EcontractContext context) {
        if(ConfigUtilAdapter.getBoolean("estamp_async_switch", false)){
            Integer recordId = context.getEcontractRecordEntity().getId();
            Integer asyncProportion = recordId % 10000;
            if(asyncProportion <= ConfigUtilAdapter.getInt("estamp_async_proportion", 0)) {
                return true;
            }
        }
        return false;
    }

    private boolean asyncEstampEcontractType(EcontractContext context){
        List<String> openEcontrctTypeList = Lists.newArrayList(ConfigUtilAdapter.getString("async_estamp_open_econtracttype", "").split(","));
        String econtractType = context.getEcontractEntity().getEcontractType();
        if(StringUtils.isNotEmpty(econtractType) && CollectionUtils.isNotEmpty(openEcontrctTypeList)){
            return openEcontrctTypeList.contains(econtractType);
        }
        return false;
    }

    public EstampSubjectInfoEnum getEstampSignKey(EcontractContext context){
        String currentEstampSignKey = context.getTaskContext().getStageInfoBo().getEstampInfoBo().getEstampMap().get("estamp_sign_key");
        EstampSubjectInfoEnum estampSubjectInfoEnum = EstampSubjectInfoEnum.getBySignKeyName(currentEstampSignKey);
        if(estampSubjectInfoEnum == null){
            String currentEstampSubjectName = context.getTaskContext().getStageInfoBo().getEstampInfoBo().getEstampMap().get("estamp_sign_key_subject");
            EstampSignKeyRelEnum estampSignKeyRelEnum = EstampSignKeyRelEnum.getBySignKeyNameAndsubjectName(currentEstampSignKey, currentEstampSubjectName);
            estampSubjectInfoEnum = EstampSubjectInfoEnum.getBySignKeyName(estampSignKeyRelEnum.getEcontractUseName());
        }
        return estampSubjectInfoEnum;
    }
}

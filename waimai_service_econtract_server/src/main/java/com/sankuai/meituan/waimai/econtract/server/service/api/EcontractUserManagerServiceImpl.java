package com.sankuai.meituan.waimai.econtract.server.service.api;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;

import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.econtract.server.adapter.UacAuthRemoteServiceAdapter;
import com.sankuai.meituan.waimai.econtract.server.adapter.WmEmployAdapter;
import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import com.sankuai.meituan.waimai.econtract.server.constants.EcontractUserConstant;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractSignPageTemplateEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractUserEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractUserRelEntity;
import com.sankuai.meituan.waimai.econtract.server.service.ability.EcontractUserAbilityService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractSignPageTemplateService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractUserRelService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractUserService;
import com.sankuai.meituan.waimai.econtract.server.template.config.entity.EcontractTemplateBaseEntity;
import com.sankuai.meituan.waimai.econtract.server.template.config.service.EcontractTemplateBaseService;
import com.sankuai.meituan.waimai.econtract.server.utils.DealVersionUtils;
import com.sankuai.meituan.waimai.econtract.server.utils.JacksonUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractBaseConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.biz.EcontractTemplateBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.biz.EcontractUserBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.common.BaseResponse;
import com.sankuai.meituan.waimai.econtrct.client.domain.config.econtractuser.EcontractUserDeleteRequestDTO;
import com.sankuai.meituan.waimai.econtrct.client.domain.config.econtractuser.EcontractUserQueryRequestDTO;
import com.sankuai.meituan.waimai.econtrct.client.domain.config.econtractuser.EcontractUserSyncRequestDTO;
import com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException;
import com.sankuai.meituan.waimai.econtrct.client.service.EcontractUserManagerService;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.elasticsearch.common.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 *
 * 用户权限管理
 * <AUTHOR> Hou
 * @date 2017/12/19
 * @time 上午11:30
 */
@Service
public class EcontractUserManagerServiceImpl implements EcontractUserManagerService {

    private static final Logger LOGGER = LoggerFactory.getLogger(EcontractUserManagerServiceImpl.class);

    private static final Joiner COMMON_JOIN = Joiner.on(",").skipNulls();


    @Autowired
    private EcontractUserService userService;

    @Autowired
    private EcontractUserRelService userRelService;

    @Resource
    private UacAuthRemoteServiceAdapter uacAuthRemoteServiceAdapter;

    @Resource
    private EcontractService econtractService;

    @Resource
    private EcontractTemplateBaseService econtractTemplateBaseService;

    @Resource
    private EcontractSignPageTemplateService econtractSignPageTemplateService;

    @Resource
    private WmEmployAdapter employAdapter;

    @Resource
    private EcontractUserAbilityService econtractUserAbilityService;


    @Override
    public List<EcontractUserBo> queryEcontractUserBoList() {
        List<EcontractUserBo> userBoList = Lists.newArrayList();
        userService.queryEcontractUserList().forEach(entity -> {
            EcontractUserBo userBo = new EcontractUserBo();
            userBo.setId(entity.getId());
            userBo.setUserName(entity.getName());
            userBo.setToken(entity.getToken());
            userBo.setType(entity.getType());
            userBo.setCtime(entity.getCtime().getTime() / 1000);
            userBo.setDescription(entity.getDescription());
            userBoList.add(userBo);
        });
        return userBoList;
    }

    @Override
    public List<EcontractUserBo> queryEcontractUserList(EcontractUserQueryRequestDTO requestDTO) throws EcontractException {
        LOGGER.info("EcontractUserManagerServiceImpl#queryEcontractUserList, requestDTO: {}", JacksonUtil.writeAsJsonStr(requestDTO));
        List<EcontractUserBo> econtractUserBoList = userService.queryEcontractUserList(requestDTO);
        LOGGER.info("EcontractUserManagerServiceImpl#queryEcontractUserList, econtractUserBoList: :{}", JacksonUtil.writeAsJsonStr(econtractUserBoList));
        return econtractUserBoList;
    }

    @Override
    public List<EcontractUserBo> queryEcontractUserBoListForAI(EcontractUserQueryRequestDTO requestDTO) {
        try {
            LOGGER.info("EcontractUserManagerServiceImpl#queryEcontractUserBoListForAI, requestDTO: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            List<EcontractUserBo> econtractUserBoList = userService.queryEcontractUserList(requestDTO);
            LOGGER.info("EcontractUserManagerServiceImpl#queryEcontractUserBoListForAI, econtractUserBoList: :{}", JacksonUtil.writeAsJsonStr(econtractUserBoList));
            return econtractUserBoList;
        } catch (EcontractException e) {
            LOGGER.warn("EcontractUserManagerServiceImpl#queryEcontractUserBoListForAI, warn", e);
            return Collections.emptyList();
        } catch (Exception e) {
            LOGGER.error("EcontractUserManagerServiceImpl#queryEcontractUserBoListForAI, error", e);
            return Collections.emptyList();
        }
    }

    @Override
    public void createEcontractUser(EcontractUserBo userInfoBo) {
        EcontractUserEntity userEntity = new EcontractUserEntity();
        userEntity.setName(userInfoBo.getUserName()+"_contract");
        userEntity.setCtime(new Date());
        userEntity.setToken(DealVersionUtils.getUserDealVersion(userInfoBo.getUserName()));
        userEntity.setType(userInfoBo.getType());
        userEntity.setValid(EcontractUserConstant.VALID);
        userService.insertSelective(userEntity);
    }

    @Override
    public void createEcontractUser4Config(EcontractUserBo userInfoBo, int userId) throws EcontractException {
        if (!checkCreateEcontractUserPermission(userId)) {
            throw new EcontractException(EcontractException.NOT_AUTHORITY, "只有超级管理员可以新增接入方");
        }

        if (isUserExist(userInfoBo.getUserName())) {
            throw new EcontractException(EcontractException.PARAM_ERROR, "当前code重复，需修改后提交");
        }

        if (econtractUserAbilityService.queryUserByDescription(userInfoBo.getDescription()) != null) {
            throw new EcontractException(EcontractException.PARAM_ERROR, "当前名称重复，需修改后提交");
        }

        EcontractUserEntity userEntity = new EcontractUserEntity();
        userEntity.setName(userInfoBo.getUserName());
        userEntity.setCtime(new Date());
        userEntity.setUtime(new Date());
        userEntity.setToken(DealVersionUtils.getUserDealVersion(userInfoBo.getUserName()));
        userEntity.setType(Strings.isEmpty(userInfoBo.getType()) ? EcontractUserConstant.TYPE_APP : userInfoBo.getType());
        userEntity.setMemberMis(COMMON_JOIN.join(userInfoBo.getMemberMisList()));
        userEntity.setDescription(userInfoBo.getDescription());
        userEntity.setValid(EcontractUserConstant.VALID);
        userService.insertSelective(userEntity);
        econtractUserAbilityService.syncEcontractUserToHaiLuo(userEntity, userId);
    }

    private boolean checkCreateEcontractUserPermission(int userId) {
        List<Long> allRoleIds = uacAuthRemoteServiceAdapter.getAllRoleIdByUid(userId);
        return isSuperAdmin(allRoleIds);
    }

    @Override
    public boolean isUserExist(String userName) {
        EcontractUserEntity userEntity = userService.queryUserByName(userName);
        if(null==userEntity){
            return false;
        }else {
            return true;
        }
    }

    @Override
    public EcontractTemplateBo queryUserByName(String userName) {
        EcontractUserEntity userEntity = userService.queryUserByName(userName);

        EcontractTemplateBo templateBo = new EcontractTemplateBo();
        BeanUtils.copyProperties(userEntity, templateBo);
        return templateBo;
    }

    @Override
    public EcontractTemplateBo queryEcontractUserById(Integer id) {
        EcontractUserBo userBo = userService.queryValidUserById(id);
        EcontractTemplateBo templateBo = new EcontractTemplateBo();
        BeanUtils.copyProperties(userBo, templateBo);
        return templateBo;
    }

    @Override
    public String getEcontractUserByUserId(Integer userId) throws EcontractException, TException {
        EcontractUserRelEntity entity = userRelService.getByUid(userId);
        return entity == null ? StringUtils.EMPTY : entity.getEcontractUser();
    }

    @Override
    public void bindUser(String econtractUser, Integer userId) throws EcontractException, TException {
        EcontractUserRelEntity entity = userRelService.getByUid(userId);
        if (entity != null) {
            entity.setEcontractUser(econtractUser);
            entity.setUtime(DateUtil.today());
            userRelService.updateByPrimaryKey(entity);
        } else {
            entity = new EcontractUserRelEntity();
            entity.setEcontractUser(econtractUser);
            entity.setUid(userId);
            entity.setValid(EcontractBaseConstant.VALID);
            entity.setCtime(DateUtil.today());
            entity.setUtime(DateUtil.today());
            userRelService.insertSelective(entity);
        }
    }

    @Override
    public BaseResponse<Boolean> deleteEcontractUser(EcontractUserDeleteRequestDTO requestDTO) {
        LOGGER.info("EcontractUserManagerServiceImpl#deleteEcontractUser, requestDTO: {}", JacksonUtil.writeAsJsonStr(requestDTO));
        if (requestDTO == null || requestDTO.getId() <= 0) {
            return BaseResponse.fail("参数异常", false);
        }
        try {
            if(!checkDeleteEcontractUserPermission(requestDTO.getOperatorId())) {
                throw new EcontractException(EcontractException.NOT_AUTHORITY, "只有超级管理员可以删除接入方");
            }
            checkOtherInfo(requestDTO.getId());

            userService.deleteEcontractUser(requestDTO.getId());
            return BaseResponse.success(true);
        } catch (Exception e) {
            LOGGER.error("EcontractUserManagerServiceImpl#deleteEcontractUser, error", e);
            return BaseResponse.fail(e.getMessage());
        }
    }

    private void checkOtherInfo(Integer managerId) throws EcontractException {
        // 内容模板
        List<EcontractTemplateBaseEntity> templateBaseEntities = econtractTemplateBaseService.queryEcontractTemplateByEcontractUserId(managerId);

        // 流程模板
        List<EcontractEntity> econtractEntities = econtractService.queryEcontractByEcontractUserId(managerId);

        // H5模板
        List<EcontractSignPageTemplateEntity> h5Entities= econtractSignPageTemplateService.getTemplateByEcontractUserId(managerId);

        if (CollectionUtils.isNotEmpty(templateBaseEntities) || CollectionUtils.isNotEmpty(econtractEntities) || CollectionUtils.isNotEmpty(h5Entities)) {
            throw new EcontractException(-1, String.format("接入方关联有%s个内容模板、%s个流程模板、%s个H5模板，不可删除", templateBaseEntities.size(), econtractEntities.size(), h5Entities.size()));
        }
    }

    private boolean checkDeleteEcontractUserPermission(Integer operatorId) {
        List<Long> roleIds = uacAuthRemoteServiceAdapter.getAllRoleIdByUid(operatorId);
        return isSuperAdmin(roleIds);
    }

    @Override
    public BaseResponse<Boolean> updateEcontractUser(EcontractUserBo userInfoBo) {
        LOGGER.info("EcontractUserManagerServiceImpl#updateEcontractUser, userInfoBo: {}", JacksonUtil.writeAsJsonStr(userInfoBo));
        try {
            if (userInfoBo == null || userInfoBo.getId() == null || userInfoBo.getId() < 0) {
                throw new EcontractException(EcontractException.PARAM_ERROR, "参数异常");
            }

            if (!Strings.isEmpty(userInfoBo.getUserName()) || !Strings.isEmpty(userInfoBo.getToken())) {
                throw new EcontractException(EcontractException.PARAM_ERROR, "不可编辑该信息");
            }

            if (!checkUpdateEcontractUserPermission(userInfoBo)) {
                throw new EcontractException(EcontractException.NOT_AUTHORITY, "只可以编辑自己有权限的接入方");
            }

            EcontractUserEntity userEntity = econtractUserAbilityService.queryUserByDescription(userInfoBo.getDescription());
            if (userEntity != null && !Objects.equals(userEntity.getId(), userInfoBo.getId())) {
                throw new EcontractException(EcontractException.PARAM_ERROR, "当前名称重复，需修改后提交");
            }

            boolean result = userService.updateEcontractUser(userInfoBo);
            LOGGER.info("EcontractUserManagerServiceImpl#updateEcontractUser, result: {}", result);
            if (result) {
                EcontractUserBo econtractUserBo = econtractUserAbilityService.queryEcontractUserById(userInfoBo.getId());
                EcontractUserEntity target = new EcontractUserEntity();
                target.setDescription(userInfoBo.getDescription());
                target.setName(econtractUserBo.getUserName());
                econtractUserAbilityService.syncEcontractUserToHaiLuo(target, userInfoBo.getOperationId());
            }
            return BaseResponse.initResult(result);
        } catch (EcontractException e) {
            LOGGER.error("EcontractUserManagerServiceImpl#updateEcontractUser, warn", e);
            return BaseResponse.fail(e.getMsg());
        } catch (Exception e) {
            LOGGER.error("EcontractUserManagerServiceImpl#updateEcontractUser, error", e);
            return BaseResponse.fail("编辑失败");
        }
    }

    @Override
    public void syncEcontractUserToHailuo(EcontractUserSyncRequestDTO requestDTO) {
        try {
            LOGGER.info("EcontractUserManagerServiceImpl#syncEcontractUserToHailuo, requestDTO: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            econtractUserAbilityService.syncEcontractUserToHailuo(requestDTO);
        } catch (Exception e) {
            LOGGER.error("EcontractUserManagerServiceImpl#syncEcontractUserToHailuo, error", e);
        }
    }

    private boolean checkUpdateEcontractUserPermission(EcontractUserBo userInfoBo) throws EcontractException {
        int userId = userInfoBo.getOperationId();
        List<Long> allRoleIds = uacAuthRemoteServiceAdapter.getAllRoleIdByUid(userId);
        if (isSuperAdmin(allRoleIds)) {
            return true;
        }

        if (isContractConfigRD(allRoleIds) || isContractConfigYW(allRoleIds)) {
            EcontractUserEntity econtractUserEntity = userService.selectByPrimaryKey(userInfoBo.getId());
            String memberMis = econtractUserEntity.getMemberMis();

            String misId = employAdapter.getMisIdByUid(userId);
            return StringUtils.isEmpty(memberMis) || memberMis.contains(misId);
        }

        return false;
    }

    private boolean isSuperAdmin(List<Long> roleIds) {
        return roleIds.contains(MccConfig.getSuperAdminRoleId());
    }

    private boolean isContractConfigRD(List<Long> roleIds) {
        return roleIds.contains(MccConfig.getContractConfigRDRoleId());
    }

    private boolean isContractConfigYW(List<Long> roleIds) {
        return roleIds.contains(MccConfig.getContractConfigYWRoleId());
    }
}

package com.sankuai.meituan.waimai.econtract.server.service.engine.notify.Impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.meituan.mtrace.thread.pool.ExecutorTraceWrapper;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractUserEntity;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractMetricService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractUserService;
import com.sankuai.meituan.waimai.econtract.server.service.engine.notify.Notifier;
import com.sankuai.meituan.waimai.econtract.server.utils.HttpClientUtil;
import com.sankuai.meituan.waimai.econtract.server.utils.MapUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractNotifyInfoBo;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.ReentrantLock;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * <AUTHOR> Hou
 * @date 2017/11/4
 * @time 上午12:23
 */
@Service
public class NotifierImpl implements Notifier {

    private static final Logger LOGGER = LoggerFactory.getLogger(NotifierImpl.class);

    @Resource
    private EcontractUserService econtractUserService;

    @Resource
    private EcontractMetricService econtractMetricService;

    private Map<Integer, Queue<EcontractNotifyInfoBo>> notifyQueueMap;

    private Map<Integer, ExecutorTraceWrapper> notifyThreadPoolMap;

    private Map<Integer, ReentrantLock> notifyLockMap;

    private Map<Integer, Condition> conditionMap;

    private static final Integer defaultQueueId = 3;

    @PostConstruct
    private void init() {
        List<EcontractUserEntity> userEntityList = econtractUserService.queryEcontractUserList();
        if (CollectionUtils.isEmpty(userEntityList)) {
            //user不存在，不进行线程池初始化
            return;
        }

        notifyQueueMap = Maps.newHashMap();
        notifyThreadPoolMap = Maps.newHashMap();
        notifyLockMap = Maps.newHashMap();
        conditionMap = Maps.newHashMap();
        for (EcontractUserEntity userEntity : userEntityList) {
            notifyQueueMap.put(userEntity.getId(), new LinkedBlockingQueue<>(2000));
            notifyThreadPoolMap.put(userEntity.getId(), new ExecutorTraceWrapper(new ThreadPoolExecutor(5, 50, 60, TimeUnit.SECONDS, new LinkedBlockingQueue<Runnable>(20000))));
            notifyLockMap.put(userEntity.getId(), new ReentrantLock());
            conditionMap.put(userEntity.getId(), notifyLockMap.get(userEntity.getId()).newCondition());
            Thread notifyDaemon = new Thread(new Runnable() {
                @Override
                public void run() {
                    while (true) {
                        doNotify(userEntity.getId());
                    }
                }
            });
            notifyDaemon.setDaemon(true);
            notifyDaemon.start();
        }
    }


    private void doNotify(int id) {
        notifyLockMap.get(id).lock();
        try {
            if (notifyQueueMap.get(id).size() == 0) {
                conditionMap.get(id).await();
            }
            final EcontractNotifyInfoBo notifyInfoBo = notifyQueueMap.get(id).poll();
            notifyThreadPoolMap.get(id).execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        LOGGER.info("notify callback:notifyBo = {}", JSON.toJSONString(notifyInfoBo));
                        String code = String.valueOf(notifyInfoBo.getCode());
                        Map<String, String> notifyMap = MapUtil.java2Map(notifyInfoBo);
                        notifyMap.put("code", code);
                        String str = HttpClientUtil.doPostRequest(notifyMap, notifyInfoBo.getNotifyUrl());
                        econtractMetricService.metricCallbackCount(notifyInfoBo, "HTTP");
                        LOGGER.info("notify str = {}, recordKey:{}", JSON.toJSONString(str),notifyInfoBo.getRecordKey());
                    } catch (Exception e) {
                        LOGGER.error("fail to add to notifyInfoBo, recordKey:{}, notify url:{}", notifyInfoBo.getRecordKey(), notifyInfoBo.getNotifyUrl(), e);
                    }
                }
            });
        } catch (Exception e) {
            LOGGER.error("fail to doNotify,id:{}",id, e);
        }
    }

    @Override
    public void submitNotify(EcontractNotifyInfoBo notifyInfoBo) {
        if (notifyQueueMap.get(notifyInfoBo.getNotifyQueueId()) == null) {
            notifyInfoBo.setNotifyQueueId(defaultQueueId);
        }

        notifyLockMap.get(notifyInfoBo.getNotifyQueueId()).lock();
        try {
            notifyQueueMap.get(notifyInfoBo.getNotifyQueueId()).add(notifyInfoBo);
            conditionMap.get(notifyInfoBo.getNotifyQueueId()).signalAll();
        } catch (Exception e) {
            LOGGER.error("fail to add to notify queue, recordKey:{}, notify url:{}", notifyInfoBo.getRecordKey(), notifyInfoBo.getNotifyUrl(), e);
        } finally {
            notifyLockMap.get(notifyInfoBo.getNotifyQueueId()).unlock();
        }
    }
}

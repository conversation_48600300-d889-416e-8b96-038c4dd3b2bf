package com.sankuai.meituan.waimai.econtract.server.service.biz.Impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.meituan.mtrace.thread.pool.TraceExecutors;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractRecordContextAreaMapper;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordContextAreaEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordContextEntity;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.*;

@Service
@Slf4j
public class EcontractRecordContextAreaService {

    private static final ExecutorService insertExecutorService = TraceExecutors
            .getTraceExecutorService(new ThreadPoolExecutor(10, 100, 0L, TimeUnit.MILLISECONDS,
                    new LinkedBlockingDeque<>(500), new ThreadPoolExecutor.CallerRunsPolicy()));

    private static final ExecutorService queryExecutorService = TraceExecutors
            .getTraceExecutorService(new ThreadPoolExecutor(10, 100, 0L, TimeUnit.MILLISECONDS,
                    new LinkedBlockingDeque<>(500), new ThreadPoolExecutor.CallerRunsPolicy()));

    @Autowired
    private EcontractRecordContextAreaMapper econtractRecordContextAreaMapper;

    public void batchInsert(Integer recordContextId, List<EcontractRecordContextAreaEntity> recordContextAreaEntityList) {
        if (CollectionUtils.isEmpty(recordContextAreaEntityList)) {
            return;
        }
        log.info("#EcontractRecordContextAreaService#batchInsert recordContextId:{} size:{}", recordContextId, recordContextAreaEntityList.size());
        List<List<EcontractRecordContextAreaEntity>> recordContextAreaEntityListList = Lists.partition(recordContextAreaEntityList, MccConfig.recordContextAreaParitionSize());
        final CountDownLatch countDownLatch = new CountDownLatch(recordContextAreaEntityListList.size());
        long beginTime = System.currentTimeMillis();
        for (List<EcontractRecordContextAreaEntity> subRecordContextAreaEntityList : recordContextAreaEntityListList) {
            try {
                insertExecutorService.execute(() -> {
                    econtractRecordContextAreaMapper.batchInsert(subRecordContextAreaEntityList);
                });
            } catch (Exception e) {
                log.error("#并发写入配送范围异常 subRecordContextAreaEntityList:{}", JSON.toJSONString(subRecordContextAreaEntityList), e);
            } finally {
                countDownLatch.countDown();
            }
        }
        try {
            countDownLatch.await(5, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.warn("多线程等待结果超时异常", e);
        }
        long endTime = System.currentTimeMillis();
        log.info("EcontractRecordContextAreaService#batchInsert recordContextId:{} cost:{} ms", recordContextId, endTime - beginTime);
    }

    public List<EcontractRecordContextAreaEntity> queryAreaEntityListByRecordContext(EcontractRecordContextEntity contextEntity) {
        List<EcontractRecordContextAreaEntity> contextAreaEntityList = Collections.synchronizedList(new ArrayList<>());
        EcontractContext coldContext = JSON.parseObject(contextEntity.getContext(), EcontractContext.class);
        List<Long> wmPoiIdList = coldContext.getAreaSeperateSaveWmPoiIdList();
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return contextAreaEntityList;
        }
        List<List<Long>> wmPoiIdListList = Lists.partition(wmPoiIdList, MccConfig.recordContextAreaParitionSize());
        final CountDownLatch countDownLatch = new CountDownLatch(wmPoiIdListList.size());
        long beginTime = System.currentTimeMillis();
        for (List<Long> subWmPoiIdList : wmPoiIdListList) {
            queryExecutorService.execute(() -> {
                try {
                    contextAreaEntityList.addAll(econtractRecordContextAreaMapper.selectByRecordContextIdAndWmPoiIds(Long.valueOf(contextEntity.getId()), subWmPoiIdList));
                } catch (Exception e) {
                    log.error("#并发获取配送范围异常 recordContextId:{}, subWmPoiIdList:{}", contextEntity.getId(), subWmPoiIdList, e);
                } finally {
                    countDownLatch.countDown();
                }
            });
        }
        try {
            countDownLatch.await(5, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.warn("多线程等待结果超时异常", e);
        }
        long endTime = System.currentTimeMillis();
        log.info("EcontractRecordContextAreaService#queryAreaEntityListByRecordContext recordContextId:{} cost:{} ms", contextEntity.getId(), endTime - beginTime);
        return contextAreaEntityList;
    }
}

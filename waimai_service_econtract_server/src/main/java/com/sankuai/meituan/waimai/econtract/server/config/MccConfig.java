package com.sankuai.meituan.waimai.econtract.server.config;

import com.google.common.base.Splitter;
import com.sankuai.meituan.config.annotation.MtConfig;
import com.sankuai.meituan.config.configuration.MccConfiguration;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.econtract.server.constants.EcontractAreaConstant;

import java.util.List;

/**
 * mcc
 */
public class MccConfig {


    private static final String MCC_CLIENT_ID = MccConfiguration.ID;

    private static final Splitter COMM_SPLITTER = Splitter.on(",").trimResults();

    /**
     * 消费消息时本地sleep时间后消费
     */
    @MtConfig(clientId = MCC_CLIENT_ID, key = "waimai.e.contract.consumer.sleep.time")
    public volatile static int CONSUMER_SLEEP_TIME = 100;

    /**
     * 消费多次失败
     */
    @MtConfig(clientId = MCC_CLIENT_ID, key = "waimai.e.contract.consumer.fail.count")
    public volatile static int CONSUMER_FAIL_READ_MASTER_COUNT = 3;

    /**
     * 消息体超过多大size后 使用数据库暂存方案
     */
    @MtConfig(clientId = MCC_CLIENT_ID, key = "waimai.e.contract.message.size")
    public volatile static int MESSAGE_SIZE = 500 * 1024;

    /**
     * token共用的公钥
     * <p>
     * 线下：MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC/dH7oGzjR19bGgrJkVGs96S5uf2kX2Owv7MXVO3SMlp2jOz4Shh7ZponHseDl0I6YKR9SPEZ/hBdoz9cToQkNSmbmer96IMGLfAhSONHvoXxfEGsRAxhrY5h7P/T8+xTVJl0GfgBGWKeMm/r3BsRBOTEAKOkE8tbGuoEqWo/2YQIDAQAB
     * 线上：MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDZW63DWEW7jYbdMt/TvtPzDJ2u3eZ7MGVJyrQam4Z3io+e9hwmG5N3R/aWMXdh8MLUtNXzAKFimIMkrMe7ajsHfGpRaH0OwWl7n09PB5NCEst9Qo5zdGBHnyBlDtMCSUX7iZaKYqgiL0QRSvCanWhvTqWA/E3RKhNEQwnO34D0RwIDAQAB
     */
    public static String getTokenPublicKey() {
        return ConfigUtilAdapter.getString("token_public_key", "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDZW63DWEW7jYbdMt/TvtPzDJ2u3eZ7MGVJyrQam4Z3io+e9hwmG5N3R/aWMXdh8MLUtNXzAKFimIMkrMe7ajsHfGpRaH0OwWl7n09PB5NCEst9Qo5zdGBHnyBlDtMCSUX7iZaKYqgiL0QRSvCanWhvTqWA/E3RKhNEQwnO34D0RwIDAQAB");
    }

    /**
     * 短链接过期时间
     *
     * @return 单位：天
     */
    public static int getShortUrlExpireDays() {
        return ConfigUtilAdapter.getInt("short_url_expire_days", 1);
    }

    /**
     * 若未指定模版版本，是否要在申请签约流程的时候自动装配当前已发布模版版本
     *
     * @return
     */
    public static boolean getReleasedTemplateVersionAutowired() {
        return ConfigUtilAdapter.getBoolean("released_template_version_autowired", true);
    }

    /**
     * 拼好饭新费率模式-签约模板名称
     */
    public static String getPHFSignTempletName() {
        return ConfigUtilAdapter.getString("sign.h5.templet.phf", "拼好饭合作方案确认");
    }

    /**
     * 拼好饭新费率模式-签约type名称
     *
     * @return
     */
    public static String getPHFSignTempletTypeList() {
        return ConfigUtilAdapter.getString("sign.h5.templet.phf.type", "phf_new_fee_contract,type_bargain_settle_contract_sign_v2,phf_online_contract");
    }

    /**
     * 存储加密-总开关(默认关)
     */
    public static boolean isEncryptionSwitch() {
        return ConfigUtilAdapter.getBoolean("is_encryption_switch", false);
    }

    /**
     * 子文本大小-默认1M
     */
    public static int subContextSize() {
        return ConfigUtilAdapter.getInt("sub_context_size", 1024 * 1024);
    }

    /**
     * 存储加密-线程池大小
     *
     * @return
     */
    public static int encryptionThreadPoolSize() {
        return ConfigUtilAdapter.getInt("encryption_thread_pool_size", 10);
    }

    /**
     * 存储加密-是否停写明文
     *
     * @return
     */
    public static boolean isFilterOriginValue() {
        return ConfigUtilAdapter.getBoolean("is_filter_origin_value", false);
    }

    /**
     * 存储加密-是否读密文
     *
     * @return
     */
    public static boolean isFillingByEncrypedValue() {
        return ConfigUtilAdapter.getBoolean("is_filling_by_encrypted_value", false);
    }

    /**
     * 存储加密-读密文灰度百分比
     *
     * @return
     */
    public static int fillingByEncrypedValuePercent() {
        return ConfigUtilAdapter.getInt("filling_by_encrypted_value_percent", 0);
    }

    /**
     * 存储加密-指定mysql表
     *
     * @return
     */
    public static String encryptionTableNameList() {
        return ConfigUtilAdapter.getString("encrytion_table_name_list", "wm_econtract_record,wm_econtract_record_context,wm_econtract_async_record");
    }

    /**
     * 存储加密-未获取到锁等待时间
     *
     * @return
     */
    public static int encryptionThreadSleepMillis() {
        return ConfigUtilAdapter.getInt("encrytion_thread_sleep_mills", 50);
    }

    /**
     * 数据清洗-任务执行截止小时数(默认执行至早上8点)
     */
    public static int dataCleanLimitHour() {
        return ConfigUtilAdapter.getInt("data_clean_limit_hour", 8);
    }

    /**
     * 历史数据加密-任务执行截止小时数(默认执行至早上8点)
     */
    public static int dataEncryptionLimitHour() {
        return ConfigUtilAdapter.getInt("data_encryption_limit_hour", 8);
    }

    /**
     * 明文和密文不相等时是否重写密文
     *
     * @return
     */
    public static boolean isOverWriteEncryptionRecord() {
        return ConfigUtilAdapter.getBoolean("is_over_write_encryption_record", false);
    }

    /**
     * 是否删除历史明文
     */
    public static boolean isRemoveOriginalRecord() {
        return ConfigUtilAdapter.getBoolean("is_remove_original_record", false);
    }

    /**
     * 存储加密，锁超时时间（秒）
     */
    public static int encryptionTairLockExpireTime() {
        return ConfigUtilAdapter.getInt("encrytion_tair_lock_expire_time", 5);
    }

    /**
     * APP/PC待签约页-签约列表线程池
     *
     * @return
     */
    public static int toSignQueryThreadPoolSize() {
        return ConfigUtilAdapter.getInt("to_sign_query_thread_pool_size", 10);
    }

    /**
     * APP/PC待签约页-批量操作线程池
     *
     * @return
     */
    public static int toSignOpThreadPoolSize() {
        return ConfigUtilAdapter.getInt("to_sign_op_thread_pool_size", 10);
    }

    /**
     * APP/PC待签约页-操作失败提示文案
     */
    public static String batchOpFailTips() {
        return ConfigUtilAdapter.getString("batch_op_fail_tips", "《{X}》等{Y}个合同签约失败，请联系您的业务经理");
    }

    /**
     * 是否反查上游状态
     */
    public static boolean isQueryUpstreamStatus() {
        return ConfigUtilAdapter.getBoolean("is_query_upstream_status", false);
    }

    /**
     * 反查上游状态延迟时间
     *
     * @return
     */
    public static int queryUpstreamStatusDelayTime() {
        return ConfigUtilAdapter.getInt("query_upstream_status_delay_time", 10);
    }

    /**
     * 反查上游状态最大延迟时间
     *
     * @return
     */
    public static int queryUpstreamStatusMaxDelayTime() {
        return ConfigUtilAdapter.getInt("query_upstream_status_max_delay_time", 500);
    }

    /**
     * 是否等待上游状态同步总开关
     */
    public static boolean isWaitForUpstreamStatusSwitch() {
        return ConfigUtilAdapter.getBoolean("is_wait_for_upstream_status_switch", false);
    }

    /**
     * 是否处理密文数据并发写入导致的脏数据
     */
    public static boolean isHandleConcurrentRecord() {
        return ConfigUtilAdapter.getBoolean("is_handle_concurrent_record", true);
    }

    /**
     * 批量打包短信签约页面-签约模板名称
     */
    public static String batchSignpageTemplateName() {
        return ConfigUtilAdapter.getString("batch_signpage_template_name", "wm_packed_sign_template_v2");
    }

    /**
     * 获取batchOp任务状态是否感知上游状态
     */
    public static boolean isBatchOpStatusWaitForUpstream() {
        return ConfigUtilAdapter.getBoolean("is_batch_op_status_wait_for_upstream", true);
    }

    /**
     * 冷数据配送范围批量插入每批大小
     */
    public static int recordContextAreaParitionSize() {
        return ConfigUtilAdapter.getInt("record_context_area_partition_size", 20);
    }

    /**
     * 异步归档合同开关
     */
    public static boolean asyncFilingSwitch() {
        return ConfigUtilAdapter.getBoolean("filing_async_switch", false);
    }

    /**
     * 异步归档开放比例
     */
    public static int asyncFilingProportion() {
        return ConfigUtilAdapter.getInt("filing_async_proportion", 0);
    }

    /**
     * 异步归档任务类型
     */
    public static String asyncFilingEcontractType() {
        return ConfigUtilAdapter.getString("async_filing_open_econtracttype", "");
    }

    /**
     * 配送范围文案-常规
     */
    public static String defaultDeliveryDocument() {
        return ConfigUtilAdapter.getString("DEFAULT_DELIVERY_DOCUMENT", EcontractAreaConstant.DEFAULT_DELIVERY_DOCUMENT);
    }

    /**
     * 配送范围文案-常规
     */
    public static String defaultDeliveryAggreDocument() {
        return ConfigUtilAdapter.getString("DEFAULT_DELIVERY_AGGRE_DOCUMENT", EcontractAreaConstant.DEFAULT_DELIVERY_AGGRE_DOCUMENT);
    }

    /**
     * 配送范围文案-常规
     */
    public static String defaultDeliverySlaWholeCityDocument() {
        return ConfigUtilAdapter.getString("DEFAULT_DELIVERY_SLA_WHOLE_CITY_DOCUMENT", EcontractAreaConstant.DEFAULT_DELIVERY_SLA_WHOLE_CITY_DOCUMENT);
    }

    /**
     * 配送范围文案-常规
     */
    public static String defaultDeliveryLongDistanceDocument() {
        return ConfigUtilAdapter.getString("DEFAULT_DELIVERY_COMPANY_CUSTOMER_LONG_DISTANCE_DOCUMENT", EcontractAreaConstant.DEFAULT_DELIVERY_COMPANY_CUSTOMER_LONG_DISTANCE_DOCUMENT);
    }

    /**
     * 配送范围文案-无人机
     */
    public static String defaultDroneDeliveryDocument() {
        return ConfigUtilAdapter.getString("DEFAULT_DRONE_DELIVERY_DOCUMENT", EcontractAreaConstant.DEFAULT_DRONE_DELIVERY_DOCUMENT);
    }

    /**
     * 获取外部签约数据重试次数
     */
    public static int queryEcontractDataRetryTimes() {
        return ConfigUtilAdapter.getInt("query_econtract_data_retry_times", 3);
    }

    /**
     * 大象异常信息告警人
     */
    public static List<String> getDaXiangAlarmMisList() {
        /**
         * taomengchun,lihaowei,zhongsihui,limingxuan,liuyunjie05,sunshuai07,liuyi60,chenyihao04,liuyu116,lixuepeng,zhaijiawen03,yinzhangfu,shenshuxin,wangzhenrong04,wangyongfang,wangyongchao04
         */
        String misIds = ConfigUtilAdapter.getString("da_xiang_alarm_mis", "liuyunjie05, zhanghaodong06");
        return COMM_SPLITTER.splitToList(misIds);
    }

    /**
     * 是否开始创建全局合同ID
     */
    public static Boolean isSyncGlobalContractId() {
        return ConfigUtilAdapter.getBoolean("is_sync_global_contract_id", false);
    }

    /**
     * 创建全局合同ID流量灰度 -1表示不全量 90表示放量90% 100表示全量
     */
    public static Integer getSyncGlobalContractIdGray() {
        return ConfigUtilAdapter.getInt("sync_global_contract_id_gray", 100);
    }

    /**
     * 是否需要使用redis缓存
     */
    public static Boolean isUseRedisCacheToGenerateGlobalId() {
        return ConfigUtilAdapter.getBoolean("is_use_redis_cache_to_generate_global_id", true);
    }

    /**
     * 默认缓存过期时间: 单位 秒
     */
    public static int getDefaultCacheExpirationSecond() {
        return ConfigUtilAdapter.getInt("default_cache_expiration_second", 60);
    }

    /**
     * 是否开启对比
     */
    public static boolean isCompareContextConsistency() {
        return ConfigUtilAdapter.getBoolean("is_compare_context_consistency", true);
    }

    /**
     * OnlyCreatePdf方法的门店数目上限值
     */
    public static Integer getTheUpperLimitOfOnlyCreatePdf() {
        return ConfigUtilAdapter.getInt("the_upper_limit_of_only_create_pdf", 300);
    }

    /**
     * 查询角色的最大查询次数
     */
    public static int getMaxQueryTimesForRoles() {
        return ConfigUtilAdapter.getInt("get_max_query_times_for_roles", 10);
    }

    /**
     * 电子合同平台-超级管理员  Uac
     */
    public static Long getSuperAdminRoleId() {
        return (long) ConfigUtilAdapter.getInt("get_super_admin_role_id", 10043211);
    }

    /**
     * 电子合同平台-合同配置RD
     */
    public static Long getContractConfigRDRoleId() {
        return (long) ConfigUtilAdapter.getInt("get_contract_config_rd_role_id", 10043066);
    }

    /**
     * 电子合同平台-合同配置业务
     */
    public static Long getContractConfigYWRoleId() {
        return (long) ConfigUtilAdapter.getInt("get_contract_config_yw_role_id", 10043210);
    }

    /**
     * 默认分页起始数字
     */
    public static Integer getDefaultPageNumber() {
        return ConfigUtilAdapter.getInt("get_default_page_number", 1);
    }

    /**
     * 默认分页大小
     */
    public static Integer getDefaultPageSize() {
        return ConfigUtilAdapter.getInt("get_default_page_size", 20);
    }


    public static Integer getDefaultRetryTimesToSendDineInServiceSms() {
        return ConfigUtilAdapter.getInt("default_retry_times_to_send_dine_in_service_sms", 5);
    }

    public static Integer getSignBatchEventRetryTime() {
        return ConfigUtilAdapter.getInt("sign_batch_event_retry_time", 3);
    }

    public static String getDcSignCompletePageInfo() {
        return ConfigUtilAdapter.getString("dc_sign_complete_page_info", "{\"additionalInfo\":\"合同生效后，您可以通过1>开店宝消息 2>手机短信查看。如有问题，请联系您的业务员或者拨打商服电话10105557\",\"detailUrlText\":\"点击查看详情\",\"detailUrl\":\"https://ecom.meituan.com/partner/kdb-pc/contract-list?source=waimai_xianfu\"}");
    }

    /**
     * 无感生效的重试次数
     */
    public static Integer getRetryTimesToSenselessEffect() {
        return ConfigUtilAdapter.getInt("retry_times_to_senseless_effect", 10);

    }

    /**
     * 更新 wm_econtract_record 时通过Mysql的lock判断任务是否已经终止
     */
    public static boolean updateEcontractRecordWithMysqlLockCheck() {
        return ConfigUtilAdapter.getBoolean("update_contract_record_with_mysql_lock_check", true);
    }

    /**
     * 是否删除重复的履约服务费处理逻辑
     */
    public static Boolean deleteDuplicatePerformanceHandle() {
        return ConfigUtilAdapter.getBoolean("delete_duplicate_performance_handle", true);
    }

    /**
     * 批量仅创建PDF链接的合同数量上限
     * @return
     */
    public static Integer batchOnlyCreaetPdfMaxSize() {
        // todo zyh 具体大小有待调整
        return ConfigUtilAdapter.getInt("batch_only_create_pdf_max_size", 10);
    }
    /**
     * 是否支持集成海螺  降级开关
     */
    public static Boolean supportIntegrationHaiLuo() {
        return ConfigUtilAdapter.getBoolean("is_support_integration_hai_luo", true);
    }

    /**
     * 先富APPCode 海螺
     *
     * @return 先富APPCode
     */
    public static String getXianFuAppCodeInHaiLuo() {
        return ConfigUtilAdapter.getString("xian_fu_app_code_in_hai_luo", "APP250604000001");
    }

    /**
     * 获取pdf创建的前缀链接
     * @return
     */
    public static String getPdfUrlPrefix() {
        return ConfigUtilAdapter.getString("pdf_url_prefix", "https://econtract.meituan.com");

    }

    /**
     * 获取电子合同幂等灰度
     *
     * @return Integer
     */
    public static Integer getEcontractIdempotentGray() {
        return ConfigUtilAdapter.getInt("econtract_idempotent_gray", 0);
    }
    
    public static int getPoiNameLimitToShow() {
        return ConfigUtilAdapter.getInt("poi_name_limit_to_show", 20);
    }
    
    public static String getHaiLuoContractTypeCode() {
        return ConfigUtilAdapter.getString("hai_luo_contract_type_code", "CT250819000004");
    }
    
    /**
     * 更新 wm_econtract_record 时，不包含SaveUrl, 通过Mysql的lock判断任务是否已经终止
     */
    public static boolean updateEcontractRecordNoSaveUrlWithMysqlLockCheck() {
        return ConfigUtilAdapter.getBoolean("update_record_no_url_with_mysql_lock_check", true);
    }
    
    public static int getAutoConfirmSignMaxConcurrentSize() {
        return ConfigUtilAdapter.getInt("auto_confirm_sign_max_concurrent_size", 1000);
    }
    
    public static long getDelayTimeToAutoConfirmSign() {
        return ConfigUtilAdapter.getLong("delay_time_to_auto_confirm_sign", 60);
    }
}

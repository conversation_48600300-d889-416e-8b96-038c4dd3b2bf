package com.sankuai.meituan.waimai.econtract.server.adapter.hailuo;

import com.meituan.it.contract.platform.model.request.template.QueryContractTemplateReq;
import com.meituan.it.contract.platform.model.response.template.QueryContractTemplateResp;
import com.meituan.it.contract.platform.service.template.ContractTemplateQueryThriftService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2025/4/8 19:37
 */
@Slf4j
@Service
public class ContractTemplateQueryThriftServiceAdapter {

    @Resource
    private ContractTemplateQueryThriftService contractTemplateQueryThriftService;

    public QueryContractTemplateResp queryContractTemplate(QueryContractTemplateReq req) {
        log.info("ContractTemplateQueryThriftServiceAdapter#queryContractTemplate, req:{}", req);
        QueryContractTemplateResp resp = contractTemplateQueryThriftService.queryContractTemplate(req);
        log.info("ContractTemplateQueryThriftServiceAdapter#queryContractTemplate, resp:{}", resp);
        return resp;
    }

}

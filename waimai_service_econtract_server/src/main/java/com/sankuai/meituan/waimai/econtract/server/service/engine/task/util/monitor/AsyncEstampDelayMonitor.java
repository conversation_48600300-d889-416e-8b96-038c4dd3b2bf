package com.sankuai.meituan.waimai.econtract.server.service.engine.task.util.monitor;

import com.alibaba.fastjson.JSON;
import com.meituan.mafka.client.bean.MafkaProducer;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.econtract.server.constants.EcontractAsyncTaskConstant;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractAsyncRecordMapper;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractAsyncRecordEntity;
import com.sankuai.meituan.waimai.econtract.server.utils.ServiceEnvUtils;
import com.sankuai.meituan.waimai.util.DaxiangUtilV2;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2022-01-21 12:00
 * Email: <EMAIL>
 * Desc:
 */
@Slf4j
@Service
public class AsyncEstampDelayMonitor {

    @Resource(name = "asyncTaskDelayProducer")
    private MafkaProducer asyncTaskDelayProducer;

    @Autowired
    private EcontractAsyncRecordMapper econtractAsyncRecordMapper;


    public void addAsyncTask(EcontractAsyncRecordEntity asyncRecord) {
        //消息发送至延迟队列
        String msg = JSON.toJSONString(asyncRecord);
        try {
            asyncTaskDelayProducer.sendMessage(msg);
        } catch (Exception e) {
            log.error("延迟消息发送失败，msg:{}", msg, e);
        }
    }

    public void checkDelayTask(EcontractAsyncRecordEntity asyncRecord) {
        String asyncId = asyncRecord.getAsyncId();
        EcontractAsyncRecordEntity asyncRecordEntity = econtractAsyncRecordMapper.selectByAsyncId(asyncId);
        if(EcontractAsyncTaskConstant.COMMIT.equals(asyncRecordEntity.getStatus())){
            pushDelayAlarm(asyncId);
        }
    }

    private void pushDelayAlarm(String asyncTaskId) {
        String receivers = ConfigUtilAdapter.getString("delay_alarm_receivers",
            "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>");
        String env = ServiceEnvUtils.getEnv();
        StringBuilder sb = new StringBuilder(env);
        sb.append("【签章平台异步处理超时】：异步任务ID：\n");
        sb.append(asyncTaskId);
        DaxiangUtilV2.push(sb.toString(), receivers.split(","));
    }
}

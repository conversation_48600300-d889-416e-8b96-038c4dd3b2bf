package com.sankuai.meituan.waimai.econtract.server.service.engine.task.Impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.itextpdf.text.DocumentException;
import com.meituan.mtrace.Tracer;
import com.sankuai.inf.octo.mns.util.ProcessInfoUtil;
import com.sankuai.meituan.waimai.econtract.server.annotation.EcontractIdempotent;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.bo.task.TaskContext;
import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import com.sankuai.meituan.waimai.econtract.server.constants.ContextConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractFtlTemplateEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordEntity;
import com.sankuai.meituan.waimai.econtract.server.exception.EcontractException;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractGlobalInfoService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractMetricService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractPdfService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.GlobalEcontractService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.Impl.EcontractFtlTemplateServiceImpl;
import com.sankuai.meituan.waimai.econtract.server.service.dto.GlobalContractInfoContext;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.TaskExecutor;
import com.sankuai.meituan.waimai.econtract.server.template.config.entity.EcontractGlobalInfoEntity;
import com.sankuai.meituan.waimai.econtract.server.template.config.service.EcontractTemplateBaseService;
import com.sankuai.meituan.waimai.econtract.server.utils.JacksonUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.global.GlobalEcontractGenerateRequestDTO;
import com.sankuai.meituan.waimai.econtrct.client.dto.SignContext;
import com.sankuai.meituan.waimai.econtrct.client.template.config.domain.EcontractTemplateBaseBo;
import com.sankuai.meituan.waimai.econtrct.client.template.config.domain.EcontractTemplateVersionBo;
import com.sankuai.meituan.waimai.econtrct.client.template.config.domain.EcontractTemplateVersionSimpleBo;
import com.sankuai.meituan.waimai.econtrct.client.template.config.exception.EcontractTemplateConfigException;
import com.sankuai.meituan.waimai.econtrct.client.template.config.service.EcontractTemplateConfigThriftService;
import com.sankuai.meituan.waimai.econtrct.client.util.MtCloudS3Util;
import com.sankuai.meituan.waimai.thrift.customer.constant.global.GlobalContractEnum;
import com.sankuai.meituan.waimai.util.DaxiangUtil;
import freemarker.template.TemplateException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.ap.internal.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR> Hou
 * @date 2017/10/25
 * @time 上午10:58
 */
@Slf4j
@Service
public class CreatePdfExecutor extends AbstractExecutor implements TaskExecutor {


    private static final Logger LOGGER = LoggerFactory.getLogger(CreatePdfExecutor.class);

    @Autowired
    private EcontractPdfService econtractPdfService;

    @Autowired
    private EcontractFtlTemplateServiceImpl econtractFtlTemplateService;

    @Autowired
    private EcontractTemplateConfigThriftService econtractTemplateConfigThriftService;

    @Resource
    private EcontractGlobalInfoService econtractGlobalInfoService;

    @Resource
    private GlobalEcontractService globalEcontractService;

    @Resource
    private EcontractTemplateBaseService econtractTemplateBaseService;

    @Resource
    private EcontractMetricService econtractMetricService;

    private static final String CREAT_PDF = "create_pdf";

    private static final Joiner COMMON_JOIN = Joiner.on(",").skipNulls();

    private static final Splitter COMM_SPLITTER = Splitter.on(",").trimResults();


    @Override
    @EcontractIdempotent
    public void executeTask(EcontractContext context) {
        try {
            LOGGER.info("CreatePdfExecutor start , recordkey : {} ", context.getEcontractRecordEntity().getRecordKey());
            String url;
            if (syncGlobalContractId(context.getEcontractRecordEntity())) {
                EcontractContext oldContext = JacksonUtil.getCopyOf(context, EcontractContext.class);
                SignContext signContext = globalEcontractService.extractSignContext(context);
                if (compareContextConsistency(oldContext, context)) {
                    url = createPdf(context.getTaskContext(), context.getEcontractRecordEntity().getRecordKey(), signContext);
                } else {
                    url = createPdf(oldContext.getTaskContext(), oldContext.getEcontractRecordEntity().getRecordKey(), signContext);
                }
            } else {
                url = createPdf(context.getTaskContext());
            }
            //数据太多生成PDF后清除数据
            context.getTaskContext().getStageInfoBo().getPdfContentInfoBoList().forEach(pdfContentInfoBo -> {
                pdfContentInfoBo.setFtlInfo(StringUtils.EMPTY);
            });
            if (StringUtils.isNotEmpty(url)) {
                context.getTaskContext().setExecutorResult(Maps.newHashMap());
                context.getTaskContext().getExecutorResult().put(TaskConstant.PDF_URL, url);
                context.setContextState(ContextConstant.CONTEXT_FINISH);
                executeSuccess(context);
                LOGGER.info("CreatePdfExecutor success , recordkey : {} , url : {}", context.getEcontractRecordEntity().getRecordKey(), url);
                metricExecutorResult(CREAT_PDF, true);
            } else {
                LOGGER.info("CreatePdfExecutor finish , recordkey : {} , url : {}", context.getEcontractRecordEntity().getRecordKey(), url);
                metricExecutorResult(CREAT_PDF, false);
            }
        } catch (Exception e) {
            metricExecutorResult(CREAT_PDF, false);
            LOGGER.error("fail to CreatePdfExecutor :" + context.getEcontractRecordEntity().getRecordKey() + ", msg = " + e.getMessage(), e);
            executeFail(context, e);
        }
    }

    private boolean compareContextConsistency(EcontractContext context1, EcontractContext context2) {
        try {
            if (!MccConfig.isCompareContextConsistency()) {
                return true;
            }
            boolean compareResult = false;
            try {
                compareResult = JacksonUtil.jsonStringsAreEqual(JacksonUtil.writeAsJsonStr(context1), JacksonUtil.writeAsJsonStr(context2));
            } catch (Exception e) {
                log.warn("CreatePdfExecutor#compareContextConsistency, 对比一致性失败, error", e);
            }
            if (!compareResult) {
                log.error("CreatePdfExecutor#compareContextConsistency, context1: {}, context2: {}",
                        JacksonUtil.writeAsJsonStr(context1), JacksonUtil.writeAsJsonStr(context2));
                DaxiangUtil.push("<EMAIL>", ProcessInfoUtil.getHostEnv() + "环境, 数据对比不一致, trace: " + Tracer.id(), MccConfig.getDaXiangAlarmMisList());
            }
            return compareResult;
        } catch (Exception e) {
            log.error("CreatePdfExecutor#compareContextConsistency, 数据对比失败, error", e);
            DaxiangUtil.push("<EMAIL>", ProcessInfoUtil.getHostEnv() + "环境, 数据对比失败, trace: " + Tracer.id(), MccConfig.getDaXiangAlarmMisList());
            return false;
        }
    }

    /**
     * @param taskContext taskContext
     * @param recordKey   recordKey
     * @param signContext 签约信息上下文
     * @return 合同PDF的URL
     */
    private String createPdf(TaskContext taskContext, String recordKey, SignContext signContext) throws IOException, TemplateException, DocumentException {
        // 识别是否需要创建pdf
        if (TaskConstant.CREATE_PDF.equals(taskContext.getStageInfoBo().getStageName())
                && CollectionUtils.isNotEmpty(taskContext.getStageInfoBo().getPdfContentInfoBoList())
                && StringUtils.isNotEmpty(taskContext.getStageInfoBo().getPdfContentInfoBoList().get(0).getPdfUrl())) {
            return generatePdfWithUrl(taskContext, recordKey, signContext);
        }
        return genePdfWithParam(taskContext, recordKey, signContext);
    }

    /**
     * @param taskContext taskContext
     * @param recordKey   recordKey
     * @param signContext 签约信息上下文
     * @return 合同PDF的URL
     */
    @VisibleForTesting
    protected String genePdfWithParam(TaskContext taskContext, String recordKey, SignContext signContext) throws IOException, TemplateException, DocumentException {
        List<String> nameList = Lists.newArrayList();
        taskContext.getStageInfoBo().getPdfContentInfoBoList().forEach(pdfContentInfoBo -> {
            if (StringUtils.isNotEmpty(pdfContentInfoBo.getPdfTemplateName())) {
                nameList.add(pdfContentInfoBo.getPdfTemplateName());
            }
        });
        List<Integer> templateIdList = new ArrayList<>();
        List<Integer> templateVersionList = new ArrayList<>();
        List<String> templateNameList = new ArrayList<>();
        // 统计使用老平台的模板 方便后续做迁移
        recordPdfTemplateName(nameList);
        //找出名字对应的配置模板
        Map<String, EcontractFtlTemplateEntity> ftlTemplateMap = econtractFtlTemplateService.batchSelectMapByNameList(nameList);
        //重写回去
        taskContext.getStageInfoBo().getPdfContentInfoBoList().forEach(pdfContentInfoBo -> {
            if (pdfContentInfoBo.getPdfTemplateId() != null && pdfContentInfoBo.getPdfTemplateId() > 0) {
                try {
                    if (pdfContentInfoBo.getPdfTemplateVersion() == null || pdfContentInfoBo.getPdfTemplateVersion() <= 0) {
                        /**
                         * 指定了模版id，但是未指定版本号的，兜底装配当前已发布版本号，如果这里版本为null或0则说明该模版没有已发布的版本。
                         * (在申请签约阶段已提前装配版本号， com.sankuai.meituan.waimai.econtract.server.service.api.EcontractAPIServiceImpl.initEcontractPdfTemplateVersion)
                         **/
                        EcontractTemplateVersionSimpleBo releasedVersion = econtractTemplateConfigThriftService.getReleasedTemplateVersionSimple(pdfContentInfoBo.getPdfTemplateId());
                        pdfContentInfoBo.setPdfTemplateVersion(releasedVersion != null ? releasedVersion.getVersion() : pdfContentInfoBo.getPdfTemplateVersion());
                    }
                    EcontractTemplateVersionBo econtractTemplateVersionBo = econtractTemplateConfigThriftService
                            .getTemplateVersionAndCheck(pdfContentInfoBo.getPdfTemplateId(), pdfContentInfoBo.getPdfTemplateVersion());
                    templateIdList.add(econtractTemplateVersionBo.getTemplateId());
                    templateVersionList.add(econtractTemplateVersionBo.getVersion());
                    LOGGER.info("使用配置的合同模版，模版ID={}，版本号={}，taskId={}", econtractTemplateVersionBo.getTemplateId(),
                            econtractTemplateVersionBo.getVersion(), taskContext.getTaskId());
                    pdfContentInfoBo.setFtlInfo(econtractTemplateVersionBo.getTargetContent());
                } catch (EcontractTemplateConfigException e) {
                    LOGGER.error("获取合同模版FTL失败，msg={}，taskId={}", e.getMessage(), taskContext.getTaskId());
                    EcontractException.PARAM_ERROR_EXCEPTION.newInstance(e.getMessage());
                }
                // 设置垂直显示
                pdfContentInfoBo.setVertical(true);
            } else if (ftlTemplateMap.get(pdfContentInfoBo.getPdfTemplateName()) != null) {
                pdfContentInfoBo.setFtlInfo(ftlTemplateMap.get(pdfContentInfoBo.getPdfTemplateName()).getFtlTemplate());
                String optionTemplate = ftlTemplateMap.get(pdfContentInfoBo.getPdfTemplateName()).getOptionTemplate();
                pdfContentInfoBo.setVertical(parseVertical(optionTemplate));
                templateNameList.add(pdfContentInfoBo.getPdfTemplateName());
            }
        });
        try {
            GlobalContractInfoContext globalContractInfoDTO = GlobalContractInfoContext.builder()
                    .taskType(taskContext.getTaskType())
                    .recordKey(recordKey)
                    .templateVersion(templateVersionList)
                    .templateId(templateIdList)
                    .templateName(templateNameList)
                    .build();
            buildAndInsertGlobalInfo(globalContractInfoDTO, signContext);
        } catch (Exception e) {
            log.error("CreatePdfExecutor#genePdfWithParam, error", e);
            DaxiangUtil.push("<EMAIL>", ProcessInfoUtil.getHostEnv() + "环境, 构建或插入全局合同信息异常, trace: " + Tracer.id(), MccConfig.getDaXiangAlarmMisList());
        }
        return econtractPdfService.createPdf(taskContext.getStageInfoBo().getPdfContentInfoBoList());
    }


    private String createPdf(TaskContext taskContext) throws IOException, TemplateException, DocumentException {
        //识别是否需要创建pdf
        if (TaskConstant.CREATE_PDF.equals(taskContext.getStageInfoBo().getStageName())
                && CollectionUtils.isNotEmpty(taskContext.getStageInfoBo().getPdfContentInfoBoList())
                && StringUtils.isNotEmpty(taskContext.getStageInfoBo().getPdfContentInfoBoList().get(0).getPdfUrl())) {
            return genePdfWithUrl(taskContext.getStageInfoBo().getPdfContentInfoBoList().get(0).getPdfUrl());
        }
        return genePdfWithParam(taskContext);
    }

    private String genePdfWithParam(TaskContext taskContext) throws IOException, TemplateException, DocumentException {
        List<String> nameList = Lists.newArrayList();
        taskContext.getStageInfoBo().getPdfContentInfoBoList().forEach(pdfContentInfoBo -> {
            if (StringUtils.isNotEmpty(pdfContentInfoBo.getPdfTemplateName())) {
                nameList.add(pdfContentInfoBo.getPdfTemplateName());
            }
        });
        //找出名字对应的配置模板
        Map<String, EcontractFtlTemplateEntity> ftlTemplateMap = econtractFtlTemplateService.batchSelectMapByNameList(nameList);
        //重写回去
        taskContext.getStageInfoBo().getPdfContentInfoBoList().forEach(pdfContentInfoBo -> {
            if (pdfContentInfoBo.getPdfTemplateId() != null && pdfContentInfoBo.getPdfTemplateId() > 0) {
                try {
                    if (pdfContentInfoBo.getPdfTemplateVersion() == null || pdfContentInfoBo.getPdfTemplateVersion() <= 0) {
                        /**
                         * 指定了模版id，但是未指定版本号的，兜底装配当前已发布版本号，如果这里版本为null或0则说明该模版没有已发布的版本。
                         * (在申请签约阶段已提前装配版本号， com.sankuai.meituan.waimai.econtract.server.service.api.EcontractAPIServiceImpl.initEcontractPdfTemplateVersion)
                         **/
                        EcontractTemplateVersionSimpleBo releasedVersion = econtractTemplateConfigThriftService.getReleasedTemplateVersionSimple(pdfContentInfoBo.getPdfTemplateId());
                        pdfContentInfoBo.setPdfTemplateVersion(releasedVersion != null ? releasedVersion.getVersion() : pdfContentInfoBo.getPdfTemplateVersion());
                    }
                    EcontractTemplateVersionBo econtractTemplateVersionBo = econtractTemplateConfigThriftService
                            .getTemplateVersionAndCheck(pdfContentInfoBo.getPdfTemplateId(), pdfContentInfoBo.getPdfTemplateVersion());
                    LOGGER.info("使用配置的合同模版，模版ID={}，版本号={}，taskId={}", econtractTemplateVersionBo.getTemplateId(),
                            econtractTemplateVersionBo.getVersion(), taskContext.getTaskId());
                    pdfContentInfoBo.setFtlInfo(econtractTemplateVersionBo.getTargetContent());
                } catch (EcontractTemplateConfigException e) {
                    LOGGER.error("获取合同模版FTL失败，msg={}，taskId={}", e.getMessage(), taskContext.getTaskId());
                    EcontractException.PARAM_ERROR_EXCEPTION.newInstance(e.getMessage());
                }
                // 设置垂直显示
                pdfContentInfoBo.setVertical(true);
            } else if (ftlTemplateMap.get(pdfContentInfoBo.getPdfTemplateName()) != null) {
                pdfContentInfoBo.setFtlInfo(ftlTemplateMap.get(pdfContentInfoBo.getPdfTemplateName()).getFtlTemplate());
                String optionTemplate = ftlTemplateMap.get(pdfContentInfoBo.getPdfTemplateName()).getOptionTemplate();
                pdfContentInfoBo.setVertical(parseVertical(optionTemplate));
            }
        });
        return econtractPdfService.createPdf(taskContext.getStageInfoBo().getPdfContentInfoBoList());
    }

    private void recordPdfTemplateName(List<String> nameList) {
        try {
            if (CollectionUtils.isEmpty(nameList)) {
                return;
            }
            nameList.forEach(econtractMetricService::metricPdfTemplateName);
        } catch (Exception e) {
            log.error("CreatePdfExecutor#recordPdfTemplateName, error", e);
        }
    }

    private String generatePdfWithUrl(TaskContext taskContext, String recordKey, SignContext signContext) {
        try {
            List<Integer> templateIdList = new ArrayList<>();
            List<Integer> templateVersionList = new ArrayList<>();
            List<String> templateNameList = new ArrayList<>();

            List<PdfContentInfoBo> pdfContentInfoBoList = taskContext.getStageInfoBo().getPdfContentInfoBoList();
            for (PdfContentInfoBo pdfContentInfoBo : pdfContentInfoBoList) {
                if (pdfContentInfoBo.getPdfTemplateId() != null && pdfContentInfoBo.getPdfTemplateId() > 0) {
                    templateIdList.add(pdfContentInfoBo.getPdfTemplateId());
                    templateVersionList.add(getTemplateVersion(pdfContentInfoBo));
                    continue;
                }
                if (!Strings.isEmpty(pdfContentInfoBo.getPdfTemplateName())) {
                    templateNameList.add(pdfContentInfoBo.getPdfTemplateName());
                }
            }
            GlobalContractInfoContext globalContractInfoDTO = GlobalContractInfoContext.builder()
                    .taskType(taskContext.getTaskType())
                    .recordKey(recordKey)
                    .templateVersion(templateVersionList)
                    .templateId(templateIdList)
                    .templateName(templateNameList)
                    .build();
            buildAndInsertGlobalInfo(globalContractInfoDTO, signContext);
        } catch (Exception e) {
            log.error("CreatePdfExecutor#genePdfWithParam, error", e);
            DaxiangUtil.push("<EMAIL>", ProcessInfoUtil.getHostEnv() + "环境, 构建或插入全局合同信息异常, trace: " + Tracer.id(), MccConfig.getDaXiangAlarmMisList());
        }
        String pdfUrl = taskContext.getStageInfoBo().getPdfContentInfoBoList().get(0).getPdfUrl();
        return genePdfWithUrl(pdfUrl);
    }

    private Integer getTemplateVersion(PdfContentInfoBo pdfContentInfoBo) {
        if (pdfContentInfoBo.getPdfTemplateVersion() == null || pdfContentInfoBo.getPdfTemplateVersion() <= 0) {
            EcontractTemplateVersionSimpleBo releasedVersion = econtractTemplateConfigThriftService.getReleasedTemplateVersionSimple(pdfContentInfoBo.getPdfTemplateId());
            return releasedVersion != null ? releasedVersion.getVersion() : pdfContentInfoBo.getPdfTemplateVersion();
        }
        return pdfContentInfoBo.getPdfTemplateVersion();
    }

    public String genePdfWithUrl(String originPdfUrl) {
        //下载
        byte[] pdfBytes = MtCloudS3Util.retryDownloadByTempUrl(originPdfUrl);
        //生成pdf名称
        String url = DigestUtils.md5Hex(pdfBytes);
        LOGGER.info("genePdfWithUrl originPdfUrl:{}, url:{}", originPdfUrl, url);
        //上传
        return MtCloudS3Util.retryUploadFileFromBytes(pdfBytes, url + ".pdf");
    }

    private boolean parseVertical(String optionTemplate) {
        if (StringUtils.isBlank(optionTemplate)) {
            return Boolean.TRUE;
        }
        JSONObject jo = JSON.parseObject(optionTemplate);
        if (jo.get("vertical") == null) {
            return Boolean.TRUE;
        }
        return jo.getBoolean("vertical");
    }

    /**
     * 构建并保存
     */
    private void buildAndInsertGlobalInfo(GlobalContractInfoContext globalContext, SignContext signContext) {
        try {
            EcontractGlobalInfoEntity globalInfo = buildEcontractGlobalInfoEntity(globalContext, signContext);
            insertEcontractGlobalInfo(globalInfo);
            reportEcontractTemplate(globalInfo);
        } catch (Exception e) {
            log.error("CreatePdfExecutor#buildAndInsertGlobalInfo, globalContext: {}, error", JSON.toJSONString(globalContext), e);
            DaxiangUtil.push("<EMAIL>", ProcessInfoUtil.getHostEnv() + "环境, 构建或插入全局合同信息异常, trace: " + Tracer.id(), MccConfig.getDaXiangAlarmMisList());
        }
    }

    /**
     * 是否创建全局合同ID
     */
    private boolean syncGlobalContractId(EcontractRecordEntity econtractRecordEntity) {
        try {
            if (!MccConfig.isSyncGlobalContractId()) {
                return false;
            }
            boolean grayResult;
            if (econtractRecordEntity == null) {
                log.warn("CreatePdfExecutor#syncGlobalContractId, 参数异常");
                grayResult = false;
            } else {
                grayResult = econtractRecordEntity.getId() == null || econtractRecordEntity.getId() % 100 < MccConfig.getSyncGlobalContractIdGray();
            }
            log.info("CreatePdfExecutor#syncGlobalContractId, grayResult: {}, econtractRecordEntity: {}", grayResult, JacksonUtil.writeAsJsonStr(econtractRecordEntity));
            return grayResult;
        } catch (Exception e) {
            log.error("CreatePdfExecutor#syncGlobalContractId, error", e);
            DaxiangUtil.push("<EMAIL>", ProcessInfoUtil.getHostEnv() + "环境, 全局灰度判断异常, trace: " + Tracer.id(), MccConfig.getDaXiangAlarmMisList());
            return false;
        }
    }

    private EcontractGlobalInfoEntity buildEcontractGlobalInfoEntity(GlobalContractInfoContext globalContractInfoContext, SignContext signContext) {
        try {
            if (globalContractInfoContext == null) {
                return null;
            }

            EcontractGlobalInfoEntity econtractGlobalInfo = new EcontractGlobalInfoEntity();
            int contractCategory = GlobalContractEnum.getContractCategory(globalContractInfoContext.getTaskType());
            Long globalContractId = generateGlobalEcontractId(contractCategory, globalContractInfoContext.getTaskType());
            econtractGlobalInfo.setGlobalContractId(globalContractId);
            econtractGlobalInfo.setRecordKey(globalContractInfoContext.getRecordKey());
            //  设置合同的PDF类型，如果任务类型为空，则默认为第三方合同类型。
            econtractGlobalInfo.setPdfType(Strings.isEmpty(globalContractInfoContext.getTaskType()) ? GlobalContractEnum.THIRD_PARTY_CONTRACT.getContractType() : globalContractInfoContext.getTaskType());
            econtractGlobalInfo.setContractTemplateId(COMMON_JOIN.join(globalContractInfoContext.getTemplateId()));
            econtractGlobalInfo.setContractTemplateVersion(COMMON_JOIN.join(globalContractInfoContext.getTemplateVersion()));
            econtractGlobalInfo.setContractTemplateName(COMMON_JOIN.join(globalContractInfoContext.getTemplateName()));
            econtractGlobalInfo.setContext(JacksonUtil.writeAsJsonStr(signContext));
            return econtractGlobalInfo;
        } catch (Exception e) {
            log.error("CreatePdfExecutor#buildEcontractGlobalInfoEntity, globalContractInfoDTO: {}, error", JacksonUtil.writeAsJsonStr(globalContractInfoContext), e);
            DaxiangUtil.push("<EMAIL>", ProcessInfoUtil.getHostEnv() + "环境, 创建全局信息异常, trace: " + Tracer.id(), MccConfig.getDaXiangAlarmMisList());
            return null;
        }
    }

    private Long generateGlobalEcontractId(int contractCategory, String taskType) {
        try {
            GlobalEcontractGenerateRequestDTO requestDTO = new GlobalEcontractGenerateRequestDTO();
            requestDTO.setContractCategory(contractCategory);
            requestDTO.setDate(System.currentTimeMillis());
            requestDTO.setTaskType(taskType);
            return globalEcontractService.generateGlobalEcontractId(requestDTO);
        } catch (Exception e) {
            throw new EcontractException(-1, e.getMessage());
        }
    }

    private void reportEcontractTemplate(final EcontractGlobalInfoEntity econtractGlobalInfo) {
        try {
            if (econtractGlobalInfo == null || Strings.isEmpty(econtractGlobalInfo.getContractTemplateId())) {
                return;
            }
            List<String> templateIdList = COMM_SPLITTER.splitToList(econtractGlobalInfo.getContractTemplateId());
            List<EcontractTemplateBaseBo> templateList = econtractTemplateBaseService.batchGetTemplateBaseByIdList(templateIdList);
            for (EcontractTemplateBaseBo econtractTemplateBaseBo : templateList) {
                String templateInfo = econtractTemplateBaseBo.getName() + "(" + econtractTemplateBaseBo.getId() + ")";
                econtractMetricService.metricTemplateDataCount(templateInfo);
            }
        } catch (Exception e) {
            log.error("CreatePdfExecutor#reportEcontractTemplate, econtractGlobalInfo: {}, error", JacksonUtil.writeAsJsonStr(econtractGlobalInfo), e);
        }
    }

    private void insertEcontractGlobalInfo(EcontractGlobalInfoEntity globalInfo) {
        try {
            econtractGlobalInfoService.insert(globalInfo);
        } catch (Exception e) {
            log.error("CreatePdfExecutor#insertEcontractGlobalInfo, globalInfo: {}, error", JacksonUtil.writeAsJsonStr(globalInfo), e);
            DaxiangUtil.push("<EMAIL>", ProcessInfoUtil.getHostEnv() + "环境, 插入全局合同信息异常, trace: " + Tracer.id(), MccConfig.getDaXiangAlarmMisList());
        }
    }

}
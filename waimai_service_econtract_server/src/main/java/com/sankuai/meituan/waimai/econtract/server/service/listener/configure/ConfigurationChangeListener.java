package com.sankuai.meituan.waimai.econtract.server.service.listener.configure;

import com.sankuai.meituan.waimai.util.wmzkconfig.ConfigUtilAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.configuration.event.ConfigurationEvent;
import org.apache.commons.configuration.event.ConfigurationListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 【Author】: 窦旭蒙
 * 【MIS】: douxumeng
 * 【Date】: 2022-09-06 20:28
 * 【Email】: <EMAIL>
 * 【Desc】: MCC信息变更的listener
 */
@Slf4j
@Service
public class ConfigurationChangeListener implements ConfigurationListener, InitializingBean {

    private ConcurrentHashMap<String, String> oldDataCache = new ConcurrentHashMap<>();

    @Autowired
    private List<ConfigurationChangeHandler> configurationChangeHandlerList;

    @Override
    public void configurationChanged(ConfigurationEvent event) {
        if (event.isBeforeUpdate()) {
            oldDataCache.put(event.getPropertyName(), ConfigUtilAdapter.getString(event.getPropertyName(), ""));
        } else {
            String oldValue = oldDataCache.get(event.getPropertyName());
            oldDataCache.remove(event.getPropertyName());

            //测试环境core.zookeeper一直在变 但是内容不变
            if (oldValue.equals(event.getPropertyValue().toString())) {
                return;
            }

            if (CollectionUtils.isNotEmpty(configurationChangeHandlerList)) {
                for (ConfigurationChangeHandler configurationChangeHandler : configurationChangeHandlerList) {
                    configurationChangeHandler.onChange(event.getPropertyName(), oldValue, event.getPropertyValue().toString());
                }
            }
            log.info("MCC变更监听，key:{}，oldValue:{}，newValue:{}", event.getPropertyName(), oldValue, event.getPropertyValue().toString());
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        ConfigUtilAdapter.getGolbalConfig().addConfigurationListener(this);
    }
}

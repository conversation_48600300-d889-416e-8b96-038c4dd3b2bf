package com.sankuai.meituan.waimai.econtract.server.utils;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractContentTypeEnum;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageInfoBo;
import joptsimple.internal.Strings;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <PERSON>
 * @date 2017/11/1
 * @time 下午2:02
 */
public class ContextUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(ContextUtils.class);


    public static StageInfoBo getStageInfoBoFromList(EcontractContext context, String stageName) {
        if (CollectionUtils.isNotEmpty(context.getStageInfoBoList())) {
            return getStageInfoBoFromSingleList(context, stageName);
        } else {
            return getStageInfoFromBatchList(context, stageName);
        }
    }

    public static StageInfoBo getStageInfoBoFromSingleList(EcontractContext context, String stageName) {
        List<StageInfoBo> stageInfoBoList = context.getStageInfoBoList().stream()
                .filter(n -> stageName.equals(n.getStageName()))
                .collect(Collectors.toList());
        return stageInfoBoList.isEmpty() ? new StageInfoBo() : stageInfoBoList.get(0);
    }

    public static StageInfoBo getStageInfoFromBatchList(EcontractContext context, String stageName) {
        List<StageBatchInfoBo> batchInfoBoList = context.getStageBatchInfoBoList().stream()
                .filter(n -> stageName.equals(n.getStageName()))
                .collect(Collectors.toList());

        if (batchInfoBoList.isEmpty()) {
            return new StageInfoBo();
        }
        return new StageInfoBo.Builder()
                .setSignerInfoBo(batchInfoBoList.get(0).getSignerInfoBo())
                .setEstampInfoBo(batchInfoBoList.get(0).getEstampInfoBo())
                .setCertifyInfoBo(batchInfoBoList.get(0).getCertifyInfoBo())
                .setPdfContentInfoBoList(batchInfoBoList.get(0).getPdfContentInfoBoMap() == null || batchInfoBoList.get(0).getPdfContentInfoBoMap().size() == 0 ?
                        Lists.newArrayList() : batchInfoBoList.get(0).getPdfContentInfoBoMap().entrySet().iterator().next().getValue())
                .setParamInfoBo(batchInfoBoList.get(0).getParamInfoBo())
                .setStageName(batchInfoBoList.get(0).getStageName())
                .setViewContentMap(MapUtils.isNotEmpty(batchInfoBoList.get(0).getStageAndViewContentMap()) ?
                        batchInfoBoList.get(0).getStageAndViewContentMap().entrySet().iterator().next().getValue() :
                        batchInfoBoList.get(0).getViewContentMap())//优先从stageAndViewContentMap中取，如果没有再从viewContentMap中取
                .setExtendDataBo(batchInfoBoList.get(0).getExtendDataBo())
                .build();
    }

    public static Map<String, StageInfoBo> getStageInfoBoMapFromList(EcontractContext context, String stageName) {
        LOGGER.info("getStageInfoBoMapFromList = context = {}, stageName = {}", JSON.toJSONString(context), stageName);
        if (CollectionUtils.isEmpty(context.getStageBatchInfoBoList())) {
            return Maps.newHashMap();
        }
        List<StageBatchInfoBo> stageBatchInfoBoList = context.getStageBatchInfoBoList().stream()
                .filter(n -> stageName.equals(n.getStageName()))
                .collect(Collectors.toList());
        LOGGER.info("stageBatchInfoBoList = {}", JSON.toJSONString(stageBatchInfoBoList));

        if (CollectionUtils.isEmpty(stageBatchInfoBoList)) {
            return Maps.newHashMap();
        }
        return batch2StageInfo(stageBatchInfoBoList.get(0));
    }

    /**
     * StageBatchInfoBo转为多个StageInfoBo
     */
    private static Map<String, StageInfoBo> batch2StageInfo(StageBatchInfoBo batchInfoBo) {
        Map<String, StageInfoBo> map = Maps.newHashMap();
        batchInfoBo.getMetaFlowList().forEach(module -> {
            StageInfoBo stageInfoBo = new StageInfoBo.Builder()
                    .setSignerInfoBo(MapUtils.isNotEmpty(batchInfoBo.getSignerInfoBoMap()) ? batchInfoBo.getSignerInfoBoMap().get(module) : batchInfoBo.getSignerInfoBo())
                    .setEstampInfoBo(MapUtils.isNotEmpty(batchInfoBo.getEstampInfoBoMap()) ? batchInfoBo.getEstampInfoBoMap().get(module) : batchInfoBo.getEstampInfoBo())
                    .setCertifyInfoBo(MapUtils.isNotEmpty(batchInfoBo.getCertifyInfoBoMap()) ? batchInfoBo.getCertifyInfoBoMap().get(module) : batchInfoBo.getCertifyInfoBo())
                    .setPdfContentInfoBoList(MapUtils.isNotEmpty(batchInfoBo.getPdfContentInfoBoMap()) ? batchInfoBo.getPdfContentInfoBoMap().get(module) : batchInfoBo.getPdfContentInfoBoList())
                    .setParamInfoBo(MapUtils.isNotEmpty(batchInfoBo.getParamInfoBoMap()) ? batchInfoBo.getParamInfoBoMap().get(module) : batchInfoBo.getParamInfoBo())
                    .setViewContentMap(MapUtils.isNotEmpty(batchInfoBo.getStageAndViewContentMap()) ? batchInfoBo.getStageAndViewContentMap().get(module) : batchInfoBo.getViewContentMap())
                    .setStageName(batchInfoBo.getStageName())
                    .build();

            map.put(module, stageInfoBo);
        });
        LOGGER.info("ContextUtils.map = {}", JSON.toJSONString(map));
        return map;
    }


    /**
     * 获取合同申请记录中viewContentMap的信息并以Map格式返回
     * 1.优先处理非批量数据，如果非批量数据不存在再处理批量数据
     * 2.如果按照非批量数据处理则返回的map中key为default，否则key为具体的类型
     *
     * @param context   合同申请记录中详细内容json格式
     * @return
     */
    public static Map<String,  Map<String, String> > getViewContentMap(EcontractContext context) {
        LOGGER.info("ContextUtils#getViewContentMap, context = {}", JacksonUtil.writeAsJsonStr(context));
        //如果存在非批量数据则从非批量数据中查econtract_view_content信息并返回
        if (CollectionUtils.isNotEmpty(context.getStageInfoBoList())) {
            Map<String,  Map<String, String>> map = new HashMap<>();
            StageInfoBo stageInfoBo = getStageInfoBoFromSingleList(context, TaskConstant.ECONTRACT_VIEW_CONTENT);
            LOGGER.info("ContextUtils#getViewContentMap, stageInfoBo = {}", JacksonUtil.writeAsJsonStr(stageInfoBo));
            map.put("default", stageInfoBo.getViewContentMap());
            return map;
        }
        if (CollectionUtils.isEmpty(context.getStageBatchInfoBoList())) {
            return Maps.newHashMap();
        }

        //组织批量数据中的econtract_view_content信息
        List<StageBatchInfoBo> stageBatchInfoBoList = context.getStageBatchInfoBoList().stream()
                .filter(n -> TaskConstant.ECONTRACT_VIEW_CONTENT.equals(n.getStageName()))
                .collect(Collectors.toList());
        LOGGER.info("ContextUtils#getViewContentMap, stageBatchInfoBoList = {}", JacksonUtil.writeAsJsonStr(stageBatchInfoBoList));
        if (CollectionUtils.isEmpty(stageBatchInfoBoList)) {
            return Maps.newHashMap();
        }
        StageBatchInfoBo stageBatchInfoBo = stageBatchInfoBoList.get(0);
        //如果批量数据中存在stageAndViewContentMap则直接返回，优先以stageAndViewContentMap中的数据为主
        if (!MapUtils.isEmpty(stageBatchInfoBo.getStageAndViewContentMap())) {
            return stageBatchInfoBo.getStageAndViewContentMap();
        }
        //如果批量数据中存在viewContentMap则组织数据并返回
        if(!MapUtils.isEmpty(stageBatchInfoBo.getViewContentMap())){
            Map<String,  Map<String, String>> map = new HashMap<>();
            map.put("default", stageBatchInfoBo.getViewContentMap());
            return map;
        }
        return Maps.newHashMap();

    }


    /**
     * 获取合同申请记录中的配送范围（spArea）信息
     *
     * @param context
     * @param stageName
     * @return 返回格式为map，key为tab类型，value为spArea具体值
     */
    public static Map<String, String> getSpAreaMap(EcontractContext context, String stageName) {
        LOGGER.info("getStageInfoBoMap = context = {}, stageName = {}", JSON.toJSONString(context), stageName);
        Map<String, Map<String, String>> map = getViewContentMap(context);
        if (MapUtils.isEmpty(map)) {
            return Maps.newHashMap();
        }
        //返回结果
        Map<String, String> result = new HashMap<>();

        Set<Map.Entry<String, Map<String, String>>> set = map.entrySet();
        Iterator<Map.Entry<String, Map<String, String>>> iterator = set.iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, Map<String, String>> entry = iterator.next();
            String data = entry.getValue().get(EcontractContentTypeEnum.SP_AREA.getName());
            if (StringUtils.isEmpty(data)) {
                continue;
            }
            result.put(entry.getKey(), data);
        }
        return result;
    }

    /**
     * 获取合同申请记录中的合同描述（contractDesc）信息
     *
     * @param context
     * @param stageName
     * @return 返回格式为String
     */
    public static String getContractDesc(EcontractContext context, String stageName) {
        LOGGER.info("getContractDesc context = {}, stageName = {}", JSON.toJSONString(context), stageName);
        Map<String, Map<String, String>> map = getViewContentMap(context);
        if (MapUtils.isEmpty(map)) {
            return StringUtils.EMPTY;
        }
        //返回结果
        String contractDesc = StringUtils.EMPTY;

        Set<Map.Entry<String, Map<String, String>>> set = map.entrySet();
        Iterator<Map.Entry<String, Map<String, String>>> iterator = set.iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, Map<String, String>> entry = iterator.next();
            String data = entry.getValue().get(EcontractContentTypeEnum.CONTRACT_DESC.getName());
            if (StringUtils.isEmpty(data)) {
                continue;
            }
            contractDesc = data;
        }
        return contractDesc;
    }

    /**
     * 获取合同申请记录中的合同描述（contractDesc）信息
     *
     * @param context
     * @param stageName
     * @return 返回格式为String
     */
    public static String getContractName(EcontractContext context, String stageName) {
        LOGGER.info("getContractDesc context = {}, stageName = {}", JSON.toJSONString(context), stageName);
        Map<String, Map<String, String>> map = getViewContentMap(context);
        if (MapUtils.isEmpty(map)) {
            return StringUtils.EMPTY;
        }
        //返回结果
        String contractDesc = StringUtils.EMPTY;

        Set<Map.Entry<String, Map<String, String>>> set = map.entrySet();
        Iterator<Map.Entry<String, Map<String, String>>> iterator = set.iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, Map<String, String>> entry = iterator.next();
            String data = entry.getValue().get(EcontractContentTypeEnum.CONTRACT_NAME.getName());
            if (StringUtils.isEmpty(data)) {
                continue;
            }
            contractDesc = data;
        }
        return contractDesc;
    }


}

package com.sankuai.meituan.waimai.econtract.server.utils;

import com.google.common.collect.Lists;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import static com.sankuai.meituan.waimai.econtract.server.utils.CryptTool.byte2hex;

/**
 * Created by zhangyuanbo02 on 16/4/26.
 */
public class CaSignUtil {

    private static final Logger log = LoggerFactory.getLogger(CaSignUtil.class);
    //设置超时时间
//    private static final int HTTP_SOCKET_TIMEOUT = 100000;
//    private static final int HTTP_CONNECT_TIMEOUT = 100000;
//    private static final int HTTP_CONNECTION_REQUEST_TIMEOUT = 100000;
    //3desc加密方式，这个表示最常用加密方式
    private static final String CRYPT_ALGORITHM = "DESede";

//    /**
//     * 说明：POST请求工具
//     *
//     * @return JSON 格式数据:{result:返回结果, code:结果代码,msg:描述,customer_id:客户编号}
//     */
//    public static String postUtil(Map<String, String> paramsTemp, String postUrl) throws ParseException, IOException {
//        try {
//            Map<String, String> params = paramsTemp;
//            HttpPost httpPost = new HttpPost(postUrl);
//            List<NameValuePair> kvs = Lists.newArrayList();
//            Iterator<Map.Entry<String, String>> it = params.entrySet().iterator();
//            while (it.hasNext()) {
//                Map.Entry<String, String> entry = it.next();
//                kvs.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
//            }
//            httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
//            httpPost.setEntity(new UrlEncodedFormEntity(kvs, StandardCharsets.UTF_8));
//            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(HTTP_SOCKET_TIMEOUT).setConnectionRequestTimeout(HTTP_CONNECTION_REQUEST_TIMEOUT).setConnectTimeout(HTTP_CONNECT_TIMEOUT).build();
//            httpPost.setConfig(requestConfig);
//
//            CloseableHttpClient httpClient = HttpClients.createDefault();
//            CloseableHttpResponse response = null;
//            String result;
//            try {
//                response = httpClient.execute(httpPost);
//                result = EntityUtils.toString(response.getEntity());
//                log.info("###http返回结果:{}", result);
//            } catch (Exception e) {
//                log.error("postUtil(), e={}", e);
//                result = "";
//            } finally {
//                if (response != null) {
//                    response.close();
//                }
//                httpPost.releaseConnection();
//                httpClient.close();
//            }
//            return result;
//        } catch (Exception e) {
//            log.error("获取个人或企业的CA证书异常，customer_name={},id_card或组织机构代码号={},e={}", e);
//        }
//        return "";
//    }

    public static String getMsgDigestForCa(String timeStr, String app_id, String app_secret) throws NoSuchAlgorithmException {
        String sha1 = CryptTool.sha1(app_id + MD5.md5Digest(timeStr) + CryptTool.sha1(app_secret));
        return new String(Base64.encode(sha1.getBytes()));
    }

    /**
     * 3DES加密模式
     */
    public static String encrypt3desc(String value, String key) throws NoSuchPaddingException, NoSuchAlgorithmException, InvalidKeyException, BadPaddingException, IllegalBlockSizeException {
        SecretKeySpec keySpec = new SecretKeySpec(key.getBytes(), CRYPT_ALGORITHM);
        Cipher cipher = Cipher.getInstance(CRYPT_ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, keySpec);
        byte[] encryptedByte = cipher.doFinal(value.getBytes());
        return byte2hex(encryptedByte);
    }

    /**
     * 3DES解密模式
     */
    public static String decrypt3desc(String value, String key) throws InvalidKeyException, NoSuchPaddingException, NoSuchAlgorithmException, BadPaddingException, IllegalBlockSizeException {
        SecretKeySpec keySpec = new SecretKeySpec(key.getBytes(), CRYPT_ALGORITHM);
        Cipher cipher = Cipher.getInstance(CRYPT_ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, keySpec);
        byte[] hexBytes = CryptTool.hex2byte(value);
        if (hexBytes.length == 0) {
            return null;
        }
        byte[] decryptedByte = cipher.doFinal(hexBytes);
        return new String(decryptedByte);
    }


    /**
     * 生成签章的Msg加密信息
     */
    public static String getMsgDigestForStamp(String timeStr, String app_id, String app_secret, String transaction_id, String customer_id) throws NoSuchAlgorithmException {
        String sha1 = CryptTool.sha1(app_secret + customer_id + "" + "");
        String md5 = MD5.md5Digest(transaction_id + "" + timeStr);
        String sha2 = CryptTool.sha1(app_id + md5.toString() + sha1);
        String base64 = new String(Base64.encode((sha2).getBytes()));
        return base64;
    }



    /**
     * 对入参进行排序
     */
    public static String getSortedParams(Map<String, String> params) {
        String result = "";
        if (MapUtils.isEmpty(params)) {
            return result;
        }
        List<String> keyList = new ArrayList<String>();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (entry.getKey() != null) {
                keyList.add(entry.getKey() + "=" + entry.getValue());
            }
        }
        result = StringUtils.join(keyList, "&");
        return result;
    }

    /**
     * 生成图片上传的Msg加密信息
     */
    public static String getMsgDigestForFileUpload(String timeStr, String contract_id, String app_id, String app_secret) {
        try {
            String sha2 = CryptTool.sha1(app_id + MD5.md5Digest(timeStr) +
                    CryptTool.sha1(app_secret + contract_id));
            String base64 = new String(Base64.encode(sha2.getBytes()));
            return base64;
        } catch (Exception e) {
            log.error("getMsgDigestForFileUpload(),e={}", e);
        }
        return null;
    }



}

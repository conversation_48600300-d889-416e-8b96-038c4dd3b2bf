package com.sankuai.meituan.waimai.econtract.server.crane;

import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executors;
import com.google.common.util.concurrent.*;
import com.sankuai.meituan.waimai.econtract.server.bo.EcontractSubContextBo;
import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import com.sankuai.meituan.waimai.econtract.server.exception.EcontractException;
import com.sankuai.meituan.waimai.econtract.server.service.encryption.EncryptionUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.cip.crane.client.spring.annotation.Crane;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractRecordEntityMapper;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractRecordContextMapper;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordEntity;
import com.sankuai.meituan.waimai.econtract.server.constants.EcontractRecordConstant;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractTaskEntityMapper;
import com.sankuai.meituan.waimai.econtrct.client.constants.TaskConstant;
import org.apache.commons.collections.CollectionUtils;
import com.sankuai.meituan.waimai.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import com.alibaba.fastjson.JSON;
import javax.annotation.Nullable;

/**
 * 电子合同数据清理(尽可能删除能删除的数据)定时job
 *
 * 热数据表有mysql2hive的同步任务，每天凌晨00：00执行
 * http://xt.sankuai.com/workbench/task/hmart_waimai.fact_wm_econtract_record_view/version/4212912
 *
 * Created by lixuepeng on 2021/9/18
 */
@Component
public class RecordDataCleanTask {

    private static final Logger LOGGER = LoggerFactory.getLogger(RecordDataCleanTask.class);

    private static Calendar calendar = Calendar.getInstance();

    private static final ListeningExecutorService executorService;

    static {
        executorService = MoreExecutors.listeningDecorator(Executors.newFixedThreadPool(20));
    }

    @Autowired
    private EcontractRecordEntityMapper  econtractRecordEntityMapper;
    @Autowired
    private EcontractRecordContextMapper econtractRecordContextMapper;
    @Autowired
    private EcontractTaskEntityMapper    econtractTaskEntityMapper;

    private static final int ONEDAYSECOND = 24 * 60 * 60;

    @Crane("disk.optimization.record.data.clean")
    public void handleRecordDataClean(String offsetDayStrAndLastIdAndSleepMillsAndSize) {
        try {
            List<String> inputStrList = Arrays.asList(offsetDayStrAndLastIdAndSleepMillsAndSize.split("#"));
            String offsetDayStr = inputStrList.get(0);
            String lastIdStr = inputStrList.get(1);
            String sleepMillsStr = inputStrList.get(2);
            String perPageSizeStr = inputStrList.get(3);

            int offsetDay = Integer.valueOf(offsetDayStr);
            int latsTime =  DateUtil.unixTime() - offsetDay * ONEDAYSECOND;//记录截止删除时间
            Date lastDate = DateUtil.fromUnixTime(latsTime);
            long lastId = Long.valueOf(lastIdStr);
            int size = Integer.valueOf(perPageSizeStr);
            List<EcontractRecordEntity> econtractRecordEntities = econtractRecordEntityMapper.queryDeleteEntityListWithLabel4Clean(lastId, lastDate, size);

            LOGGER.info("handleRecordDataClean startTime:{}", DateUtil.unixTime());
            while (CollectionUtils.isNotEmpty(econtractRecordEntities)) {
                CountDownLatch latch = new CountDownLatch(econtractRecordEntities.size());
                //多线程处理当前批次任务
                for (EcontractRecordEntity recordEntity : econtractRecordEntities) {
                    ListenableFuture<Boolean> future = executorService.submit(new Callable() {
                        @Override
                        public Boolean call() throws Exception {
                            //（失败or成功）且任务状态为结束
                            if ((EcontractRecordConstant.FAIL.equals(recordEntity.getEcontractState())
                                    || EcontractRecordConstant.SUCCESS.equals(recordEntity.getEcontractState()))
                                    && TaskConstant.ECONTRACT_FINISH.equals(recordEntity.getEcontractStage())) {
                                //处理热数据
                                handleHotData(recordEntity);
                                //更新热数据信息
                                econtractRecordEntityMapper.updateOptimisticLock(recordEntity);
                                //删除冷数据
                                econtractRecordContextMapper.deleteByRecordId(recordEntity.getId());
                                //删除对应task任务
                                econtractTaskEntityMapper.deleteByRecordId(recordEntity.getId());
                                return true;
                            }
                            return false;
                        }
                    });
                    Futures.addCallback(future, new FutureCallback<Boolean>() {
                        @Override
                        public void onSuccess(@Nullable Boolean result) {
                            latch.countDown();
                        }
                        @Override
                        public void onFailure(Throwable t) {
                            LOGGER.error("handleRecordDataClean 执行数据清理任务异常 异常id:{}", recordEntity.getId());
                            latch.countDown();
                        }
                    });
                }
                latch.await();

                //时间判断-不在执行时间则结束
                if (MccConfig.dataCleanLimitHour() < getHourByDate(new Date())) {
                    break;
                }

                Thread.sleep(Long.valueOf(sleepMillsStr));//延迟一定时间后再执行下一批
                //翻页获取下一页信息
                lastId = econtractRecordEntities.get(econtractRecordEntities.size() -1).getId();
                econtractRecordEntities = econtractRecordEntityMapper.queryDeleteEntityListWithLabel4Clean(lastId, lastDate, size);
            }
            LOGGER.info("handleRecordDataClean endTime:{}, lastId:{}", DateUtil.unixTime(), lastId);
        } catch (Exception e) {
            LOGGER.error("handleRecordDataClean 执行数据清理任务异常", e);
        }
    }

    //热数据不能直接删除，因此只保留关键信息
    private void handleHotData(EcontractRecordEntity recordEntity) {
        try {
            if (StringUtils.isNotEmpty(recordEntity.getEcontractRecordContext())) {
                EcontractContext OriginContext = JSON.parseObject(recordEntity.getEcontractRecordContext(), EcontractContext.class);
                EcontractContext finalContext = new EcontractContext();
                //只保存签章平台合同id信息
                finalContext.setTaskTypeAndContractIdMap(OriginContext.getTaskTypeAndContractIdMap());
                recordEntity.setEcontractRecordContext(JSON.toJSONString(finalContext));
                return;
            }
            return;
        } catch (Exception e) {
            //异常情况下，说明没有反序列化成功，可能是老数据格式导致
            recordEntity.setEcontractRecordContext("");
            LOGGER.error("handleHotData recordEntityId:{}", recordEntity.getId(), e);
        }
    }

    public static int getHourByDate(Date date) {
        if (date == null) {
            return 0;
        }
        synchronized (calendar) {
            calendar.setTime(date);
            return calendar.get(Calendar.HOUR_OF_DAY);
        }
    }
}

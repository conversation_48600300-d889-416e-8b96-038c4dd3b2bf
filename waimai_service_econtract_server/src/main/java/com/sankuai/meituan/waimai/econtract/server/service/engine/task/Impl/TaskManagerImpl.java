package com.sankuai.meituan.waimai.econtract.server.service.engine.task.Impl;

import com.google.common.collect.Maps;
import com.sankuai.meituan.util.ConfigUtilAdapter;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.constants.MccConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.MqConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtract.server.service.SpringBeanUtil;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.TaskExecutor;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.TaskManager;
import com.sankuai.meituan.waimai.econtract.server.service.mq.TaskMsg;
import com.sankuai.meituan.waimai.econtract.server.service.mq.TaskProducer;

import com.sankuai.meituan.waimai.econtract.server.service.mq.mafka.producer.adapter.TaskCommitProducerAdaptor;
import com.sankuai.meituan.waimai.econtract.server.service.mq.mafka.producer.adapter.TaskExecutorProducerAdaptor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;

/**
 * <AUTHOR> Hou
 * @date 2017/10/20
 * @time 上午11:03
 */
@Service
public class TaskManagerImpl implements TaskManager {

    private static final Logger LOGGER = LoggerFactory.getLogger(TaskManagerImpl.class);

    public static HashMap<String, TaskExecutor> executorRepository = Maps.newHashMap();

    @Autowired
    private TaskProducer taskProducer;

    @Resource
    private TaskExecutorProducerAdaptor taskExecutorProducerAdaptor;

    @Resource
    private TaskCommitProducerAdaptor taskCommitProducerAdaptor;

    private final String MAFKA_MIGRATE_PROPORTION = "mafka_migrate_proportion";

    @Override
    public void init() {
        executorRepository.put(TaskConstant.ESTAMP_EXECUTOR, (TaskExecutor) SpringBeanUtil.getBean(TaskConstant
                .ESTAMP_EXECUTOR));
        executorRepository.put(TaskConstant.CA_CERTIFY_EXECUTOR, (TaskExecutor) SpringBeanUtil.getBean(TaskConstant
                .CA_CERTIFY_EXECUTOR));
        executorRepository.put(TaskConstant.CREATE_PDF_EXECUTOR, (TaskExecutor) SpringBeanUtil.getBean(TaskConstant
                .CREATE_PDF_EXECUTOR));
        executorRepository.put(TaskConstant.SMS_EXECUTOR, (TaskExecutor) SpringBeanUtil.getBean(TaskConstant
                .SMS_EXECUTOR));
        executorRepository.put(TaskConstant.UPLOAD_EXECUTOR, (TaskExecutor) SpringBeanUtil.getBean(TaskConstant
                .UPLOAD_EXECUTOR));
        executorRepository.put(TaskConstant.FILING_ECONTRACT_EXECUTOR, (TaskExecutor) SpringBeanUtil.getBean
                (TaskConstant.FILING_ECONTRACT_EXECUTOR));
        executorRepository.put(TaskConstant.FINISH_EXECUTOR, (TaskExecutor) SpringBeanUtil.getBean(TaskConstant
                .FINISH_EXECUTOR));

    }

    @Override
    public void submitTask(TaskMsg msg, boolean isMafka) {
        msg.setMessageType(MqConstant.MSG_EXECUTE);
        LOGGER.info("submitTask#主题waimai.econtract.task.execute迁移至mafka");
        msg.setQueueName(setQueueName(msg.getMessageBody()));
        if (msg.getContextId() != null) {
            msg.setMessageBody(null);
        }
        try {
            taskExecutorProducerAdaptor.sendMessage(msg);
        } catch (Exception e) {
            LOGGER.error("submitTask#mafka主题waimai.econtract.task.execute消息发送失败，消息体:{}", JSON.toJSONString(msg), e);
        }
    }


    @Override
    public void commitTask(TaskMsg msg, boolean isMafka) {
        msg.setMessageType(MqConstant.MSG_COMMIT);
        LOGGER.info("commitTask#主题waimai.econtract.task.commit.topic迁移至mafka");
        msg.setQueueName(setQueueName(msg.getMessageBody()));
        if (msg.getContextId() != null) {
            msg.setMessageBody(null);
        }
        try {
            taskCommitProducerAdaptor.sendMessage(msg);
        } catch (Exception e) {
            LOGGER.error("commitTask#mafka主题waimai.econtract.task.commit.topic消息发送失败，消息体:{}", JSON.toJSONString(msg), e);
        }
    }

    @Override
    public void failTask(EcontractContext context) {

    }

    @Override
    public void executeTask(EcontractContext context) {


        //重试开关
        boolean mafkaRetry = ConfigUtilAdapter.getBoolean(MccConstant.MAFKA_RETRY, false);
        if (mafkaRetry) {
            //签章平台上传pdf和上传签单延时队列存放
            boolean isCondition = TaskConstant.ESTAMP_EXECUTOR.equalsIgnoreCase(context.getTaskContext()
                    .getExecutorType()) || TaskConstant
                    .UPLOAD_EXECUTOR.equalsIgnoreCase(context.getTaskContext().getExecutorType());
            boolean isRetryTimesLessThanThree = context.getTaskContext() != null && context.getTaskContext()
                    .getRetryTimes() < 3;
            if (isCondition && isRetryTimesLessThanThree) {
                TaskMsg msg = getTaskMsg(context);
                taskProducer.kafkaProduceTask(msg);
            }
        }
        executorRepository.get(context.getTaskContext().getExecutorType()).executeTask(context);
    }

    /**
     * 获取执行消息
     *
     * @param context
     * @return
     */
    private TaskMsg getTaskMsg(EcontractContext context) {
        TaskMsg msg = new TaskMsg();
        msg.setMessageType(MqConstant.MSG_EXECUTE);
        msg.setMessageBody(JSON.toJSONString(context));
        msg.setEcontractRecordKey(context.getEcontractRecordEntity().getRecordKey());
        return msg;
    }

    @Override
    public boolean mafkaMigrateSwitch() {
        Integer migrateProportion = (int) (Math.random() * 100);
        return migrateProportion < ConfigUtilAdapter.getInt(MAFKA_MIGRATE_PROPORTION, 0);
    }

    private String setQueueName(String messageBody){
        if(StringUtils.isEmpty(messageBody)){
            LOGGER.error("消息发送设置队列失败，messageBody为空");
        }
        EcontractContext context = JSON.parseObject(messageBody, EcontractContext.class);
        return context.getQueueName();
    }

}

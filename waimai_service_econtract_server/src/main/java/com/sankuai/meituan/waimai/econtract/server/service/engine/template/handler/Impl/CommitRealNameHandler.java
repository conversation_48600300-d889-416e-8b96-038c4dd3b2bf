package com.sankuai.meituan.waimai.econtract.server.service.engine.template.handler.Impl;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.bo.task.TaskContext;
import com.sankuai.meituan.waimai.econtract.server.bo.task.TaskNodeBo;
import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import com.sankuai.meituan.waimai.econtract.server.constants.ContextConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.RealNameConstanst;
import com.sankuai.meituan.waimai.econtract.server.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractTaskEntity;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractRealNameAuthService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractRecordService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractTaskService;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.TaskManager;
import com.sankuai.meituan.waimai.econtract.server.service.engine.template.handler.TaskHandler;
import com.sankuai.meituan.waimai.econtract.server.service.engine.template.processor.Impl.AbstractProcessor;
import com.sankuai.meituan.waimai.econtract.server.service.mq.TaskMsg;
import com.sankuai.meituan.waimai.econtract.server.utils.ContextUtils;
import com.sankuai.meituan.waimai.econtract.server.utils.TimeUtils;
import com.sankuai.meituan.waimai.econtrct.client.constants.CallbackConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.SignerInfoBo;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR> Hou
 * @date 2017/11/26
 * @time 下午1:23
 */
@Service
public class CommitRealNameHandler extends AbstractProcessor implements TaskHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(CommitRealNameHandler.class);

    @Autowired
    private EcontractRecordService econtractRecordService;
    @Autowired
    private EcontractTaskService econtractTaskService;
    @Autowired
    private TaskManager taskManager;
    @Autowired
    private EcontractRealNameAuthService realNameAuthService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleTask(EcontractContext context) {
        LOGGER.info("econtract context record : {} commit realName : {} ", context.getEcontractRecordEntity().getRecordKey(),context.getCurrentTaskNode().getTaskName());
        TaskNodeBo currentTaskNode = context.getCurrentTaskNode();
        TaskNodeBo nextTaskNode = currentTaskNode.getNextTask();
        TaskNodeBo callBackTaskNode = currentTaskNode.getCallBackTask();
        currentTaskNode.setTaskState(TaskConstant.TASK_SUCCESS);
        nextTaskNode.setTaskState(TaskConstant.TASK_RUNNIG);

        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(context.getEcontractRecordEntity().getRecordKey());


        //1.更新老task
        EcontractTaskEntity oldTaskEntity = econtractTaskService.selectByPrimaryKey(currentTaskNode.getTaskId());
        EcontractTaskEntity callBackTaskEntity = econtractTaskService.selectByPrimaryKey(callBackTaskNode.getTaskId());
        oldTaskEntity.setTaskState(TaskConstant.TASK_SUCCESS);
        oldTaskEntity.setUtime(TimeUtils.getUTime());
        callBackTaskEntity.setTaskState(TaskConstant.TASK_SUCCESS);
        callBackTaskEntity.setUtime(TimeUtils.getUTime());
        econtractTaskService.updateByPrimaryKeySelective(oldTaskEntity);
        econtractTaskService.updateByPrimaryKeySelective(callBackTaskEntity);


        //2.更新record状态，存储URL
        recordEntity.setUtime(TimeUtils.getUTime());
        context.setCurrentTaskNode(nextTaskNode);
        context.setContextState(ContextConstant.CONTEXT_TO_EXECUTE);
        recordEntity.setEcontractRecordContext(JSON.toJSONString(context));
        econtractRecordService.updateByPrimaryKeySelective(recordEntity);

        //3.更新实名认证池
        SignerInfoBo signerInfoBo = null;
        if (context.getSingerStep().equals(TaskConstant.SIGNER_STEP_A)) {
            signerInfoBo = ContextUtils.getStageInfoBoFromList(context, TaskConstant.REAL_NAME_AUTH_A).getSignerInfoBo();
        } else if (context.getSingerStep().equals(TaskConstant.SIGNER_STEP_B)) {
            signerInfoBo = ContextUtils.getStageInfoBoFromList(context, TaskConstant.REAL_NAME_AUTH_B).getSignerInfoBo();
        } else if (context.getSingerStep().equals(TaskConstant.SIGNER_STEP_C)) {
            signerInfoBo = ContextUtils.getStageInfoBoFromList(context, TaskConstant.REAL_NAME_AUTH_C).getSignerInfoBo();
        } else if (context.getSingerStep().equals(TaskConstant.SIGNER_STEP_D)) {
            signerInfoBo = ContextUtils.getStageInfoBoFromList(context, TaskConstant.REAL_NAME_AUTH_D).getSignerInfoBo();
        } else if (context.getSingerStep().equals(TaskConstant.SIGNER_STEP_E)) {
            signerInfoBo = ContextUtils.getStageInfoBoFromList(context, TaskConstant.REAL_NAME_AUTH_E).getSignerInfoBo();
        } else if (context.getSingerStep().equals(TaskConstant.SIGNER_STEP_F)) {
            signerInfoBo = ContextUtils.getStageInfoBoFromList(context, TaskConstant.REAL_NAME_AUTH_F).getSignerInfoBo();
        } else if (context.getSingerStep().equals(TaskConstant.SIGNER_STEP_G)) {
            signerInfoBo = ContextUtils.getStageInfoBoFromList(context, TaskConstant.REAL_NAME_AUTH_G).getSignerInfoBo();
        } else if (context.getSingerStep().equals(TaskConstant.SIGNER_STEP_H)) {
            signerInfoBo = ContextUtils.getStageInfoBoFromList(context, TaskConstant.REAL_NAME_AUTH_H).getSignerInfoBo();
        }

        String dealVersion = context.getTaskContext().getExecutorResult().get(CallbackConstant.REAL_NAME_DEAL_VERSION);
        if ("true".equals(context.getTaskContext().getExecutorResult().get(CallbackConstant.REAL_NAME_AUTH_RESULT))) {
            realNameAuthService.refreshRealNameAuthPool(signerInfoBo, context.getEcontractUserEntity().getId(), RealNameConstanst.SUCCESS, dealVersion);
        }


        if (currentTaskNode.isNeedNotify()) {
            TaskContext taskContext = createNotifierTaskContext(context,recordEntity.getEcontractStage(),TaskConstant.TASK_SUCCESS);
            doNotifier(context, taskContext);
        }

        String contextStr = JSON.toJSONString(context);
        if (context.getUseMafka() && contextStr.getBytes().length > MccConfig.MESSAGE_SIZE){
            context.setContextId(messageProducerHandler.getEntityId(contextStr));
        }

    }

    @Override
    public void handleTaskWithNoTransactionalMessage(EcontractContext context) {
        CommitRealNameHandler commitRealNameHandler = (CommitRealNameHandler) AopContext
                .currentProxy();
        LOGGER.info("#commitRealNameHandler={}", commitRealNameHandler);
        boolean isMafka = taskManager.mafkaMigrateSwitch();
        context.setUseMafka(isMafka);
        commitRealNameHandler.handleTask(context);
        TaskMsg taskMsg = handleMessage(context,isMafka);
        taskManager.commitTask(taskMsg,isMafka);
    }
}

package com.sankuai.meituan.waimai.econtract.server.service.api;

import com.google.common.collect.Maps;

import com.alibaba.fastjson.JSON;
import com.meituan.mtrace.Tracer;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import com.sankuai.meituan.waimai.econtract.server.constants.EcontractRecordConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.MqConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtract.server.entity.*;
import com.sankuai.meituan.waimai.econtract.server.exception.EcontractException;
import com.sankuai.meituan.waimai.econtract.server.service.biz.*;
import com.sankuai.meituan.waimai.econtract.server.service.biz.Impl.FrameContractConfigService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.Impl.data.EcontractApplySubDataService;
import com.sankuai.meituan.waimai.econtract.server.service.engine.template.manager.TemplateManager;
import com.sankuai.meituan.waimai.econtract.server.service.sms.EcontractSmsActivityService;
import com.sankuai.meituan.waimai.econtract.server.utils.EcontractStampRouteUtil;
import com.sankuai.meituan.waimai.econtract.server.utils.MapUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.*;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.*;
import com.sankuai.meituan.waimai.econtrct.client.service.EcontractAPIResponse;
import com.sankuai.meituan.waimai.econtrct.client.service.EcontractAPIService;

import com.sankuai.meituan.waimai.econtrct.client.template.config.domain.EcontractTemplateVersionSimpleBo;
import com.sankuai.meituan.waimai.econtrct.client.template.config.service.EcontractTemplateConfigThriftService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.ap.internal.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;


/**
 * 电子合同业务方提交流程、重发短信接口
 *
 * <AUTHOR> Hou
 * @date 2017/10/21
 * @time 上午10:30
 */
@Service
public class EcontractAPIServiceImpl implements EcontractAPIService {


    private static final Logger LOGGER = LoggerFactory.getLogger(EcontractAPIServiceImpl.class);

    @Autowired
    private EcontractUserService econtractUserService;
    @Autowired
    private EcontractService econtractService;
    @Autowired
    private EcontractRecordService recordService;
    @Autowired
    private TemplateManager templateManager;
    @Autowired
    private EcontractTaskService taskService;
    @Autowired
    private EcontractUserService userService;
    @Autowired
    private EcontractRealNameAuthService realNameAuthService;
    @Autowired
    private EcontractTemplateConfigThriftService econtractTemplateConfigThriftService;
    @Autowired
    private EcontractSignRecordBatchService econtractSignRecordBatchService;
    @Autowired
    private EcontractSignRecordBatchEventService econtractSignRecordBatchEventService;
    @Autowired
    private EcontractMetricService econtractMetricService;
    @Autowired
    private EcontractApplySubDataService econtractApplySubDataService;

    @Resource
    private FrameContractConfigService frameContractConfigService;

    @Resource
    private EcontractSmsActivityService econtractSmsActivityService;

    // 这里之所以新加一个SUCCESS_CODE, 是因为前端是根据0做的判断
    private static final Integer SUCCESS_CODE = 0;

    @Override
    public EcontractAPIResponse applyEcontract(EcontractBo econtractBo) {
        LOGGER.info("receive applyEcontract : {}", JSON.toJSONString(econtractBo));
        EcontractContext econtractContext = new EcontractContext();
        try {
            checkKeyInfo(econtractBo);
            initEcontractInfo(econtractContext, econtractBo);
            String uuid = templateManager.applyEcontractByTemplate(econtractContext, econtractBo);
            Map<String, String> data = Maps.newHashMap();
            data.put(EcontractAPIResponseConstant.UUID, uuid);
            metricApplyCount(econtractBo.getEcontractType(), econtractBo.getRecordBatchId());
            LOGGER.info("receive applyEcontract success,recordKey:{}", uuid);
            return apiSuccessResponse(data);
        } catch (Exception e) {
            LOGGER.error("fail to applyEcontract  ", e);
            return apiFailResponse(e);
        }
    }

    /**
     * 提交任务入口
     *
     * @param batchBo
     * @return
     */
    @Override
    public EcontractAPIResponse applyBatchEcontract(EcontractBatchBo batchBo) {
        try {
            LOGGER.info("receive applyBatchEcontract: {}", JSON.toJSONString(batchBo));
            EcontractContext econtractContext = new EcontractContext();
            // 从外部系统补齐电子签约数据
            econtractApplySubDataService.completeEcontractData(batchBo);
            frameContractConfigService.completeContractInfoAfterConfig(batchBo);
            LOGGER.info("final applyBatchEcontract: {}", JSON.toJSONString(batchBo));
            // 对象转化
            initEcontractBatchInfo(econtractContext, batchBo);
            String uuid = templateManager.applyEcontractBatchByTemplet(econtractContext, batchBo);
            Map<String, String> data = Maps.newHashMap();
            data.put(EcontractAPIResponseConstant.UUID, uuid);
            metricApplyCount(batchBo.getEcontractType(), batchBo.getRecordBatchId());
            LOGGER.info("receive applyBatchEcontract success, recordKey:{}", uuid);
            return apiSuccessResponse(data);
        } catch (Exception e) {
            LOGGER.error("EcontractAPIServiceImpl#applyBatchEcontract, error", e);
            return apiFailResponse(e);
        }
    }

    /**
     * 上游状态同步
     *
     * @param recordKey
     * @param statusEnum
     * @return
     */
    public EcontractAPIResponse syncUpstreamStatus(String recordKey, UpstreamStatusEnum statusEnum) {
        LOGGER.info("syncUpstreamStatus recordKey : {}, statusEnum : {}", recordKey, statusEnum);
        recordService.updateUpstreamStatusByRecordKey(recordKey, statusEnum.getCode());
        return apiSuccessResponse();
    }

    @Override
    public EcontractAPIResponse queryEcontractByRecordKey(String token, String recordKey) {
        LOGGER.info("queryEcontractByRecordKey recordKey : {}, userToken : {}", recordKey, token);
        try {
            checkKeyInfo(recordKey, token);
            EcontractRecordEntity recordEntity = recordService.queryRecordByRecordKey(recordKey);
            Map<String, String> map = Maps.newHashMap();
            if (null != recordEntity && recordEntity.getValid() == EcontractRecordConstant.valid) {
                map = MapUtil.java2Map(recordEntity);
            }
            return apiSuccessResponse(map);
        } catch (Exception e) {
            LOGGER.error("fail to queryEcontractByRecordKey  ", e);
            return apiFailResponse(e);
        }
    }

    @Override
    public EcontractAPIResponse cancelEContractByRecordKey(String token, String recordKey) {
        LOGGER.info("cancelEContractByRecordKey recordKey : {}, userToken : {}", recordKey, token);
        try {
            checkKeyInfo(recordKey, token);
            templateManager.cancelSignEContract(recordKey, TaskConstant.SERVER_CANCEL);
            return apiSuccessResponse();
        } catch (Exception e) {
            LOGGER.error("fail to dropEcontractByRecordKey  ", e);
            return apiFailResponse(e);
        }
    }

    @Override
    public EcontractAPIResponse callBackEcontractByRecordKey(String recordKey, String callType, Map<String, String> paramMap) {
        LOGGER.info("callBackCommitTemplateStage recordKey : {}, callType : {}", recordKey, callType);
        try {
            templateManager.callBackCommitTemplateStage(recordKey, callType, paramMap);
            return apiSuccessResponse();
        } catch (Exception e) {
            LOGGER.error("fail to callBackEcontractByRecordKey  ", e);
            return apiFailResponse(e);
        }

    }

    @Override
    public EcontractAPIResponse callBackEcontractByRealNamDealVersion(String dealVersion, String callType, Map<String, String> paramMap) {
        try {
            EcontractRealNameDealEntity realNameDealEntity = realNameAuthService.queryRealNameDealEntityByDealVersion(dealVersion);
            if (null == realNameDealEntity) {
                EcontractException.REAL_NAME_EXCEPTION.newInstance("can not find realNameDealEntity : deal version is " + dealVersion);
            }
            EcontractTaskEntity taskEntity = taskService.selectByPrimaryKey(realNameDealEntity.getEcontractTaskId());
            EcontractRecordEntity recordEntity = recordService.selectByPrimaryKey(taskEntity.getEcontractRecordId());
            callBackEcontractByRecordKey(recordEntity.getRecordKey(), callType, paramMap);
            return apiSuccessResponse();
        } catch (Exception e) {
            LOGGER.error("fail to callBackEcontractByTaskId  ", e);
            return apiFailResponse(e);
        }
    }

    @Override
    public EcontractAPIResponse doRetrySms(String recordKey, String token) {
        try {
            LOGGER.info("doRetrySms recordKey = {}, token = {}", recordKey, token);
            checkKeyInfo(recordKey, token);
            List<String> phoneList = templateManager.doRetrySms(recordKey);
            if (CollectionUtils.isEmpty(phoneList)) {
                return apiSuccessResponse(Maps.newHashMap());
            }

            Map<String, String> resultMap = Maps.newHashMap();
            resultMap.put("mobiles", StringUtils.join(phoneList, ","));
            return apiSuccessResponse(resultMap);
        } catch (Exception e) {
            LOGGER.warn("fail to doRetrySms recordKey ={} e={}", recordKey, e);
            return apiFailResponse(e);
        }
    }

    @Override
    public EcontractAPIResponse doRetrySmsForBatch(String recordKey, String token) {
        try {
            LOGGER.info("doRetrySmsForBatch recordKey = {}, token = {}", recordKey, token);
            checkKeyInfo(recordKey, token);
            List<String> phoneList = templateManager.doRetrySmsForBatch(recordKey);
            if (CollectionUtils.isEmpty(phoneList)) {
                return apiSuccessResponse(Maps.newHashMap());
            }
            Map<String, String> resultMap = Maps.newHashMap();
            resultMap.put("mobiles", StringUtils.join(phoneList, ","));
            return apiSuccessResponse(resultMap);
        } catch (Exception e) {
            LOGGER.warn("fail to doRetrySmsForBatch recordKey ={} e={}", recordKey, e);
            return apiFailResponse(e);
        }
    }


    private EcontractAPIResponse apiSuccessResponse() {
        EcontractAPIResponse econtractAPIResponse = new EcontractAPIResponse();
        econtractAPIResponse.setCode(EcontractAPIResponseConstant.SUCCESS_CODE);
        econtractAPIResponse.setStatus(EcontractAPIResponseConstant.SUCCESS);
        return econtractAPIResponse;
    }

    private EcontractAPIResponse apiSuccessResponse(Map<String, String> dataMap) {
        EcontractAPIResponse econtractAPIResponse = new EcontractAPIResponse();
        econtractAPIResponse.setCode(EcontractAPIResponseConstant.SUCCESS_CODE);
        econtractAPIResponse.setStatus(EcontractAPIResponseConstant.SUCCESS);
        econtractAPIResponse.setReturnData(dataMap);
        return econtractAPIResponse;
    }

    private EcontractAPIResponse apiSuccessResponse(Map<String, String> dataMap, int code) {
        EcontractAPIResponse econtractAPIResponse = new EcontractAPIResponse();
        econtractAPIResponse.setCode(code);
        econtractAPIResponse.setStatus(EcontractAPIResponseConstant.SUCCESS);
        econtractAPIResponse.setReturnData(dataMap);
        return econtractAPIResponse;
    }

    private EcontractAPIResponse apiFailResponse(Exception e) {
        LOGGER.info("apiFailResponse::e = {}", e);
        EcontractAPIResponse econtractAPIResponse = new EcontractAPIResponse();
        if (e instanceof EcontractException) {
            EcontractException e1 = (EcontractException) e;
            econtractAPIResponse.setCode(e1.getErrorCode());
            econtractAPIResponse.setMsg(e.getMessage());
            econtractAPIResponse.setStatus(EcontractAPIResponseConstant.FAIL);
        } else {
            econtractAPIResponse.setCode(EcontractAPIResponseConstant.SERVER_ERROR);
            econtractAPIResponse.setMsg("server error");
            econtractAPIResponse.setStatus(EcontractAPIResponseConstant.FAIL);
        }
        return econtractAPIResponse;
    }


    /**
     * 检查关键信息
     */
    private void checkKeyInfo(EcontractBo econtractBo) throws EcontractException {
        if (null == econtractBo) {
            EcontractException.PARAM_ERROR_EXCEPTION.newInstance("econtractBo is null");
        }
        if (StringUtils.isEmpty(econtractBo.getToken())) {
            EcontractException.PARAM_ERROR_EXCEPTION.newInstance("user token is null");
        }
        if (StringUtils.isEmpty(econtractBo.getEcontractType())) {
            EcontractException.PARAM_ERROR_EXCEPTION.newInstance("econtract type is null");
        }
    }

    private void checkKeyInfo(String recordKey, String userToken) throws EcontractException {
        if (StringUtils.isEmpty(userToken)) {
            EcontractException.PARAM_ERROR_EXCEPTION.newInstance("user token is null");
        }
        if (!userService.hasEcontractRecord(userToken, recordKey)) {
            EcontractException.PARAM_ERROR_EXCEPTION.newInstance("do not have authority");
        }
    }

    private void checkKeyInfo(String userToken) throws EcontractException {
        if (StringUtils.isEmpty(userToken)) {
            EcontractException.PARAM_ERROR_EXCEPTION.newInstance("user token is null");
        }
        if (userService.queryUserByToken(userToken) == null) {
            EcontractException.PARAM_ERROR_EXCEPTION.newInstance("do not have authority");
        }
    }


    /**
     * 检查是否有权限
     * 设置用户信息至上下文
     * 设置合同模板信息至上下文
     */
    private EcontractContext initEcontractInfo(EcontractContext econtractContext, EcontractBo econtractBo) throws EcontractException {
        EcontractUserEntity userEntity = econtractUserService.queryUserByToken(econtractBo.getToken());
        if (null == userEntity) {
            EcontractException.PARAM_ERROR_EXCEPTION.newInstance("can not find user");
        }
        EcontractEntity econtractEntity = econtractService.queryEcontractByUserIdAndType(userEntity.getId(), econtractBo.getEcontractType());
        if (null == econtractEntity) {
            EcontractException.PARAM_ERROR_EXCEPTION.newInstance("can not find user econtract type");
        }
        econtractContext.setEcontractUserEntity(userEntity);
        econtractEntity.setProcedureTemplate(null);//由于ProcedureTemplate数据量太大，因此不存储在context中
        econtractContext.setEcontractEntity(econtractEntity);
        econtractContext.setStageInfoBoList(econtractBo.getStageInfoBoList());

        if (TaskConstant.ECONTRACT_TYPE_WM_EXCLUSIVE.equals(econtractEntity.getEcontractType())) {
            econtractContext.setTemplateProcessorType(TaskConstant.WM_EXCLUSIVE_PROCESSOR);
        } else {
            econtractContext.setTemplateProcessorType(TaskConstant.COMMON_PROCESSOR);
        }

        econtractContext.setCallBackUrl(econtractBo.getCallBackUrl());
        econtractContext.setRecordBizKey(econtractBo.getEcontractName());
        if (StringUtils.isNotEmpty(econtractBo.getEcontractBizId())) {
            econtractContext.setRecordBizKey(econtractBo.getEcontractBizId());
        }
        econtractContext.setStampKey(EcontractStampRouteUtil.getRouteKey());

        if (MccConfig.getReleasedTemplateVersionAutowired()) {
            // 填入pdf模版版本
            initEcontractPdfTemplateVersion(econtractContext);
        }

        //传入了签约链接有效期天数
        if (econtractBo.getSignUrlExpireDays() != null) {
            econtractContext.setSignUrlExpireDays(econtractBo.getSignUrlExpireDays());
        }

        econtractContext.setRecordSource(econtractBo.getEcontractBatchSource() == null ?
                EcontractRecordSourceEnum.NORMAL : econtractBo.getEcontractBatchSource());

        if (recordDistributeGray(econtractBo.getEcontractBizId())) {
            //任务分流
            recordService.distributeRecord(econtractContext, econtractBo);
        } else {
            //总开关关闭时默认走原队列
            econtractContext.setQueueName(MqConstant.FAST_QUEUE);
        }

        econtractContext.setCallBackDsl(econtractBo.getCallBackDsl());
        econtractContext.setIsWaitForUpstream(econtractBo.getIsWaitForUpstream());
        econtractContext.setIsCallBackByMQ(econtractBo.getIsCallBackByMQ());
        econtractContext.setAppKey(Tracer.getRemoteAppKey());

        return econtractContext;
    }

    /**
     * 批量流程上下文初始化
     */
    private EcontractContext initEcontractBatchInfo(EcontractContext econtractContext, EcontractBatchBo econtractBatchBo) throws EcontractException {
        EcontractUserEntity userEntity = econtractUserService.queryUserByToken(econtractBatchBo.getToken());
        if (userEntity == null) {
            EcontractException.PARAM_ERROR_EXCEPTION.newInstance("can not find user");
        }
        EcontractEntity econtractEntity = econtractService.queryEcontractByUserIdAndType(userEntity.getId(), econtractBatchBo.getEcontractType());
        if (econtractEntity == null) {
            EcontractException.PARAM_ERROR_EXCEPTION.newInstance("can not find user econtract type");
        }
        econtractContext.setEcontractUserEntity(userEntity);
        econtractEntity.setProcedureTemplate(null);//由于ProcedureTemplate数据量太大，因此不存储在context中
        econtractContext.setEcontractEntity(econtractEntity);
        econtractContext.setBatch(Boolean.TRUE);
        econtractContext.setFlowList(econtractBatchBo.getFlowList());
        econtractContext.setStageBatchInfoBoList(econtractBatchBo.getStageInfoBoList());
        econtractContext.setTemplateProcessorType(TaskConstant.COMMON_PROCESSOR);
        econtractContext.setCallBackUrl(econtractBatchBo.getCallBackUrl());
        econtractContext.setRecordBizKey(econtractBatchBo.getEcontractName());
        econtractContext.setRecordBatchId(econtractBatchBo.getRecordBatchId());
        econtractContext.setRecordSource(econtractBatchBo.getEcontractBatchSource() == null ?
                EcontractRecordSourceEnum.NORMAL : econtractBatchBo.getEcontractBatchSource());//source兼容存量任务，需设置默认值
        econtractContext.setEcontractType(econtractBatchBo.getEcontractType());
        if (StringUtils.isNotEmpty(econtractBatchBo.getEcontractBizId())) {
            econtractContext.setRecordBizKey(econtractBatchBo.getEcontractBizId());
        }
        econtractContext.setStampKey(EcontractStampRouteUtil.getRouteKey());

        if (MccConfig.getReleasedTemplateVersionAutowired()) {
            // 填入pdf模版版本-批量流程
            initEcontractBatchPdfTemplateVersion(econtractContext);
        }

        //传入了签约链接有效期天数
        if (econtractBatchBo.getSignUrlExpireDays() != null) {
            econtractContext.setSignUrlExpireDays(econtractBatchBo.getSignUrlExpireDays());
        }

        if (recordDistributeGray(econtractBatchBo.getEcontractBizId())) {
            //任务分流
            recordService.distributeRecord(econtractContext, econtractBatchBo);
        } else {
            //总开关关闭时默认走原队列
            econtractContext.setQueueName(MqConstant.FAST_QUEUE);
        }

        econtractContext.setCallBackDsl(econtractBatchBo.getCallBackDsl());
        econtractContext.setIsWaitForUpstream(econtractBatchBo.getIsWaitForUpstream());
        econtractContext.setIsCallBackByMQ(econtractBatchBo.getIsCallBackByMQ());
        econtractContext.setIsAreaSeperateSave(econtractBatchBo.getIsAreaSeperateSave());
        econtractContext.setAreaSeperateSaveWmPoiIdList(econtractBatchBo.getAreaSeperateSaveWmPoiIdList());
        econtractContext.setAppKey(Tracer.getRemoteAppKey());

        return econtractContext;
    }

    @Override
    public EcontractAPIResponse getSmsShortLink(String recordKey, String token) {
        try {
            LOGGER.info("getSmsShortLink recordKey = {}, token = {}", recordKey, token);
            checkKeyInfo(recordKey, token);
            String shortLink = templateManager.getShortLink(recordKey);
            Map<String, String> resultMap = Maps.newHashMap();
            resultMap.put("shortLink", shortLink);
            return apiSuccessResponse(resultMap);
        } catch (Exception e) {
            LOGGER.warn("fail to getSmsShortLink recordKey ={} e={}", recordKey, e);
            return apiFailResponse(e);
        }
    }

    @Override
    public EcontractAPIResponse getSmsShortLinkIgnoreToken(String recordKey) {
        try {
            LOGGER.info("EcontractAPIServiceImpl#getSmsShortLinkIgnoreToken, recordKey = {}", recordKey);
            String shortLink = templateManager.getShortLink(recordKey);
            Map<String, String> resultMap = Maps.newHashMap();
            resultMap.put("shortLink", shortLink);
            return apiSuccessResponse(resultMap, SUCCESS_CODE);
        } catch (Exception e) {
            LOGGER.warn("EcontractAPIServiceImpl#getSmsShortLinkIgnoreToken, error", e);
            return apiFailResponse(e);
        }
    }


    /**
     * 流程上下文初始化-填入pdf模版版本
     */
    private void initEcontractPdfTemplateVersion(EcontractContext econtractContext) {
        // 非批量流程专用
        if (CollectionUtils.isEmpty(econtractContext.getStageInfoBoList())) {
            return;
        }
        for (StageInfoBo stageInfoBo : econtractContext.getStageInfoBoList()) {
            // 找到生成PDF任务参数
            if (TaskConstant.CREATE_PDF.equals(stageInfoBo.getStageName()) && CollectionUtils.isNotEmpty(stageInfoBo.getPdfContentInfoBoList())) {
                for (PdfContentInfoBo pdfContentInfoBo : stageInfoBo.getPdfContentInfoBoList()) {
                    // 指定了模版id，但是未指定版本号的
                    if (pdfContentInfoBo.getPdfTemplateId() != null && pdfContentInfoBo.getPdfTemplateId() > 0 && (pdfContentInfoBo.getPdfTemplateVersion() == null || pdfContentInfoBo.getPdfTemplateVersion() <= 0)) {
                        EcontractTemplateVersionSimpleBo releasedVersion = econtractTemplateConfigThriftService.getReleasedTemplateVersionSimple(pdfContentInfoBo.getPdfTemplateId());
                        pdfContentInfoBo.setPdfTemplateVersion(releasedVersion != null ? releasedVersion.getVersion() : pdfContentInfoBo.getPdfTemplateVersion());
                    }
                }
            }
        }

    }

    /**
     * 批量流程上下文初始化-填入pdf模版版本
     */
    private void initEcontractBatchPdfTemplateVersion(EcontractContext econtractContext) {
        // 批量流程专用
        if (CollectionUtils.isEmpty(econtractContext.getStageBatchInfoBoList())) {
            return;
        }
        for (StageBatchInfoBo stageBatchInfoBo : econtractContext.getStageBatchInfoBoList()) {
            // 找到生成PDF任务参数
            if (TaskConstant.CREATE_PDF.equals(stageBatchInfoBo.getStageName()) && stageBatchInfoBo.getPdfContentInfoBoMap() != null) {
                for (Map.Entry<String, List<PdfContentInfoBo>> entry : stageBatchInfoBo.getPdfContentInfoBoMap().entrySet()) {
                    for (PdfContentInfoBo pdfContentInfoBo : entry.getValue()) {
                        // 指定了模版id，但是未指定版本号的
                        if (pdfContentInfoBo.getPdfTemplateId() != null && pdfContentInfoBo.getPdfTemplateId() > 0 && (pdfContentInfoBo.getPdfTemplateVersion() == null || pdfContentInfoBo.getPdfTemplateVersion() <= 0)) {
                            EcontractTemplateVersionSimpleBo releasedVersion = econtractTemplateConfigThriftService.getReleasedTemplateVersionSimple(pdfContentInfoBo.getPdfTemplateId());
                            pdfContentInfoBo.setPdfTemplateVersion(releasedVersion != null ? releasedVersion.getVersion() : pdfContentInfoBo.getPdfTemplateVersion());
                        }
                    }
                }
            }
        }

    }

    @Override
    public EcontractAPIResponse applyEcontractBatchNum(ApplyBatchNumBo batchNumBo) {
        if (batchNumBo == null) {
            EcontractException.PARAM_ERROR_EXCEPTION.newInstance("param batchNumBo is null");
        }
        LOGGER.info("applyEcontractBatchNum param: {}", JSON.toJSONString(batchNumBo));
        checkKeyInfo(batchNumBo.getToken());
        EcontractSignRecordBatchEntity batchEntity = initRecordBatchEntity(batchNumBo);
        int batchNum = econtractSignRecordBatchService.insertRecordBatch(batchEntity);
        Map<String, String> data = Maps.newHashMap();
        data.put(EcontractAPIResponseConstant.BATCH_NUM, String.valueOf(batchNum));
        metricBatchApplyCount(batchNumBo.getBizLine());
        return apiSuccessResponse(data);
    }

    private EcontractSignRecordBatchEntity initRecordBatchEntity(ApplyBatchNumBo batchNumBo) {
        EcontractSignRecordBatchEntity signRecordBatchEntity = new EcontractSignRecordBatchEntity();
        signRecordBatchEntity.setBizId(batchNumBo.getBizId());
        signRecordBatchEntity.setBizLine(batchNumBo.getBizLine());
        signRecordBatchEntity.setCommitUid(batchNumBo.getCommitUid());
        signRecordBatchEntity.setForceAllOp(batchNumBo.getForceAllOp());
        signRecordBatchEntity.setBatchStatus(BatchSignStatusEnum.CREATE_BATCH.getCode());
        signRecordBatchEntity.setVersion(1);
        return signRecordBatchEntity;
    }

    @Override
    public EcontractAPIResponse completeApplyBatchRecord(String token, Long batchId) {
        try {
            LOGGER.info("completeApplyBatchRecord token = {}, batchId = {}", token, batchId);
            if (batchId == null) {
                EcontractException.PARAM_ERROR_EXCEPTION.newInstance("param batchId is null");
            }
            checkKeyInfo(token);
            EcontractSignBatchEventEntity event = new EcontractSignBatchEventEntity();
            event.setBatchId(batchId.intValue());
            event.setEventType(BatchSignEventEnum.COMPLETE_BATCH.name());
            event.setStatus(BatchEventStatusEnum.NO_EXE.getCode());
            //存在并发场景，读主库
            List<EcontractSignBatchEventEntity> eventList = econtractSignRecordBatchEventService.getSignBatchEventListByBatchId(batchId.intValue());
            if (CollectionUtils.isEmpty(eventList)) {
                eventList = econtractSignRecordBatchEventService.getSignBatchEventListByBatchIdMaster(batchId.intValue());
            }
            if (CollectionUtils.isEmpty(eventList)) {
                econtractSignRecordBatchEventService.insertSelective(event);
                econtractSignRecordBatchService.updateRecordBatchStatusByBatchId(batchId.intValue(), BatchSignStatusEnum.COMPLETE_BATCH.getCode());
            } else if (eventList.size() == 1) {
                LOGGER.warn("completeApplyBatchRecord触发幂等校验，token:{}，batchId:{}", token, batchId);
            } else {
                LOGGER.error("completeApplyBatchRecord产生脏数据，token:{}，batchId:{}", token, batchId);
            }
            Map<String, String> resultMap = Maps.newHashMap();
            resultMap.put("batchId", String.valueOf(batchId));
            return apiSuccessResponse(resultMap);
        } catch (EcontractException e) {
            LOGGER.warn("fail to completeApplyBatchRecord batchId ={} e={}", batchId, e);
            return apiFailResponse(e);
        }
    }

    private void metricApplyCount(String econtractType, String recordBatchId) {
        econtractMetricService.metricAllApplyCount(econtractType, recordBatchId);
    }

    private void metricBatchApplyCount(Integer bizLine) {
        econtractMetricService.metricBatchApplyCount(bizLine);
    }

    private boolean recordDistributeGray(String econtractBizId) {
        if (ConfigUtilAdapter.getBoolean("mq_chaifen_total_switch", false)) {
            if (StringUtils.isEmpty(econtractBizId)) {
                econtractBizId = String.valueOf((int) (1 + Math.random() * 10000));
            }
            int recordHashCode = Math.abs(econtractBizId.hashCode());
            return recordHashCode % 10000 <= ConfigUtilAdapter.getInt("mq_chaifen_gary_proportion", 0);
        }
        return false;
    }
}

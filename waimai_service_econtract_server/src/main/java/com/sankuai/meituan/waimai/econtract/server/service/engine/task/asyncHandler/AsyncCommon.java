package com.sankuai.meituan.waimai.econtract.server.service.engine.task.asyncHandler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.econtract.server.adapter.EsignClient;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractAsyncRecordMapper;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractAsyncRecordEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordContextEntity;
import com.sankuai.meituan.waimai.econtract.server.exception.EcontractException;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractMetricService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractRecordService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.Impl.EcontractBigRecordParseService;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.Impl.AbstractExecutor;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.Impl.EstampExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 【Author】: 窦旭蒙
 * 【MIS】: douxumeng
 * 【Date】: 2022-12-15 18:05
 * 【Email】: <EMAIL>
 * 【Desc】:
 */
@Slf4j
@Service
public class AsyncCommon {

    @Autowired
    EsignClient esignClient;

    @Autowired
    AbstractExecutor abstractExecutor;

    @Autowired
    EstampExecutor estampExecutor;

    @Autowired
    EcontractRecordService econtractRecordService;

    @Autowired
    EcontractBigRecordParseService econtractBigRecordParseService;

    @Autowired
    EcontractAsyncRecordMapper econtractAsyncRecordMapper;

    @Autowired
    EcontractMetricService econtractMetricService;

    public EcontractContext parseContext(EcontractAsyncRecordEntity econtractAsyncRecord) {
        EcontractContext context = JSONObject.parseObject(econtractAsyncRecord.getTaskContext(), EcontractContext.class);
        if (null == context) {
            log.error("AsyncCommon 异步处理热数据为空，msg:{}", JSON.toJSONString(econtractAsyncRecord));
            throw new EcontractException(EcontractException.ASYNC_HANDLER_ERROR, "异步处理冷数据为空");
        }
        Integer econtracRecordId = context.getEcontractRecordEntity().getId();
        EcontractRecordContextEntity coldData = econtractBigRecordParseService.selectByRecordId(econtracRecordId);
        if (null == coldData || StringUtils.isEmpty(coldData.getContext())) {
            log.error("AsyncCommon 异步处理冷数据为空, econtracRecordId:{}, msg:{}", econtracRecordId, JSON.toJSONString(econtractAsyncRecord));
            throw new EcontractException(EcontractException.ASYNC_HANDLER_ERROR, "异步处理冷数据为空");
        }
        String coldDataContext = coldData.getContext();
        EcontractContext coldContext = JSON.parseObject(coldDataContext, EcontractContext.class);
        context.setEcontractUserEntity(coldContext.getEcontractUserEntity());
        context.setFlowList(coldContext.getFlowList());
        context.setStageInfoBoList(coldContext.getStageInfoBoList());
        context.setStageBatchInfoBoList(coldContext.getStageBatchInfoBoList());
        return context;
    }

    public void updateAsyncTaskStatus(EcontractAsyncRecordEntity econtractAsyncRecord, String status) {
        econtractAsyncRecordMapper.updateStatusByPrimaryKey(econtractAsyncRecord.getId(), status);
    }
}

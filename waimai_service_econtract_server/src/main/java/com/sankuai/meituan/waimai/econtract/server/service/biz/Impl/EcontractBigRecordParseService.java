package com.sankuai.meituan.waimai.econtract.server.service.biz.Impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractRecordContextMapper;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractRecordEntityMapper;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordContextAreaEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordContextEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordEntity;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractContentTypeEnum;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractPdfTypeEnum;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.applybiz.WmPoiSpAreaContentBo;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * Author: 窦旭蒙 MIS: douxumeng Date: 2021-07-27 21:16 Email: <EMAIL> Desc:
 */
@Service
@Slf4j
public class EcontractBigRecordParseService {

    @Autowired
    private EcontractRecordContextMapper econtractRecordContextMapper;

    @Autowired
    private EcontractBigContextManageService econtractBigContextManageService;

    @Autowired
    private EcontractRecordEntityMapper econtractRecordEntityMapper;

    @Autowired
    private EcontractRecordContextAreaService econtractRecordContextAreaService;

    // com.sankuai.meituan.waimai.econtract.server.dao.EcontractRecordContextMapper.insertSelective
    public void insertSelective(EcontractRecordContextEntity recordContextEntity) {
        if (econtractBigContextManageService.econtractRecordGarySwitch(recordContextEntity)) {
            // 大文本上传
            String bigEcontractRecordContext = econtractBigContextManageService.econtractRecordBigContextRemove(recordContextEntity);
            econtractRecordContextMapper.insertSelective(recordContextEntity);
            String objectName = parseRecordObjectName(recordContextEntity);
            econtractBigContextManageService.uploadObject(objectName, recordContextEntity.getContext());
            // 记录S3文本信息
            econtractRecordContextMapper.updateBigApplyContextById(objectName, recordContextEntity.getId());
            // 上传完成后将大文本回写
            econtractBigContextManageService.econtractRecordBigContextFill(recordContextEntity, bigEcontractRecordContext);
        } else if (isAreaSeperateSave(recordContextEntity)) {
            // 冷数据Entity过滤配送范围，同时组装冷数据配送范围对象
            List<EcontractRecordContextAreaEntity> contextAreaEntityList = extractAreaData(recordContextEntity);
            // 保存冷数据Entity
            econtractRecordContextMapper.insertSelective(recordContextEntity);
            // 冷数据配送范围对象填充冷数据主键ID
            contextAreaEntityList.stream().forEach(item -> item.setRecordContextId(Long.valueOf(recordContextEntity.getId())));
            // 保存冷数据配送范围对象列表
            econtractRecordContextAreaService.batchInsert(recordContextEntity.getId(), contextAreaEntityList);
        } else {
            // 正常存储
            econtractRecordContextMapper.insertSelective(recordContextEntity);
        }
    }

    // com.sankuai.meituan.waimai.econtract.server.dao.EcontractRecordContextMapper.selectByRecordId
    public EcontractRecordContextEntity selectByRecordId(Integer recordId) {
        EcontractRecordContextEntity econtractRecordContextEntity;
        if (readFromS3AndDB()) {
            econtractRecordContextEntity = econtractRecordContextMapper.selectByRecordId(recordId);
            fillRecordInfo(econtractRecordContextEntity);
        } else {
            econtractRecordContextEntity = econtractRecordContextMapper.selectByRecordId(recordId);
            // 配送数据已拆分存储
            if (isAreaSeperateSave(econtractRecordContextEntity)) {
                assemblyRecordContext(econtractRecordContextEntity);
            }
        }
        return econtractRecordContextEntity;
    }

    public EcontractRecordContextEntity selectByRecordIdWithRT(Integer recordId) {
        EcontractRecordContextEntity econtractRecordContextEntity = econtractRecordContextMapper.selectByRecordIdWithRT(recordId);
        // 配送数据已拆分存储
        if (isAreaSeperateSave(econtractRecordContextEntity)) {
            assemblyRecordContext(econtractRecordContextEntity);
        }
        return econtractRecordContextEntity;
    }

    private void fillRecordInfo(EcontractRecordContextEntity recordInfo) {
        if (null == recordInfo) {
            return;
        }
        // Context为空再从S3获取，不为空说明没有命中大文本逻辑
        if (StringUtils.isNotEmpty(recordInfo.getContext())) {
            return;
        }
        byte[] contextBytes = econtractBigContextManageService.getObject(recordInfo.getObjectName());
        String context = Base64.getEncoder().encodeToString(contextBytes);
        recordInfo.setContext(context);
    }

    private void fillRecordInfoList(List<EcontractRecordContextEntity> wmEcontractSignTaskDBList) {
        if (CollectionUtils.isEmpty(wmEcontractSignTaskDBList)) {
            return;
        }
        wmEcontractSignTaskDBList.stream().forEach(recordInfo -> fillRecordInfo(recordInfo));
    }

    private boolean readFromS3AndDB() {
        return ConfigUtilAdapter.getBoolean("econtract_bigcontext_read_switch", false);// 默认关闭
    }

    private String parseRecordObjectName(EcontractRecordContextEntity contextEntity) {
        // 查询econtract_type
        EcontractRecordEntity econtractRecordEntity = econtractRecordEntityMapper.selectBaseByPrimaryKey(contextEntity.getEcontractRecordId());
        StringBuilder sb = new StringBuilder();
        sb.append(econtractRecordEntity.getEcontractType());
        sb.append("_");
        sb.append(contextEntity.getEcontractRecordId());
        return sb.toString();
    }

    private boolean isAreaSeperateSave(EcontractRecordContextEntity contextEntity) {
        if (contextEntity == null || StringUtils.isEmpty(contextEntity.getContext())) {
            return false;
        }
        EcontractContext coldContext = JSON.parseObject(contextEntity.getContext(), EcontractContext.class);
        return coldContext.getIsAreaSeperateSave() != null && coldContext.getIsAreaSeperateSave();
    }

    /**
     * 提取一个签约任务下的所有配送范围信息(门店维度拆分)
     * 同时将对应冷数据的配送范围信息过滤
     * @param contextEntity
     * @return
     */
    private List<EcontractRecordContextAreaEntity> extractAreaData(EcontractRecordContextEntity contextEntity) {
        if (contextEntity == null || StringUtils.isEmpty(contextEntity.getContext())) {
            return Lists.newArrayList();
        }
        EcontractContext coldContext = JSON.parseObject(contextEntity.getContext(), EcontractContext.class);
        if (coldContext == null || CollectionUtils.isEmpty(coldContext.getStageBatchInfoBoList())) {
            return Lists.newArrayList();
        }

        List<EcontractRecordContextAreaEntity> contextAreaEntityList = Lists.newArrayList();
        for (StageBatchInfoBo stageBatchInfoBo : coldContext.getStageBatchInfoBoList()) {
            // 判断是否存储配送范围的stage，若否则跳转至下一个stage
            if (!TaskConstant.ECONTRACT_VIEW_CONTENT.equals(stageBatchInfoBo.getStageName())) {
              continue;
            }
            Map<String, Map<String, String>> viewContentMap = stageBatchInfoBo.getStageAndViewContentMap();
            List<EcontractRecordContextAreaEntity> subContextAreaEntityList = generateRecordContextArea(viewContentMap);
            if (CollectionUtils.isNotEmpty(subContextAreaEntityList)) {
                contextAreaEntityList.addAll(subContextAreaEntityList);
            }
        }
        // 重置冷数据上下文
        contextEntity.setContext(JSON.toJSONString(coldContext));
        return contextAreaEntityList;
    }

    /**
     * 生成签约任务的配送范围信息
     */
    private List<EcontractRecordContextAreaEntity> generateRecordContextArea(Map<String, Map<String, String>> viewContentMap) {
        List<EcontractRecordContextAreaEntity> contextAreaEntityList = Lists.newArrayList();
        contextAreaEntityList.addAll(clearAndExtractAreaData(viewContentMap, AreaOperateEnum.DELIVERY));
        contextAreaEntityList.addAll(clearAndExtractAreaData(viewContentMap, AreaOperateEnum.DELIVERY_WHOLE_CITY));
        contextAreaEntityList.addAll(clearAndExtractAreaData(viewContentMap, AreaOperateEnum.DELIVERY_AGGREGATION));
        contextAreaEntityList.addAll(clearAndExtractAreaData(viewContentMap, AreaOperateEnum.DELIVERY_LONG_DISTANCE));
        contextAreaEntityList.addAll(clearAndExtractAreaData(viewContentMap, AreaOperateEnum.DRONE_DELIVERY));
        return contextAreaEntityList;
    }

    /**
     * 如果配送范围存在，则清空配送范围，并返回，加入到list中
     * 否则返回空list
     */
    private List<EcontractRecordContextAreaEntity> clearAndExtractAreaData(Map<String, Map<String, String>> viewContentMap, AreaOperateEnum areaOperateEnum) {
        if (areaOperateEnum.getHasFunction().test(viewContentMap) && areaOperateEnum.getHasAreaFunction().test(viewContentMap)) {
            String area = areaOperateEnum.getGetFunction().apply(viewContentMap);
            areaOperateEnum.getSetFunction().accept(viewContentMap, StringUtils.EMPTY);
            return assemblyContextAreaEntityList(areaOperateEnum.pdfTypeEnum.getName(), area);
        } else {
            return Lists.newArrayList();
        }
    }

    private List<EcontractRecordContextAreaEntity> assemblyContextAreaEntityList(String areaType, String AreaStr) {
        List<EcontractRecordContextAreaEntity> contextAreaEntityList = Lists.newArrayList();
        List<WmPoiSpAreaContentBo> spAreaContentBoList = JSON.parseArray(AreaStr, WmPoiSpAreaContentBo.class);
        log.info("#assemblyContextAreaEntityList areaType:{} size:{}", areaType, spAreaContentBoList.size());
        for (WmPoiSpAreaContentBo spAreaContentBo : spAreaContentBoList) {
            EcontractRecordContextAreaEntity contextAreaEntity = new EcontractRecordContextAreaEntity();
            contextAreaEntity.setWmPoiId(Long.valueOf(spAreaContentBo.getWmPoiId()));
            contextAreaEntity.setAreaType(areaType);
            contextAreaEntity.setArea(JSON.toJSONString(spAreaContentBo));
            contextAreaEntity.setValid(1);
            contextAreaEntityList.add(contextAreaEntity);
        }
        return contextAreaEntityList;
    }

    private void assemblyRecordContext(EcontractRecordContextEntity recordContextEntity) {
        List<EcontractRecordContextAreaEntity> recordContextAreaEntityList = econtractRecordContextAreaService.queryAreaEntityListByRecordContext(recordContextEntity);
        if (CollectionUtils.isEmpty(recordContextAreaEntityList)) {
            return;
        }
        // key为配送范围类型
        Map<String, List<EcontractRecordContextAreaEntity>> recordContextAreaEntityMap = recordContextAreaEntityList
                .stream().collect(Collectors.groupingBy(EcontractRecordContextAreaEntity::getAreaType));

        EcontractContext coldContext = JSON.parseObject(recordContextEntity.getContext(), EcontractContext.class);
        for (StageBatchInfoBo stageBatchInfoBo : coldContext.getStageBatchInfoBoList()) {
            // 判断是否存储配送范围的stage，若否则跳转至下一个stage
            if (!TaskConstant.ECONTRACT_VIEW_CONTENT.equals(stageBatchInfoBo.getStageName())) {
                continue;
            }
            Map<String, Map<String, String>> viewContentMap = stageBatchInfoBo.getStageAndViewContentMap();
            assemblyViewContentMap(viewContentMap, recordContextAreaEntityMap);
        }
        recordContextEntity.setContext(JSON.toJSONString(coldContext));
    }

    private void assemblyViewContentMap(Map<String, Map<String, String>> viewContentMap, Map<String, List<EcontractRecordContextAreaEntity>> recordContextAreaEntityMap) {
        // 常规配送类型
        assemblyDeliveryArea(viewContentMap, recordContextAreaEntityMap.get(AreaOperateEnum.DELIVERY.pdfTypeEnum.getName()), AreaOperateEnum.DELIVERY);
        // 全城送配送类型
        assemblyDeliveryArea(viewContentMap, recordContextAreaEntityMap.get(AreaOperateEnum.DELIVERY_WHOLE_CITY.pdfTypeEnum.getName()), AreaOperateEnum.DELIVERY_WHOLE_CITY);
        // 聚合配送类型
        assemblyDeliveryArea(viewContentMap, recordContextAreaEntityMap.get(AreaOperateEnum.DELIVERY_AGGREGATION.pdfTypeEnum.getName()), AreaOperateEnum.DELIVERY_AGGREGATION);
        // 企客远距离配送类型
        assemblyDeliveryArea(viewContentMap, recordContextAreaEntityMap.get(AreaOperateEnum.DELIVERY_LONG_DISTANCE.pdfTypeEnum.getName()), AreaOperateEnum.DELIVERY_LONG_DISTANCE);
        // 无人机配送类型
        assemblyDeliveryArea(viewContentMap, recordContextAreaEntityMap.get(AreaOperateEnum.DRONE_DELIVERY.pdfTypeEnum.getName()), AreaOperateEnum.DRONE_DELIVERY);
    }

    /**
     * 若配送合同存在且对应的配送范围存在，则
     */
    private void assemblyDeliveryArea(Map<String, Map<String, String>> viewContentMap, List<EcontractRecordContextAreaEntity> recordContextAreaEntityList, AreaOperateEnum areaOperateEnum) {
        if (areaOperateEnum.getHasFunction().test(viewContentMap)
                && CollectionUtils.isNotEmpty(recordContextAreaEntityList)) {
            List<WmPoiSpAreaContentBo> spAreaContentBoList = new ArrayList<>();
            for (EcontractRecordContextAreaEntity deliveryAreaEntity : recordContextAreaEntityList) {
                spAreaContentBoList.add(JSON.parseObject(deliveryAreaEntity.getArea(), WmPoiSpAreaContentBo.class));
            }
            areaOperateEnum.getSetFunction().accept(viewContentMap, JSON.toJSONString(spAreaContentBoList));
        }
    }

    private static Predicate<Map<String, Map<String, String>>> hasDeliveryFunction = viewContentMap -> MapUtils.isNotEmpty(viewContentMap.get(EcontractPdfTypeEnum.DELIVERY.getName()));

    private static Predicate<Map<String, Map<String, String>>> hasDroneDeliveryFunction = viewContentMap -> MapUtils.isNotEmpty(viewContentMap.get(EcontractPdfTypeEnum.DRONE_DELIVERY.getName()));

    private static Predicate<Map<String, Map<String, String>>> hasWholeCityDeliveryFunction = viewContentMap -> MapUtils.isNotEmpty(viewContentMap.get(EcontractPdfTypeEnum.DELIVERY_WHOLE_CITY.getName()));

    private static Predicate<Map<String, Map<String, String>>> hasAggregationDeliveryFunction = viewContentMap -> MapUtils.isNotEmpty(viewContentMap.get(EcontractPdfTypeEnum.DELIVERY_AGGREGATION.getName()));

    private static Predicate<Map<String, Map<String, String>>> hasLongDistanceDeliveryFunction = viewContentMap -> MapUtils.isNotEmpty(viewContentMap.get(EcontractPdfTypeEnum.DELIVERY_COMPANY_CUSTOMER_LONG_DISTANCE.getName()));

    private static Predicate<Map<String, Map<String, String>>> hasDeliveryAreaFunction = viewContentMap -> StringUtils.isNotEmpty(viewContentMap.get(EcontractPdfTypeEnum.DELIVERY.getName()).get(EcontractContentTypeEnum.SP_AREA.getName()));

    private static Predicate<Map<String, Map<String, String>>> hasDroneDeliveryAreaFunction = viewContentMap -> StringUtils.isNotEmpty(viewContentMap.get(EcontractPdfTypeEnum.DRONE_DELIVERY.getName()).get(EcontractContentTypeEnum.SP_AREA.getName()));

    private static Predicate<Map<String, Map<String, String>>> hasWholeCityDeliveryAreaFunction = viewContentMap -> StringUtils.isNotEmpty(viewContentMap.get(EcontractPdfTypeEnum.DELIVERY_WHOLE_CITY.getName()).get(EcontractContentTypeEnum.SP_AREA.getName()));

    private static Predicate<Map<String, Map<String, String>>> hasAggregationDeliveryAreaFunction = viewContentMap -> StringUtils.isNotEmpty(viewContentMap.get(EcontractPdfTypeEnum.DELIVERY_AGGREGATION.getName()).get(EcontractContentTypeEnum.SP_AREA.getName()));

    private static Predicate<Map<String, Map<String, String>>> hasLongDistanceDeliveryAreaFunction = viewContentMap -> StringUtils.isNotEmpty(viewContentMap.get(EcontractPdfTypeEnum.DELIVERY_COMPANY_CUSTOMER_LONG_DISTANCE.getName()).get(EcontractContentTypeEnum.SP_AREA.getName()));

    private static BiConsumer<Map<String, Map<String, String>>, String> setDeliveryAreaFunction = (viewContentMap, area) -> viewContentMap.get(EcontractPdfTypeEnum.DELIVERY.getName()).put(EcontractContentTypeEnum.SP_AREA.getName(), area);

    private static BiConsumer<Map<String, Map<String, String>>, String> setDroneDeliveryAreaFunction = (viewContentMap, area) -> viewContentMap.get(EcontractPdfTypeEnum.DRONE_DELIVERY.getName()).put(EcontractContentTypeEnum.SP_AREA.getName(), area);

    private static BiConsumer<Map<String, Map<String, String>>, String> setWholeCityDeliveryAreaFunction = (viewContentMap, area) -> viewContentMap.get(EcontractPdfTypeEnum.DELIVERY_WHOLE_CITY.getName()).put(EcontractContentTypeEnum.SP_AREA.getName(), area);

    private static BiConsumer<Map<String, Map<String, String>>, String> setAggregationDeliveryAreaFunction = (viewContentMap, area) -> viewContentMap.get(EcontractPdfTypeEnum.DELIVERY_AGGREGATION.getName()).put(EcontractContentTypeEnum.SP_AREA.getName(), area);

    private static BiConsumer<Map<String, Map<String, String>>, String> setLongDistanceDeliveryAreaFunction = (viewContentMap, area) -> viewContentMap.get(EcontractPdfTypeEnum.DELIVERY_COMPANY_CUSTOMER_LONG_DISTANCE.getName()).put(EcontractContentTypeEnum.SP_AREA.getName(), area);

    private static Function<Map<String, Map<String, String>>, String> getDeliveryAreaFunction = viewContentMap -> viewContentMap.get(EcontractPdfTypeEnum.DELIVERY.getName()).get(EcontractContentTypeEnum.SP_AREA.getName());

    private static Function<Map<String, Map<String, String>>, String> getDroneDeliveryAreaFunction = viewContentMap -> viewContentMap.get(EcontractPdfTypeEnum.DRONE_DELIVERY.getName()).get(EcontractContentTypeEnum.SP_AREA.getName());

    private static Function<Map<String, Map<String, String>>, String> getWholeCityDeliveryAreaFunction = viewContentMap -> viewContentMap.get(EcontractPdfTypeEnum.DELIVERY_WHOLE_CITY.getName()).get(EcontractContentTypeEnum.SP_AREA.getName());

    private static Function<Map<String, Map<String, String>>, String> getAggregationDeliveryAreaFunction = viewContentMap -> viewContentMap.get(EcontractPdfTypeEnum.DELIVERY_AGGREGATION.getName()).get(EcontractContentTypeEnum.SP_AREA.getName());

    private static Function<Map<String, Map<String, String>>, String> getLongDistanceDeliveryAreaFunction = viewContentMap -> viewContentMap.get(EcontractPdfTypeEnum.DELIVERY_COMPANY_CUSTOMER_LONG_DISTANCE.getName()).get(EcontractContentTypeEnum.SP_AREA.getName());

    public enum AreaOperateEnum {
        /*
         * 主配送方式对应的范围
         */
        DELIVERY(hasDeliveryFunction, hasDeliveryAreaFunction, setDeliveryAreaFunction, getDeliveryAreaFunction, EcontractPdfTypeEnum.DELIVERY),

        /*
         * 全城送对应的范围
         */
        DELIVERY_WHOLE_CITY(hasWholeCityDeliveryFunction, hasWholeCityDeliveryAreaFunction, setWholeCityDeliveryAreaFunction, getWholeCityDeliveryAreaFunction, EcontractPdfTypeEnum.DELIVERY_WHOLE_CITY),

        /*
         * 聚合配对应的范围
         */
        DELIVERY_AGGREGATION(hasAggregationDeliveryFunction, hasAggregationDeliveryAreaFunction, setAggregationDeliveryAreaFunction, getAggregationDeliveryAreaFunction, EcontractPdfTypeEnum.DELIVERY_AGGREGATION),

        /*
         * 企客远距离对应的范围
         */
        DELIVERY_LONG_DISTANCE(hasLongDistanceDeliveryFunction, hasLongDistanceDeliveryAreaFunction, setLongDistanceDeliveryAreaFunction, getLongDistanceDeliveryAreaFunction, EcontractPdfTypeEnum.DELIVERY_COMPANY_CUSTOMER_LONG_DISTANCE),

        /**
         * 无人机配送对应的范围
         */
        DRONE_DELIVERY(hasDroneDeliveryFunction, hasDroneDeliveryAreaFunction, setDroneDeliveryAreaFunction, getDroneDeliveryAreaFunction, EcontractPdfTypeEnum.DRONE_DELIVERY);

        /*
         * 判断是否有配送合同的函数
         */
        private Predicate<Map<String, Map<String, String>>> hasFunction;

        /*
         * 判断配送合同中是否有范围的函数
         */
        private Predicate<Map<String, Map<String, String>>> hasAreaFunction;

        /*
         * 上下文中设置配送范围的函数
         */
        private BiConsumer<Map<String, Map<String, String>>, String> setFunction;

        /*
         * 上线文中读取配送范围的函数
         */
        private Function<Map<String, Map<String, String>>, String> getFunction;

        /*
         * 申请PDF的类型
         */
        private EcontractPdfTypeEnum pdfTypeEnum;

        AreaOperateEnum(Predicate<Map<String, Map<String, String>>> hasFunction,
                        Predicate<Map<String, Map<String, String>>> hasAreaFunction,
                        BiConsumer<Map<String, Map<String, String>>, String> setFunction,
                        Function<Map<String, Map<String, String>>, String> getFunction,
                        EcontractPdfTypeEnum pdfTypeEnum) {
            this.hasFunction = hasFunction;
            this.hasAreaFunction = hasAreaFunction;
            this.setFunction = setFunction;
            this.getFunction = getFunction;
            this.pdfTypeEnum = pdfTypeEnum;
        }

        public Predicate<Map<String, Map<String, String>>> getHasFunction() {
            return hasFunction;
        }

        public void setHasFunction(Predicate<Map<String, Map<String, String>>> hasFunction) {
            this.hasFunction = hasFunction;
        }

        public Predicate<Map<String, Map<String, String>>> getHasAreaFunction() {
            return hasAreaFunction;
        }

        public void setHasAreaFunction(Predicate<Map<String, Map<String, String>>> hasAreaFunction) {
            this.hasAreaFunction = hasAreaFunction;
        }

        public Function<Map<String, Map<String, String>>, String> getGetFunction() {
            return getFunction;
        }

        public void setGetFunction(Function<Map<String, Map<String, String>>, String> getFunction) {
            this.getFunction = getFunction;
        }

        public BiConsumer<Map<String, Map<String, String>>, String> getSetFunction() {
            return setFunction;
        }

        public void setSetFunction(BiConsumer<Map<String, Map<String, String>>, String> setFunction) {
            this.setFunction = setFunction;
        }

        public EcontractPdfTypeEnum getPdfTypeEnum() {
            return pdfTypeEnum;
        }

        public void setPdfTypeEnum(EcontractPdfTypeEnum pdfTypeEnum) {
            this.pdfTypeEnum = pdfTypeEnum;
        }
    }
}

package com.sankuai.meituan.waimai.econtract.server.service.engine.task.asyncHandler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.google.common.collect.Maps;
import com.meituan.sankuai.bsi.esign.common.model.entity.result.GetContractResult;
import com.sankuai.meituan.waimai.econtract.server.adapter.EsignClient;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.constants.ContextConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.EcontractAsyncTaskConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractAsyncRecordMapper;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractAsyncRecordEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordContextEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordEntity;
import com.sankuai.meituan.waimai.econtract.server.exception.EcontractException;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractRecordService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.Impl.EcontractBigRecordParseService;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.AsyncTaskHandler;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.Impl.AbstractExecutor;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.Impl.EstampExecutor;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.util.monitor.AsyncEstampDelayMonitor;
import com.sankuai.meituan.waimai.econtract.server.utils.DealVersionUtils;
import com.sankuai.meituan.waimai.econtract.server.utils.ServiceEnvUtils;
import com.sankuai.meituan.waimai.econtract.server.utils.UrlConvetUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.CallbackConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2022-01-17 17:54
 * Email: <EMAIL>
 * Desc:
 */
@Slf4j
@Service
public class AsyncEstampHandler extends AsyncEstampCommon implements AsyncTaskHandler {

    @Override
    public void handlerAsyncTask(JSONObject resultObject) {
        //获取异步任务记录
        String asyncTaskId = resultObject.getString("asyncTaskId");
        //获取签约流程上下文
        EcontractAsyncRecordEntity econtractAsyncRecord = econtractAsyncRecordMapper.selectByAsyncId(asyncTaskId);
        log.info("AsyncEstampHandler#asyncTaskId:{}, env:{}", asyncTaskId, econtractAsyncRecord.getEnv());
        //如果是st产生的消息，不处理
        if (econtractAsyncRecord.getEnv().equals("staging")) {
            return;
        }
        super.handlerAsyncTaskCommon(resultObject, econtractAsyncRecord);
    }
}

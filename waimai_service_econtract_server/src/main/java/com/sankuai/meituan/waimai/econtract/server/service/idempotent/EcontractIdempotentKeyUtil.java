package com.sankuai.meituan.waimai.econtract.server.service.idempotent;

import com.google.common.base.Splitter;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.exception.EcontractException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.elasticsearch.common.Strings;

import java.util.List;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2025/4/27 19:54
 */
@Slf4j
public class EcontractIdempotentKeyUtil {

    private static final Splitter COMM_SPLITTER = Splitter.on("|").trimResults();

    public static String getEcontractIdempotentKey(EcontractContext context) {
        String recordKey = context.getEcontractRecordEntity().getRecordKey();
        String taskType = context.getTaskContext().getTaskType();
        String stageTaskName = context.getCurrentTaskNode().getTaskName();

        if (Strings.isEmpty(recordKey) || Strings.isEmpty(stageTaskName)) {
            log.error("获取幂等键失败, recordKey:{}, stageTaskName:{}", recordKey, stageTaskName);
            throw new EcontractException(EcontractException.IDEMPOTENT_ERROR, "获取幂等键失败");
        }

        if (CollectionUtils.isNotEmpty(context.getFlowList()) && CollectionUtils.size(context.getFlowList()) > 1 && Strings.isEmpty(taskType)) {
            throw new EcontractException(EcontractException.IDEMPOTENT_ERROR, "获取幂等键的当前任务类型失败");
        }
        return String.format("%s|%s|%s", recordKey, Strings.isEmpty(taskType) ? "_" : taskType, stageTaskName);
    }

    /**
     * 分割电子合同幂等键，提取recordKey和stageTaskName
     *
     * @param idempotentKey 幂等键，格式为"recordKey|taskType|stageTaskName"
     * @return Pair对象，left为recordKey，right为stageTaskName
     * @throws EcontractException 当幂等键为空或格式不正确时抛出异常
     */
    public static Pair<String, String> splitEcontractIdempotentKey(String idempotentKey) {
        if (Strings.isEmpty(idempotentKey)) {
            log.error("幂等键为空，无法分割");
            throw new EcontractException(EcontractException.IDEMPOTENT_ERROR, "幂等键为空，无法分割");
        }

        try {
            List<String> parts = COMM_SPLITTER.splitToList(idempotentKey);

            if (parts.size() < 3) {
                log.error("幂等键格式不正确: {}, 无法分割", idempotentKey);
                throw new EcontractException(EcontractException.IDEMPOTENT_ERROR, "幂等键格式不正确，无法分割");
            }

            String recordKey = parts.get(0);
            String stageTaskName = parts.get(2);

            if (Strings.isEmpty(recordKey) || Strings.isEmpty(stageTaskName)) {
                log.error("幂等键分割后的recordKey或stageTaskName为空: {}", idempotentKey);
                throw new EcontractException(EcontractException.IDEMPOTENT_ERROR, "幂等键分割后的关键信息为空");
            }

            return Pair.of(recordKey, stageTaskName);
        } catch (Exception e) {
            if (e instanceof EcontractException) {
                throw e;
            }
            log.error("分割幂等键时发生异常: {}", idempotentKey, e);
            throw new EcontractException(EcontractException.IDEMPOTENT_ERROR, "分割幂等键时发生异常: " + e.getMessage());
        }
    }

}

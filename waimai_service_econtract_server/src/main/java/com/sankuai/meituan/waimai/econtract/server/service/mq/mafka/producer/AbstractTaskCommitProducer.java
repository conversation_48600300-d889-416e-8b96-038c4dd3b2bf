package com.sankuai.meituan.waimai.econtract.server.service.mq.mafka.producer;

import com.alibaba.fastjson.JSONObject;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.producer.ProducerResult;
import com.sankuai.meituan.waimai.econtract.server.constants.MessageStatusEnum;
import com.sankuai.meituan.waimai.econtract.server.constants.MqConstant;
import com.sankuai.meituan.waimai.econtract.server.service.biz.Impl.EcontractBigMessageServiceImpl;
import com.sankuai.meituan.waimai.econtract.server.service.mq.TaskMsg;
import java.util.Properties;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public abstract class AbstractTaskCommitProducer extends AbstractMafkaProducer {

    @Resource
    private EcontractBigMessageServiceImpl eContractBigMessageService;

    @Override
    protected void doSetUpProducer() throws Exception {
        Properties properties = new Properties();
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, MqConstant.BG_NAME_SPACE);
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, MqConstant.APP_KEY);
        producer = MafkaClient.buildProduceFactory(properties, getTopic(), false);
    }

    public void sendMessage(TaskMsg msg) throws Exception {
        try {
            log.info("{}, sendMessage msq={}", getTaskCommitProducerClassName(), JSONObject.toJSONString(msg));
            ProducerResult result = producer.sendMessage(JSONObject.toJSONString(msg), msg.getEcontractRecordKey());
            log.info("{}, 消息发送完成！MsgId = {},recordKey:{}", getTaskCommitProducerClassName(), result.getMessageID(), msg.getEcontractRecordKey());
            eContractBigMessageService.updateStatus(MessageStatusEnum.SEND_SUCCESS.getStatus(), msg.getContextId());
        } catch (Exception e) {
            log.error("{}, sendMessage exception, recordKey={}, e:", getTaskCommitProducerClassName(), msg.getEcontractRecordKey(), e);
            throw e;
        }
    }

    protected abstract String getTopic();

    protected abstract String getTaskCommitProducerClassName();
}

package com.sankuai.meituan.waimai.econtract.server.adapter;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.req.BatchQueryPoiSignDataParam;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.req.OperatorParam;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.req.PoiSession;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.resp.ElectronicContractBatchDeliveryPerInfoBo;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.resp.ElectronicContractDeliveryPerDiscountInfoBo;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.resp.ElectronicContractDeliveryPerInfoBo;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.resp.ElectronicContractSingleDeliveryPerInfoBo;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.resp.base.BmContractPlatformProcessResp;
import com.sankuai.meituan.banma.deliverycontract.platform.process.sdk.thrift.BmContractSettleSignThriftIface;
import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import com.sankuai.meituan.waimai.econtract.server.utils.JacksonUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractErrorCode;
import com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDeliveryTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.delivery.EcontractDeliveryPerDiscountInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.delivery.EcontractDeliveryPerInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.delivery.batch.EcontractBatchDeliveryPerInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.delivery.single.EcontractSingleDeliveryPerInfoBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 履约服务费相关接口
 * Created by lixuepeng on 2023/6/5
 */
@Slf4j
@Service
public class DeliveryContractPlatformAdapter {

    @Autowired
    private BmContractSettleSignThriftIface bmContractSettleSignThriftIface;

    public String queryPerformanceSignDataWithRetry(Map<Long, Long> wmPoiAndBizMap) throws EcontractException {
        if (MapUtils.isEmpty(wmPoiAndBizMap)) {
            return null;
        }
        int retryTimes = MccConfig.queryEcontractDataRetryTimes();
        for (int i = 0; i < retryTimes; i++) {
            try {
                BatchQueryPoiSignDataParam param = new BatchQueryPoiSignDataParam();
                // 门店-session关系列表
                List<PoiSession> poiSessionList = new ArrayList<>();
                for (Map.Entry<Long, Long> entry : wmPoiAndBizMap.entrySet()) {
                    PoiSession poiSession = new PoiSession();
                    poiSession.setWmPoiId(entry.getKey());
                    poiSession.setSessionId(entry.getValue());
                    poiSessionList.add(poiSession);
                }
                // 操作人信息
                OperatorParam operatorParam = new OperatorParam();
                operatorParam.setOpId(-99L);
                operatorParam.setOpName("电子签约系统");
                operatorParam.setMisId("电子签约系统");

                param.setPoiSessionList(poiSessionList);
                param.setOperatorParam(operatorParam);

                BmContractPlatformProcessResp<ElectronicContractBatchDeliveryPerInfoBo> resp = bmContractSettleSignThriftIface.batchQueryPoiSignData(param);
                log.info("DeliveryContractPlatformAdapter#queryPerformanceSignDataWithRetry, resp:{}", JacksonUtil.writeAsJsonStr(resp));
                if (resp == null || !resp.isSuccess() || resp.getCode() != 0 || resp.getData() == null) {
                    log.info("#queryPerformanceSignDataWithRetry 获取履约服务费签约数据失败 wmPoiAndBizMap:{}", wmPoiAndBizMap);
                    throw new EcontractException(EcontractErrorCode.BUSINESS_ERROR, "调用配送系统获取履约服务费签约数据失败");
                }
                EcontractBatchDeliveryPerInfoBo econtractBatchDeliveryPerInfoBo = assemblyBatchPerInfoBo(resp.getData());
                String result = JSON.toJSONString(econtractBatchDeliveryPerInfoBo);
                log.info("#queryPerformanceSignDataWithRetry result:{}",result);
                return result;
            } catch (TException ex) {
                // TException 为超时异常，进行重试
                log.error("#queryPerformanceSignDataWithRetry 获取履约服务费签约数据超时异常 wmPoiAndBizMap:{}", wmPoiAndBizMap, ex);
            } catch (Exception e) {
                // 其他异常则直接catch并抛出业务异常
                log.error("#queryPerformanceSignDataWithRetry 获取履约服务费签约数据异常 wmPoiAndBizMap:{}", wmPoiAndBizMap, e);
                throw new EcontractException(EcontractErrorCode.BUSINESS_ERROR, "调用配送系统获取履约服务费签约数据异常");
            }
        }
        return null;
    }

    private EcontractBatchDeliveryPerInfoBo assemblyBatchPerInfoBo(ElectronicContractBatchDeliveryPerInfoBo electronicContractBatchDeliveryPerInfoBo) {
        log.info("DeliveryContractPlatformAdapter#assemblyBatchPerInfoBo, perfData: {}", JSON.toJSONString(electronicContractBatchDeliveryPerInfoBo));
        EcontractBatchDeliveryPerInfoBo econtractBatchDeliveryPerInfoBo = new EcontractBatchDeliveryPerInfoBo();
        Map<Long, EcontractSingleDeliveryPerInfoBo> batchPerInfoMap = new HashMap<>();
        log.info("electronicContractBatchDeliveryPerInfoBo:{}", JSON.toJSONString(electronicContractBatchDeliveryPerInfoBo));
        for (Map.Entry<Long, ElectronicContractSingleDeliveryPerInfoBo> entry : electronicContractBatchDeliveryPerInfoBo.getBatchPerInfoMap().entrySet()) {
            batchPerInfoMap.put(entry.getKey(), assemblySinglePerInfoBo(entry.getValue()));
        }
        econtractBatchDeliveryPerInfoBo.setBatchPerInfoMap(batchPerInfoMap);
        return econtractBatchDeliveryPerInfoBo;
    }

    private EcontractSingleDeliveryPerInfoBo assemblySinglePerInfoBo(ElectronicContractSingleDeliveryPerInfoBo electronicContractSingleDeliveryPerInfoBo) {
        EcontractSingleDeliveryPerInfoBo econtractSingleDeliveryPerInfoBo = new EcontractSingleDeliveryPerInfoBo();
        if (MapUtils.isNotEmpty(electronicContractSingleDeliveryPerInfoBo.getPerInfoMap())) {
            Map<Integer, EcontractDeliveryPerInfoBo> perInfoMap = new HashMap<>();
            for (Map.Entry<Integer, ElectronicContractDeliveryPerInfoBo> entry : electronicContractSingleDeliveryPerInfoBo.getPerInfoMap().entrySet()) {
                EcontractDeliveryPerInfoBo econtractDeliveryPerInfoBo = new EcontractDeliveryPerInfoBo();
                ElectronicContractDeliveryPerInfoBo electronicContractDeliveryPerInfoBo = entry.getValue();
                econtractDeliveryPerInfoBo.setBasePrice(electronicContractDeliveryPerInfoBo.getPerfBasicPrice());
                econtractDeliveryPerInfoBo.setDistanceRule(electronicContractDeliveryPerInfoBo.getPerfDistanceDetail());
                econtractDeliveryPerInfoBo.setWeightRule(electronicContractDeliveryPerInfoBo.getPerfWeightDetail());
                econtractDeliveryPerInfoBo.setTimeRule(electronicContractDeliveryPerInfoBo.getPerfTimeDetail());
                econtractDeliveryPerInfoBo.setDistanceRuleMinPrice(electronicContractDeliveryPerInfoBo.getPerfBasicPrice());
                econtractDeliveryPerInfoBo.setDistanceRulePlus(electronicContractDeliveryPerInfoBo.getPerfDistanceDetail());
                econtractDeliveryPerInfoBo.setIncrementalCost(electronicContractDeliveryPerInfoBo.getIncrementalCost());
                econtractDeliveryPerInfoBo.setInitiatePrice(electronicContractDeliveryPerInfoBo.getInitiatePrice());
                econtractDeliveryPerInfoBo.setTieredPriceByDistance(electronicContractDeliveryPerInfoBo.getTieredPriceByDistance());
                econtractDeliveryPerInfoBo.setChargeByPrice(electronicContractDeliveryPerInfoBo.getChargeByPrice());
                econtractDeliveryPerInfoBo.setPerformanceServiceTopRatio(electronicContractDeliveryPerInfoBo.getPerformanceServiceTopRatio());
                econtractDeliveryPerInfoBo.setPerformanceServiceSpDiscountFactor(electronicContractDeliveryPerInfoBo.getPerformanceServiceSpDiscountFactor());
                econtractDeliveryPerInfoBo.setPerformanceServiceSpExpirationTime(electronicContractDeliveryPerInfoBo.getPerformanceServiceSpExpirationTime());
                econtractDeliveryPerInfoBo.setExclusiveFeeDiscountFactor2(electronicContractDeliveryPerInfoBo.getExclusiveFeeDiscountFactor2());
                econtractDeliveryPerInfoBo.setPromiseFinishRate(electronicContractDeliveryPerInfoBo.getPromiseFinishRate());
                econtractDeliveryPerInfoBo.setPerformanceServiceDiscountFactor(electronicContractDeliveryPerInfoBo.getPerformanceServiceDiscountFactor());
                if (EcontractDeliveryTypeEnum.AGGREGATION.getType() == entry.getKey()) {
                    econtractDeliveryPerInfoBo.setStandardPerformanceFeeRuleByDistance(electronicContractDeliveryPerInfoBo.getTieredPriceByDistance());
                    econtractDeliveryPerInfoBo.setStandardPerformanceFeeRuleByTimePeriod(electronicContractDeliveryPerInfoBo.getIncrementalCost());
                    econtractDeliveryPerInfoBo.setAggregationExclusiveFeeDiscountFactor2(electronicContractDeliveryPerInfoBo.getExclusiveFeeDiscountFactor2());
                } else {
                    econtractDeliveryPerInfoBo.setStandardPerformanceFeeRuleByDistance("");
                    econtractDeliveryPerInfoBo.setStandardPerformanceFeeRuleByTimePeriod("");
                    econtractDeliveryPerInfoBo.setAggregationExclusiveFeeDiscountFactor2("");
                }
                econtractDeliveryPerInfoBo.setPerformanceDiscountUrl("");
                econtractDeliveryPerInfoBo.setAgentPerfCap("");
                econtractDeliveryPerInfoBo.setAgentPerfCapEndTime("");
                econtractDeliveryPerInfoBo.setAgentNewDistance("");
                econtractDeliveryPerInfoBo.setAgentNewPrice("");
                econtractDeliveryPerInfoBo.setCompensateRate(electronicContractDeliveryPerInfoBo.getCompensateRate());
                econtractDeliveryPerInfoBo.setDroneAirportUsageFee(electronicContractDeliveryPerInfoBo.getDroneAirportUsageFee());
                econtractDeliveryPerInfoBo.setDroneCloudPlatformServiceFee(electronicContractDeliveryPerInfoBo.getDroneCloudPlatformServiceFee());
                econtractDeliveryPerInfoBo.setSpecialPerfBasicPrice(electronicContractDeliveryPerInfoBo.getSpecialPerfBasicPrice());
                econtractDeliveryPerInfoBo.setSpecialEndTime(electronicContractDeliveryPerInfoBo.getSpecialEndTime());
                econtractDeliveryPerInfoBo.setLiquidatedDamagesFeeClauseOverview(electronicContractDeliveryPerInfoBo.getLiquidatedDamagesFeeClauseOverview());
                econtractDeliveryPerInfoBo.setLiquidatedDamagesFeeClauseExample(electronicContractDeliveryPerInfoBo.getLiquidatedDamagesFeeClauseExample());
                econtractDeliveryPerInfoBo.setLiquidatedDamagesFeeClauseInText(electronicContractDeliveryPerInfoBo.getLiquidatedDamagesFeeClauseInText());
                perInfoMap.put(entry.getKey(), econtractDeliveryPerInfoBo);
            }
            econtractSingleDeliveryPerInfoBo.setPerInfoMap(perInfoMap);
        }
        if (electronicContractSingleDeliveryPerInfoBo.getPerDiscountInfo() != null) {
            EcontractDeliveryPerDiscountInfoBo perDiscountInfo = new EcontractDeliveryPerDiscountInfoBo();
            ElectronicContractDeliveryPerDiscountInfoBo deliveryPerDiscountInfoBo = electronicContractSingleDeliveryPerInfoBo.getPerDiscountInfo();
            perDiscountInfo.setDiscountFactor(deliveryPerDiscountInfoBo.getDiscountFactor());
            perDiscountInfo.setDiscountTimelimit(deliveryPerDiscountInfoBo.getDiscountTimeLimit());
            econtractSingleDeliveryPerInfoBo.setPerDiscountInfo(perDiscountInfo);
        }
        log.info("DeliveryContractPlatformAdapter#assemblySinglePerInfoBo, econtractSingleDeliveryPerInfoBo: {}", JSON.toJSONString(econtractSingleDeliveryPerInfoBo));
        return econtractSingleDeliveryPerInfoBo;
    }
}
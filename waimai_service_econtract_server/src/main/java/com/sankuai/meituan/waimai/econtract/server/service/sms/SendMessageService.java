package com.sankuai.meituan.waimai.econtract.server.service.sms;

import com.alibaba.fastjson.JSON;
import com.dianping.cms.biz.CMSService;
import com.dianping.cms.dto.CMSResultDTO;
import com.dianping.cms.dto.CMSSendDTO;
import com.sankuai.conch.certify.tokenaccess.thrift.GetMobileTokenAndEncryptDataTo;
import com.sankuai.conch.certify.tokenaccess.thrift.GetMobileTokenAndEncryptReqTo;
import com.sankuai.conch.certify.tokenaccess.thrift.GetMobileTokenAndEncryptResTo;
import com.sankuai.conch.certify.tokenaccess.thrift.TokenAccessThriftService;
import com.sankuai.conch.certify.tokenaccess.util.EncryptUtil;
import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractMetricService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/4/3
 */

@Service
@Slf4j
public class SendMessageService {

    @Autowired
    private TokenAccessThriftService.Iface tokenAccessThriftService;


    @Autowired
    private CMSService cmsService;

    @Autowired
    private EcontractMetricService econtractMetricService;

    /**
     * 线上线下通用
     */
    private static final Integer clientId = 2350;


    /**
     * 发送短信
     *
     * @param mobile
     * @param templateId
     * @param pair
     */
    public void sms(String mobile, String templateId, Map<String, String> pair) {
        log.info("sms::mobile = {}, templateId = {}, pair = {}", mobile, templateId, JSON.toJSONString(pair));
        boolean flag = smsEncrypt(mobile, templateId, pair);
        if (!flag) {
            smsNormal(mobile, templateId, pair);
        }
    }


    /**
     * 普通发短信
     *
     * @param mobile
     * @param templateId
     * @param pair
     */
    public void smsNormal(String mobile, String templateId, Map<String, String> pair) {
        log.info("sendNormal::mobile = {}, templateId = {}, pair = {}", mobile, templateId, JSON.toJSONString(pair));
        try {
            int templateIdInt = Integer.parseInt(templateId);
            cmsService.send(templateIdInt, mobile, pair);
        } catch (Exception e) {
            log.error("sendNormal::短信发送失败 mobile:{}, templateId:{}", mobile, templateId, e);
        }
    }

    /**
     * 手机号加密发送短信
     *
     * @param mobile
     * @param templateId
     * @param pair
     * @return
     */
    public boolean smsEncrypt(String mobile, String templateId, Map<String, String> pair) {
        log.info("smsEncrypt::mobile = {}, templateId = {}, pair = {}", mobile, templateId, JSON.toJSONString(pair));
        GetMobileTokenAndEncryptDataTo res = getEncryptMobile(mobile);
        if (res == null) {
            log.warn("手机号加密失败, 尝试不加密直接发送短信, mobile = {}, templateId = {}", mobile, templateId);
            return false;
        }
        CMSSendDTO cmsSendDTO = new CMSSendDTO();
        cmsSendDTO.setMobileEncrypt(res.getMobileNoEncrypt());
        cmsSendDTO.setMobileToken(res.getMobileNoToken());
        cmsSendDTO.setPair(pair);
        CMSResultDTO cmsResultDTO = cmsService.singleSend(Integer.parseInt(templateId), cmsSendDTO);
        log.info("smsEncrypt result = {}", JSON.toJSONString(cmsResultDTO));
        if (cmsResultDTO.isSuccess()) {
            log.info("smsEncrypt::手机号加密短信发送成功, mobile = {}, msgId = {}", mobile, cmsResultDTO.getMsgId());
            return true;
        }
        log.warn("smsEncrypt::手机号加密短信发送失败, mobile = {}, templateId = {}, pair = {}, cmsResultDTO = {}", mobile, templateId, pair, cmsResultDTO);
        return false;
    }


    /**
     * 获取手机号加密信息
     *
     * @param mobile
     * @return
     */
    private GetMobileTokenAndEncryptDataTo getEncryptMobile(String mobile) {
        log.info("getEncryptMobile::mobile = {}", mobile);
        try {
            GetMobileTokenAndEncryptReqTo req = new GetMobileTokenAndEncryptReqTo();
            req.setClientId(clientId);
            String publicKey = MccConfig.getTokenPublicKey();
            req.setMobileNoEncrypt(EncryptUtil.encrypt(mobile, publicKey));
            GetMobileTokenAndEncryptResTo res = tokenAccessThriftService.getMobileTokenAndEncrypt(req);
            if (res == null || res.getStatus().equals("fail")) {
                log.error("getEncryptMobile::调用token平台手机号加密失败, error = {}", res == null ? "" : res.getError());
                return null;
            } else {
                return res.getData();
            }
        } catch (Exception e) {
            log.error("getEncryptMobile::调用token平台异常，mobile = {},e={}", mobile, e.getMessage(), e);
            return null;
        }
    }

}

package com.sankuai.meituan.waimai.econtract.server.service.engine.task.asyncHandler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.meituan.sankuai.bsi.esign.common.model.entity.result.GetContractResult;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.constants.ContextConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.EcontractAsyncTaskConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractAsyncRecordEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordEntity;
import com.sankuai.meituan.waimai.econtract.server.exception.EcontractException;
import com.sankuai.meituan.waimai.econtract.server.utils.DealVersionUtils;
import com.sankuai.meituan.waimai.econtract.server.utils.UrlConvetUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.CallbackConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2022-03-10 11:02
 * Email: <EMAIL>
 * Desc:
 */
@Slf4j
@Service
public class AsyncEstampCommon extends AsyncCommon {


    public void handlerAsyncTaskCommon(JSONObject resultObject, EcontractAsyncRecordEntity econtractAsyncRecord) {
        log.debug("AsyncEstampHandler#handlerAsyncTask#econtractAsyncRecord:{}", JSON.toJSONString(econtractAsyncRecord));
        if (null == econtractAsyncRecord || StringUtils.isEmpty(econtractAsyncRecord.getTaskContext())) {
            log.error("异步签章处理流水信息为空，msg:{}", resultObject.toJSONString());
            throw new EcontractException(EcontractException.ASYNC_HANDLER_ERROR, "异步签章处理流水信息为空");
        }
        EcontractContext context = parseContext(econtractAsyncRecord);
        try {
            //获取任务执行结果
            JSONObject data = resultObject.getJSONObject("data");
            if (null == data) {
                throw new EcontractException(EcontractException.ESIGN_CALLBACK_ERROR, "签章平台回调结果格式异常");
            }
            Integer code = data.getInteger("code");
            //1000:成功；300039:合同重复签署，继续流程
            if (code == 1000 || code == 300039) {
                String taskType = abstractExecutor.getTaskType(context);
                String contractId = context.getTaskTypeAndContractIdMap().get(taskType);
                GetContractResult getContractResult = esignClient.downloadContract(contractId);
                String stampUrl = UrlConvetUtil.changeEstampUrl(getContractResult.getContract());
                Map<String, String> paramMap = Maps.newHashMap();
                paramMap.put(CallbackConstant.ESTAMP_RESULT_CODE, "1000");
                paramMap.put(CallbackConstant.ESTAMP_DOWNLOAD_URL, stampUrl);
                paramMap.put(CallbackConstant.ESTAMP_TRANSCATION_ID,
                        DealVersionUtils.getEstampDealVersion(context.getTaskContext().getTaskId()));
                context.getTaskContext().setExecutorResult(paramMap);
                context.getTaskContext().setState(TaskConstant.TASK_SUCCESS);
                EcontractRecordEntity recordEntity =
                        econtractRecordService.queryRecordByRecordKey(context.getEcontractRecordEntity().getRecordKey());
                context.getTaskContext().setEcontractStage(recordEntity.getEcontractStage());
                context.setContextState(ContextConstant.CONTEXT_CALLBACK);
                context.setExecuteTaskId(context.getTaskContext().getTaskId());
                estampExecutor.casRecordSaveUrl(recordEntity, stampUrl, taskType);
                abstractExecutor.executeCallback(context);
                //更新异步任务信息
                updateAsyncTaskStatus(econtractAsyncRecord, EcontractAsyncTaskConstant.SUCCESS);
                //记录异步签章耗时
                metricAsyncEstampCost(econtractAsyncRecord);
                //记录签章执行状态
                metricEstampStatus("签章成功", "异步签章");
            } else {
                throw new EcontractException(EcontractException.ESIGN_CALLBACK_ERROR, "签章平台异步签章失败");
            }
        } catch (EcontractException econtractException) {
            //失败重试
            log.error("handlerAsyncTask签章平台处理异常，recordKey:{},errorCode:{}, errorMsg:{}, result:{}",context.getRecordBizKey(),
                    econtractException.getErrorCode(), econtractException.getMessage(), resultObject.toJSONString());
            updateAsyncTaskStatus(econtractAsyncRecord, EcontractAsyncTaskConstant.FAIL);
            failRetry(context);
            metricEstampStatus("签章失败", "异步签章");
        } catch (Exception exception) {
            //失败重试
            log.error("handlerAsyncTask系统异常，recordKey:{},result:{}", context.getRecordBizKey(),resultObject.toJSONString(), exception);
            updateAsyncTaskStatus(econtractAsyncRecord, EcontractAsyncTaskConstant.FAIL);
            failRetry(context);
            metricEstampStatus("签章失败", "异步签章");
        }
    }

    private void failRetry(EcontractContext context) {
        String msg = "未知错误,签章失败";
        context.getTaskContext().setErrorCode(EcontractException.ESTAMP_UPLOAD_ERROR);
        context.getTaskContext().setState(TaskConstant.TASK_FAIL);
        context.getTaskContext().setFailMessage(msg);
        log.info("failRetry 未知错误,签章失败,recordKey:{}",context.getRecordBizKey());
        abstractExecutor.executeFail(context, msg);

    }

    private void metricAsyncEstampCost(EcontractAsyncRecordEntity econtractAsyncRecord) {
        Long currentTime = System.currentTimeMillis();
        Long costTime = currentTime - econtractAsyncRecord.getCtime();
        econtractMetricService.metricAsyncOperateCost("estamp", costTime);
    }

    private void metricEstampStatus(String estampStatus, String estampType) {
        econtractMetricService.metricEstampStatus(estampStatus, estampType);
    }
}

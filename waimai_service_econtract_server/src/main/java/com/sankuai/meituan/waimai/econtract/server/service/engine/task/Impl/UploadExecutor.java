package com.sankuai.meituan.waimai.econtract.server.service.engine.task.Impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.meituan.sankuai.bsi.esign.common.model.entity.request.ContractRequest;
import com.meituan.sankuai.bsi.esign.common.model.entity.result.CreateContractResult;
import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.econtract.server.adapter.EsignClient;
import com.sankuai.meituan.waimai.econtract.server.annotation.EcontractIdempotent;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EstampFileUpLoadResultBo;
import com.sankuai.meituan.waimai.econtract.server.constants.ContextConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.KmsConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.MccConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractTaskEntity;
import com.sankuai.meituan.waimai.econtract.server.exception.EcontractException;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractRecordService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractTaskService;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.TaskExecutor;
import com.sankuai.meituan.waimai.econtract.server.template.config.entity.EcontractTemplateVersionEntity;
import com.sankuai.meituan.waimai.econtract.server.template.config.entity.EcontractTemplateVersionSimpleEntity;
import com.sankuai.meituan.waimai.econtract.server.template.config.service.EcontractTemplateBaseService;
import com.sankuai.meituan.waimai.econtract.server.template.config.service.EcontractTemplateVersionService;
import com.sankuai.meituan.waimai.econtract.server.utils.*;
import com.sankuai.meituan.waimai.econtrct.client.template.config.domain.EcontractTemplateBaseBo;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.elasticsearch.common.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Map;

/**
 * <AUTHOR> Hou
 * @date 2017/10/25
 * @time 上午10:58
 */
@Service
public class UploadExecutor extends AbstractExecutor implements TaskExecutor {

    private static final Logger LOGGER = LoggerFactory.getLogger(UploadExecutor.class);

    private static final int SUCCESS = 6000;

    private static final Integer MAX_CAS_RETRY_TIMES = 5;

    private static final String CREATOR_CUSTOMER_ID = "creator.customer.id";

    public static final String MAX_CAS_KEY = "updateContractId_MAX_CAS_RETRY_TIMES";

    @Resource
    private EcontractTaskService econtractTaskService;

    @Resource
    private EcontractRecordService econtractRecordService;

    @Resource
    private EcontractTemplateBaseService econtractTemplateBaseService;
    @Resource
    private EcontractTemplateVersionService econtractTemplateVersionService;

    @Resource
    private EsignClient esignClient;

    private static final String UPLOAD_PDF = "upload_pdf";

    @Override
    @EcontractIdempotent
    public void executeTask(EcontractContext context) {
        try {
            LOGGER.info("UploadExecutor executeTask,recordKey:{}",context.getEcontractRecordEntity().getRecordKey());
            routeToPostForUpload(context);
        } catch (Exception e) {
            LOGGER.error("fail to UploadExecutor :" + context.getEcontractRecordEntity().getRecordKey() + ", msg:" + e.getMessage(), e);
            executeFail(context, e);
        }
    }

    private void handlePostForUpLoadPdf(EcontractContext context) throws ParseException, IOException, EcontractException, KmsResultNullException {
        if (postForUpLoadPdfByFile(context)) {
            context.setContextState(ContextConstant.CONTEXT_FINISH);
            executeSuccess(context);
            LOGGER.info("UploadExecutor success , recordkey : {} ", context.getEcontractRecordEntity().getRecordKey());
            metricExecutorResult(UPLOAD_PDF, true);
        } else {
            metricExecutorResult(UPLOAD_PDF, false);
        }
    }

    private boolean postForUpLoadPdfByFile(EcontractContext context) throws ParseException, IOException, EcontractException, KmsResultNullException {
        String uploadCallbackUrl = ConfigUtilAdapter.getString(MccConstant.CERTIFY_UPLOAD_CALL_BACK_URL, "http://econtract.waimai.dev.sankuai.com/econtract/restful/api/v1/call_back/ca/upload_pdf");
        String app_id = Kms.getByName(MccConstant.APP_KEY, KmsConstant.CERTIFY_APP_ID);
        String app_secret = Kms.getByName(MccConstant.APP_KEY, KmsConstant.CERTIFY_APP_SECRET);
        Timestamp ts = new Timestamp(System.currentTimeMillis());
        String timeStr = new SimpleDateFormat("yyyyMMddHHmmss").format(ts);
        String transactionId = DealVersionUtils.getUploadPdfDealVersion(context.getTaskContext().getTaskId());

        String contractId = genContractId(context, context.getEcontractRecordEntity().getRecordKey());
        context.getTaskTypeAndContractIdMap().put(getTaskType(context), contractId);
        Map<String, String> paramMap = Maps.newHashMap();
        paramMap.put("app_id", app_id);
        paramMap.put("v", "2.0");
        paramMap.put("contract_id", contractId);
        paramMap.put("transaction_id", transactionId);
        paramMap.put("timestamp", timeStr);
        paramMap.put("msg_digest", CaSignUtil.getMsgDigestForFileUpload(timeStr, contractId, app_id, app_secret));
        paramMap.put("doc_type", ".pdf");
        paramMap.put("callback", uploadCallbackUrl);

        EcontractTaskEntity taskEntity = econtractTaskService.selectByPrimaryKeyMaster(context.getTaskContext().getTaskId());
        LOGGER.info("paramMap:" + JSON.toJSONString(paramMap));
        String result = HttpClientUtil.doPostForUpLoad(ImageUtil.getCloudPicBytes(RecordUtil.getExecutiveUrl(context, taskEntity)), paramMap, ConfigUtilAdapter.getString(MccConstant.CERTIFY_UPLOAD_URL));
        EstampFileUpLoadResultBo estampFileUpLoadResultBo = null;
        if (!HttpClientUtil.HTTP_RETURN_ERROR.equals(result)) {
            estampFileUpLoadResultBo = JSON.parseObject(result, EstampFileUpLoadResultBo.class);
            LOGGER.info("estampFileUpLoad return : {}", result);
            if (SUCCESS == estampFileUpLoadResultBo.getCode()) {
                return true;
            } else {
                EcontractException.ESTAMP_UPLOAD_EXCEPTION.newInstance(estampFileUpLoadResultBo.getMsg());
            }
        }
        return false;
    }

    private void handlePostForUpload(EcontractContext context) throws IOException {
        String contractId = postForUpload(context);
        if (StringUtils.isNotBlank(contractId)) {
            context.setExecuteTaskId(context.getTaskContext().getTaskId());
            context.setContextState(ContextConstant.CONTEXT_CALLBACK);
            executeCallback(context);
            LOGGER.info("UploadExecutor callback , recordkey : {} ", context.getEcontractRecordEntity().getRecordKey());
            metricExecutorResult(UPLOAD_PDF, true);
        } else {
            metricExecutorResult(UPLOAD_PDF, false);
            context.setContextState(ContextConstant.CONTEXT_FINISH);
            executeFail(context, "上传PDF失败");
        }
    }

    private String postForUpload(EcontractContext context) throws IOException {
        EcontractTaskEntity taskEntity = econtractTaskService.selectByPrimaryKeyMaster(context.getTaskContext().getTaskId());
        ContractRequest request = initContractRequest(context, taskEntity);
        LOGGER.info("UploadExecutor#postForUpload, recordKey:{}, request: {}", context.getEcontractRecordEntity().getRecordKey(), JacksonUtil.writeAsJsonStr(request));
        CreateContractResult createRes = esignClient.createContract(request);
        LOGGER.info("UploadExecutor#postForUpload, recordKey:{}, createRes:{}", context.getEcontractRecordEntity().getRecordKey(), JacksonUtil.writeAsJsonStr(createRes));
        if (StringUtils.isNotBlank(createRes.getContractId())) {
            String taskType = getTaskType(context);
            context.getTaskContext().getExecutorResult().put(TaskConstant.CONTRACT_ID, createRes.getContractId());
            context.getTaskContext().getExecutorResult().put(TaskConstant.TASK_TYPE, taskType);
            Map<String, String> map = updateContractId(context.getEcontractRecordEntity().getRecordKey(), taskType, createRes.getContractId());
            context.setTaskTypeAndContractIdMap(map);
            LOGGER.info("UploadExecutor#postForUpload, taskTypeAndContractIdMap = {}", JSON.toJSONString(context.getTaskTypeAndContractIdMap()));
            return createRes.getContractId();
        }
        return StringUtils.EMPTY;
    }

    private ContractRequest initContractRequest(EcontractContext context, EcontractTaskEntity taskEntity) throws IOException {
        // 1. 初始化请求对象
        ContractRequest request = new ContractRequest();
        request.setContractDatas(ImageUtil.getCloudPicBytes(RecordUtil.getExecutiveUrl(context, taskEntity)));

        // 2. 获取模板信息
        Pair<Integer, Integer> templatePair = ContractContentTemplateUtil.extractTemplateIdAndVersionFromEcontractContext(context);
        if (templatePair == null || templatePair.getLeft() == null) {
            return request;
        }

        // 3. 设置模板编码
        Integer templateId = templatePair.getLeft();
        EcontractTemplateBaseBo templateBaseBo = econtractTemplateBaseService.getTemplateBaseById(templateId);
        if (Strings.isEmpty(templateBaseBo.getHaiLuoTemplateCode())) {
            return request;
        }
        request.setTemplateCode(templateBaseBo.getHaiLuoTemplateCode());

        // 4. 设置模板版本
        Integer haiLuoTemplateVersion = getHaiLuoTemplateVersion(templateId, templatePair.getRight());
        if (haiLuoTemplateVersion != null && haiLuoTemplateVersion > 0) {
            request.setTemplateVersion(haiLuoTemplateVersion);
        }
        return request;
    }

    /**
     * 获取模板版本号
     */
    private Integer getHaiLuoTemplateVersion(Integer templateId, Integer version) {
        try {
            if (version != null && version > 0) {
                // 指定版本
                EcontractTemplateVersionEntity versionEntity =
                    econtractTemplateVersionService.getTemplateVersionEntityByTemplateIdAndVersion(templateId, version);
                return versionEntity != null ? Math.toIntExact(versionEntity.getHailuoTemplateVersion()) : null;
            }
            // 未指定版本号的，兜底装配当前已发布版本号，如果这里版本为null或0则说明该模版没有已发布的版本。
            EcontractTemplateVersionSimpleEntity versionEntity =
                econtractTemplateVersionService.getReleasedTemplateVersionEntity(templateId);
            return versionEntity != null ? Math.toIntExact(versionEntity.getHailuoTemplateVersion()) : null;
        } catch (Exception e) {
            LOGGER.error("UploadExecutor#getTemplateVersion, 获取模板版本失败, templateId:{}, version:{}", templateId, version, e);
            return null;
        }
    }

    /**
     * 更新数据库中的contractId
     */
    private Map<String, String> updateContractId(String recordKey, String taskType, String contractId) {
        int updateCount = 0;
        for (int retryTime = ConfigUtilAdapter.getInt(MAX_CAS_KEY,MAX_CAS_RETRY_TIMES); retryTime > 0; retryTime--) {
            EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(recordKey);
            EcontractContext context = JSON.parseObject(recordEntity.getEcontractRecordContext(), EcontractContext.class);
            context.getTaskTypeAndContractIdMap().put(taskType, contractId);
            recordEntity.setEcontractRecordContext(JSON.toJSONString(context));
            updateCount += econtractRecordService.updateOptimisticLock(recordEntity);

            if (updateCount > 0) {
                return context.getTaskTypeAndContractIdMap();
            }
        }
        return Maps.newHashMap();
    }

    public void routeToPostForUpload(EcontractContext context)
        throws ParseException, IOException, KmsResultNullException {
        if (EcontractStampRouteUtil.checkRoutingToSSQ(context.getStampKey())) {
            handlePostForUpload(context);
        } else {
            handlePostForUpLoadPdf(context);
        }
    }

}

package com.sankuai.meituan.waimai.econtract.server.service.engine.task.Impl;

import com.google.common.collect.Maps;

import com.alibaba.fastjson.JSON;
import com.meituan.sankuai.bsi.esign.common.model.entity.result.ApplyCertResult;
import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.econtract.server.adapter.EsignClient;
import com.sankuai.meituan.waimai.econtract.server.annotation.EcontractIdempotent;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractCaCertifyResultBo;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.bo.task.TaskContext;
import com.sankuai.meituan.waimai.econtract.server.constants.CertifyConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.CertifyKeyEnum;
import com.sankuai.meituan.waimai.econtract.server.constants.ContextConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.KmsConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.MccConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractCertifyEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordEntity;
import com.sankuai.meituan.waimai.econtract.server.exception.EcontractException;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractCertifyService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractRecordService;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.TaskExecutor;
import com.sankuai.meituan.waimai.econtract.server.utils.CaSignUtil;
import com.sankuai.meituan.waimai.econtract.server.utils.CryptTool;
import com.sankuai.meituan.waimai.econtract.server.utils.EcontractStampRouteUtil;
import com.sankuai.meituan.waimai.econtract.server.utils.HttpClientUtil;
import com.sankuai.meituan.waimai.econtract.server.utils.StageInfoCheckUtils;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.CAType;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.CertifyInfoBo;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

import javax.annotation.Resource;

/**
 * <AUTHOR> Hou
 * @date 2017/10/23
 * @time 下午12:43
 */
@Service
public class CaCertifyExecutor extends AbstractExecutor implements TaskExecutor {

    private static final Logger LOGGER = LoggerFactory.getLogger(CaCertifyExecutor.class);

    private static final Integer MAX_CAS_RETRY_TIMES = 5;

    @Autowired
    private EcontractCertifyService econtractCertifyService;

    @Resource
    private EcontractRecordService econtractRecordService;

    @Resource
    private EsignClient esignClient;

    private static final String CA_CERTIFY = "ca_certify";


    @Override
    @EcontractIdempotent
    public void executeTask(EcontractContext context) {
        try {
            LOGGER.info("CaCertifyExecutor executeTask,recordKey:{}",context.getEcontractRecordEntity().getRecordKey());
            String customerId = "";
            //1.校验CA信息是否完整
            if (!StageInfoCheckUtils.checkForPostCaInfo(context.getTaskContext().getStageInfoBo().getCertifyInfoBo())) {
                EcontractException.CA_PARAM_EXCEPTION.newInstance("CA信息不完整");
            }
            //2.验证是否是美团或者钱袋宝
            if (CertifyConstant.MEI_TUAN_CA.equals(context.getTaskContext().getStageInfoBo().getCertifyInfoBo().getCustomerName())
                || CertifyConstant.QIAN_DAI_BAO_CA.equals(context.getTaskContext().getStageInfoBo().getCertifyInfoBo().getCustomerName())) {
                LOGGER.info("CaCertifyTaskExecutor success , do not need CA, recordkey : {} , customer name is :{}",
                            context.getEcontractRecordEntity().getRecordKey(),
                            context.getTaskContext().getStageInfoBo().getCertifyInfoBo().getCustomerName());
                context.getTaskContext().setExecutorResult(Maps.newHashMap());
                context.getTaskContext().getExecutorResult().put(TaskConstant.CUSTOMER_ID, customerId);
                context.setContextState(ContextConstant.CONTEXT_FINISH);
                executeSuccess(context);
                return;
            }
            //3.验证是否存在的customerId
            if (StringUtils.isNotBlank(context.getTaskContext().getStageInfoBo().getCertifyInfoBo().getCustomerId())) {
                context.getTaskContext().setExecutorResult(Maps.newHashMap());
                context.getTaskContext().getExecutorResult().put(TaskConstant.CUSTOMER_ID, context.getTaskContext().getStageInfoBo().getCertifyInfoBo().getCustomerId());
                context.setContextState(ContextConstant.CONTEXT_FINISH);
                Map<String, String> customerIdMap = updateCustomerId(context.getEcontractRecordEntity().getRecordKey(),
                                                           context.getTaskContext().getStageInfoBo().getStageName(),
                                                           context.getTaskContext().getStageInfoBo().getCertifyInfoBo().getCustomerId());
                context.setStageNameAndCustomerId(customerIdMap);
                executeSuccess(context);
                return;
            }

            //3.查询CA池子
            context.getTaskContext().getStageInfoBo().getCertifyInfoBo().setStampKey(context.getStampKey());
            EcontractCertifyEntity certifyEntity = econtractCertifyService.queryByCertifyInfoBo(context.getTaskContext().getStageInfoBo().getCertifyInfoBo(), CertifyKeyEnum.WITHSOURCE);
            if (certifyEntity != null && StringUtils.isNotBlank(certifyEntity.getCustomerId())) {
                LOGGER.info("CaCertifyTaskExecutor success , do not need CA, recordkey : {} , customerId is :{}", context.getEcontractRecordEntity().getRecordKey(), certifyEntity.getCustomerId());
                context.getTaskContext().setExecutorResult(Maps.newHashMap());
                context.getTaskContext().getExecutorResult().put(TaskConstant.CUSTOMER_ID, certifyEntity.getCustomerId());
                context.setContextState(ContextConstant.CONTEXT_FINISH);
                Map<String, String> customerIdMap = updateCustomerId(context.getEcontractRecordEntity().getRecordKey(),
                                                                     context.getTaskContext().getStageInfoBo().getStageName(),
                                                                     certifyEntity.getCustomerId());
                context.setStageNameAndCustomerId(customerIdMap);
                executeSuccess(context);
                return;
            }

            //4.执行CA校验
            CertifyInfoBo certifyInfoBo = context.getTaskContext().getStageInfoBo().getCertifyInfoBo();
            EcontractCaCertifyResultBo resultBo =
                routePostForCA(context.getStampKey(), certifyInfoBo, context.getEcontractRecordEntity().getRecordKey());
            LOGGER.info("CaCertifyExecutor executeTask result,recordKey:{},resultBo:{}",context.getEcontractRecordEntity().getRecordKey(),JSON.toJSONString(resultBo));
            if (StringUtils.isNotEmpty(resultBo.getCustomer_id())) {
                context.getTaskContext().setExecutorResult(Maps.newHashMap());
                context.getTaskContext().getExecutorResult().put(TaskConstant.CUSTOMER_ID, resultBo.getCustomer_id());
                context.getTaskContext().getExecutorResult().put(TaskConstant.CA_RETURN_CODE, String.valueOf(resultBo.getCode()));
                context.setContextState(ContextConstant.CONTEXT_FINISH);
                insertCaPool(context.getTaskContext().getStageInfoBo().getCertifyInfoBo(), context);
                Map<String, String> customerIdMap = updateCustomerId(context.getEcontractRecordEntity().getRecordKey(),
                                                                     context.getTaskContext().getStageInfoBo().getStageName(),
                                                                     resultBo.getCustomer_id());
                context.setStageNameAndCustomerId(customerIdMap);
                executeSuccess(context);
                LOGGER.info("CaCertifyTaskExecutor success , recordkey : {} ",
                            context.getEcontractRecordEntity().getRecordKey());
                metricExecutorResult(CA_CERTIFY, true);
            } else {
                EcontractException.CA_EXCEPTION.newInstance(resultBo.getMsg());
            }
        } catch (EcontractException e) {
            metricExecutorResult(CA_CERTIFY, false);
            LOGGER.warn("fail to CaCertifyTaskExecutor :" + context.getEcontractRecordEntity().getRecordKey() + ",msg :" + e.getMessage(), e);
            executeFail(context, e);
        } catch (Exception e) {
            metricExecutorResult(CA_CERTIFY, false);
            LOGGER.error("fail to CaCertifyTaskExecutor :" + context.getEcontractRecordEntity().getRecordKey() + ",msg :" + e.getMessage(), e);
            executeFail(context, e);
        }
    }

    /**
     * 更新数据库中的contractId
     */
    private Map<String, String> updateCustomerId(String recordKey, String stageName, String customerId) {
        int updateCount = 0;
        for (int retryTime = MAX_CAS_RETRY_TIMES; retryTime > 0; retryTime--) {
            EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(recordKey);
            EcontractContext context =
                JSON.parseObject(recordEntity.getEcontractRecordContext(), EcontractContext.class);
            context.getStageNameAndCustomerId().put(stageName, customerId);
            recordEntity.setEcontractRecordContext(JSON.toJSONString(context));
            updateCount += econtractRecordService.updateOptimisticLock(recordEntity);

            if (updateCount > 0) {
                return context.getStageNameAndCustomerId();
            }
        }
        return Maps.newHashMap();
    }

    private void insertCaPool(CertifyInfoBo certifyInfoBo, EcontractContext context) throws NoSuchAlgorithmException {
        TaskContext taskContext = context.getTaskContext();
        EcontractCertifyEntity econtractCertifyEntity = new EcontractCertifyEntity();
        econtractCertifyEntity.setEcontractUserId(context.getEcontractUserEntity().getId());
        econtractCertifyEntity.setType(certifyInfoBo.getCaType().getValue());
        econtractCertifyEntity.setDetail(JSON.toJSONString(certifyInfoBo));
        econtractCertifyEntity.setResult(Integer.parseInt(taskContext.getExecutorResult().get(TaskConstant.CA_RETURN_CODE)));
        econtractCertifyEntity.setValid(CertifyConstant.VALID);
        econtractCertifyEntity.setCtime(new Date());
        econtractCertifyEntity.setUtime(new Date());
        econtractCertifyEntity.setCustomerId(taskContext.getExecutorResult().get(TaskConstant.CUSTOMER_ID));

        econtractCertifyEntity.setCertifyKey(econtractCertifyService.wrapEstampCaKeyWithStampSource(certifyInfoBo));
        insertOrUpdate(certifyInfoBo, taskContext, econtractCertifyEntity, CertifyKeyEnum.WITHSOURCE);

        if (ConfigUtilAdapter.getBoolean("certify_write_compatible_all", true)) {
            econtractCertifyEntity.setCertifyKey(econtractCertifyService.wrapEstampCaKey(certifyInfoBo));
            insertOrUpdate(certifyInfoBo, taskContext, econtractCertifyEntity, CertifyKeyEnum.ALL);
        }

        if (ConfigUtilAdapter.getBoolean("certify_write_compatible", true)) {
            econtractCertifyEntity.setCertifyKey(econtractCertifyService.wrapEstampCaKey(certifyInfoBo.getEmail(), certifyInfoBo.getMobile()));
            insertOrUpdate(certifyInfoBo, taskContext, econtractCertifyEntity, CertifyKeyEnum.EMAIL_AND_PHONE);
        }
    }

    private void insertOrUpdate(CertifyInfoBo certifyInfoBo, TaskContext taskContext, EcontractCertifyEntity econtractCertifyEntity, CertifyKeyEnum keyEnum)
        throws NoSuchAlgorithmException {
        EcontractCertifyEntity oldEntity = econtractCertifyService.queryByCertifyInfoBo(certifyInfoBo, keyEnum);
        if (null == oldEntity) {
            econtractCertifyService.insertSelective(econtractCertifyEntity);
        } else {
            oldEntity.setCustomerId(taskContext.getExecutorResult().get(TaskConstant.CUSTOMER_ID));
            oldEntity.setUtime(new Date());
            econtractCertifyService.updateByPrimaryKeySelective(oldEntity);
        }
    }

    private EcontractCaCertifyResultBo postForCA(CertifyInfoBo certifyInfoBo, String recordKey) throws IOException, ParseException, NoSuchAlgorithmException, KmsResultNullException {
        //存放请求key:val数据对
        Map<String, String> params = Maps.newHashMap();
        //获取个人ca请求url,配置于mcc上
        String caUrl = "";

        if (CAType.PERSON.equals(certifyInfoBo.getCaType())) {
            caUrl = ConfigUtilAdapter.getString(MccConstant.CERTIFY_PERSON_URL, "http://10.4.240.232:8080/api/syncPerson_auto.api");
        } else {
            caUrl = ConfigUtilAdapter.getString(MccConstant.CERTIFY_COMPANY_URL, "");
        }

        //获取app_id,配置于KMS上
        String app_id = Kms.getByName(MccConstant.APP_KEY, KmsConstant.CERTIFY_APP_ID);
        //获取app_secret,配置于KMS上
        String app_secret = Kms.getByName(MccConstant.APP_KEY, KmsConstant.CERTIFY_APP_SECRET);
        params.put("customer_name", certifyInfoBo.getCustomerName());
        params.put("email", certifyInfoBo.getEmail());
        params.put("app_id", app_id);
        //生成特定格式的时间戳
        Timestamp ts = new Timestamp(System.currentTimeMillis());
        DateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String timestamp = sdf.format(ts);
        params.put("timestamp", timestamp);
        //生成mobile_id
        String id_moblie = CryptTool.encrypt(certifyInfoBo.getQuaNum() + "|" + certifyInfoBo.getMobile(), app_secret);
        params.put("id_mobile", id_moblie);
        //生成msg_digest
        String msg_digest = CaSignUtil.getMsgDigestForCa(timestamp, app_id, app_secret);
        params.put("msg_digest", msg_digest);
        //设置版本，默认2.0，非必填
        params.put("v", "2.0");
        String postResult = HttpClientUtil.doPostRequest(params, caUrl);
        EcontractCaCertifyResultBo econtractCaCertifyResultBo = JSON.parseObject(postResult, EcontractCaCertifyResultBo.class);
        if (StringUtils.isBlank(econtractCaCertifyResultBo.getMsg()) && StringUtils.isNotBlank(econtractCaCertifyResultBo.getMessage())) {
            econtractCaCertifyResultBo.setMsg(econtractCaCertifyResultBo.getMessage());
        }
        LOGGER.info("record {}， CA certify result : {} ", recordKey, JSON.toJSONString(econtractCaCertifyResultBo));
        return econtractCaCertifyResultBo;
    }

    private EcontractCaCertifyResultBo postForCA(CertifyInfoBo certifyInfoBo) {
        ApplyCertResult applyResult = esignClient.applyCert(certifyInfoBo);
        EcontractCaCertifyResultBo econtractResultBo = new EcontractCaCertifyResultBo();
        econtractResultBo.setCode(applyResult.getCode());
        econtractResultBo.setCustomer_id(applyResult.getCustomerId());
        econtractResultBo.setMsg(applyResult.getMessage());
        econtractResultBo.setMessage(applyResult.getMessage());
        return econtractResultBo;
    }

    /**
     * 根据签章路由关键字，申请不同平台的CA认证
     */
    private EcontractCaCertifyResultBo routePostForCA(String stampKey, CertifyInfoBo certifyInfoBo, String recordKey)
        throws KmsResultNullException, NoSuchAlgorithmException, ParseException, IOException {
        LOGGER.info("CaCertifyExecutor routePostForCA,recordKey:{},stampKey:{}",recordKey,stampKey);
        if (EcontractStampRouteUtil.checkRoutingToSSQ(stampKey)) {
            return postForCA(certifyInfoBo);
        } else {
            return postForCA(certifyInfoBo, recordKey);
        }
    }
}

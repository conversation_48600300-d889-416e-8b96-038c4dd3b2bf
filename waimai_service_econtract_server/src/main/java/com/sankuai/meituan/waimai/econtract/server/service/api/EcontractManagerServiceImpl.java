package com.sankuai.meituan.waimai.econtract.server.service.api;

import static com.sankuai.meituan.waimai.econtract.server.exception.EcontractException.SERVER_ERROR;
import static com.sankuai.meituan.waimai.econtrct.client.constants.EcontractAPIResponseConstant.*;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.itextpdf.text.DocumentException;
import com.meituan.mtrace.Tracer;
import com.sankuai.inf.octo.mns.util.ProcessInfoUtil;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.econtract.server.adapter.UacAuthRemoteServiceAdapter;
import com.sankuai.meituan.waimai.econtract.server.adapter.WmEmployAdapter;
import com.sankuai.meituan.waimai.econtract.server.bo.query.EcontractRecordQueryBo;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import com.sankuai.meituan.waimai.econtract.server.constants.MccConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractFtlTemplateEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRelEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractSignBatchEventEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractSignPageTemplateEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractSignRecordBatchEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractUserEntity;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractPdfService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractRecordService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractRelService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractSignPageTemplateService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractSignRecordBatchEventService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractSignRecordBatchService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractTaskService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractUserService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.Impl.EcontractFtlTemplateServiceImpl;
import com.sankuai.meituan.waimai.econtract.server.service.config.EcontractSignPageService;
import com.sankuai.meituan.waimai.econtract.server.service.engine.template.manager.TemplateManager;
import com.sankuai.meituan.waimai.econtract.server.service.sms.EcontractSmsActivityService;
import com.sankuai.meituan.waimai.econtract.server.template.config.service.EcontractTemplateVersionService;
import com.sankuai.meituan.waimai.econtract.server.utils.*;
import com.sankuai.meituan.waimai.econtrct.client.constants.BatchEventStatusEnum;
import com.sankuai.meituan.waimai.econtrct.client.constants.BatchSignBizLineEnum;
import com.sankuai.meituan.waimai.econtrct.client.constants.BatchSignStatusEnum;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractBaseConstant;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractErrorCode;
import com.sankuai.meituan.waimai.econtrct.client.constants.WebViewConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.*;
import com.sankuai.meituan.waimai.econtrct.client.domain.base.BoolResult;
import com.sankuai.meituan.waimai.econtrct.client.domain.biz.EcontractRecordBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.biz.EcontractRecordWebQueryBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.biz.EcontractTemplateBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.biz.PageAndListInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.biz.PageInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.common.BaseResponse;
import com.sankuai.meituan.waimai.econtrct.client.domain.config.econtracttemplate.EcontractTemplateCopyRequestDTO;
import com.sankuai.meituan.waimai.econtrct.client.domain.config.signflow.*;
import com.sankuai.meituan.waimai.econtrct.client.domain.config.signpage.*;
import com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException;
import com.sankuai.meituan.waimai.econtrct.client.service.EcontractManagerService;
import com.sankuai.meituan.waimai.econtrct.client.template.config.domain.EcontractTemplateVersionBo;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.util.DaxiangUtil;
import freemarker.template.TemplateException;
import java.io.IOException;
import java.net.URISyntaxException;
import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.util.Strings;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 电子合同模板管理
 *
 * <AUTHOR> Hou
 * @date 2017/12/19
 * @time 上午11:30
 */
@Service
public class EcontractManagerServiceImpl implements EcontractManagerService {

    private static final Logger LOGGER = LoggerFactory.getLogger(EcontractManagerServiceImpl.class);


    @Autowired
    private EcontractRecordService econtractRecordService;
    @Autowired
    private EcontractService econtractService;
    @Autowired
    private EcontractUserService userService;
    @Autowired
    private EcontractPdfService econtractPdfService;
    @Autowired
    private EcontractRelService econtractRelService;
    @Autowired
    private EcontractFtlTemplateServiceImpl econtractFtlTemplateService;
    @Autowired
    private EcontractSignPageTemplateService econtractSignPageTemplateService;
    @Autowired
    private EcontractTaskService econtractTaskService;
    @Autowired
    private EcontractSignRecordBatchEventService econtractSignRecordBatchEventService;
    @Autowired
    private EcontractSignRecordBatchService econtractSignRecordBatchService;
    @Autowired
    private TemplateManager templateManager;

    @Resource
    private EcontractTemplateVersionService econtractTemplateVersionService;

    @Resource
    private EcontractSignPageService econtractSignPageService;

    @Resource
    private WmEmployAdapter employAdapter;

    @Resource
    private UacAuthRemoteServiceAdapter uacAuthRemoteServiceAdapter;

    @Resource
    private EcontractSmsActivityService econtractSmsActivityService;


    @Override
    public List<EcontractRecordBo> queryEcontractRecordByPage(EcontractRecordWebQueryBo webQueryBo) {
        EcontractUserEntity econtractUserEntity = userService.queryUserByName(webQueryBo.getEcontractUserName());

        EcontractRecordQueryBo queryBo = new EcontractRecordQueryBo();
        queryBo.setRecordKey(StringUtils.isNotEmpty(webQueryBo.getRecordKey()) ? webQueryBo.getRecordKey() : null);
        queryBo.setRecordBizKey(StringUtils.isNotEmpty(webQueryBo.getRecordBizKey()) ? webQueryBo.getRecordBizKey() :
            null);
        queryBo.setEcontractStage(StringUtils.isNotEmpty(webQueryBo.getEcontractStage()) ?
            webQueryBo.getEcontractStage() : null);
        queryBo.setEcontractState(StringUtils.isNotEmpty(webQueryBo.getEcontractState()) ?
            webQueryBo.getEcontractState() : null);
        queryBo.setEcontractType(StringUtils.isNotEmpty(webQueryBo.getEcontractType()) ?
            webQueryBo.getEcontractType() : null);
        if (null != econtractUserEntity) {
            queryBo.setEcontractUserId(econtractUserEntity.getId());
        }
        if ((null != webQueryBo.getStartTime() && webQueryBo.getStartTime() > 0)
            && (null != webQueryBo.getEndTime() && webQueryBo.getEndTime() > 0)) {
            queryBo.setStartTime(new Date(webQueryBo.getStartTime() * 1000));
            queryBo.setEndTime(new Date(webQueryBo.getEndTime() * 1000));
        }
        queryBo.setOffset(PageUtil.getOffset(webQueryBo.getPageNum(), WebViewConstant.PAGE_SIZE));
        queryBo.setLimit(WebViewConstant.PAGE_SIZE);

        List<EcontractRecordEntity> econtractRecordEntityList = econtractRecordService.queryEcontractRecordByPage(
            queryBo);
        List<EcontractRecordBo> econtractRecordBoList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(econtractRecordEntityList)) {
            for (EcontractRecordEntity entity : econtractRecordEntityList) {
                EcontractRecordBo econtractRecordBo = new EcontractRecordBo();
                econtractRecordBo.setEcontractStage(entity.getEcontractStage());
                econtractRecordBo.setEcontractState(entity.getEcontractState());
                econtractRecordBo.setEcontractType(entity.getEcontractType());
                econtractRecordBo.setFailMessage(entity.getFailMessage());
                econtractRecordBo.setSaveUrl(
                    ConfigUtilAdapter.getString(MccConstant.DOWN_LOAD_HOST) + entity.getSaveUrl());
                econtractRecordBo.setRecordKey(entity.getRecordKey());
                econtractRecordBo.setCtime(entity.getCtime().getTime() / 1000);
                econtractRecordBo.setUtime(entity.getUtime().getTime() / 1000);
                econtractRecordBoList.add(econtractRecordBo);
            }
        }
        return econtractRecordBoList;
    }

    @Override
    public List<EcontractTemplateBo> queryEcontractTemplateBoList() {
        List<EcontractTemplateBo> templateBoList = econtractService.queryEcontractList();
        LOGGER.info("EcontractManagerServiceImpl#queryEcontractTemplateBoList, templateBoList: {}", JacksonUtil.writeAsJsonStr(templateBoList));
        return templateBoList;
    }


    @Override
    public PageInfoBo queryEcontractRecordPageInfo(EcontractRecordWebQueryBo webQueryBo) {
        EcontractUserEntity econtractUserEntity = userService.queryUserByName(webQueryBo.getEcontractUserName());

        EcontractRecordQueryBo queryBo = new EcontractRecordQueryBo();
        queryBo.setRecordKey(StringUtils.isNotEmpty(webQueryBo.getRecordKey()) ? webQueryBo.getRecordKey() : null);
        queryBo.setRecordBizKey(
            StringUtils.isNotEmpty(webQueryBo.getRecordBizKey()) ? webQueryBo.getRecordBizKey() : null);
        queryBo.setEcontractStage(
            StringUtils.isNotEmpty(webQueryBo.getEcontractStage()) ? webQueryBo.getEcontractStage() : null);
        queryBo.setEcontractState(
            StringUtils.isNotEmpty(webQueryBo.getEcontractState()) ? webQueryBo.getEcontractState() : null);
        queryBo.setEcontractType(
            StringUtils.isNotEmpty(webQueryBo.getEcontractType()) ? webQueryBo.getEcontractType() : null);
        if (null != econtractUserEntity) {
            queryBo.setEcontractUserId(econtractUserEntity.getId());
        }
        if ((null != webQueryBo.getStartTime() && webQueryBo.getStartTime() > 0)
            && (null != webQueryBo.getEndTime() && webQueryBo.getEndTime() > 0)) {
            queryBo.setStartTime(new Date(webQueryBo.getStartTime()));
            queryBo.setEndTime(new Date(webQueryBo.getEndTime()));
        }
        Integer count = econtractRecordService.queryEcontractRecordByPageCount(queryBo);

        PageInfoBo pageInfoBo = new PageInfoBo();
        pageInfoBo.setMaxCount(count);
        pageInfoBo.setPageNum(webQueryBo.getPageNum());
        pageInfoBo.setMaxPage(PageUtil.getMaxPageNum(count, WebViewConstant.PAGE_SIZE));
        return pageInfoBo;
    }

    @Override
    public void createEcontractTemplate(EcontractTemplateBo templateBo) {
        LOGGER.info("EcontractManagerServiceImpl#createEcontractTemplate, templateBo: {}", JacksonUtil.writeAsJsonStr(templateBo));
        EcontractEntity econtractEntity = new EcontractEntity();
        EcontractUserEntity userEntity = userService.queryUserByName(templateBo.getUserName());
        econtractEntity.setName(templateBo.getName());
        econtractEntity.setEcontractType(templateBo.getEcontractType());
        econtractEntity.setEcontractUserId(userEntity.getId());
        econtractEntity.setCtime(new Date());
        econtractEntity.setUtime(new Date());
        econtractEntity.setValid((byte) 1);
        econtractEntity.setAuthorityMisId(templateBo.getAuthorityMisId());
        econtractService.insertSelective(econtractEntity);
    }

    @Override
    public boolean createSignFlowTemplate(EcontractTemplateBo templateBo, Integer uid) {
        LOGGER.info("EcontractManagerServiceImpl#createSignFlowTemplate, templateBo: {}, uid: {}", JacksonUtil.writeAsJsonStr(templateBo), uid);
        if (!hasCreateSignFlowEcontractTemplateAuth(uid)) {
            LOGGER.warn("EcontractManagerServiceImpl#createSignFlowTemplate, 没有新增签约流程模板权限");
            return false;
        }
        EcontractEntity econtractEntity = new EcontractEntity();
        EcontractUserEntity userEntity = userService.queryUserByName(templateBo.getUserName());
        econtractEntity.setName(templateBo.getName());
        econtractEntity.setEcontractType(templateBo.getEcontractType());
        econtractEntity.setEcontractUserId(userEntity.getId());
        econtractEntity.setCtime(new Date());
        econtractEntity.setUtime(new Date());
        econtractEntity.setValid((byte) 1);
        econtractEntity.setAuthorityMisId(templateBo.getAuthorityMisId());
        econtractService.insertSelective(econtractEntity);
        return true;
    }

    @Override
    public void delEcontractTemplate(Integer id) {
        EcontractEntity econtractEntity = econtractService.selectByPrimaryKey(id);
        econtractEntity.setValid((byte) 0);
        econtractService.updateByPrimaryKeySelective(econtractEntity);
    }

    @Override
    public boolean isEcontractTemplateExist(String typeName) {
        EcontractEntity econtractEntity = econtractService.queryEcontractByType(typeName);
        if (null == econtractEntity) {
            return false;
        } else {
            return true;
        }
    }

    @Override
    public EcontractTemplateBo queryEcontractTemplateById(Integer id) {
        EcontractEntity econtractEntity = econtractService.selectByPrimaryKey(id);
        EcontractTemplateBo econtractTemplateBo = new EcontractTemplateBo();
        if (null != econtractEntity) {
            econtractTemplateBo.setId(econtractEntity.getId());
            econtractTemplateBo.setEcontractType(econtractEntity.getEcontractType());
            econtractTemplateBo.setName(econtractEntity.getName());
            econtractTemplateBo.setProcedureTemplate(econtractEntity.getProcedureTemplate());
            econtractTemplateBo.setAuthorityMisId(econtractEntity.getAuthorityMisId());
        }
        return econtractTemplateBo;
    }

    @Override
    public EcontractTemplateBo queryEcontractTemplateById(Integer templateId, Integer uid) {
        EcontractEntity econtractEntity = econtractService.selectByPrimaryKey(templateId);
        if (econtractEntity == null) {
            return null;
        }
        EcontractTemplateBo econtractTemplateBo = new EcontractTemplateBo();
        if (!hasSignFlowEcontractTemplateAuth(econtractEntity, uid)) {
            return null;
        }
        econtractTemplateBo.setId(econtractEntity.getId());
        econtractTemplateBo.setEcontractType(econtractEntity.getEcontractType());
        econtractTemplateBo.setName(econtractEntity.getName());
        econtractTemplateBo.setProcedureTemplate(econtractEntity.getProcedureTemplate());
        econtractTemplateBo.setAuthorityMisId(econtractEntity.getAuthorityMisId());
        return econtractTemplateBo;
    }

    @Override
    public EcontractTemplateBo querySignFlowTemplateIdByIdAndUid(Integer templateId, Integer uid) {
        EcontractEntity econtractEntity = econtractService.selectByPrimaryKey(templateId);
        if (econtractEntity == null) {
            return null;
        }
        EcontractTemplateBo econtractTemplateBo = new EcontractTemplateBo();
        if (!hasSignFlowEcontractTemplateAuth(econtractEntity, uid)) {
            return null;
        }
        econtractTemplateBo.setId(econtractEntity.getId());
        econtractTemplateBo.setEcontractType(econtractEntity.getEcontractType());
        econtractTemplateBo.setName(econtractEntity.getName());
        econtractTemplateBo.setProcedureTemplate(econtractEntity.getProcedureTemplate());
        econtractTemplateBo.setAuthorityMisId(econtractEntity.getAuthorityMisId());
        return econtractTemplateBo;
    }

    @Override
    public String updateEcontracTemplate(EcontractTemplateBo econtractTemplateBo, String currentMisId) {
        LOGGER.info("updateEcontractTemplate econtractTemplateBo:{} currentMisId:{}", JSON.toJSONString(econtractTemplateBo), currentMisId);
        if (!hasSignFlowEcontractTemplateAuth(econtractTemplateBo.getId(), currentMisId)) {
            return "无权限，请找权限所有人或管理员修改";
        }
        EcontractEntity econtractEntity = econtractService.selectByPrimaryKey(econtractTemplateBo.getId());
        econtractEntity.setProcedureTemplate(econtractTemplateBo.getProcedureTemplate());
        econtractEntity.setUtime(new Date());
        econtractService.updateByPrimaryKeySelective(econtractEntity);
        return "";
    }

    private boolean hasCreateSignFlowEcontractTemplateAuth(Integer uid) {
        try {
            List<Long> roleIdList = uacAuthRemoteServiceAdapter.getAllRoleIdByUid(uid);
            if (roleIdList.contains(MccConfig.getSuperAdminRoleId())) {
                return true;
            }
            return roleIdList.contains(MccConfig.getContractConfigRDRoleId());
        } catch (Exception e) {
            LOGGER.error("EcontractManagerServiceImpl#hasCreateSignFlowEcontractTemplateAuth, error", e);
            return false;
        }
    }

    private boolean hasSignFlowEcontractTemplateAuth(Integer templateId, String misId) {
        try {
            WmEmploy employ = employAdapter.getEmployByMisId(misId);
            if (employ == null) {
                return false;
            }

            List<Long> roleIdList = uacAuthRemoteServiceAdapter.getAllRoleIdByUid(employ.getUid());
            if (roleIdList.contains(MccConfig.getSuperAdminRoleId())) {
                return true;
            }

            if (!roleIdList.contains(MccConfig.getContractConfigRDRoleId())) {
                return false;
            }
            EcontractEntity econtractEntity = econtractService.selectByPrimaryKey(templateId);
            if (econtractEntity.getEcontractUserId() == null || econtractEntity.getEcontractUserId() <= 0) {
                return true;
            }
            return userService.checkUserPermission(econtractEntity.getEcontractUserId(), misId);
        } catch (Exception e) {
            LOGGER.error("EcontractManagerServiceImpl#hasUpdateEcontractTemplateAuth, error", e);
            return false;
        }
    }

    private boolean hasSignFlowEcontractTemplateAuth(final EcontractEntity econtractEntity, Integer uid) {
        try {
            List<Long> roleIdList = uacAuthRemoteServiceAdapter.getAllRoleIdByUid(uid);
            if (roleIdList.contains(MccConfig.getSuperAdminRoleId())) {
                return true;
            }

            if (!roleIdList.contains(MccConfig.getContractConfigRDRoleId())) {
                return false;
            }
            if (econtractEntity.getEcontractUserId() == null || econtractEntity.getEcontractUserId() <= 0) {
                return true;
            }
            WmEmploy employ = employAdapter.getById(uid);
            if (employ == null) {
                return false;
            }
            return userService.checkUserPermission(econtractEntity.getEcontractUserId(), employ.getMisId());
        } catch (Exception e) {
            LOGGER.error("EcontractManagerServiceImpl#hasUpdateEcontractTemplateAuth, error", e);
            return false;
        }
    }

    @Override
    public String createPdf(PdfContentInfoBo pdfBo) throws EcontractException, TException {
        LOGGER.info("createPdf pdfBo:{}", JSON.toJSONString(pdfBo));
        try {
            return econtractPdfService.createPdfByFtl(Lists.newArrayList(pdfBo));
        } catch (IOException e) {
            LOGGER.error("生成PDF失败, msg = " + e.getMessage(), e);
            throw new EcontractException(IO_ERROR, e.getMessage());
        } catch (DocumentException e) {
            LOGGER.error("生成PDF失败, msg = " + e.getMessage(), e);
            throw new EcontractException(ITEXT_ERROR, e.getMessage());
        } catch (TemplateException e) {
            LOGGER.error("生成PDF失败, msg = " + e.getMessage(), e);
            throw new EcontractException(FREEMARKER_ERROR, e.getMessage());
        }
    }

    @Override
    public String generateTemplatePdf(PdfContentInfoBo pdfBo) throws EcontractException, TException {
        LOGGER.info("generateTemplatePdf pdfBo:{}", JSON.toJSONString(pdfBo));
        EcontractTemplateVersionBo econtractTemplateVersionBo = econtractTemplateVersionService.getLatestTemplateVersion(
            pdfBo.getPdfTemplateId(),
            true);
        Map JsonInfoContent = JSON.parseObject(
            Objects.nonNull(pdfBo.getJsonInfo()) ? pdfBo.getJsonInfo() : econtractTemplateVersionBo.getJsonData(),
            Map.class);
        Map pdfMetaContentMap = JSON.parseObject(String.valueOf(
                Objects.nonNull(JsonInfoContent) ? JsonInfoContent.getOrDefault(
                    PdfConstant.PDF_META_CONTENT, StringUtils.EMPTY)
                    : StringUtils.EMPTY),
            Map.class);
        PdfContentInfoBo pdfContentInfoBo = new PdfContentInfoBo.Builder().setPdfBizContent(pdfBo.getPdfBizContent())
            .setPdfMetaContent(Objects.nonNull(pdfMetaContentMap) ? pdfMetaContentMap : MapUtils.EMPTY_MAP)
            .setFtlInfo(econtractTemplateVersionBo.getTargetContent())
            .setVertical(true)
            .build();
        LOGGER.info("createPdfByFtl pdfContentInfoBo:{}", JSON.toJSONString(pdfContentInfoBo));
        try {
            return econtractPdfService.createPdfByFtl(Lists.newArrayList(pdfContentInfoBo));
        } catch (IOException e) {
            LOGGER.error("生成PDF失败IO异常, msg = ", e);
            throw new EcontractException(IO_ERROR, e.getMessage());
        } catch (DocumentException e) {
            LOGGER.error("生成PDF失败Doc异常, msg = ", e);
            throw new EcontractException(ITEXT_ERROR, e.getMessage());
        } catch (TemplateException e) {
            LOGGER.error("生成PDF失败模版异常, msg = ", e);
            throw new EcontractException(FREEMARKER_ERROR, e.getMessage());
        } catch (Exception ex) {
            LOGGER.error("pdf前预览异常", ex);
            throw new EcontractException(SERVER_ERROR, ex.getMessage());
        }
    }

    @Override
    public String createPdfAndStamp(PdfContentInfoBo pdfBo) throws EcontractException, TException {
        LOGGER.info("createPdfAndStamp pdfBo:{}", JSON.toJSONString(pdfBo));
        try {
            return econtractPdfService.createPdfByFtlAndStamp(Lists.newArrayList(pdfBo));
        } catch (IOException | URISyntaxException e) {
            LOGGER.error("生成PDF并盖章失败, msg = " + e.getMessage(), e);
            throw new EcontractException(IO_ERROR, e.getMessage());
        } catch (DocumentException e) {
            LOGGER.error("生成PDF并盖章失败, msg = " + e.getMessage(), e);
            throw new EcontractException(ITEXT_ERROR, e.getMessage());
        } catch (TemplateException e) {
            LOGGER.error("生成PDF并盖章失败, msg = " + e.getMessage(), e);
            throw new EcontractException(FREEMARKER_ERROR, e.getMessage());
        }
    }

    @Override
    public String savePdfTemplate(String userName, PdfContentInfoBo pdfBo) throws EcontractException, TException {
        LOGGER.info("savePdfTemplate pdfBo:{} userName:{}", JSON.toJSONString(pdfBo), userName);
        EcontractUserEntity userEntity = userService.queryUserByName(userName);
        if (userEntity == null) {
            throw new EcontractException(EcontractErrorCode.BUSINESS_ERROR, "用户不存在分组，不允许保存");
        }
        if (pdfBo.getId() == null || pdfBo.getId() == 0) {
            EcontractFtlTemplateEntity entity = econtractFtlTemplateService.selectByName(pdfBo.getPdfTemplateName());
            if (entity != null) {
                throw new EcontractException(EcontractErrorCode.BUSINESS_ERROR,
                    "名称" + pdfBo.getPdfTemplateName() + "对应的PDF模板已经存在");
            }
        }
        EcontractFtlTemplateEntity entity = new EcontractFtlTemplateEntity();
        entity.setName(pdfBo.getPdfTemplateName());
        entity.setEcontractUserId(userEntity.getId());
        entity.setFtlTemplate(pdfBo.getFtlInfo());
        entity.setOptionTemplate(pdfBo.getOptionsInfo());
        entity.setJsonTemplate(pdfBo.getJsonInfo());
        entity.setCtime(DateUtil.fromUnixTime(DateUtil.unixTime()));
        entity.setUtime(DateUtil.fromUnixTime(DateUtil.unixTime()));
        entity.setAuthorityMisId(pdfBo.getAuthorityMisId());
        return econtractFtlTemplateService.saveByName(entity);
    }

    @Override
    public PageAndListInfoBo listPdfTemplate(String userName, Integer pageNum, Integer pageSize)
        throws EcontractException, TException {
        EcontractUserEntity userEntity = userService.queryUserByName(userName);
        if (userEntity == null) {
            return new PageAndListInfoBo.Builder()
                .pageList(Lists.newArrayList())
                .pageInfoBo(new PageInfoBo())
                .build();
        }
        List<EcontractFtlTemplateEntity> ftlTemplateList = econtractFtlTemplateService.listFtlTemplate(userEntity.getId(),
            pageNum,
            pageSize);
        int count = econtractFtlTemplateService.countFtlTemplate(userEntity.getId());

        //list数据转换
        List<EcontractTemplateBo> templateBoList = Lists.newArrayList();
        ftlTemplateList.forEach(ftlTemplate -> {
            EcontractTemplateBo templateBo = new EcontractTemplateBo();
            templateBo.setId(ftlTemplate.getId());
            templateBo.setName(ftlTemplate.getName());
            templateBo.setUserName(userName);
            templateBo.setCtime((long) DateUtil.date2Unixtime(ftlTemplate.getCtime()));
            templateBo.setAuthorityMisId(ftlTemplate.getAuthorityMisId());
            templateBoList.add(templateBo);
        });

        //分页信息
        PageInfoBo pageInfoBo = new PageInfoBo();
        pageInfoBo.setMaxCount(count);
        pageInfoBo.setMaxPage(PageUtil.getMaxPageNum(count, pageSize));
        pageInfoBo.setPageNum(pageNum);

        //返回
        return new PageAndListInfoBo.Builder()
            .pageList(templateBoList)
            .pageInfoBo(pageInfoBo)
            .build();
    }

    @Override
    public PdfContentInfoBo getPdfTemplate(String userName, String templateName) throws EcontractException, TException {
        EcontractFtlTemplateEntity entity = econtractFtlTemplateService.selectByName(templateName);
        if (entity == null) {
            return new PdfContentInfoBo();
        }

        PdfContentInfoBo pdfContentInfoBo = new PdfContentInfoBo();
        pdfContentInfoBo.setId((long) entity.getId());
        pdfContentInfoBo.setPdfTemplateName(templateName);
        pdfContentInfoBo.setFtlInfo(entity.getFtlTemplate());
        pdfContentInfoBo.setJsonInfo(entity.getJsonTemplate());
        pdfContentInfoBo.setOptionsInfo(entity.getOptionTemplate());
        return pdfContentInfoBo;
    }

    /**
     * 删除Pdf模板
     */
    @Override
    public String deletePdfTemplate(String userName, String templateName, String misId)
        throws EcontractException, TException {
        LOGGER.info("deletePdfTemplate userName:{} templateName:{} misId:{}", userName, templateName, misId);
        checkUserName(userName);
        EcontractFtlTemplateEntity entity = econtractFtlTemplateService.selectByName(templateName);
        if (entity == null) {
            throw new EcontractException(EcontractErrorCode.BUSINESS_ERROR, "对应的模板不存在，无法删除");
        }
        if (StringUtils.isBlank(entity.getAuthorityMisId()) && !AuthorityUtil.isSuperAuthority(misId)) {
            return "无权限，请找管理员分配后修改";
        }
        if (!misId.equals(entity.getAuthorityMisId()) && !AuthorityUtil.isSuperAuthority(misId)) {
            return "无权限，请找权限所有人或管理员修改";
        }
        econtractFtlTemplateService.deleteByPrimaryKey(Long.valueOf(String.valueOf(entity.getId())));
        return "";
    }

    /**
     * 跨环境同步Pdf模板
     */
    @Override
    public BoolResult syncPdfTemplate(Integer id, String toEnv) throws EcontractException, TException {
        IntAssertUtil.assertMoreThanTarget(id, 0, "id必须大于0");
        StringAssertUtil.assertNotEmpty(toEnv, "toEnv不能为空");
        EcontractFtlTemplateEntity ftlTemplateEntity = econtractFtlTemplateService.selectByPrimaryKey(id);
        ObjectAssertUtil.assertObjectNotNull(ftlTemplateEntity, "id对应的PDF模板不存在");

        ftlTemplateEntity.setEnv(toEnv);
        econtractFtlTemplateService.saveByNameAndEnv(ftlTemplateEntity);
        return new BoolResult(Boolean.TRUE);
    }

    /**
     * 保存签约模板
     */
    @Override
    public String saveSignPageTemplate(String userName, SignPageInfoBo signPageInfoBo)
        throws EcontractException, TException {
        LOGGER.info("saveSignPageTemplate userName:{} signPageInfoBo:{}", userName, JSON.toJSONString(signPageInfoBo));
        EcontractUserEntity userEntity = userService.queryUserByName(userName);
        if (userEntity == null) {
            throw new EcontractException(EcontractErrorCode.BUSINESS_ERROR, "用户不存在分组，不允许保存");
        }
        if (signPageInfoBo.getId() == null || signPageInfoBo.getId() == 0) {
            EcontractSignPageTemplateEntity entity = econtractSignPageTemplateService.selectByNameCompatible(
                signPageInfoBo.getTemplateName());
            if (entity != null) {
                throw new EcontractException(EcontractErrorCode.BUSINESS_ERROR,
                    "名称" + signPageInfoBo.getTemplateName() + "对应的签约配置模板已经存在");
            }
        }

        EcontractSignPageTemplateEntity entity = new EcontractSignPageTemplateEntity();
        entity.setName(signPageInfoBo.getTemplateName());
        entity.setEcontractUserId(userEntity.getId());
        entity.setContext(JSON.toJSONString(signPageInfoBo));
        entity.setCtime(DateUtil.fromUnixTime(DateUtil.unixTime()));
        entity.setUtime(DateUtil.fromUnixTime(DateUtil.unixTime()));
        entity.setAuthorityMisId(signPageInfoBo.getAuthorityMisId());
        return econtractSignPageTemplateService.saveByName(entity);
    }

    /**
     * 批量查询签约模板
     */
    @Override
    public PageAndListInfoBo listSignPageTemplate(String userName, Integer pageNum, Integer pageSize)
        throws EcontractException, TException {
        EcontractUserEntity userEntity = userService.queryUserByName(userName);
        if (userEntity == null) {
            return new PageAndListInfoBo.Builder()
                .pageList(Lists.newArrayList())
                .pageInfoBo(new PageInfoBo())
                .build();
        }
        List<EcontractSignPageTemplateEntity> signPageList = econtractSignPageTemplateService.listSignPage(userEntity.getId(),
            pageNum,
            pageSize);
        int count = econtractSignPageTemplateService.countSignPage(userEntity.getId());

        //列表信息
        List<EcontractTemplateBo> templateBoList = Lists.newArrayList();
        signPageList.forEach(signPage -> {
            EcontractTemplateBo templateBo = new EcontractTemplateBo();
            templateBo.setId(signPage.getId());
            templateBo.setUserName(userName);
            templateBo.setName(signPage.getName());
            templateBo.setCtime((long) DateUtil.date2Unixtime(signPage.getCtime()));
            templateBo.setAuthorityMisId(signPage.getAuthorityMisId());
            templateBoList.add(templateBo);
        });

        //页数信息
        PageInfoBo pageInfoBo = new PageInfoBo();
        pageInfoBo.setMaxCount(count);
        pageInfoBo.setPageNum(pageNum);
        pageInfoBo.setMaxPage(PageUtil.getMaxPageNum(count, pageSize));

        return new PageAndListInfoBo.Builder()
            .pageList(templateBoList)
            .pageInfoBo(pageInfoBo)
            .build();
    }

    /**
     * 查询签约H5模板
     */
    @Override
    public SignPageInfoBo getSignPageTemplate(String userName, String templateName)
        throws EcontractException, TException {
        EcontractSignPageTemplateEntity entity = econtractSignPageTemplateService.selectByNameCompatible(templateName);
        if (entity == null) {
            return new SignPageInfoBo();
        }

        SignPageInfoBo signPageInfoBo = JSON.parseObject(entity.getContext(), SignPageInfoBo.class);
        signPageInfoBo.setId((long) entity.getId());
        return signPageInfoBo;
    }

    /**
     * 删除H5签约模板
     */
    @Override
    public String deleteSignPageTemplate(String userName, String templateName, String misId)
        throws EcontractException, TException {
        LOGGER.info("deleteSignPageTemplate userName:{} templateName:{} misId:{}", userName, templateName, misId);
        checkUserName(userName);
        EcontractSignPageTemplateEntity entity = econtractSignPageTemplateService.selectByNameCompatible(templateName);
        if (entity == null) {
            throw new EcontractException(EcontractErrorCode.BUSINESS_ERROR, "对应的模板不存在，无法删除");
        }
        if (StringUtils.isBlank(entity.getAuthorityMisId()) && !AuthorityUtil.isSuperAuthority(misId)) {
            return "无权限，请找管理员分配后修改";
        }
        if (!misId.equals(entity.getAuthorityMisId()) && !AuthorityUtil.isSuperAuthority(misId)) {
            return "无权限，请找权限所有人或管理员修改";
        }
        econtractSignPageTemplateService.deleteByPrimaryKey(Long.valueOf(String.valueOf(entity.getId())));
        return "";
    }

    @Override
    public BoolResult syncSignPageTemplate(Integer id, String toEnv) throws EcontractException, TException {
        IntAssertUtil.assertMoreThanTarget(id, 0, "id必须大于0");
        StringAssertUtil.assertNotEmpty(toEnv, "toEnv不能为空");
        EcontractSignPageTemplateEntity signPageEntity = econtractSignPageTemplateService.selectByPrimaryKey(id);
        ObjectAssertUtil.assertObjectNotNull(signPageEntity, "id对应的H5签约对象不存在");

        signPageEntity.setEnv(toEnv);
        econtractSignPageTemplateService.saveByNameAndEnv(signPageEntity);
        return new BoolResult(Boolean.TRUE);
    }

    @Override
    public void bindSignPageTemplate(String econtractName, String signPageName) throws EcontractException, TException {
        EcontractRelEntity entity = econtractRelService.selectBytemplateNameAndEnv(econtractName);
        if (entity != null) {
            entity.setEcontractName(econtractName);
            entity.setSignPageName(signPageName == null ? StringUtils.EMPTY : signPageName);
            entity.setUtime(DateUtil.fromUnixTime(DateUtil.unixTime()));
            econtractRelService.updateByPrimaryKey(entity);
        } else {
            entity = new EcontractRelEntity();
            entity.setEcontractName(econtractName);
            entity.setSignPageName(signPageName);
            entity.setValid(EcontractBaseConstant.VALID);
            entity.setCtime(DateUtil.fromUnixTime(DateUtil.unixTime()));
            entity.setUtime(DateUtil.fromUnixTime(DateUtil.unixTime()));
            econtractRelService.insertSelective(entity);
        }
    }

    @Override
    public BoolResult unbindSignPageTemplate(String userName, String templateName, String signPageName)
        throws EcontractException, TException {
        checkUserName(userName);
        EcontractRelEntity entity = econtractRelService.selectBytemplateNameAndEnv(templateName);
        ObjectAssertUtil.assertObjectNotNull(entity, "解绑签约页模板失败");
        econtractRelService.deleteByPrimaryKey(entity.getId());
        return new BoolResult(Boolean.TRUE);
    }

    @Override
    public BoolResult syncEcontractBindRel(Integer id, String toEnv) throws EcontractException, TException {
        IntAssertUtil.assertMoreThanTarget(id, 0, "id必须大于0");
        StringAssertUtil.assertNotEmpty(toEnv, "toEnv不能为空");
        EcontractRelEntity econtractRelEntity = econtractRelService.selectByPrimaryKey(id);
        ObjectAssertUtil.assertObjectNotNull(econtractRelEntity, "id对应的关联关系不存在");

        econtractRelEntity.setEnv(toEnv);
        econtractRelService.saveByTemplateNameAndEnv(econtractRelEntity);
        return new BoolResult(Boolean.TRUE);
    }

    private void checkUserName(String userName) throws EcontractException {
        EcontractUserEntity userEntity = userService.queryUserByName(userName);
        if (userEntity == null) {
            throw new EcontractException(EcontractErrorCode.BUSINESS_ERROR, "用户不存在分组，不允许保存");
        }
    }

    @Override
    public List<Integer> selectFinishIdsByUtime(Integer startId, Integer time, Integer pageSize)
        throws EcontractException, TException {
        IntAssertUtil.assertNotLessThanTarget(startId, 0, "startId必须大于0");
        IntAssertUtil.assertMoreThanTarget(time, 0, "time必须大于0");
        IntAssertUtil.assertMoreThanTarget(pageSize, 0, "pageSize必须大于0");
        IntAssertUtil.assertLessThanTarget(pageSize, PageUtil.getConfigMaxPageSize(), "");

        Date date = DateUtil.fromUnixTime(time);
        return econtractRecordService.selectFinishIdsByUtime(startId, date, pageSize);
    }

    @Override
    public Integer deleteRecordAndTask(Integer recordId)
        throws EcontractException, TException {
        IntAssertUtil.assertMoreThanTarget(recordId, 0, "recordId必须大于0");
        EcontractRecordEntity recordEntity = econtractRecordService.selectColdDataByPrimaryKey(recordId);
        if (recordEntity == null
            || !TaskConstant.ECONTRACT_FINISH.equals(recordEntity.getEcontractStage())) {
            LOGGER.info("当前任务未结束不允许删除:" + recordId);
            return 0;
        }
        if (!ConfigUtilAdapter.getBoolean(MccConstant.FORCE_DELETE, false)) {
            LOGGER.info("当前record可删除，但是不执行删除:" + recordId);
            return 0;
        }

        //删除record信息
        econtractRecordService.deleteById(recordId);
        //删除task信息
        econtractTaskService.deleteByRecordId(recordId);
        LOGGER.info("删除成功:" + recordId);
        return 1;
    }

    @Override
    public String updateContractAuthorityById(Integer id, String newAuthorityMisId, String authorityMisId) {
        EcontractEntity econtractEntity = econtractService.selectByPrimaryKey(id);
        if (econtractEntity == null) {
            return "合同不存在";
        }
        if (StringUtils.isBlank(econtractEntity.getAuthorityMisId())
            && !AuthorityUtil.isSuperAuthority(authorityMisId)) {
            return "无权限，请找管理员更改责任人";
        }
        if (!authorityMisId.equals(econtractEntity.getAuthorityMisId()) && !AuthorityUtil.isSuperAuthority(
            authorityMisId)) {
            return "无权限，请找责任人或管理员进行变更";
        }
        int result = econtractService.updateAuthorityById(id, newAuthorityMisId);
        if (result < 1) {
            return "更新失败";
        }
        return "";
    }

    @Override
    public String updateContractFtlAuthorityById(Integer id, String authorityMisId, String currentMisId) {
        LOGGER.info("updateContractFtlAuthorityById id:{} authorityMisId:{} currentMisId:{}",
            id,
            authorityMisId,
            currentMisId);
        EcontractFtlTemplateEntity econtractFtlTemplateEntity = econtractFtlTemplateService.selectByPrimaryKey(id);
        if (econtractFtlTemplateEntity == null) {
            return "pdf模板不存在";
        }
        if (StringUtils.isBlank(econtractFtlTemplateEntity.getAuthorityMisId()) && !AuthorityUtil.isSuperAuthority(
            currentMisId)) {
            return "无权限，请找管理员更改责任人";
        }
        if (!currentMisId.equals(econtractFtlTemplateEntity.getAuthorityMisId()) && !AuthorityUtil.isSuperAuthority(
            currentMisId)) {
            return "无权限，请找责任人或管理员进行变更";
        }
        int result = econtractFtlTemplateService.updateContractAuthorityById(id, authorityMisId);
        if (result < 1) {
            return "更新失败";
        }
        return "";
    }

    @Override
    public String updateSignFtlAuthorityById(Integer id, String authorityMisId, String currentMisId) {
        LOGGER.info("updateSignFtlAuthorityById id:{} authorityMisId:{} currentMisId:{}",
            id,
            authorityMisId,
            currentMisId);
        EcontractSignPageTemplateEntity econtractSignPageTemplateEntity = econtractSignPageTemplateService.selectByPrimaryKey(
            id);
        if (econtractSignPageTemplateEntity == null) {
            return "签约模板不存在";
        }
        if (StringUtils.isBlank(econtractSignPageTemplateEntity.getAuthorityMisId()) && !AuthorityUtil.isSuperAuthority(
            currentMisId)) {
            return "无权限，请找管理员更改责任人";
        }
        if (!currentMisId.equals(econtractSignPageTemplateEntity.getAuthorityMisId())
            && !AuthorityUtil.isSuperAuthority(currentMisId)) {
            return "无权限，请找责任人或管理员进行变更";
        }
        int result = econtractSignPageTemplateService.updateSignAuthorityById(id, authorityMisId);
        if (result < 1) {
            return "更新失败";
        }
        return "";
    }

    //该接口当前只服务于拼好饭业务线
    @Override
    public BoolResult batchSignSendSms(Integer queryTime) throws EcontractException, TException {
        LOGGER.info("批量签约发送短信入参：queryTime={}", queryTime);
        if (queryTime == null) {
            return new BoolResult(false);
        }
        List<EcontractSignBatchEventEntity> eventEntities = econtractSignRecordBatchEventService.getUnExecuteEventTask(
            queryTime);
        if (CollectionUtils.isEmpty(eventEntities)) {
            LOGGER.info("批量签约发送短信未查询到需要发送短信的事件：queryTime={}", queryTime);
            return new BoolResult(true);
        }
        //过滤出拼好饭业务线
        LOGGER.info("batchSignSendSms#eventEntities={}", JSON.toJSONString(eventEntities));
        List<Integer> batchIdList = eventEntities.stream()
            .map(EcontractSignBatchEventEntity::getBatchId)
            .collect(Collectors.toList());
        LOGGER.info("batchSignSendSms#batchIdList={}", JSON.toJSONString(batchIdList));
        List<EcontractSignRecordBatchEntity> recordBatchEntities = econtractSignRecordBatchService.queryBatchEntityListByIds(
            batchIdList);
        LOGGER.info("batchSignSendSms#recordBatchEntities={}", JSON.toJSONString(recordBatchEntities));
        List<Long> phfRecordBatchEntitieIdList = recordBatchEntities.stream()
            .filter(record -> record.getBizLine() == BatchSignBizLineEnum.PHF.getCode())
            .map(EcontractSignRecordBatchEntity::getId)
            .collect(Collectors.toList());
        LOGGER.info("batchSignSendSms#phfRecordBatchEntitieIdList={}", JSON.toJSONString(phfRecordBatchEntitieIdList));
        eventEntities = eventEntities.stream()
            .filter(event -> phfRecordBatchEntitieIdList.contains(event.getBatchId().longValue()))
            .collect(Collectors.toList());
        LOGGER.info("batchSignSendSms#eventEntities:{}", JSON.toJSONString(eventEntities));
        List<EcontractSignBatchEventEntity> eventEntitiesNoExe = eventEntities.stream()
            .filter(event -> BatchEventStatusEnum.NO_EXE.getCode().equals(event.getStatus()))
            .collect(Collectors.toList());
        List<EcontractSignBatchEventEntity> eventEntitiesLater = eventEntities.stream()
            .filter(event -> BatchEventStatusEnum.EXE_LATER.getCode().equals(event.getStatus()))
            .collect(Collectors.toList());
        doNoExeBatchEvent(eventEntitiesNoExe);
        doExeLaterBatchEvent(eventEntitiesLater);
        return new BoolResult(true);
    }

    @Override
    public void sendBatchSignTaskSms(SendBatchSignSmsRequestDTO requestDTO) {
        try {
            LOGGER.info("EcontractManagerServiceImpl#sendBatchSignTaskSms, requestDTO: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            econtractSmsActivityService.sendSmsForBatchTask(requestDTO.getBatchId().longValue());
        } catch (Exception e) {
            LOGGER.error("EcontractManagerServiceImpl#sendBatchSignTaskSms, error", e);
            DaxiangUtil.push("<EMAIL>", ProcessInfoUtil.getHostEnv() + "环境, 发送打包签约任务短信失败, trace: " + Tracer.id(), MccConfig.getDaXiangAlarmMisList());
        }
    }

    @Override
    public void sendFallbackTaskSmsWithinPeriod(SendFallbackTaskSmsRequestDTO requestDTO) {
        try {
            LOGGER.info("EcontractManagerServiceImpl#sendFallbackTaskSmsWithinPeriod, requestDTO: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            if (CollectionUtils.isEmpty(requestDTO.getBizLineList())) {
                return;
            }
            econtractSmsActivityService.sendFallbackTaskSmsWithinPeriod(requestDTO.getQueryTime(), requestDTO.getBizLineList());
        } catch (Exception e) {
            LOGGER.error("EcontractManagerServiceImpl#sendFallbackTaskSmsWithinPeriod, error", e);
            DaxiangUtil.push("<EMAIL>", ProcessInfoUtil.getHostEnv() + "环境, Job兜底发送打包签约任务短信失败, trace: " + Tracer.id(), MccConfig.getDaXiangAlarmMisList());
        }
    }

    @Override
    public String onlyCreatePdf(PdfContentInfoBo pdfBo) throws EcontractException {
        checkCreatePdfParam(pdfBo);
        LOGGER.info("EcontractManagerServiceImpl#onlyCreatePdf, pdfBo: {}", JacksonUtil.writeAsJsonStr(pdfBo));
        try {
            String pdfUrl = econtractPdfService.onlyCreatePdf(pdfBo);
            LOGGER.info("EcontractManagerServiceImpl#onlyCreatePdf, pdfUrl: {}", pdfUrl);
            return pdfUrl;
        } catch (IOException e) {
            LOGGER.error("EcontractManagerServiceImpl#onlyCreatePdf, 生成PDF失败, msg: {}", e.getMessage(), e);
            throw new EcontractException(IO_ERROR, e.getMessage());
        } catch (DocumentException e) {
            LOGGER.error("EcontractManagerServiceImpl#onlyCreatePdf, 生成PDF失败, msg: {}", e.getMessage(), e);
            throw new EcontractException(ITEXT_ERROR, e.getMessage());
        } catch (TemplateException e) {
            LOGGER.error("EcontractManagerServiceImpl#onlyCreatePdf, 生成PDF失败, msg: {}", e.getMessage(), e);
            throw new EcontractException(FREEMARKER_ERROR, e.getMessage());
        } catch (Exception e) {
            LOGGER.error("EcontractManagerServiceImpl#onlyCreatePdf, error", e);
            throw new EcontractException(SERVER_ERROR, "创建Pdf失败");
        }
    }

    private void checkCreatePdfParam(PdfContentInfoBo pdfBo) throws EcontractException {
        if (pdfBo == null) {
            throw new EcontractException(IO_ERROR, "参数异常");
        }
        Integer pdfTemplateId = pdfBo.getPdfTemplateId();
        String pdfTemplateName = pdfBo.getPdfTemplateName();
        if (pdfTemplateId != null && pdfTemplateId > 0 && Strings.isNotEmpty(pdfTemplateName)) {
            throw new EcontractException(IO_ERROR, "模板重复指定");
        }
    }

    @Override
    public BaseResponse<EcontractTemplateQueryResponseDTO> querySignFlowEcontractTemplate(EcontractTemplateQueryDTO queryDTO) {
        try {
            LOGGER.info("EcontractManagerServiceImpl#querySignFlowEcontractTemplate, queryDTO: {}", JacksonUtil.writeAsJsonStr(queryDTO));
            EcontractTemplateQueryResponseDTO responseDTO = econtractService.querySignFlowEcontractTemplate(queryDTO);
            LOGGER.info("EcontractManagerServiceImpl#querySignFlowEcontractTemplate, responseDTO: {}", JacksonUtil.writeAsJsonStr(responseDTO));
            return BaseResponse.success(responseDTO);
        } catch (Exception e) {
            LOGGER.error("EcontractManagerServiceImpl#querySignFlowEcontractTemplate, error", e);
            return BaseResponse.fail("查询失败");
        }
    }

    @Override
    public BaseResponse<Boolean> deleteSignFlowEcontractTemplate(SignFlowTemplateDeleteRequestDTO requestDTO) {
        try {
            LOGGER.info("EcontractManagerServiceImpl#deleteEcontractTemplate, requestDTO: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            boolean result = econtractService.deleteEcontractTemplate(requestDTO);
            LOGGER.info("EcontractManagerServiceImpl#deleteEcontractTemplate, result: {}", result);
            return BaseResponse.initResult(result);
        } catch (EcontractException e) {
            LOGGER.warn("EcontractManagerServiceImpl#deleteEcontractTemplate, EcontractException", e);
            return BaseResponse.fail(e.getMsg());
        } catch (Exception e) {
            LOGGER.error("EcontractManagerServiceImpl#deleteEcontractTemplate, error", e);
            return BaseResponse.fail("删除失败");
        }
    }

    @Override
    public BaseResponse<Boolean> updateSignFlowEcontractTemplate(SignFlowTemplateUpdateRequestDTO requestDTO) {
        try {
            LOGGER.info("EcontractManagerServiceImpl#updateSignFlowEcontractTemplate, requestDTO: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            boolean result = econtractService.updateSignFlowEcontractTemplate(requestDTO);
            LOGGER.info("EcontractManagerServiceImpl#updateSignFlowEcontractTemplate, result: {}", result);
            return BaseResponse.initResult(result);
        } catch (EcontractException e) {
            LOGGER.warn("EcontractManagerServiceImpl#updateSignFlowEcontractTemplate, EcontractException", e);
            return BaseResponse.fail(e.getMsg());
        } catch (Exception e) {
            LOGGER.error("EcontractManagerServiceImpl#updateSignFlowEcontractTemplate, error", e);
            return BaseResponse.fail("更新失败");
        }
    }

    @Override
    public BaseResponse<Boolean> copyContractTemplate(EcontractTemplateCopyRequestDTO requestDTO) {
        try {
            LOGGER.info("EcontractManagerServiceImpl#copyContractTemplate, requestDTO: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            boolean result = econtractService.copyContractTemplate(requestDTO);
            LOGGER.info("EcontractManagerServiceImpl#copyContractTemplate, result: {}", result);
            return BaseResponse.initResult(result);
        } catch (Exception e) {
            LOGGER.error("EcontractManagerServiceImpl#copyContractTemplate, error", e);
            return BaseResponse.fail("复制失败");
        }
    }

    @Override
    public BaseResponse<Boolean> copyContractTemplateForAI(EcontractTemplateCopyRequestDTO requestDTO) {
        try {
            LOGGER.info("EcontractManagerServiceImpl#copyContractTemplateNoAuthCheck, requestDTO: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            boolean result = econtractService.copyContractTemplateForAI(requestDTO);
            LOGGER.info("EcontractManagerServiceImpl#copyContractTemplateNoAuthCheck, result: {}", result);
            return BaseResponse.initResult(result);
        } catch (EcontractException e) {
            LOGGER.warn("EcontractManagerServiceImpl#copyContractTemplateNoAuthCheck, warn", e);
            return BaseResponse.fail(e.getMsg());
        } catch (Exception e) {
            LOGGER.error("EcontractManagerServiceImpl#copyContractTemplateNoAuthCheck, error", e);
            return BaseResponse.fail("流程模板创建失败");
        }
    }

    @Override
    public BaseResponse<Boolean> saveSignPageEcontractTemplate(SignPageSaveRequestDTO requestDTO) {
        try {
            LOGGER.info("EcontractManagerServiceImpl#saveSignPageTemplate, requestDTO: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            boolean result = econtractSignPageService.saveSignPageTemplate(requestDTO);
            LOGGER.info("EcontractManagerServiceImpl#saveSignPageTemplate, result: {}", result);
            return BaseResponse.initResult(result);
        } catch (EcontractException e) {
            LOGGER.warn("EcontractManagerServiceImpl#saveSignPageTemplate, EcontractException", e);
            return BaseResponse.fail(e.getMsg());
        } catch (Exception e) {
            LOGGER.error("EcontractManagerServiceImpl#saveSignPageTemplate, error", e);
            return BaseResponse.fail("保存失败");
        }
    }

    @Override
    public BaseResponse<Boolean> copySignPageTemplate(SignPageCopyRequestDTO requestDTO) {
        try {
            LOGGER.info("EcontractManagerServiceImpl#copySignPageTemplate, requestDTO: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            boolean result = econtractSignPageService.copySignPageTemplate(requestDTO);
            LOGGER.info("EcontractManagerServiceImpl#copySignPageTemplate, result: {}", result);
            return BaseResponse.initResult(result);
        } catch (EcontractException e) {
            LOGGER.warn("EcontractManagerServiceImpl#copySignPageTemplate, EcontractException", e);
            return BaseResponse.fail(e.getMsg());
        } catch (Exception e) {
            LOGGER.error("EcontractManagerServiceImpl#copySignPageTemplate, error", e);
            return BaseResponse.fail("复制失败");
        }
    }

    @Override
    public BaseResponse<Boolean> deleteSignPageTemplate4Config(SignPageDeleteRequestDTO requestDTO) {
        try {
            LOGGER.info("EcontractManagerServiceImpl#deleteSignPageTemplate, requestDTO: {}", JacksonUtil.writeAsJsonStr(requestDTO));
            boolean result = econtractSignPageService.deleteSignPageTemplate(requestDTO);
            LOGGER.info("EcontractManagerServiceImpl#deleteSignPageTemplate, result: {}", result);
            return BaseResponse.initResult(result);
        } catch (EcontractException e) {
            LOGGER.warn("EcontractManagerServiceImpl#deleteSignPageTemplate, EcontractException", e);
            return BaseResponse.fail(e.getMsg());
        } catch (Exception e) {
            LOGGER.error("EcontractManagerServiceImpl#deleteSignPageTemplate, error", e);
            return BaseResponse.fail("删除失败");
        }
    }

    @Override
    public BaseResponse<SignPageTemplateQueryResp> querySignPageTemplate(SignPageTemplateQueryParam queryParam) {
        try {
            LOGGER.info("EcontractManagerServiceImpl#querySignPageTemplate, queryParam: {}", JacksonUtil.writeAsJsonStr(queryParam));
            SignPageTemplateQueryResp queryResponse = econtractSignPageService.querySignPageTemplate(queryParam);
            LOGGER.info("EcontractManagerServiceImpl#querySignPageTemplate, queryResponse: {}", JacksonUtil.writeAsJsonStr(queryResponse));
            return BaseResponse.success(queryResponse);
        } catch (EcontractException e) {
            LOGGER.warn("EcontractManagerServiceImpl#querySignPageTemplate, warn", e);
            return BaseResponse.fail(e.getMsg());
        } catch (Exception e) {
            LOGGER.error("EcontractManagerServiceImpl#querySignPageTemplate, error", e);
            return BaseResponse.fail("查询失败");
        }
    }

    @Override
    public List<String> batchOnlyCreatePdf(List<PdfContentInfoBo> pdfBos) throws EcontractException, TException {
        checkBatchCreatePdfParam(pdfBos);
        LOGGER.info("EcontractManagerServiceImpl#batchOnlyCreatePdfWithSingleTemplate, pdfBo: {}", JacksonUtil.writeAsJsonStr(pdfBos));
        try {
            List<String> pdfUrls = econtractPdfService.batchOnlyCreatePdf(pdfBos);
            LOGGER.info("EcontractManagerServiceImpl#batchOnlyCreatePdfWithSingleTemplate, pdfUrls: {}", pdfUrls);
            return pdfUrls;
        } catch (IOException e) {
            LOGGER.error("EcontractManagerServiceImpl#batchOnlyCreatePdfWithSingleTemplate, 生成PDF失败, msg: {}", e.getMessage(), e);
            throw new EcontractException(IO_ERROR, e.getMessage());
        } catch (DocumentException e) {
            LOGGER.error("EcontractManagerServiceImpl#batchOnlyCreatePdfWithSingleTemplate, 生成PDF失败, msg: {}", e.getMessage(), e);
            throw new EcontractException(ITEXT_ERROR, e.getMessage());
        } catch (TemplateException e) {
            LOGGER.error("EcontractManagerServiceImpl#batchOnlyCreatePdfWithSingleTemplate, 生成PDF失败, msg: {}", e.getMessage(), e);
            throw new EcontractException(FREEMARKER_ERROR, e.getMessage());
        } catch (Exception e) {
            LOGGER.error("EcontractManagerServiceImpl#batchOnlyCreatePdfWithSingleTemplate, error", e);
            throw new EcontractException(SERVER_ERROR, "创建Pdf失败");
        }

    }

    @Override
    public StageBatchInfoResponseDTO queryStageBatchInfoBoListByRecordKey(String token, String recordKey) throws EcontractException, TException {
        LOGGER.info("queryStageBatchInfoBoListByRecordKey, recordKey: {}, token:{} ", recordKey, token);
        checkKeyInfo(recordKey, token);
        try {
            EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(recordKey);
            return extractStageBatchInfo(recordEntity);
        } catch (Exception e) {
            LOGGER.error("EcontractManagerServiceImpl#queryStageBatchInfoBoListByRecordKey, error", e);
            throw new EcontractException(SERVER_ERROR, "查询StageBatchInfo失败");
        }

    }

    private void checkKeyInfo(String recordKey, String userToken) throws EcontractException {
        if (StringUtils.isEmpty(userToken)) {
            throw new EcontractException(EcontractException.PARAM_ERROR, "userToken is null");
        }
        if (!userService.hasEcontractRecord(userToken, recordKey)) {
            throw new EcontractException(EcontractException.PARAM_ERROR, "recordKey is invalid");
        }
    }

    /**
     * 提取stageBatchInfoBoList，转换成JSON
     * @param recordEntity
     * @return
     */
    private StageBatchInfoResponseDTO extractStageBatchInfo(EcontractRecordEntity recordEntity) throws EcontractException {
        if (recordEntity == null) {
            return null;
        }
        try {
            String econtractRecordContext = recordEntity.getEcontractRecordContext();
            EcontractContext econtractContext = JSON.parseObject(econtractRecordContext, EcontractContext.class);
            LOGGER.info("extractStageBatchInfo, econtractContext: {}", JacksonUtil.writeAsJsonStr(econtractContext));
            List<StageBatchInfoBo> stageBatchInfoBoList = econtractContext.getStageBatchInfoBoList();
            String econtractType = recordEntity.getEcontractType();

            StageBatchInfoResponseDTO stageBatchInfoResponseDTO = new StageBatchInfoResponseDTO();
            stageBatchInfoResponseDTO.setEcontractType(econtractType);
            stageBatchInfoResponseDTO.setStageBatchInfoBoList(stageBatchInfoBoList);
            return stageBatchInfoResponseDTO;
        } catch (Exception e) {
            LOGGER.error("extractStageBatchInfo, error", e);
            throw new EcontractException(SERVER_ERROR, "提取stageBatchInfoBoList失败");
        }
    }

    /**
     * 批量创建PDF参数校验
     * @param pdfBos
     * @throws EcontractException
     */
    private void checkBatchCreatePdfParam(List<PdfContentInfoBo> pdfBos) throws EcontractException {
        if (pdfBos.size() > MccConfig.batchOnlyCreaetPdfMaxSize()) {
            throw new EcontractException(PARAM_ERROR, "单次批量创建PDF数量超过限制");
        }
        for (PdfContentInfoBo pdfBo : pdfBos) {
            if (Objects.isNull(pdfBo)) {
                throw new EcontractException(PARAM_ERROR, "存在null对象");
            }

            // 如果有pdf链接直接跳过。
            if (StringUtils.isNotEmpty(pdfBo.getPdfUrl())) {
                continue;
            }

            Integer pdfTemplateId = pdfBo.getPdfTemplateId();
            if (pdfTemplateId == null || pdfTemplateId <= 0) {
                throw new EcontractException(PARAM_ERROR, "未指定templateId");
            }

            if (Objects.nonNull(pdfBo.getPdfBizContent())
                    && pdfBo.getPdfBizContent().size() > MccConfig.getTheUpperLimitOfOnlyCreatePdf()) {
                throw new EcontractException(PARAM_ERROR, "门店数目超出上限");
            }
        }

    }

    //    @Override
//    public BaseResponse<SignPageTemplateInfo> querySignPageCompleteContent(SignPageContentRequestDTO requestDTO) {
//        try {
//            LOGGER.info("EcontractManagerServiceImpl#querySignPageContent, requestDTO: {}", JacksonUtil.writeAsJsonStr(requestDTO));
//            SignPageTemplateInfo templateInfo = econtractSignPageService.querySignPageCompleteContent(requestDTO);
//            return BaseResponse.success(templateInfo);
//        } catch (EcontractException e) {
//            LOGGER.warn("EcontractManagerServiceImpl#requestDTO, warn", e);
//            return BaseResponse.fail(e.getMsg());
//        } catch (Exception e) {
//            LOGGER.error("EcontractManagerServiceImpl#requestDTO, error", e);
//            return BaseResponse.fail("查询失败");
//        }
//    }

//    @Override
//    public BaseResponse<Boolean> saveSignPageContent(SignPageSaveRequestDTO requestDTO) {
//        try {
//            LOGGER.info("EcontractManagerServiceImpl#saveSignPageContent, requestDTO: {}", JacksonUtil.writeAsJsonStr(requestDTO));
//            boolean result = econtractSignPageService.saveSignPageContent(requestDTO);
//            return BaseResponse.success(result);
//        } catch (EcontractException e) {
//            LOGGER.warn("EcontractManagerServiceImpl#requestDTO, warn", e);
//            return BaseResponse.fail(e.getMsg());
//        } catch (Exception e) {
//            LOGGER.error("EcontractManagerServiceImpl#requestDTO, error", e);
//            return BaseResponse.fail("查询失败");
//        }
//    }

    private void doNoExeBatchEvent(List<EcontractSignBatchEventEntity> eventEntitiesNoExe) {
        LOGGER.info("批量签约发送短信处理未执行的任务：eventEntitiesNoExe={}", JSON.toJSONString(eventEntitiesNoExe));
        if (CollectionUtils.isEmpty(eventEntitiesNoExe)) {
            return;
        }
        eventEntitiesNoExe.stream().forEach(event -> {
            List<EcontractRecordEntity> recordEntities = econtractRecordService.queryEcontractRecordByBatchId(event.getBatchId());
            if (CollectionUtils.isNotEmpty(recordEntities)) {
                dealSendMessage(event);
            } else {
                event.setStatus(BatchEventStatusEnum.EXE_LATER.getCode());
                event.setExtMsg(BatchEventStatusEnum.EXE_LATER.name() + ":" + event.getBatchId());
                event.setRetryTime(event.getRetryTime() + 1);
                econtractSignRecordBatchEventService.updateByPrimaryKeySelective(event);
            }
        });
    }

    private void doExeLaterBatchEvent(List<EcontractSignBatchEventEntity> eventEntitiesLater) {
        LOGGER.info("批量签约发送短信处理稍后执行的任务：eventEntitiesLater={}", JSON.toJSONString(eventEntitiesLater));
        if (CollectionUtils.isEmpty(eventEntitiesLater)) {
            return;
        }
        Integer retry = ConfigUtilAdapter.getInt(MccConstant.SIGN_BATCH_EVENT_RETRY_TIME, 3);
        eventEntitiesLater.stream().forEach(event -> {
            int retryTimes = event.getRetryTime();
            if (retryTimes >= retry) {
                event.setStatus(BatchEventStatusEnum.EXE_FAIL.getCode());
                event.setExtMsg(BatchEventStatusEnum.EXE_FAIL.name() + ":" + event.getBatchId());
                econtractSignRecordBatchEventService.updateByPrimaryKeySelective(event);
                econtractSignRecordBatchService.updateRecordBatchStatusByBatchId(event.getBatchId(),
                    BatchSignStatusEnum.SEND_SMS_FAIL.getCode());
                LOGGER.error("批量签约发送短信处理稍后执行的任务：eventEntitiesLater={}，尝试多次均失败", JSON.toJSONString(eventEntitiesLater));
                Cat.logMetricForCount("job_send_sms_fail");//多次尝试发送短信均失败，埋点告警
            } else {
                dealSendMessage(event);
            }
        });
    }

    private void dealSendMessage(EcontractSignBatchEventEntity event) {
        Optional result = templateManager.doBatchSms(event.getBatchId(), StringUtils.EMPTY);
        if (result.isPresent()) {
            Pair<String, List<String>> resultPair = (Pair<String, List<String>>) result.get();
            event.setStatus(BatchEventStatusEnum.EXE_SUCCESS.getCode());
            event.setExtMsg(BatchEventStatusEnum.EXE_SUCCESS.name() + ":" + event.getBatchId());
            event.setRecordKey(resultPair.getLeft());
            econtractSignRecordBatchEventService.updateByPrimaryKeySelective(event);
            econtractSignRecordBatchService.updateRecordBatchStatusByBatchId(event.getBatchId(),
                BatchSignStatusEnum.SEND_SMS.getCode());
        } else {
            event.setStatus(BatchEventStatusEnum.EXE_LATER.getCode());
            event.setExtMsg(BatchEventStatusEnum.EXE_LATER.name() + ":" + event.getBatchId());
            event.setRetryTime(event.getRetryTime() + 1);
            econtractSignRecordBatchEventService.updateByPrimaryKeySelective(event);
        }
    }
}

package com.sankuai.meituan.waimai.econtract.server.service.engine.task.asyncHandler;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractAsyncRecordEntity;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.AsyncTaskHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 【Author】: 窦旭蒙
 * 【MIS】: douxumeng
 * 【Date】: 2022-12-15 15:31
 * 【Email】: <EMAIL>
 * 【Desc】:
 */
@Slf4j
@Service
public class AsyncFilingContractStHandler extends AsyncFilingContractCommon implements AsyncTaskHandler {

    @Override
    public void handlerAsyncTask(JSONObject resultObject) {
        //获取异步任务记录
        String asyncTaskId = resultObject.getString("asyncTaskId");
        //获取签约流程上下文
        EcontractAsyncRecordEntity econtractAsyncRecord = econtractAsyncRecordMapper.selectByAsyncId(asyncTaskId);
        log.info("AsyncFilingContractStHandler#asyncTaskId:{}, env:{}", asyncTaskId, econtractAsyncRecord.getEnv());
        //只处理st产生的消息，非st消息不处理
        if (!econtractAsyncRecord.getEnv().equals("staging")) {
            return;
        }
        super.handlerAsyncTaskCommon(resultObject, econtractAsyncRecord);
    }
}

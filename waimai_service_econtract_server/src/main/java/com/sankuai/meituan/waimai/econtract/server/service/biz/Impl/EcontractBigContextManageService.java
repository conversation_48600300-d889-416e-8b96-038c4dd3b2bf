package com.sankuai.meituan.waimai.econtract.server.service.biz.Impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractRecordEntityMapper;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractBigMessageEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordContextEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractUserEntity;
import com.sankuai.meituan.waimai.econtract.server.utils.S3Helper;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextDTO;
import com.sankuai.meituan.waimai.thrift.customer.service.WmEcontractSignThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2021-07-19 20:25
 * Email: <EMAIL>
 * Desc:
 */
@Service
@Slf4j
public class EcontractBigContextManageService {

    public static final String bucketName = "econtract_sign_bigcontext";

    @Autowired
    private S3Helper s3Helper;

    @Autowired
    private EcontractRecordEntityMapper econtractRecordEntityMapper;

    @Autowired
    private WmEcontractSignThriftService wmEcontractSignThriftService;


    public boolean econtractRecordGarySwitch(EcontractRecordContextEntity contextEntity) {
        if(!ConfigUtilAdapter.getBoolean("bigcontext_open_switch", false)){
            log.info("record大文本适配灰度开关关闭,custoermid:{}",contextEntity.getEcontractRecordId());
            return false;
        }
        EcontractBatchContextDTO econtractBatchContextDTO = queryBatchByRecordContext(contextEntity);
        if(null == econtractBatchContextDTO){
            return false;
        }
        String batchType = econtractBatchContextDTO.getBatchType();
        Integer customerId = econtractBatchContextDTO.getCustomerId();
        String context = contextEntity.getContext();
        return garyJudgeCommon(batchType, customerId, context);
    }

    public boolean bigMessageGarySwitch(EcontractBigMessageEntity entity) {
        if(!ConfigUtilAdapter.getBoolean("bigcontext_open_switch", false)){
            log.info("bigmsg大文本适配灰度开关关闭,custoermid:{}",entity.getId());
            return false;
        }
        EcontractBatchContextDTO econtractBatchContextDTO = queryBatchByBigMessageContext(entity);
        if(null == econtractBatchContextDTO){
            return false;
        }
        String batchType = econtractBatchContextDTO.getBatchType();
        Integer customerId = econtractBatchContextDTO.getCustomerId();
        String context = entity.getContext();
        return garyJudgeCommon(batchType, customerId, context);
    }

    private boolean garyJudgeCommon(String batchType, Integer customerId, String context) {
        // 灰度策略：1.客户id；2.任务类型；3.客户占比；4.文本大小
        // 客户id过滤
        String garyCustomerIdStr = ConfigUtilAdapter.getString("gary_bigcontext_customer_id", "");//默认关闭
        List<String> garyCustomerIdStrList = Arrays.asList(garyCustomerIdStr.split(","));
        List<Integer> garyCustomerIdList = garyCustomerIdStrList.stream().map(id-> Integer.valueOf(customerId)).collect(Collectors.toList());
        if(!garyCustomerIdList.contains(customerId)){
            return false;
        }
        // 任务类型过滤
        String garyTypeStr = ConfigUtilAdapter.getString("gary_record_type", "");//默认关闭
        List<String> garyTaskTypeList = Arrays.asList(garyTypeStr.split(","));
        if (!garyTaskTypeList.contains(batchType)) {
            return false;
        }
        // 客户灰度占比
        if (customerId % 100 > ConfigUtilAdapter.getInt("gary_sign_customer_prop", 0)) {
            return false;
        }
        // 文本大小控制
        // todo:dxm，可以更细粒度的判断四项冷数据
        if (!isBigContext(context)) {
            return false;
        }
        return true;
    }

    private EcontractBatchContextDTO queryBatchByRecordContext(EcontractRecordContextEntity contextEntity) {
        // 获取econtract_id
        // todo:dxm，econtractRecordEntity结果为空处理
        EcontractRecordEntity econtractRecordEntity = econtractRecordEntityMapper.selectBaseByPrimaryKey(contextEntity.getEcontractRecordId());
        if(null == econtractRecordEntity){
            return null;
        }
        String recordKey = econtractRecordEntity.getRecordKey();
        return queryBatchByRecordKey(recordKey);
    }

    private EcontractBatchContextDTO queryBatchByBigMessageContext(EcontractBigMessageEntity entity) {
        EcontractContext context = JSONObject.parseObject(entity.getContext(), EcontractContext.class);
        String recordKey = context.getEcontractRecordEntity().getRecordKey();
        return queryBatchByRecordKey(recordKey);
    }

    private EcontractBatchContextDTO queryBatchByRecordKey(String recordKey) {
        // 根据recordKey获取EcontractBatchType
        EcontractBatchContextDTO econtractBatchContextDTO = null;
        try {
            econtractBatchContextDTO = wmEcontractSignThriftService.queryBatchTypeByRecordKey(recordKey);
        } catch (Exception e) {
            log.warn("queryBatchtypeByRecordKey获取batchtype异常，recordKey:{}", recordKey);
            return econtractBatchContextDTO;
        }
        return econtractBatchContextDTO;
    }


    public boolean isBigContext(String context) {
        if (StringUtils.isNotEmpty(context)) {
            Long bigContextLimit = ConfigUtilAdapter.getLong("econtract_sign_bigcontextlimit", 1024L * 100);//默认100K
            if (context.getBytes().length > bigContextLimit) {
                return true;
            }
        }
        return false;
    }


    public void uploadObject(String objectName, String content) {
        // bucket存在性校验
        s3Helper.createBucketIfNotExistExample(bucketName);
        // 上传
        s3Helper.putObject(bucketName, objectName, content);
    }

    public byte[] getObject(String objectName) {
        return s3Helper.getObject(bucketName, objectName);
    }


    public String econtractRecordBigContextRemove(EcontractRecordContextEntity contextEntity) {
        String recordBigContext = contextEntity.getContext();
        contextEntity.setContext(StringUtils.EMPTY);
        return recordBigContext;
    }


    public void econtractRecordBigContextFill(EcontractRecordContextEntity contextEntity, String bigTaskContext) {
        contextEntity.setContext(bigTaskContext);
    }


    public String econtractBigMessageContextRemove(EcontractBigMessageEntity entity) {
        EcontractContext context = JSONObject.parseObject(entity.getContext(), EcontractContext.class);
        Map<String, Object> bigMessageColdContext = Maps.newHashMap();
        bigMessageColdContext.put("EcontractUserEntity", context.getEcontractUserEntity());
        bigMessageColdContext.put("FlowList", context.getFlowList());
        bigMessageColdContext.put("StageInfoBoList", context.getStageInfoBoList());
        bigMessageColdContext.put("StageBatchInfoBoList", context.getStageBatchInfoBoList());
        String bigMessageContext = JSONObject.toJSONString(bigMessageColdContext);
        //将context中的冷数据置为空
        context.setEcontractUserEntity(new EcontractUserEntity());
        context.setFlowList(new ArrayList<>());
        context.setStageInfoBoList(new ArrayList<>());
        context.setStageBatchInfoBoList(new ArrayList<>());
        entity.setContext(JSONObject.toJSONString(context));
        return bigMessageContext;
    }


    public void econtractBigMessageContextFill(EcontractBigMessageEntity entity, String bigTaskContext) {
        EcontractContext context = JSONObject.parseObject(entity.getContext(), EcontractContext.class);
        Map<String, Object> bigMessageColdContext = JSONObject.parseObject(bigTaskContext, Map.class);
        context.setEcontractUserEntity((EcontractUserEntity) bigMessageColdContext.get("EcontractUserEntity"));
        context.setFlowList((ArrayList) bigMessageColdContext.get("FlowList"));
        context.setStageInfoBoList((ArrayList) bigMessageColdContext.get("StageInfoBoList"));
        context.setStageBatchInfoBoList((ArrayList) bigMessageColdContext.get("StageBatchInfoBoList"));
    }

}

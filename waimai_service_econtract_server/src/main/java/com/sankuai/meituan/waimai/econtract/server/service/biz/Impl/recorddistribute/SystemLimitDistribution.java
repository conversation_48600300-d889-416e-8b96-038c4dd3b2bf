package com.sankuai.meituan.waimai.econtract.server.service.biz.Impl.recorddistribute;

import com.google.common.util.concurrent.RateLimiter;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.constants.MccConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.MqConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 【Author】: 窦旭蒙
 * 【MIS】: douxumeng
 * 【Date】: 2022-09-05 20:10
 * 【Email】: <EMAIL>
 * 【Desc】: 动态流量分发器（动态可配）
 */
@Slf4j
@Service
public class SystemLimitDistribution implements RecordDistribution {

    //初始化限流值
    public static RateLimiter rateLimiter = RateLimiter.create(ConfigUtilAdapter.getInt(MccConstant.RECORD_LIMITER_INIT_NUM, 1));


    @Override
    public boolean distribute(EcontractContext econtractContext) {
        if (rateLimiter.tryAcquire()) {
            log.info("recordBizKey:{}，获取到流量令牌，未触发限流逻辑", econtractContext.getRecordBizKey());
        } else {
            log.info("recordBizKey:{}，未获取到流量令牌，进入队列:{}", econtractContext.getRecordBizKey(), MqConstant.SLOW_QUEUE);
            econtractContext.setQueueName(MqConstant.SLOW_QUEUE);
            return true;
        }
        return false;
    }

}

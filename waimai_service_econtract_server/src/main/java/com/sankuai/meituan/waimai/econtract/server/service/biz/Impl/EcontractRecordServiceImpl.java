package com.sankuai.meituan.waimai.econtract.server.service.biz.Impl;

import static com.sankuai.meituan.waimai.econtract.server.constants.MccConstant.RECORD_SEPERATE_COLDHOT;
import static com.sankuai.meituan.waimai.econtract.server.constants.MccConstant.RECORD_SEPERATE_COLDHOT_DEFAULT;
import static com.sankuai.meituan.waimai.econtract.server.constants.MccConstant.RECORD_SEPERATE_COLDHOT_PERCENTAGE;
import static com.sankuai.meituan.waimai.econtract.server.constants.MccConstant.RECORD_SEPERATE_COLDHOT_PERCENTAGE_DEFAULT;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.econtract.server.bo.query.EcontractRecordQueryBo;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import com.sankuai.meituan.waimai.econtract.server.constants.EcontractRecordConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.MqConstant;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractRecordContextMapper;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractRecordEntityMapper;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordContextEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordEntity;
import com.sankuai.meituan.waimai.econtract.server.exception.EcontractException;
import com.sankuai.meituan.waimai.econtract.server.service.SpringBeanUtil;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractMetricService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractRecordService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.Impl.recorddistribute.RecordDistribution;
import com.sankuai.meituan.waimai.econtract.server.utils.JacksonUtil;
import com.sankuai.meituan.waimai.econtract.server.utils.TimeUtils;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractRecordSourceEnum;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBo;
import com.sankuai.meituan.waimai.econtrct.client.util.GrayUtil;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR> Hou
 * @date 2017/10/19
 * @time 下午4:42
 */
@Service
public class EcontractRecordServiceImpl implements EcontractRecordService {

    private static final Logger LOGGER = LoggerFactory.getLogger(EcontractRecordServiceImpl.class);

    @Autowired
    private EcontractRecordEntityMapper econtractRecordEntityMapper;

    @Autowired
    private EcontractRecordContextMapper econtractRecordContextMapper;

    @Autowired
    private EcontractBigContextManageService econtractBigContextManageService;

    @Autowired
    private EcontractBigRecordParseService econtractBigRecordParseService;

    @Resource
    private EcontractMetricService econtractMetricService;

    private static final int HOT_CONTEXT_QUERY_RETRY_TIME = 3;

    public static List<RecordDistribution> recordDistributionList = Lists.newArrayList();

    public void init() {
        recordDistributionList.add((RecordDistribution) SpringBeanUtil.getBean("systemLimitDistribution"));
        recordDistributionList.add((RecordDistribution) SpringBeanUtil.getBean("econtractTypeDistribution"));
        recordDistributionList.add((RecordDistribution) SpringBeanUtil.getBean("sourceDistribution"));
    }

    @Override
    public void deleteByPrimaryKey(Long id) {
        // 不允许的操作
    }

    @Override
    public void insertSelective(EcontractRecordEntity entity) {
        if (entity == null) {
            LOGGER.error("插入record信息为空");
            return;
        }

        if (!isSeperateHotAndColdData()) {
            econtractRecordEntityMapper.insertSelective(entity);
        } else {
            // 保存热数据(主表数据)
            insertRecordHotData(entity);
            // 保存冷数据(上下文信息)
            insertRecordColdData(entity);
        }
    }

    /**
     * 保存record热数据(状态等信息) 对方法外无感知，context数据不会做任何变更
     */
    private void insertRecordHotData(EcontractRecordEntity entity) {
        // 全量数据备份
        String backupContext = entity.getEcontractRecordContext();
        backupContext = wrapColdDataFieldList(backupContext);
        // 生成热数据，用于保存
        entity.setEcontractRecordContext(getHotData(backupContext));
        LOGGER.info("insertRecordHotData context = " + entity.getEcontractRecordContext());
        econtractRecordEntityMapper.insertSelective(entity);
        // 全量context数据恢复
        entity.setEcontractRecordContext(backupContext);
    }

    /**
     * 保存record冷数据(context信息) 对方法外无感知，context数据不会做任何变更
     */
    private void insertRecordColdData(EcontractRecordEntity entity) {
        // 全量数据备份
        String backupContext = entity.getEcontractRecordContext();
        // 生成热数据，用于保存
        entity.setEcontractRecordContext(getColdData(backupContext));
        // 保存record的context信息(冷数据)
        EcontractRecordContextEntity contextEntity = new EcontractRecordContextEntity();
        contextEntity.setEcontractRecordId(entity.getId());
        contextEntity.setContext(entity.getEcontractRecordContext());
        contextEntity.setCtime(TimeUtils.getCTime());
        contextEntity.setUtime(TimeUtils.getUTime());
        contextEntity.setValid((byte) 1);
        contextEntity.setVersion(0);
        econtractBigRecordParseService.insertSelective(contextEntity);
        // 全量context数据恢复
        entity.setEcontractRecordContext(backupContext);
    }

    @Override
    public void insert(EcontractRecordEntity entity) {
        // 不允许的操作
    }

    /**
     * 根据主键查询
     */
    @Override
    public EcontractRecordEntity selectByPrimaryKey(Integer id) {
        EcontractRecordEntity recordEntity = econtractRecordEntityMapper.selectByPrimaryKey(id);
        LOGGER.info("EcontractRecordServiceImpl#selectByPrimaryKey, recordEntity: {}", JacksonUtil.writeAsJsonStr(recordEntity));
        return this.retryWrapColdAndHotData(recordEntity);
    }

    /**
     * 更新语句，只允许更新热数据
     */
    @Override
    public void updateByPrimaryKeySelective(EcontractRecordEntity entity) {
        if (!isConfigSeperateInContext(entity.getEcontractRecordContext())) {
            updateEcontractRecord(entity);
        } else {
            // 全量数据备份
            String backupContext = entity.getEcontractRecordContext();
            // 生成热数据，用于保存
            entity.setEcontractRecordContext(getHotData(backupContext));
            LOGGER.info("updateByPrimaryKeySelective entity = {}", entity);
            updateEcontractRecord(entity);
            // 全量context数据恢复
            entity.setEcontractRecordContext(backupContext);
        }
    }

    @VisibleForTesting
    protected void updateEcontractRecord(EcontractRecordEntity entity) {
        if (MccConfig.updateEcontractRecordWithMysqlLockCheck()) {
            updateNotFailEcontractRecord(entity);
        } else {
            EcontractRecordEntity recordEntity = this.queryRecordByRecordKey(entity.getRecordKey());
            String dbState = recordEntity.getEcontractState();
            String targetState = entity.getEcontractState();
            if (EcontractRecordConstant.FAIL.equals(dbState) && !EcontractRecordConstant.FAIL.equals(targetState)) {
                LOGGER.error("EcontractRecordServiceImpl#updateEcontractRecord, 任务失败后不可再置为其他状态, recordKey: {}", entity.getRecordKey());
                throw new EcontractException(EcontractException.SERVER_ERROR, "任务状态流转异常");
            }
            econtractRecordEntityMapper.updateByPrimaryKeySelective(entity);
        }
    }

    /**
     * 使用判断状态的SQL进行更新操作
     */
    @VisibleForTesting
    protected void updateNotFailEcontractRecord(EcontractRecordEntity entity) {
        int updated = econtractRecordEntityMapper.updateNotFailRecordByPrimaryKeySelective(entity);
        if (updated > 0) {
            return;
        }
        econtractMetricService.metricUpdateFailRecord(entity.getRecordKey());
        LOGGER.error("EcontractRecordServiceImpl#updateNotFailEcontractRecord, 任务失败后不可再置为其他状态, recordKey: {}", entity.getRecordKey());
        throw new EcontractException(EcontractException.SERVER_ERROR, "任务状态流转异常");
    }

    /**
     * 更新语句，只允许更新热数据，不更新saveUrl
     */
    @Override
    public void updateByPrimaryKeySelectiveNoSaveUrl(EcontractRecordEntity entity) {
        LOGGER.info("updateByPrimaryKeySelectiveNoSaveUrl 执行了不更新saveUrl的方法");
        if (!isConfigSeperateInContext(entity.getEcontractRecordContext())) {
            updateNoSaveUrl(entity);
        } else {
            // 全量数据备份
            String backupContext = entity.getEcontractRecordContext();
            // 生成热数据，用于保存
            entity.setEcontractRecordContext(getHotData(backupContext));
            LOGGER.info("updateByPrimaryKeySelectiveNoSaveUrl context = " + entity.getEcontractRecordContext());
            updateNoSaveUrl(entity);
            // 全量context数据恢复
            entity.setEcontractRecordContext(backupContext);
        }
    }

    private void updateNoSaveUrl(EcontractRecordEntity entity) {
        if (MccConfig.updateEcontractRecordNoSaveUrlWithMysqlLockCheck()) {
            // 更新非失败的记录（不更新saveUrl）
            LOGGER.info("updateNoSaveUrl 更新非失败的任务（不更新saveUrl）");
            econtractRecordEntityMapper.updateNotFailByPrimaryKeySelectiveNoSaveUrl(entity);
        } else {
            econtractRecordEntityMapper.updateByPrimaryKeySelectiveNoSaveUrl(entity);
        }
    }


    @Override
    public Map<String, String> parseMultiPdfUrl(String saveUrl) {
        LOGGER.info("parseMultiPdfUrl#param:{}", saveUrl);
        Map<String, String> result = Maps.newHashMap();
        if (StringUtils.isEmpty(saveUrl)) {
            return result;
        }
        if (saveUrl.startsWith("{") && saveUrl.endsWith("}")) {
            saveUrl = saveUrl.substring(1, saveUrl.length());
            saveUrl = saveUrl.substring(0, saveUrl.length() - 1);
        }
        List<String> pdfUrlList = Lists.newArrayList(saveUrl.split(","));
        // 20211201当前只处理多pdf任务，此处遇到单pdf时可以处理为default，后续如果需要考虑单pdf任务时，此处需迭代
        if (pdfUrlList.size() == 1) {
            result.put("default", pdfUrlList.get(0));
        } else {
            pdfUrlList.stream().forEach(pdfUrl -> {
                Integer index = pdfUrl.indexOf(":");
                String pdfUrlKey = pdfUrl.substring(0, index).replace("\"", "");
                String pdfUrlValue = pdfUrl.substring(index + 1, pdfUrl.length()).replace("\"", "");
                result.put(pdfUrlKey, pdfUrlValue);
            });
        }
        LOGGER.info("parseMultiPdfUrl#result:{}", JSON.toJSONString(result));
        return result;
    }

    @Override
    public int updateOptimisticLock(EcontractRecordEntity entity) {
        if (!isConfigSeperateInContext(entity.getEcontractRecordContext())) {
            return econtractRecordEntityMapper.updateOptimisticLock(entity);
        } else {
            // 全量数据备份
            String backupContext = entity.getEcontractRecordContext();
            // 生成热数据，用于保存
            entity.setEcontractRecordContext(getHotData(backupContext));
            LOGGER.info("updateOptimisticLock context = " + entity.getEcontractRecordContext());
            int updateCount = econtractRecordEntityMapper.updateOptimisticLock(entity);
            // 全量context数据恢复
            entity.setEcontractRecordContext(backupContext);
            return updateCount;
        }
    }

    @Override
    public void updateByPrimaryKey(EcontractRecordEntity entity) {

    }

    @Override
    public int deleteById(Integer id) {
        // 删除热数据
        int count = econtractRecordEntityMapper.deleteById(id);
        // 删除冷数据
        econtractRecordContextMapper.deleteByRecordId(id);
        return count;
    }

    @Override
    public List<Integer> selectFinishIdsByUtime(Integer startId, Date utime, Integer pageSize) {
        return econtractRecordEntityMapper.selectFinishIdsByUtime(startId, utime, pageSize);
    }

    @Override
    public EcontractRecordEntity selectColdDataByPrimaryKey(Integer id) {
        return econtractRecordEntityMapper.selectBaseByPrimaryKey(id);
    }

    @Override
    public EcontractRecordEntity queryRecordByRecordKey(String recordKey) {
        EcontractRecordEntity recordEntity = econtractRecordEntityMapper.queryRecordByRecordKey(recordKey);
        LOGGER.info("EcontractRecordServiceImpl#queryRecordByRecordKey, recordEntity: {}", JacksonUtil.writeAsJsonStr(recordEntity));
        if (recordEntity == null) {
            LOGGER.warn("EcontractRecordServiceImpl#queryRecordByRecordKey, recordKey: {}", recordKey);
            throw new EcontractException(EcontractException.PARAM_ERROR, "未查询到签约信息");
        }
        return this.retryWrapColdAndHotData(recordEntity);
    }

    private EcontractRecordEntity retryWrapColdAndHotData(EcontractRecordEntity recordEntity) {
        if (StringUtils.isEmpty(recordEntity.getEcontractRecordContext())) {
            LOGGER.info("recordKey:{}，queryRecordByRecordKey签约热数据上下文为空，开启重试", recordEntity.getRecordKey());
            for (int time = 0; time < HOT_CONTEXT_QUERY_RETRY_TIME; time++) {
                try {
                    Thread.sleep(10);
                } catch (Exception e) {
                    LOGGER.error("queryRecordByRecordKey重试获取签约热数据上下文线程等待异常，recordKey:{}", recordEntity.getRecordKey(), e);
                }
                recordEntity = econtractRecordEntityMapper.queryRecordByRecordKey(recordEntity.getRecordKey());
                if (StringUtils.isNotEmpty(recordEntity.getEcontractRecordContext())) {
                    LOGGER.info("recordKey:{}，queryRecordByRecordKey重试{}次后获取到签约热数据上下文", recordEntity.getRecordKey(), ++time);
                    break;
                }
            }
            LOGGER.info("recordKey:{}，queryRecordByRecordKey重试后仍未获取到签约热数据上下文", recordEntity.getRecordKey());
        }
        return this.wrapColdByHotData(recordEntity);
    }

    /**
     * 批量查询只有冷数据
     */
    @Override
    public List<EcontractRecordEntity> queryEcontractRecordByPage(EcontractRecordQueryBo queryBo) {
        return econtractRecordEntityMapper.queryEcontractRecordByPage(queryBo);
    }

    /**
     * 批量查询只有冷数据
     */
    @Override
    public Integer queryEcontractRecordByPageCount(EcontractRecordQueryBo queryBo) {
        if (isEmptyQuery(queryBo)) {
            return econtractRecordEntityMapper.queryLastIndex();
        }
        return econtractRecordEntityMapper.queryEcontractRecordByPageCount(queryBo);
    }

    /**
     * 根据recordKey列表更新对应批次ID
     *
     * @param recordKeyList
     * @param batchOpId
     */
    @Override
    public void batchUpdateBatchOpIdByRecordKeyList(List<String> recordKeyList, Long batchOpId) {
        econtractRecordEntityMapper.batchUpdateBatchOpIdByRecordKeyList(recordKeyList, batchOpId);
    }

    /**
     * 根据recordKey列表获取record列表(无需冷数据)
     *
     * @param recordKeyList
     * @return
     */
    @Override
    public List<EcontractRecordEntity> queryRecordListByRecordKeyList(List<String> recordKeyList) {
        return econtractRecordEntityMapper.queryRecordListByRecordKeyList(recordKeyList);
    }

    /**
     * 根据recordKey更新上游状态
     *
     * @param recordKey
     * @param upstreamStatus
     * @return
     */
    public int updateUpstreamStatusByRecordKey(String recordKey, Integer upstreamStatus) {
        return econtractRecordEntityMapper.updateUpstreamStatusByRecordKey(recordKey, upstreamStatus);
    }

    @Override
    public Integer queryRecordBatchIdByRecordKey(String recordKey) {
        return econtractRecordEntityMapper.queryRecordBatchIdByRecordKey(recordKey);
    }

    @Override
    public void distributeRecord(EcontractContext econtractContext, EcontractBatchBo econtractBatchBo) {
        distributeRecordCommon(econtractContext,
            econtractBatchBo.getEcontractBizId(),
            econtractBatchBo.getEcontractBatchSource());
    }

    @Override
    public void distributeRecord(EcontractContext econtractContext, EcontractBo econtractBo) {
        distributeRecordCommon(econtractContext,
            econtractBo.getEcontractBizId(),
            econtractBo.getEcontractBatchSource());
    }

    @Override
    public String queryEcontractTypeByRecordKey(String recordKey) {
        EcontractRecordEntity recordEntity = econtractRecordEntityMapper.queryRecordByRecordKey(recordKey);
        return recordEntity.getEcontractType();
    }

    private void distributeRecordCommon(EcontractContext econtractContext, String econtractBizId,
                                        EcontractRecordSourceEnum econtractRecordSource) {
        LOGGER.info("distributeRecordCommon#econtractContext:{}，econtractBizId:{}，econtractRecordSource:{}",
            JSON.toJSONString(econtractContext), econtractBizId, econtractRecordSource);
        for (RecordDistribution recordDistribution : recordDistributionList) {
            if (recordDistribution.distribute(econtractContext)) {
                LOGGER.info("econtractBizId:{}，econtractBatchSource:{}，由处理器:{}完成队列分配",
                    econtractBizId, econtractRecordSource, recordDistribution.getClass().getSimpleName());
                break;
            }
        }
        if (StringUtils.isEmpty(econtractContext.getQueueName())) {
            //兜底逻辑，将任务分配至快队列
            econtractContext.setQueueName(MqConstant.FAST_QUEUE);
            LOGGER.warn("econtractBizId:{}，econtractBatchSource:{}，未分配处理队列，触发兜底分配逻辑",
                econtractBizId, econtractRecordSource);
            Cat.logMetricForCount("record_not_allocate_mq_queue");
        }
        LOGGER.info("econtractBizId:{}，econtractBatchSource:{}，队列分配完成，queueName:{}",
            econtractBizId, econtractRecordSource, econtractContext.getQueueName());
    }

    /**
     * 判断电子合同平台首页查询条件是否为空
     */
    private boolean isEmptyQuery(EcontractRecordQueryBo queryBo) {
        return queryBo.getEcontractStage() == null && queryBo.getEcontractState() == null
            && queryBo.getRecordKey() == null && queryBo.getRecordBizKey() == null
            && queryBo.getEcontractType() == null;
    }

    /**
     * 是否冷热数据分离
     */
    private boolean isSeperateHotAndColdData() {
        // 灰度总开关打开且灰度比例配置开关打开
        // RECORD_SEPERATE_COLDHOT:总开关
        // RECORD_SEPERATE_COLDHOT_DEFAULT:总开关默认值
        // RECORD_SEPERATE_COLDHOT_PERCENTAGE:比例开关
        // RECORD_SEPERATE_COLDHOT_PERCENTAGE_DEFAULT:比例开关默认值
        return GrayUtil.isGray(RECORD_SEPERATE_COLDHOT, RECORD_SEPERATE_COLDHOT_DEFAULT,
            RECORD_SEPERATE_COLDHOT_PERCENTAGE, RECORD_SEPERATE_COLDHOT_PERCENTAGE_DEFAULT);
    }

    /**
     * 上下文中是否配置了冷热数据分离
     */
    private boolean isConfigSeperateInContext(String context) {
        EcontractContext econtractContext = JSON.parseObject(context, EcontractContext.class);
        return CollectionUtils.isNotEmpty(econtractContext.getColdDataList());
    }

    /**
     * 拼装冷数据列表到上下文中
     */
    private String wrapColdDataFieldList(String context) {
        if (StringUtils.isBlank(context)) {
            return StringUtils.EMPTY;
        }

        EcontractContext econtractContext = JSON.parseObject(context, EcontractContext.class);
        econtractContext.setColdDataList(getColdDataFieldList());
        return JSON.toJSONString(econtractContext);
    }

    /**
     * 获取冷数据字段名
     */
    private List<String> getColdDataFieldList() {
        return Lists.newArrayList("econtractUserEntity", "flowList", "stageInfoBoList", "stageBatchInfoBoList");
    }

    /**
     * 获取context中的热数据 将冷数据全部置为null
     */
    private String getHotData(String context) {
        EcontractContext econtractContext = JSON.parseObject(context, EcontractContext.class);
        econtractContext.setEcontractUserEntity(null);
        econtractContext.setFlowList(null);
        econtractContext.setStageInfoBoList(null);
        econtractContext.setStageBatchInfoBoList(null);
        return JSON.toJSONString(econtractContext);
    }

    /**
     * 获取冷数据 给冷数据赋值
     */
    private String getColdData(String context) {
        EcontractContext econtractContext = JSON.parseObject(context, EcontractContext.class);
        EcontractContext coldContext = new EcontractContext();
        coldContext.setEcontractUserEntity(econtractContext.getEcontractUserEntity());
        coldContext.setFlowList(econtractContext.getFlowList());
        coldContext.setStageInfoBoList(econtractContext.getStageInfoBoList());
        coldContext.setStageBatchInfoBoList(econtractContext.getStageBatchInfoBoList());
        coldContext.setIsAreaSeperateSave(econtractContext.getIsAreaSeperateSave());
        coldContext.setAreaSeperateSaveWmPoiIdList(econtractContext.getAreaSeperateSaveWmPoiIdList());
        return JSON.toJSONString(coldContext);
    }

    /**
     * 根据已查询到的热数据拼装响应的冷数据
     */
    private EcontractRecordEntity wrapColdByHotData(EcontractRecordEntity recordEntity) {
        if (recordEntity == null) {
            return null;
        }
        EcontractRecordContextEntity contextEntity = econtractBigRecordParseService
            .selectByRecordId(recordEntity.getId());
        return this.wrapHotAndColdData(recordEntity, contextEntity);
    }

    /**
     * 拼装冷热数据(兼容历史没有冷数据的方式) 1.若没有冷数据，则认为是历史数据，直接返回热数据作为全量数据
     * 2.若存在冷数据，则以冷数据为准，冷数据存在则直接覆盖热数据相关数据项。因为进入冷数据的理论上只有第一次写入。后续不会更新
     */
    private EcontractRecordEntity wrapHotAndColdData(EcontractRecordEntity recordEntity,
                                                     EcontractRecordContextEntity contextEntity) {
        if (contextEntity == null) {
            return recordEntity;
        }

        String fullContext = wrapHotAndColdData(recordEntity.getEcontractRecordContext(), contextEntity.getContext());
        recordEntity.setEcontractRecordContext(fullContext);
        return recordEntity;
    }

    /**
     * 冷数据与热数据拼装成最终的context信息
     */
    private String wrapHotAndColdData(String hotData, String coldData) {
        EcontractContext hotContext = JSON.parseObject(hotData, EcontractContext.class);
        EcontractContext coldContext = JSON.parseObject(coldData, EcontractContext.class);
        LOGGER.debug("wrapHotAndColdData#hotContext:{},coldContext:{}",
            JSON.toJSONString(hotContext),
            JSON.toJSONString(coldContext));
        hotContext.setEcontractUserEntity(coldContext.getEcontractUserEntity());
        hotContext.setFlowList(coldContext.getFlowList());
        hotContext.setStageInfoBoList(coldContext.getStageInfoBoList());
        hotContext.setStageBatchInfoBoList(coldContext.getStageBatchInfoBoList());
        return JSON.toJSONString(hotContext);
    }

    @Override
    public List<EcontractRecordEntity> queryEcontractRecordByBatchId(Integer batchId) {
        LOGGER.info("queryEcontractRecordByBatchId#param:{}", batchId);
        return econtractRecordEntityMapper.queryEcontractRecordByBatchId(batchId);
    }

    @Override
    public List<EcontractRecordEntity> queryColdAndHotEcontractRecordByBatchId(Integer batchId) {
        List<EcontractRecordEntity> entityList = this.queryEcontractRecordByBatchId(batchId);
        return entityList.stream().map(this::retryWrapColdAndHotData).collect(Collectors.toList());
    }

    @Override
    public List<EcontractRecordEntity> queryEcontractRecordByBatchIdAsc(Integer batchId) {
        LOGGER.info("queryEcontractRecordByBatchIdAsc#param:{}", batchId);
        return econtractRecordEntityMapper.queryEcontractRecordByBatchIdAsc(batchId);
    }

    @Override
    public List<EcontractRecordEntity> queryEcontractRecordByBatchIds(List<Integer> batchIds) {
        LOGGER.info("queryEcontractRecordByBatchIds#batchIds:{}", batchIds);
        return econtractRecordEntityMapper.queryEcontractRecordByBatchIds(batchIds);
    }

    @Override
    public List<EcontractRecordEntity> queryRecordList(List<String> recordKeyList) {
        LOGGER.info("queryRecordList#recordKeyList:{}", recordKeyList);
        List<EcontractRecordEntity> econtractRecordEntityList = econtractRecordEntityMapper.queryRecordList(
            recordKeyList);
        LOGGER.info("queryRecordList#econtractRecordEntityList:{}", econtractRecordEntityList);
        return econtractRecordEntityList;
    }
}
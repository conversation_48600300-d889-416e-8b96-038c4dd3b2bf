package com.sankuai.meituan.waimai.econtract.server.service.mq.mafka.producer;

import com.google.common.base.Throwables;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractMafkaProducer {

    protected IProducerProcessor producer;

    protected abstract void doSetUpProducer() throws Exception;

    @PostConstruct
    public void setUp() {
        log.info("MafkaProducer开始启动");
        try {
            doSetUpProducer();
        } catch (Exception e) {
            log.error("MafkaProducer启动出错", e);
            if (producer != null) {
                try {
                    producer.close();
                    producer = null;
                } catch (Exception e1) {
                    log.error("MafkaProducer close exception", e1);
                }
            }
            Throwables.propagate(new RuntimeException("MafkaProducer 启动异常"));
        }
    }

    /**
     * true 消息体过大  false消息体小不需要操作
     * @param context
     * @return
     */
    protected boolean checkSize(String context){
        return context.getBytes().length > MccConfig.MESSAGE_SIZE;
    }

    @PreDestroy
    public void destroy() {
        try {
            if (producer != null) {
                producer.close();
                producer = null;
            }
        } catch (Exception e) {
            log.error("MafkaProducer close exception", e);
        }
    }
}
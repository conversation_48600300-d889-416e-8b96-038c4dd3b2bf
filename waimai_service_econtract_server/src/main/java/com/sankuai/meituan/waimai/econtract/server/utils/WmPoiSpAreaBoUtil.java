package com.sankuai.meituan.waimai.econtract.server.utils;

import java.util.List;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import com.google.common.base.MoreObjects;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.econtrct.client.domain.applybiz.WmPoiAreaBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.applybiz.WmPoiDeliveryPlanBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.applybiz.WmPoiLocation;
import com.sankuai.meituan.waimai.econtrct.client.domain.applybiz.WmPoiSpAreaContentBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.biz.EcontractContentBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class WmPoiSpAreaBoUtil {

    private static final String DEFAULT_CLS_NAME = "常规时段";

    public static EcontractContentBo transEcontractWmPoiSelfDeliveryAreaBo2EcontractContentBo(EcontractWmPoiSpAreaBo input) {
        if (input == null) {
            return null;
        }
        // 兼容老sla，getAreaBoList有值的情况
        if (CollectionUtils.isNotEmpty(input.getAreaBoList())) {
            return transEcontractWmPoiSpAreaBo2EcontractContentBo(input, input.getSlaText());
        }
        WmPoiSpAreaContentBo result = new WmPoiSpAreaContentBo();
        result.setWmPoiId(input.getWmPoiId());
        result.setWmPoiName(input.getWmPoiName());
        result.setCenterPoint(transEcontractWmPoiSpAreaBo2WmPoiLocation(input.getCenterPoint()));
        List<EcontractWmPoiSelfDeliveryPlanBo> econtractWmPoiSelfDeliveryPlanBoList = input.getSelfDeliveryPlanBoList();
        List<WmPoiDeliveryPlanBo> wmPoiDeliveryPlanBoList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(econtractWmPoiSelfDeliveryPlanBoList)) {
            WmPoiDeliveryPlanBo wmPoiDeliveryPlanBo = null;
            for (EcontractWmPoiSelfDeliveryPlanBo temp : econtractWmPoiSelfDeliveryPlanBoList) {
                wmPoiDeliveryPlanBo = new WmPoiDeliveryPlanBo();
                wmPoiDeliveryPlanBo.setClsName(temp.getClsName());
                wmPoiDeliveryPlanBo.setStartTime(temp.getStartTime());
                wmPoiDeliveryPlanBo.setEndTime(temp.getEndTime());
                wmPoiDeliveryPlanBo.setAreaBoList(transEcontractWmPoiAreaPlanBoList2AreaBoListNewVersion(temp.getAreaBoList()));
                wmPoiDeliveryPlanBo.setCls(temp.getCls());
                wmPoiDeliveryPlanBo.setOriginalStartTime(temp.getOriginalStartTime());
                wmPoiDeliveryPlanBo.setOriginalEndTime(temp.getOriginalEndTime());
                wmPoiDeliveryPlanBo.setDiffType(temp.getDiffType() == null ? null : temp.getDiffType().getCode());
                wmPoiDeliveryPlanBoList.add(wmPoiDeliveryPlanBo);
            }
        }
        result.setDeliveryPlanBoList(wmPoiDeliveryPlanBoList);
        if (StringUtils.isNotEmpty(input.getSlaText())) {
            result.setSlaText(input.getSlaText());
        }
        return result;
    }

    private static List<WmPoiAreaBo> transEcontractWmPoiAreaPlanBoList2AreaBoListNewVersion(List<EcontractWmPoiAreaPlanBo> areaBoList) {
        if (CollectionUtils.isEmpty(areaBoList)) {
            return Lists.newArrayList();
        }
        List<WmPoiAreaBo> result = Lists.newArrayList();
        WmPoiAreaBo wmPoiAreaBo = null;
        for (EcontractWmPoiAreaPlanBo temp : areaBoList) {
            wmPoiAreaBo = new WmPoiAreaBo();
            wmPoiAreaBo.setMinPrice(temp.getMinPrice());
            wmPoiAreaBo.setShippingFee(temp.getShippingFee());
            wmPoiAreaBo.setAcreage(temp.getAcreage());
            wmPoiAreaBo.setArea(transEcontractWmPoiSpAreaBoList2WmPoiLocationList(temp.getArea()));
            wmPoiAreaBo.setOriginalMinPrice(temp.getOriginalMinPrice());
            wmPoiAreaBo.setOriginalShippingFee(temp.getOriginalShippingFee());
            wmPoiAreaBo.setOriginalAcreage(temp.getOriginalAcreage());
            wmPoiAreaBo.setAreaName(temp.getAreaName());
            wmPoiAreaBo.setDiffType(temp.getDiffType() == null ? null : temp.getDiffType().getCode());
            wmPoiAreaBo.setOriginalArea(transEcontractWmPoiSpAreaBoList2WmPoiLocationList(temp.getOriginalArea()));
            result.add(wmPoiAreaBo);
        }
        return result;
    }

    public static EcontractContentBo transEcontractWmPoiSpAreaBo2EcontractContentBo(EcontractWmPoiSpAreaBo input, String document) {
        WmPoiSpAreaContentBo result = new WmPoiSpAreaContentBo();
        result.setWmPoiId(input.getWmPoiId());
        result.setWmPoiName(input.getWmPoiName());
        result.setCenterPoint(transEcontractWmPoiSpAreaBo2WmPoiLocation(input.getCenterPoint()));

        List<EcontractWmPoiAreaSlaPlanBo> econtractWmPoiAreaSlaPlanBoList = input.getAreaBoList();
        List<WmPoiDeliveryPlanBo> wmPoiDeliveryPlanBoList = Lists.newArrayList();
        WmPoiDeliveryPlanBo wmPoiDeliveryPlanBo = null;
        WmPoiAreaBo areaBoTemp = null;
        if (CollectionUtils.isNotEmpty(econtractWmPoiAreaSlaPlanBoList)) {
            for (EcontractWmPoiAreaSlaPlanBo temp : econtractWmPoiAreaSlaPlanBoList) {
                wmPoiDeliveryPlanBo = new WmPoiDeliveryPlanBo();
                wmPoiDeliveryPlanBo.setClsName(MoreObjects.firstNonNull(temp.getClsName(), DEFAULT_CLS_NAME));
                wmPoiDeliveryPlanBo.setStartTime(temp.getStartTime());
                wmPoiDeliveryPlanBo.setEndTime(temp.getEndTime());
                areaBoTemp = new WmPoiAreaBo();
                areaBoTemp.setMinPrice(temp.getMinPrice());
                areaBoTemp.setShippingFee(temp.getShippingFee());
                areaBoTemp.setAcreage(temp.getAcreage());
                areaBoTemp.setArea(transEcontractWmPoiSpAreaBoList2WmPoiLocationList(temp.getArea()));
                wmPoiDeliveryPlanBo.setAreaBoList(Lists.newArrayList(areaBoTemp));
                wmPoiDeliveryPlanBoList.add(wmPoiDeliveryPlanBo);
            }
        }
        result.setDeliveryPlanBoList(wmPoiDeliveryPlanBoList);
        if (StringUtils.isNotEmpty(input.getSlaText())) {
            result.setSlaText(input.getSlaText());
        }
        if (StringUtils.isNotEmpty(document)) {
            result.setSlaText(document);
        }
        return result;
    }

    public static WmPoiLocation transEcontractWmPoiSpAreaBo2WmPoiLocation(EcontractWmPoiLocation input) {
        if (input == null) {
            return null;
        }
        WmPoiLocation result = new WmPoiLocation();
        result.setX(input.getX());
        result.setY(input.getY());
        return result;
    }

    public static List<WmPoiLocation> transEcontractWmPoiSpAreaBoList2WmPoiLocationList(List<EcontractWmPoiLocation> input) {
        List<WmPoiLocation> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(input)) {
            return result;
        }
        for (EcontractWmPoiLocation temp : input) {
            result.add(transEcontractWmPoiSpAreaBo2WmPoiLocation(temp));
        }
        return result;
    }
}

package com.sankuai.meituan.waimai.econtract.server.service.engine.template.processor.Impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import com.meituan.mtrace.thread.pool.TraceExecutors;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.bizsso.thrift.CaptchaTService;
import com.sankuai.meituan.waimai.bizsso.thrift.PhoneCaptcha;
import com.sankuai.meituan.waimai.bizsso.thrift.Source;
import com.sankuai.meituan.waimai.bizsso.thrift.exception.BizssoServerException;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.bo.task.TaskContext;
import com.sankuai.meituan.waimai.econtract.server.bo.task.TaskNodeBo;
import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import com.sankuai.meituan.waimai.econtract.server.constants.ContextConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.EcontractRecordConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.MccConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtract.server.service.biz.*;
import com.sankuai.meituan.waimai.econtrct.client.constants.*;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractSmsDealEntityMapper;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRealNameDealEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractSmsDealEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractTaskEntity;
import com.sankuai.meituan.waimai.econtract.server.exception.EcontractException;
import com.sankuai.meituan.waimai.econtract.server.service.SpringBeanUtil;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.Impl.SmsExecutor;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.TaskManager;
import com.sankuai.meituan.waimai.econtract.server.service.engine.template.handler.TaskHandler;
import com.sankuai.meituan.waimai.econtract.server.service.engine.template.processor.TemplateProcessor;
import com.sankuai.meituan.waimai.econtract.server.service.engine.template.resolver.ProcessResolverService;
import com.sankuai.meituan.waimai.econtract.server.service.mq.TaskMsg;
import com.sankuai.meituan.waimai.econtract.server.utils.*;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.*;
import com.sankuai.meituan.waimai.kv.groupm.service.TairLockHandler;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.*;

import static com.sankuai.meituan.waimai.econtract.server.constants.MccConstant.REAL_NAME_JUMP;

/**
 * <AUTHOR> Hou
 * @date 2017/11/26
 * @time 下午12:06
 */
@Service
public class CommonProcessor extends AbstractProcessor implements TemplateProcessor {

    private static final Logger LOGGER = LoggerFactory.getLogger(CommonProcessor.class);

    private static final Integer MAX_CAS_RETRY_TIMES = 5;

    @Autowired
    private CaptchaTService.Iface captchaTService;
    @Autowired
    private EcontractRecordService econtractRecordService;
    @Autowired
    private EcontractService econtractService;
    @Autowired
    private EcontractTaskService econtractTaskService;
    @Autowired
    private TaskManager taskManager;
    @Autowired
    private EcontractRealNameAuthService realNameAuthService;
    @Autowired
    private TairService tairService;
    @Autowired
    private TairLockHandler tairLockHandler;
    @Autowired
    private ProcessResolverService resolverService;
    @Autowired
    private EcontractSmsDealEntityMapper smsDealEntityMapper;
    @Autowired
    private SmsExecutor smsExecutor;


    public static ConcurrentMap<String, TaskHandler> taskHandlerRepository = Maps.newConcurrentMap();

    private static final ExecutorService executorService = TraceExecutors
            .getTraceExecutorService(new ThreadPoolExecutor(10, 100, 0L, TimeUnit.MILLISECONDS,
                    new LinkedBlockingDeque<>(500), new ThreadPoolExecutor.CallerRunsPolicy()));

    public void init() {
        taskHandlerRepository.put(TaskConstant.TURN_APPLY_ECONTRACT_HANDLER, (TaskHandler) SpringBeanUtil.getBean(TaskConstant.TURN_APPLY_ECONTRACT_HANDLER));
        taskHandlerRepository.put(TaskConstant.COMMIT_APPLY_ECONTRACT_HANDLER, (TaskHandler) SpringBeanUtil.getBean(TaskConstant.COMMIT_APPLY_ECONTRACT_HANDLER));
        taskHandlerRepository.put(TaskConstant.TURN_CREATE_PDF_HANDLER, (TaskHandler) SpringBeanUtil.getBean(TaskConstant.TURN_CREATE_PDF_HANDLER));
        taskHandlerRepository.put(TaskConstant.TURN_CREATE_PDF_BATCH_HANDLER, (TaskHandler) SpringBeanUtil.getBean(TaskConstant.TURN_CREATE_PDF_BATCH_HANDLER));
        taskHandlerRepository.put(TaskConstant.COMMIT_CREATE_PDF_HANDLER, (TaskHandler) SpringBeanUtil.getBean(TaskConstant.COMMIT_CREATE_PDF_HANDLER));
        taskHandlerRepository.put(TaskConstant.COMMIT_CREATE_PDF_BATCH_HANDLER, (TaskHandler) SpringBeanUtil.getBean(TaskConstant.COMMIT_CREATE_PDF_BATCH_HANDLER));
        taskHandlerRepository.put(TaskConstant.TURN_UPLOAD_PDF_HANDLER, (TaskHandler) SpringBeanUtil.getBean(TaskConstant.TURN_UPLOAD_PDF_HANDLER));
        taskHandlerRepository.put(TaskConstant.TURN_UPLOAD_PDF_BATCH_HANDLER, (TaskHandler) SpringBeanUtil.getBean(TaskConstant.TURN_UPLOAD_PDF_BATCH_HANDLER));
        taskHandlerRepository.put(TaskConstant.COMMIT_UPLOAD_PDF_HANDLER, (TaskHandler) SpringBeanUtil.getBean(TaskConstant.COMMIT_UPLOAD_PDF_HANDLER));
        taskHandlerRepository.put(TaskConstant.COMMIT_UPLOAD_PDF_BATCH_HANDLER, (TaskHandler) SpringBeanUtil.getBean(TaskConstant.COMMIT_UPLOAD_PDF_BATCH_HANDLER));
        taskHandlerRepository.put(TaskConstant.TURN_WAIT_CALL_BACK_HANDLER, (TaskHandler) SpringBeanUtil.getBean(TaskConstant.TURN_WAIT_CALL_BACK_HANDLER));
        taskHandlerRepository.put(TaskConstant.COMMIT_WAIT_CALL_BACK_HANDLER, (TaskHandler) SpringBeanUtil.getBean(TaskConstant.COMMIT_WAIT_CALL_BACK_HANDLER));
        taskHandlerRepository.put(TaskConstant.TURN_CA_CERTIFY_HANDLER, (TaskHandler) SpringBeanUtil.getBean(TaskConstant.TURN_CA_CERTIFY_HANDLER));
        taskHandlerRepository.put(TaskConstant.COMMIT_CA_CERTIFY_HANDLER, (TaskHandler) SpringBeanUtil.getBean(TaskConstant.COMMIT_CA_CERTIFY_HANDLER));
        taskHandlerRepository.put(TaskConstant.TURN_SMS_HANDLER, (TaskHandler) SpringBeanUtil.getBean(TaskConstant.TURN_SMS_HANDLER));
        taskHandlerRepository.put(TaskConstant.COMMIT_SMS_HANDLER, (TaskHandler) SpringBeanUtil.getBean(TaskConstant.COMMIT_SMS_HANDLER));
        taskHandlerRepository.put(TaskConstant.TURN_REALNAME_AUTH_HANDLER, (TaskHandler) SpringBeanUtil.getBean(TaskConstant.TURN_REALNAME_AUTH_HANDLER));
        taskHandlerRepository.put(TaskConstant.COMMIT_REALNAME_AUTH_HANDLER, (TaskHandler) SpringBeanUtil.getBean(TaskConstant.COMMIT_REALNAME_AUTH_HANDLER));
        taskHandlerRepository.put(TaskConstant.TURN_CONFIRM_ESTAMP_HANDLER, (TaskHandler) SpringBeanUtil.getBean(TaskConstant.TURN_CONFIRM_ESTAMP_HANDLER));
        taskHandlerRepository.put(TaskConstant.COMMIT_CONFIRM_ESTAMP_HANDLER, (TaskHandler) SpringBeanUtil.getBean(TaskConstant.COMMIT_CONFIRM_ESTAMP_HANDLER));
        taskHandlerRepository.put(TaskConstant.TURN_ESTAMP_HANDLER, (TaskHandler) SpringBeanUtil.getBean(TaskConstant.TURN_ESTAMP_HANDLER));
        taskHandlerRepository.put(TaskConstant.TURN_ESTAMP_BATCH_HANDLER, (TaskHandler) SpringBeanUtil.getBean(TaskConstant.TURN_ESTAMP_BATCH_HANDLER));
        taskHandlerRepository.put(TaskConstant.COMMIT_ESTAMP_HANDLER, (TaskHandler) SpringBeanUtil.getBean(TaskConstant.COMMIT_ESTAMP_HANDLER));
        taskHandlerRepository.put(TaskConstant.COMMIT_ESTAMP_BATCH_HANDLER, (TaskHandler) SpringBeanUtil.getBean(TaskConstant.COMMIT_ESTAMP_BATCH_HANDLER));
        taskHandlerRepository.put(TaskConstant.TURN_FILING_CONTRACT_HANDLER, (TaskHandler) SpringBeanUtil.getBean(TaskConstant.TURN_FILING_CONTRACT_HANDLER));
        taskHandlerRepository.put(TaskConstant.TURN_FILING_CONTRACT_BATCH_HANDLER, (TaskHandler) SpringBeanUtil.getBean(TaskConstant.TURN_FILING_CONTRACT_BATCH_HANDLER));
        taskHandlerRepository.put(TaskConstant.COMMIT_FILING_CONTRACT_HANDLER, (TaskHandler) SpringBeanUtil.getBean(TaskConstant.COMMIT_FILING_CONTRACT_HANDLER));
        taskHandlerRepository.put(TaskConstant.COMMIT_FILING_CONTRACT_BATCH_HANDLER, (TaskHandler) SpringBeanUtil.getBean(TaskConstant.COMMIT_FILING_CONTRACT_BATCH_HANDLER));
        taskHandlerRepository.put(TaskConstant.TURN_FINISH_HANDLER, (TaskHandler) SpringBeanUtil.getBean(TaskConstant.TURN_FINISH_HANDLER));
        taskHandlerRepository.put(TaskConstant.COMMIT_FINISH_HANDLER, (TaskHandler) SpringBeanUtil.getBean(TaskConstant.COMMIT_FINISH_HANDLER));
    }

    @Override
    public void applyCheck(EcontractContext econtractContext, EcontractBo econtractBo) {

    }

    @Transactional
    public void applyEcontractByTemplateDBProcessWithRecordKey(EcontractContext context, String recordKey){
        //1.创建操作流水
        EcontractRecordEntity econtractRecordEntity = new EcontractRecordEntity();
        econtractRecordEntity.setRecordKey(recordKey);
        econtractRecordEntity.setEcontractUserId(context.getEcontractUserEntity().getId());
        econtractRecordEntity.setEcontractId(context.getEcontractEntity().getId());
        econtractRecordEntity.setEcontractType(context.getEcontractEntity().getEcontractType());
        econtractRecordEntity.setEcontractState(EcontractRecordConstant.INIT);
        econtractRecordEntity.setEcontractStage(TaskConstant.APPLY_ECONTRACT);
        econtractRecordEntity.setValid(EcontractRecordConstant.valid);
        econtractRecordEntity.setUtime(TimeUtils.getUTime());
        econtractRecordEntity.setCtime(TimeUtils.getCTime());
        econtractRecordEntity.setVersion(0);
        econtractRecordEntity.setRecordBizKey(context.getRecordBizKey());
        econtractRecordEntity.setRecordBatchId(StringUtils.isNotEmpty(context.getRecordBatchId())?Integer.parseInt(context.getRecordBatchId()):0);
        econtractRecordEntity.setEcontractRecordContext(JSON.toJSONString(context));
        econtractRecordEntity.setUpstreamStatus(UpstreamStatusEnum.INIT.getCode());
        econtractRecordService.insertSelective(econtractRecordEntity);
        //后置处理冷数据列表，冷数据完全由record控制(保存后只处理结果)
        EcontractContext storeContext = JSON.parseObject(econtractRecordEntity.getEcontractRecordContext(), EcontractContext.class);
        context.setColdDataList(storeContext.getColdDataList());
        LOGGER.info("1.applyEcontractByTemplate 创建操作流水 : " + recordKey);
        //2.操作流水设置入上下文
        EcontractRecordEntity contextRecordEntity = new EcontractRecordEntity();
        contextRecordEntity.setRecordKey(econtractRecordEntity.getRecordKey());
        contextRecordEntity.setId(econtractRecordEntity.getId());
        context.setEcontractRecordEntity(contextRecordEntity);
        context.setCurrentTaskNode(initTaskNodeBoList(context));
        econtractRecordEntity.setEcontractRecordContext(JSON.toJSONString(context));
        LOGGER.info("2.applyEcontractByTemplate 操作流水设置入上下文 : " + recordKey);
        //3.创建父任务
        int parentTaskId = createTask(context, 0, TaskConstant.PARENT_TASK);
        LOGGER.info("3.applyEcontractByTemplate 创建父任务 : " + recordKey);
        //4.创建子任务
        context.getTaskNodeBoList().stream().forEach(taskNodeBo -> {
            taskNodeBo.setTaskId(createTask(context, parentTaskId, taskNodeBo.getTaskName()));
        });
        context.setTaskNodeBoList(null);
        econtractRecordService.updateByPrimaryKeySelective(econtractRecordEntity);
        LOGGER.info("4.applyEcontractByTemplate 创建子任务 : " + recordKey);
        //5.提交任务
        context.setContextState(ContextConstant.CONTEXT_TO_EXECUTE);
        TaskContext taskContext = new TaskContext();
        taskContext.setState(TaskConstant.TASK_SUCCESS);
        taskContext.setEcontractStage(TaskConstant.APPLY_ECONTRACT);
        context.setTaskContext(taskContext);
        //6.判断是否大文本
        String contextStr = JSON.toJSONString(context);
        LOGGER.info("CommonProcessor#applyEcontractByTemplateDBProcessWithRecordKey, contextStrLength: {}", contextStr.getBytes().length);
        if (context.getUseMafka() && contextStr.getBytes().length > MccConfig.MESSAGE_SIZE){
            context.setContextId(messageProducerHandler.getEntityId(contextStr));
        }
    }

    @Override
    public String applyEcontractByTemplate(EcontractContext context) {
        CommonProcessor commonProcessor = (CommonProcessor) AopContext.currentProxy();
        boolean isMafka = taskManager.mafkaMigrateSwitch();
        context.setUseMafka(isMafka);
        LOGGER.info("[MCC开关清理]:已删除key=is_async_apply_econtract，value=true");
        // 提前获取recordKey
        String[] recordKeyArray = context.getEcontractEntity().getEcontractType().split("_");
        String recordKey = DealVersionUtils.getRecordKeyDealVersion(recordKeyArray[recordKeyArray.length - 1]);
        // 异步提交后续流程
        executorService.execute(() -> {
            commonProcessor.applyEcontractByTemplateDBProcessWithRecordKey(context, recordKey);
            TaskMsg taskMsg = handleMessage(context, isMafka);
            taskManager.commitTask(taskMsg, isMafka);
            LOGGER.info("5.applyEcontractByTemplate 提交任务 : " + recordKey);
        });
        return recordKey;
    }

    /**
     * 初始化任务节点
     * 对于batch节点这次初始化并没有细化
     * 只是生成了一级任务节点与每个Stage对应的二级任务节点
     * 后续在Turn处理过程中生产Stage对应的多个Flow任务节点
     *
     * 二级节点为流程进度节点
     */
    private TaskNodeBo initTaskNodeBoList(EcontractContext context) {
        //预加载配置ProcedureTemplate信息
        EcontractEntity econtractEntity = econtractService.selectByPrimaryKey(context.getEcontractEntity().getId());
        context.getEcontractEntity().setProcedureTemplate(econtractEntity.getProcedureTemplate());
        //数据解析，解析成链表信息
        TaskNodeBo taskNodeBo = resolverService.resolveEcontract(context);
        //重置ProcedureTemplate信息为null，防止context信息爆掉
        context.getEcontractEntity().setProcedureTemplate(null);
        return taskNodeBo;
    }


    @Override
    public void controlTemplate(EcontractContext context, String callBcakType) {
        EcontractRecordEntity oldRecord = econtractRecordService.selectByPrimaryKey(context.getEcontractRecordEntity().getId());
        oldRecord.setEcontractRecordContext("");
        context.setEcontractRecordEntity(oldRecord);
        TaskNodeBo currentTaskNode = context.getCurrentTaskNode();
        boolean isBatch = context.getFlowList() != null && CollectionUtils.size(context.getFlowList()) > 1;
        LOGGER.info("controlTemplate contextState = {}, taskType = {}", context.getContextState(), currentTaskNode.getTaskType());

        //处理并发导致的取消后流程问题
        if (oldRecord == null || EcontractRecordConstant.FAIL.equals(oldRecord.getEcontractState())){
            LOGGER.info("controlTemplate record state is state_fail, return !");
            return;
        }

        String lockKey = context.getEcontractRecordEntity().getRecordKey() + "|" + currentTaskNode.getTaskType() + "|" + context.getContextState();
        //加锁失败
        try {
            if (!tairLockHandler.tryLock(lockKey)) {
                boolean isMafka = taskManager.mafkaMigrateSwitch();
                TaskMsg taskMsg = messageProducerHandler.handler(context,isMafka);
                taskManager.commitTask(taskMsg,isMafka);
                return;
            }

            //处理失败
            if (TaskConstant.TASK_FAIL.equals(context.getTaskContext().getState())) {
                LOGGER.info("fail to execute task , recordKey is {} ",
                             context.getEcontractRecordEntity().getRecordKey());
                failTask(context);
                boolean isMafka = taskManager.mafkaMigrateSwitch();
                TaskMsg taskMsg = handleMessage(context,isMafka);
                taskManager.submitTask(taskMsg,isMafka);
            } else if (ContextConstant.CONTEXT_TO_EXECUTE.equals(context.getContextState())
                       && TaskConstant.APPLY_ECONTRACT.equals(currentTaskNode.getTaskType())) {
                TaskHandler taskHandler = taskHandlerRepository.get(TaskConstant.TURN_APPLY_ECONTRACT_HANDLER);
                taskHandler.handleTaskWithNoTransactionalMessage(context);
            } else if (ContextConstant.CONTEXT_FINISH.equals(context.getContextState())
                       && TaskConstant.APPLY_ECONTRACT.equals(currentTaskNode.getTaskType())) {
                TaskHandler taskHandler = taskHandlerRepository.get(TaskConstant.COMMIT_APPLY_ECONTRACT_HANDLER);
                taskHandler.handleTaskWithNoTransactionalMessage(context);
            } else if (ContextConstant.CONTEXT_TO_EXECUTE.equals(context.getContextState())
                       && TaskConstant.CREATE_PDF.equals(currentTaskNode.getTaskType())) {
                if (isBatch) {
                    taskHandlerRepository.get(TaskConstant.TURN_CREATE_PDF_BATCH_HANDLER).handleTaskWithNoTransactionalMessage(context);
                } else {
                    TaskHandler taskHandler = taskHandlerRepository.get(TaskConstant.TURN_CREATE_PDF_HANDLER);
                    taskHandler.handleTaskWithNoTransactionalMessage(context);
                }
            } else if (ContextConstant.CONTEXT_FINISH.equals(context.getContextState())
                       && TaskConstant.CREATE_PDF.equals(currentTaskNode.getTaskType())) {
                if (isBatch) {
                    taskHandlerRepository.get(TaskConstant.COMMIT_CREATE_PDF_BATCH_HANDLER).handleTaskWithNoTransactionalMessage(context);
                } else {
                    TaskHandler taskHandler = taskHandlerRepository.get(TaskConstant.COMMIT_CREATE_PDF_HANDLER);
                    taskHandler.handleTaskWithNoTransactionalMessage(context);
                }
            } else if (ContextConstant.CONTEXT_TO_EXECUTE.equals(context.getContextState())
                       && TaskConstant.UPLOAD_PDF.equals(currentTaskNode.getTaskType())) {
                if (isBatch) {
                    taskHandlerRepository.get(TaskConstant.TURN_UPLOAD_PDF_BATCH_HANDLER).handleTaskWithNoTransactionalMessage(context);
                } else {
                    TaskHandler taskHandler = taskHandlerRepository.get(TaskConstant.TURN_UPLOAD_PDF_HANDLER);
                    taskHandler.handleTaskWithNoTransactionalMessage(context);
                }
            } else if (ContextConstant.CONTEXT_CALLBACK.equals(context.getContextState())
                       && TaskConstant.UPLOAD_PDF.equals(currentTaskNode.getTaskType())) {
                if (isBatch) {
                    taskHandlerRepository.get(TaskConstant.COMMIT_UPLOAD_PDF_BATCH_HANDLER).handleTaskWithNoTransactionalMessage(context);
                } else {
                    TaskHandler taskHandler = taskHandlerRepository.get(TaskConstant.COMMIT_UPLOAD_PDF_HANDLER);
                    taskHandler.handleTaskWithNoTransactionalMessage(context);
                }
            } else if (ContextConstant.CONTEXT_TO_EXECUTE.equals(context.getContextState())
                       && TaskConstant.CA_CERTIFY_TASK.equals(currentTaskNode.getTaskType())) {
                TaskHandler taskHandler = taskHandlerRepository.get(TaskConstant.TURN_CA_CERTIFY_HANDLER);
                taskHandler.handleTaskWithNoTransactionalMessage(context);
            } else if (ContextConstant.CONTEXT_FINISH.equals(context.getContextState())
                       && TaskConstant.CA_CERTIFY_TASK.equals(currentTaskNode.getTaskType())) {
                TaskHandler taskHandler = taskHandlerRepository.get(TaskConstant.COMMIT_CA_CERTIFY_HANDLER);
                taskHandler.handleTaskWithNoTransactionalMessage(context);
            } else if (ContextConstant.CONTEXT_TO_EXECUTE.equals(context.getContextState())
                       && TaskConstant.SMS_SIGNER_TASK.equals(currentTaskNode.getTaskType())) {
                TaskHandler taskHandler = taskHandlerRepository.get(TaskConstant.TURN_SMS_HANDLER);
                taskHandler.handleTaskWithNoTransactionalMessage(context);
            } else if (ContextConstant.CONTEXT_FINISH.equals(context.getContextState())
                       && TaskConstant.SMS_SIGNER_TASK.equals(currentTaskNode.getTaskType())) {
                TaskHandler taskHandler = taskHandlerRepository.get(TaskConstant.COMMIT_SMS_HANDLER);
                taskHandler.handleTaskWithNoTransactionalMessage(context);
            } else if (ContextConstant.CONTEXT_TO_EXECUTE.equals(context.getContextState())
                       && TaskConstant.REAL_NAME_AUTH_TASK.equals(currentTaskNode.getTaskType())) {
                TaskHandler taskHandler = taskHandlerRepository.get(TaskConstant.TURN_REALNAME_AUTH_HANDLER);
                taskHandler.handleTaskWithNoTransactionalMessage(context);
            } else if (ContextConstant.CONTEXT_CALLBACK.equals(context.getContextState())
                       && TaskConstant.REAL_NAME_AUTH_TASK.equals(currentTaskNode.getTaskType())) {
                TaskHandler taskHandler = taskHandlerRepository.get(TaskConstant.COMMIT_REALNAME_AUTH_HANDLER);
                taskHandler.handleTaskWithNoTransactionalMessage(context);
            } else if (ContextConstant.CONTEXT_TO_EXECUTE.equals(context.getContextState())
                       && TaskConstant.CONFIRM_STAMP_TASK.equals(currentTaskNode.getTaskType())) {
                TaskHandler taskHandler = taskHandlerRepository.get(TaskConstant.TURN_CONFIRM_ESTAMP_HANDLER);
                taskHandler.handleTaskWithNoTransactionalMessage(context);
            } else if (ContextConstant.CONTEXT_CALLBACK.equals(context.getContextState())
                       && TaskConstant.CONFIRM_STAMP_TASK.equals(currentTaskNode.getTaskType())) {
                TaskHandler taskHandler = taskHandlerRepository.get(TaskConstant.COMMIT_CONFIRM_ESTAMP_HANDLER);
                taskHandler.handleTaskWithNoTransactionalMessage(context);
            } else if (ContextConstant.CONTEXT_TO_EXECUTE.equals(context.getContextState())
                       && TaskConstant.ECONTRACT_STAMP_TASK.equals(currentTaskNode.getTaskType())) {
                if (isBatch) {
                    taskHandlerRepository.get(TaskConstant.TURN_ESTAMP_BATCH_HANDLER).handleTaskWithNoTransactionalMessage(context);
                } else {
                    TaskHandler taskHandler = taskHandlerRepository.get(TaskConstant.TURN_ESTAMP_HANDLER);
                    taskHandler.handleTaskWithNoTransactionalMessage(context);
                }
            } else if (ContextConstant.CONTEXT_CALLBACK.equals(context.getContextState())
                       && TaskConstant.ECONTRACT_STAMP_TASK.equals(currentTaskNode.getTaskType())) {
                if (isBatch) {
                    taskHandlerRepository.get(TaskConstant.COMMIT_ESTAMP_BATCH_HANDLER).handleTaskWithNoTransactionalMessage(context);
                } else {
                    TaskHandler taskHandler = taskHandlerRepository.get(TaskConstant.COMMIT_ESTAMP_HANDLER);
                    taskHandler.handleTaskWithNoTransactionalMessage(context);
                }
            } else if (ContextConstant.CONTEXT_TO_EXECUTE.equals(context.getContextState())
                       && TaskConstant.ECONTRACT_FILING.equals(currentTaskNode.getTaskType())) {
                if (isBatch) {
                    taskHandlerRepository.get(TaskConstant.TURN_FILING_CONTRACT_BATCH_HANDLER).handleTaskWithNoTransactionalMessage(context);
                } else {
                    TaskHandler taskHandler = taskHandlerRepository.get(TaskConstant.TURN_FILING_CONTRACT_HANDLER);
                    taskHandler.handleTaskWithNoTransactionalMessage(context);
                }
            } else if (ContextConstant.CONTEXT_FINISH.equals(context.getContextState())
                       && TaskConstant.ECONTRACT_FILING.equals(currentTaskNode.getTaskType())) {
                if (isBatch) {
                    taskHandlerRepository.get(TaskConstant.COMMIT_FILING_CONTRACT_BATCH_HANDLER).handleTaskWithNoTransactionalMessage(context);
                } else {
                    TaskHandler taskHandler = taskHandlerRepository.get(TaskConstant.COMMIT_FILING_CONTRACT_HANDLER);
                    taskHandler.handleTaskWithNoTransactionalMessage(context);
                }
            } else if (ContextConstant.CONTEXT_TO_EXECUTE.equals(context.getContextState())
                       && TaskConstant.ECONTRACT_FINISH.equals(currentTaskNode.getTaskType())) {
                TaskHandler taskHandler = taskHandlerRepository.get(TaskConstant.TURN_FINISH_HANDLER);
                taskHandler.handleTaskWithNoTransactionalMessage(context);
            } else if (ContextConstant.CONTEXT_FINISH.equals(context.getContextState())
                       && TaskConstant.ECONTRACT_FINISH.equals(currentTaskNode.getTaskType())) {
                TaskHandler taskHandler = taskHandlerRepository.get(TaskConstant.COMMIT_FINISH_HANDLER);
                taskHandler.handleTaskWithNoTransactionalMessage(context);
            } else if (ContextConstant.CONTEXT_TO_EXECUTE.equals(context.getContextState())
                       && TaskConstant.WAIT_CALL_BACK_TASK.equals(currentTaskNode.getTaskType())) {
                TaskHandler taskHandler = taskHandlerRepository.get(TaskConstant.TURN_WAIT_CALL_BACK_HANDLER);
                taskHandler.handleTaskWithNoTransactionalMessage(context);
            } else if (ContextConstant.CONTEXT_CALLBACK.equals(context.getContextState())
                       && TaskConstant.WAIT_CALL_BACK_TASK.equals(currentTaskNode.getTaskType())) {
                TaskHandler taskHandler = taskHandlerRepository.get(TaskConstant.COMMIT_WAIT_CALL_BACK_HANDLER);
                taskHandler.handleTaskWithNoTransactionalMessage(context);
            }
        } finally {
            tairLockHandler.unLock(lockKey);
        }
    }


    @Override
    public void callBackCommitTemplateStage(EcontractContext context, String callType, Map<String, String> paramMap) {
        LOGGER.info("CommonController callBackCommitTemplateStage recordKey is:{},callType is:{},param is :{}", context.getEcontractRecordEntity().getRecordKey(), callType, JSON.toJSONString(paramMap));
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(context.getEcontractRecordEntity().getRecordKey());
        TaskNodeBo callBackTaskNode = context.getCurrentTaskNode().getCallBackTask();
        TaskNodeBo currentTaskNode = context.getCurrentTaskNode().getCallBackTask();
        context.setExecuteTaskId(getExecuteTaskIdByTranscationId(paramMap.get(CallbackConstant.ESTAMP_TRANSCATION_ID)));
        if (EcontractRecordConstant.WAIT_CALL_BACK.equals(recordEntity.getEcontractState())
                && callBackTaskNode.getTaskName().equals(callType)) {
            replaceEcontractBo(context, paramMap);
            context.setContextState(ContextConstant.CONTEXT_CALLBACK);
            if (TaskConstant.WAIT_BIZ_CALL_BACK.equals(recordEntity.getEcontractStage())) {
                LOGGER.info("biz call back : {}", recordEntity.getRecordKey());
                TaskContext taskContext = new TaskContext();
                taskContext.setState(TaskConstant.TASK_SUCCESS);
                context.setTaskContext(taskContext);
                controlTemplate(context, CallbackConstant.TYPE_GATE_WAY);
            } else if (TaskConstant.UPLOAD_PDF.equals(recordEntity.getEcontractStage())) {
                //上传签章平台成功 1000是成功，2002是重复上传,现在改为2002重复上传改为2004
                TaskContext taskContext = new TaskContext();
                taskContext.setEcontractStage(TaskConstant.UPLOAD_PDF);
                context.setTaskContext(taskContext);
                if ("1000".equals(paramMap.get(CallbackConstant.UPLOAD_RESULT_CODE))
                        || "2004".equals(paramMap.get(CallbackConstant.UPLOAD_RESULT_CODE))) {
                    taskContext.setState(TaskConstant.TASK_SUCCESS);
                    controlTemplate(context, CallbackConstant.TYPE_GATE_WAY);
                } else {
                    LOGGER.error("fail to call back upload pdf , recordKey is:{},callType is:{},param is :{} ",
                            context.getEcontractRecordEntity().getRecordKey(), callType, JSON.toJSONString(paramMap));
                    String msg = JSON.toJSONString(paramMap);
                    taskContext.setState(TaskConstant.TASK_FAIL);
                    taskContext.setFailMessage(msg);
                    taskContext.setErrorCode(EcontractException.ESTAMP_UPLOAD_ERROR);
                    dropTemplateByCallBack(context, msg);
                }
            } else if (TaskConstant.REAL_NAME_AUTH_A.equals(recordEntity.getEcontractStage())
                    || TaskConstant.REAL_NAME_AUTH_B.equals(recordEntity.getEcontractStage())
                    || TaskConstant.REAL_NAME_AUTH_C.equals(recordEntity.getEcontractStage())
                    || TaskConstant.REAL_NAME_AUTH_D.equals(recordEntity.getEcontractStage())
                    || TaskConstant.REAL_NAME_AUTH_E.equals(recordEntity.getEcontractStage())
                    || TaskConstant.REAL_NAME_AUTH_F.equals(recordEntity.getEcontractStage())
                    || TaskConstant.REAL_NAME_AUTH_G.equals(recordEntity.getEcontractStage())
                    || TaskConstant.REAL_NAME_AUTH_H.equals(recordEntity.getEcontractStage())) {
                TaskContext taskContext = new TaskContext();
                taskContext.setEcontractStage(currentTaskNode.getTaskName());
                context.setTaskContext(taskContext);
                if ("true".equals(paramMap.get(CallbackConstant.REAL_NAME_AUTH_RESULT))) {
                    //创建一个成功的回调任务
                    taskContext.setExecutorResult(paramMap);
                    taskContext.setState(TaskConstant.TASK_SUCCESS);
                    controlTemplate(context, CallbackConstant.TYPE_GATE_WAY);
                } else {
                    String msg = RealNameAuthFailCode.getByKey(Integer.parseInt(paramMap.get(CallbackConstant.REAL_NAME_AUTH_FAIL_CODE)));
                    LOGGER.error("fail to realNameAuth pdf , recordKey is:{},msg is :{} ",
                            context.getEcontractRecordEntity().getRecordKey(), msg);
                    taskContext.setState(TaskConstant.TASK_FAIL);
                    taskContext.setFailMessage(msg);
                    taskContext.setErrorCode(EcontractException.REAL_NAME_ERROR);
                    dropTemplateByCallBack(context, msg);
                }
            } else if (TaskConstant.ECONTRACT_STAMP_A.equals(recordEntity.getEcontractStage())
                    || TaskConstant.ECONTRACT_STAMP_B.equals(recordEntity.getEcontractStage())
                    || TaskConstant.ECONTRACT_STAMP_C.equals(recordEntity.getEcontractStage())
                    || TaskConstant.ECONTRACT_STAMP_D.equals(recordEntity.getEcontractStage())
                    || TaskConstant.ECONTRACT_STAMP_E.equals(recordEntity.getEcontractStage())
                    || TaskConstant.ECONTRACT_STAMP_F.equals(recordEntity.getEcontractStage())
                    || TaskConstant.ECONTRACT_STAMP_G.equals(recordEntity.getEcontractStage())
                    || TaskConstant.ECONTRACT_STAMP_H.equals(recordEntity.getEcontractStage())) {
                //签章成功
                TaskContext taskContext = new TaskContext();
                taskContext.setEcontractStage(recordEntity.getEcontractStage());
                context.setTaskContext(taskContext);
                if ("1000".equals(paramMap.get(CallbackConstant.ESTAMP_RESULT_CODE))) {
                    Thread thread = new Thread(new Runnable() {
                        @Override
                        public void run() {
                            taskContext.setExecutorResult(paramMap);
                            taskContext.setState(TaskConstant.TASK_SUCCESS);
                            EcontractTaskEntity taskEntity = econtractTaskService.selectByPrimaryKey(context.getExecuteTaskId());
                            String stampUrl = UrlConvetUtil.changeEstampUrl(paramMap.get(CallbackConstant.ESTAMP_DOWNLOAD_URL));
                            casRecordSaveUrl(recordEntity, stampUrl, taskEntity.getTaskType());
                            controlTemplate(context, CallbackConstant.TYPE_GATE_WAY);
                        }
                    });
                    thread.start();
                } else {
                    String msg = paramMap.get(CallbackConstant.ESTAMP_RESULT_MSG);
                    LOGGER.error("fail to call estamp pdf , recordKey is:{},error is :{} ",
                            context.getEcontractRecordEntity().getRecordKey(), msg);
                    taskContext.setErrorCode(EcontractException.ESTAMP_UPLOAD_ERROR);
                    taskContext.setState(TaskConstant.TASK_FAIL);
                    taskContext.setFailMessage(msg);
                    dropTemplateByCallBack(context, msg);
                }
            }
        }
    }

    private void casRecordSaveUrl(EcontractRecordEntity recordEntity, String saveUrl, String executeTaskType) {
        int updateCount = 0;
        for (int retryTime = MAX_CAS_RETRY_TIMES; retryTime > 0; retryTime--) {
            if (updateCount > 0) {
                return;
            }

            recordEntity = econtractRecordService.queryRecordByRecordKey(recordEntity.getRecordKey());
            recordEntity.setSaveUrl(urlFormat(recordEntity.getSaveUrl(), saveUrl, executeTaskType));
            LOGGER.info("update changeUrl, recordkey is : {} , recordVersion is :{} , change URL is {}",recordEntity.getRecordKey(),recordEntity.getVersion(),recordEntity.getSaveUrl());
            updateCount += econtractRecordService.updateOptimisticLock(recordEntity);
        }
    }

    private String urlFormat(String storeUrl, String toUrl, String taskType) {
        if (StringUtils.isEmpty(storeUrl) || storeUrl.endsWith(".pdf")) {
            return toUrl;
        }

        JSONObject jo = JSON.parseObject(storeUrl);
        jo.put(taskType, toUrl);
        return jo.toJSONString();
    }

    private String urlFormat(String storeUrl, String toUrl, Integer executeTaskId) {
        if (StringUtils.isEmpty(storeUrl) || storeUrl.endsWith(".pdf")) {
            return toUrl;
        }

        JSONObject jo = JSON.parseObject(storeUrl);
        EcontractTaskEntity taskEntity = econtractTaskService.selectByPrimaryKey(executeTaskId);
        jo.put(taskEntity.getTaskType(), toUrl);
        return jo.toJSONString();
    }

    private Integer getExecuteTaskIdByTranscationId(String transcationId) {
        if (StringUtils.isEmpty(transcationId)) {
            return 0;
        }

        String[] str = StringUtils.split(transcationId, "_");
        return Integer.parseInt(str[1]);
    }

    /**
     * 1.流程流转   下发短信---->实名认证
     * 2.此时currentNode 指针已经指向 实名认证，但是需要找到与 下发短信绑定的task
     * 3.做 硬编码逻辑判断，吐过是 c实名认证，则对应c下发短信
     *
     * @param recordKey
     * @return
     */
    @Override
    public String queryRealNameRedirectUrl(String recordKey) {
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(recordKey);
        EcontractContext context = JSON.parseObject(recordEntity.getEcontractRecordContext(), EcontractContext.class);
        if (TaskConstant.REAL_NAME_AUTH_TASK.equals(context.getCurrentTaskNode().getTaskType())) {
            StageInfoBo stageInfoBo = ContextUtils.getStageInfoBoFromList(context, context.getCurrentTaskNode().getTaskName());
            SignerInfoBo signerInfoBo = stageInfoBo.getSignerInfoBo();
            EcontractTaskEntity taskEntity = econtractTaskService.selectByPrimaryKey(context.getCurrentTaskNode().getTaskId());
            if (TaskConstant.SIGNER_STEP_A.equals(context.getSingerStep())){
                taskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.SMS_SIGNER_A);
            } else if (TaskConstant.SIGNER_STEP_B.equals(context.getSingerStep())){
                taskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.SMS_SIGNER_B);
            } else if (TaskConstant.SIGNER_STEP_C.equals(context.getSingerStep())){
                taskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.SMS_SIGNER_C);
            } else if (TaskConstant.SIGNER_STEP_D.equals(context.getSingerStep())){
                taskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.SMS_SIGNER_D);
            } else if (TaskConstant.SIGNER_STEP_E.equals(context.getSingerStep())){
                taskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.SMS_SIGNER_E);
            } else if (TaskConstant.SIGNER_STEP_F.equals(context.getSingerStep())){
                taskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.SMS_SIGNER_F);
            } else if (TaskConstant.SIGNER_STEP_G.equals(context.getSingerStep())){
                taskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.SMS_SIGNER_G);
            } else if (TaskConstant.SIGNER_STEP_H.equals(context.getSingerStep())){
                taskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.SMS_SIGNER_H);
            }
            String realNameDealVersion = DealVersionUtils.getRealNameDealVersion(taskEntity.getId());
            return realNameAuthService.queryRealNameRedirectUrl(realNameDealVersion, signerInfoBo, taskEntity);
        } else {
            return "";
        }
    }


    @Override
    public boolean sendPhoneCap(String recordKey)
        throws com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException {
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(recordKey);
        EcontractContext context = JSON.parseObject(recordEntity.getEcontractRecordContext(), EcontractContext.class);
        EcontractTaskEntity taskEntity = econtractTaskService.selectByPrimaryKey(context.getCurrentTaskNode().getTaskId());
        try {
            if (TaskConstant.CONFIRM_STAMP_TASK.equals(context.getCurrentTaskNode().getTaskType()) && EcontractRecordConstant.WAIT_CALL_BACK.equals(recordEntity.getEcontractState())) {
                StageInfoBo stageInfoBo = ContextUtils.getStageInfoBoFromList(context, context.getCurrentTaskNode().getStageInfoBoKey());
                SignerInfoBo signerInfoBo = stageInfoBo.getSignerInfoBo();
                if (signerInfoBo == null) {
                    stageInfoBo = ContextUtils.getStageInfoBoFromList(context, toSmsKey(context.getCurrentTaskNode().getStageInfoBoKey()));
                    signerInfoBo = stageInfoBo.getSignerInfoBo();
                }
                //60秒失效校验
                String phoneCaptchaKey = DealVersionUtils.getCaptchaVersion(taskEntity.getId());
                String turlingId = tairService.getStrValue(phoneCaptchaKey);
                if (null != turlingId) {
                    LOGGER.info("fail to ReSendPhoneCap in 60 seconds , recordKey {}", recordKey);
                    com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException.PHONE_CAP_EXCEPTION.newInstance("60s内不允许重发短信");
                }

                PhoneCaptcha phoneCaptcha = null;
                String phone = ConfigUtilAdapter.getString(MccConstant.TEST_PHONE_CAP);
                if (StringUtils.isNotEmpty(phone) && (StringUtils.isNotEmpty(phone.split(",")[0]))) {
                    phone = phone.split(",")[0];
                } else {
                    phone = signerInfoBo.getPhone();
                }
                LOGGER.info("getPhoneCaptcha phone = {}, source = {}", phone, Source.WAIMAI_M_CONTRACT);
                phoneCaptcha = captchaTService.getPhoneCaptcha(phone, Source.WAIMAI_M_CONTRACT);
                tairService.setStrValue(phoneCaptchaKey, phoneCaptcha.getTurlingTestId(), 60);
                return true;
            }
        } catch (com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException e) {
            LOGGER.warn(String.format("fail to sendPhoneCap , recordKey %s , error is %s", recordKey, e.getMessage()), e);
            throw new com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException(e.getCode(), e.getMsg());
        } catch (BizssoServerException e) {
            LOGGER.warn(String.format("fail to sendPhoneCap , recordKey %s , error is %s", recordKey, e.getMsg()), e);
            throw new com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException(e.getCode(), e.getMsg());
        } catch (Exception e) {
            LOGGER.error(String.format("fail to sendPhoneCap , recordKey %s , error is %s", recordKey, e.getMessage()), e);
            throw new com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException(EcontractException.SERVER_ERROR, e.getMessage());
        }
        return false;
    }

    private String toSmsKey(String key) {
        switch (key) {
            case TaskConstant.ECONTRACT_STAMP_A:
                return TaskConstant.REAL_NAME_AUTH_A;
            case TaskConstant.CONFIRM_STAMP_A:
                return TaskConstant.REAL_NAME_AUTH_A;
            case TaskConstant.ECONTRACT_STAMP_B:
                return TaskConstant.REAL_NAME_AUTH_B;
            case TaskConstant.CONFIRM_STAMP_B:
                return TaskConstant.REAL_NAME_AUTH_B;
            case TaskConstant.ECONTRACT_STAMP_C:
                return TaskConstant.REAL_NAME_AUTH_C;
            case TaskConstant.CONFIRM_STAMP_C:
                return TaskConstant.REAL_NAME_AUTH_C;
            case TaskConstant.ECONTRACT_STAMP_D:
                return TaskConstant.REAL_NAME_AUTH_D;
            case TaskConstant.CONFIRM_STAMP_D:
                return TaskConstant.REAL_NAME_AUTH_D;
            case TaskConstant.ECONTRACT_STAMP_E:
                return TaskConstant.REAL_NAME_AUTH_E;
            case TaskConstant.CONFIRM_STAMP_E:
                return TaskConstant.REAL_NAME_AUTH_E;
            case TaskConstant.ECONTRACT_STAMP_F:
                return TaskConstant.REAL_NAME_AUTH_F;
            case TaskConstant.CONFIRM_STAMP_F:
                return TaskConstant.REAL_NAME_AUTH_F;
            case TaskConstant.ECONTRACT_STAMP_G:
                return TaskConstant.REAL_NAME_AUTH_G;
            case TaskConstant.CONFIRM_STAMP_G:
                return TaskConstant.REAL_NAME_AUTH_G;
            case TaskConstant.CONFIRM_STAMP_H:
                return TaskConstant.REAL_NAME_AUTH_H;
            default:
                return key;
        }
    }

    @Override
    public boolean verifyPhoneCaptcha(String recordKey, String code) {
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(recordKey);
        EcontractContext context = JSON.parseObject(recordEntity.getEcontractRecordContext(), EcontractContext.class);
        EcontractTaskEntity taskEntity = econtractTaskService.selectByPrimaryKey(context.getCurrentTaskNode().getTaskId());
        try {
            if (TaskConstant.CONFIRM_STAMP_TASK.equals(context.getCurrentTaskNode().getTaskType()) && EcontractRecordConstant.WAIT_CALL_BACK.equals(recordEntity.getEcontractState())) {
                StageInfoBo stageInfoBo = ContextUtils.getStageInfoBoFromList(context, context.getCurrentTaskNode().getStageInfoBoKey());
                SignerInfoBo signerInfoBo = stageInfoBo.getSignerInfoBo();
                if (signerInfoBo == null) {
                    stageInfoBo = ContextUtils.getStageInfoBoFromList(context, toSmsKey(context.getCurrentTaskNode().getStageInfoBoKey()));
                    signerInfoBo = stageInfoBo.getSignerInfoBo();
                }
                String turlingId = tairService.getStrValue(DealVersionUtils.getCaptchaVersion(taskEntity.getId()));
                return toVerify(turlingId, signerInfoBo, code);
            }
        } catch (BizssoServerException e){
            LOGGER.warn(String.format("fail to verifyPhoneCaptcha , recordKey %s , error is %s", recordKey, e.getMsg()), e);
        } catch (Exception e) {
            LOGGER.error(String.format("fail to verifyPhoneCaptcha , recordKey %s", recordKey), e);
        }
        return false;
    }


    @Override
    public void cancelSignEContract(String recordKey, String type) {
        super.cancelSignEContract(recordKey, type);
    }

    @Override
    public void confirmSignEContract(String recordKey)
        throws com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException {
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(recordKey);
        EcontractContext context = JSON.parseObject(recordEntity.getEcontractRecordContext(), EcontractContext.class);
        if (TaskConstant.CONFIRM_STAMP_TASK.equals(context.getCurrentTaskNode().getTaskType())
                && EcontractRecordConstant.WAIT_CALL_BACK.equals(recordEntity.getEcontractState())) {
            context.setContextState(ContextConstant.CONTEXT_CALLBACK);
            context.setTaskContext(new TaskContext());
            controlTemplate(context, recordKey);
        } else {
            com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException.STATUS_ERROR_EXCEPTION.newInstance("电子合同状态异常");
        }
    }

    @Override
    public List<String> doRetrySms(String recordKey) {
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(recordKey);
        if (recordEntity == null) {
            EcontractException.SMS_RETRY_NAME_EXCEPTION.newInstance("重发短信失败,请取消后重试");
        }
        //由于法大大下线，对于历史法大大的任务，提示短信已超6个月，不允许重发短信
        EcontractContext context = JSON.parseObject(recordEntity.getEcontractRecordContext(), EcontractContext.class);
        if (context == null || !EcontractStampRouteUtil.isSSQ(context.getStampKey())) {
            EcontractException.SMS_RETRY_NAME_EXCEPTION.newInstance("无法操作重发短信：发送短信已超6个月，需操作取消签约后重新提交");
        }
        //若该签约任务不发送短信给商家，则不允许重发短信
        if (context.getTaskContext() == null || context.getTaskContext().getStageInfoBo() == null || context.getTaskContext().getStageInfoBo().getSignerInfoBo() == null) {
            LOGGER.warn("重发短信时，签约信息异常，recordKey={}", recordKey);
        } else {
            SignerInfoBo signerInfoBo = context.getTaskContext().getStageInfoBo().getSignerInfoBo();
            if (Boolean.FALSE.equals(signerInfoBo.getSendSms()) && false) {
                LOGGER.info("该签约任务不发送短信给商家，不允许重发短信，recordKey={}", recordKey);
                EcontractException.SMS_RETRY_NAME_EXCEPTION.newInstance("无法操作重发短信：该签约任务不发送短信给商家");
            }
        }

        EcontractTaskEntity taskEntity = null;
        EcontractSmsDealEntity smsDealEntity = null;
        //自动跳过实名认证
        if (ConfigUtilAdapter.getBoolean(REAL_NAME_JUMP, false)) {
            if (TaskConstant.REAL_NAME_AUTH_A.equals(recordEntity.getEcontractStage())
                    || TaskConstant.REAL_NAME_AUTH_B.equals(recordEntity.getEcontractStage())
                    || TaskConstant.REAL_NAME_AUTH_C.equals(recordEntity.getEcontractStage())
                    || TaskConstant.REAL_NAME_AUTH_D.equals(recordEntity.getEcontractStage())
                    || TaskConstant.REAL_NAME_AUTH_E.equals(recordEntity.getEcontractStage())
                    || TaskConstant.REAL_NAME_AUTH_F.equals(recordEntity.getEcontractStage())
                    || TaskConstant.REAL_NAME_AUTH_G.equals(recordEntity.getEcontractStage())
                    || TaskConstant.REAL_NAME_AUTH_H.equals(recordEntity.getEcontractStage())) {
                autoRealName(recordKey);
            }
        }
        if (TaskConstant.REAL_NAME_AUTH_A.equals(recordEntity.getEcontractStage()) ||
                TaskConstant.CONFIRM_STAMP_A.equals(recordEntity.getEcontractStage())) {
            taskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.SMS_SIGNER_A);
            List<EcontractSmsDealEntity> list = smsDealEntityMapper.queryDealByTaskId(taskEntity.getId());
            if (CollectionUtils.isEmpty(list)) {
                return Lists.newArrayList();
            }
            smsDealEntity = list.get(0);
            return doRetrySmsDealEntity(smsDealEntity,recordEntity,TaskConstant.REAL_NAME_AUTH_A);

        } else if (TaskConstant.REAL_NAME_AUTH_B.equals(recordEntity.getEcontractStage()) ||
                TaskConstant.CONFIRM_STAMP_B.equals(recordEntity.getEcontractStage())) {
            taskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.SMS_SIGNER_B);
            List<EcontractSmsDealEntity> list = smsDealEntityMapper.queryDealByTaskId(taskEntity.getId());
            if (CollectionUtils.isEmpty(list)) {
                return Lists.newArrayList();
            }
            smsDealEntity = list.get(0);
            return doRetrySmsDealEntity(smsDealEntity,recordEntity,TaskConstant.REAL_NAME_AUTH_B);

        } else if (TaskConstant.REAL_NAME_AUTH_C.equals(recordEntity.getEcontractStage()) ||
                TaskConstant.CONFIRM_STAMP_C.equals(recordEntity.getEcontractStage())) {
            taskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.SMS_SIGNER_C);
            List<EcontractSmsDealEntity> list = smsDealEntityMapper.queryDealByTaskId(taskEntity.getId());
            if (CollectionUtils.isEmpty(list)) {
                return Lists.newArrayList();
            }
            smsDealEntity = list.get(0);
            return doRetrySmsDealEntity(smsDealEntity,recordEntity,TaskConstant.REAL_NAME_AUTH_C);

        } else if (TaskConstant.REAL_NAME_AUTH_D.equals(recordEntity.getEcontractStage()) ||
                TaskConstant.CONFIRM_STAMP_D.equals(recordEntity.getEcontractStage())) {
            taskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.SMS_SIGNER_D);
            List<EcontractSmsDealEntity> list = smsDealEntityMapper.queryDealByTaskId(taskEntity.getId());
            if (CollectionUtils.isEmpty(list)) {
                return Lists.newArrayList();
            }
            smsDealEntity = list.get(0);
            return doRetrySmsDealEntity(smsDealEntity,recordEntity,TaskConstant.REAL_NAME_AUTH_D);

        } else if (TaskConstant.REAL_NAME_AUTH_E.equals(recordEntity.getEcontractStage()) ||
                TaskConstant.CONFIRM_STAMP_E.equals(recordEntity.getEcontractStage())) {
            taskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.SMS_SIGNER_E);
            List<EcontractSmsDealEntity> list = smsDealEntityMapper.queryDealByTaskId(taskEntity.getId());
            if (CollectionUtils.isEmpty(list)) {
                return Lists.newArrayList();
            }
            smsDealEntity = list.get(0);
            return doRetrySmsDealEntity(smsDealEntity,recordEntity,TaskConstant.REAL_NAME_AUTH_E);
        } else if (TaskConstant.REAL_NAME_AUTH_F.equals(recordEntity.getEcontractStage()) ||
                TaskConstant.CONFIRM_STAMP_F.equals(recordEntity.getEcontractStage())) {
            taskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.SMS_SIGNER_F);
            List<EcontractSmsDealEntity> list = smsDealEntityMapper.queryDealByTaskId(taskEntity.getId());
            if (CollectionUtils.isEmpty(list)) {
                return Lists.newArrayList();
            }
            smsDealEntity = list.get(0);
            return doRetrySmsDealEntity(smsDealEntity,recordEntity,TaskConstant.REAL_NAME_AUTH_F);
        } else if (TaskConstant.REAL_NAME_AUTH_G.equals(recordEntity.getEcontractStage()) ||
                TaskConstant.CONFIRM_STAMP_G.equals(recordEntity.getEcontractStage())) {
            taskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.SMS_SIGNER_G);
            List<EcontractSmsDealEntity> list = smsDealEntityMapper.queryDealByTaskId(taskEntity.getId());
            if (CollectionUtils.isEmpty(list)) {
                return Lists.newArrayList();
            }
            smsDealEntity = list.get(0);
            return doRetrySmsDealEntity(smsDealEntity,recordEntity,TaskConstant.REAL_NAME_AUTH_G);
        } else if (TaskConstant.REAL_NAME_AUTH_H.equals(recordEntity.getEcontractStage()) ||
                TaskConstant.CONFIRM_STAMP_H.equals(recordEntity.getEcontractStage())) {
            taskEntity = econtractTaskService.queryTaskByContractRecordIdAndTaskType(recordEntity.getId(), TaskConstant.SMS_SIGNER_H);
            List<EcontractSmsDealEntity> list = smsDealEntityMapper.queryDealByTaskId(taskEntity.getId());
            if (CollectionUtils.isEmpty(list)) {
                return Lists.newArrayList();
            }
            smsDealEntity = list.get(0);
            return doRetrySmsDealEntity(smsDealEntity,recordEntity,TaskConstant.REAL_NAME_AUTH_G);
        } else {
            EcontractException.SMS_RETRY_NAME_EXCEPTION.newInstance("当前状态不允许重发短信");
            return Lists.newArrayList();
        }
    }

    @Override
    public List<String> doBatchSms(String recordKey) {
        List<EcontractRecordEntity> recordEntityList = Lists.newArrayList();
        Set<String> smsResult = Sets.newHashSet();
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(recordKey);
        if (recordEntity == null) {
            EcontractException.SMS_RETRY_NAME_EXCEPTION.newInstance("发送短信失败,请取消后重试");
        }
        if (recordEntity.getRecordBatchId() != null && recordEntity.getRecordBatchId() > 0) {
            recordEntityList.addAll(econtractRecordService.queryEcontractRecordByBatchId(recordEntity.getRecordBatchId()));
        } else {
            recordEntityList.add(recordEntity);
        }
        if (CollectionUtils.isEmpty(recordEntityList)) {
            EcontractException.SMS_RETRY_NAME_EXCEPTION.newInstance("发送短信失败,请取消后重试");
        }
        recordEntityList.stream().forEach(record -> {
            boolean flag = false;
            if (recordKey.equals(record.getRecordKey())) {
                flag = true;
            }
            smsResult.addAll(sendSmsByRecord(record, flag));
        });
        return Lists.newArrayList(smsResult);
    }

    private List<String> sendSmsByRecord(EcontractRecordEntity recordEntity,boolean realSendSms) {
        LOGGER.info("sendSmsByRecord 发送短信参数 recordKey = {},realSendSms={}",recordEntity.getRecordKey(),realSendSms);
        EcontractContext context = JSON.parseObject(recordEntity.getEcontractRecordContext(), EcontractContext.class);
        if (context == null || !EcontractStampRouteUtil.isSSQ(context.getStampKey())) {
            EcontractException.SMS_RETRY_NAME_EXCEPTION.newInstance("无法操作发送短信：发送短信已超6个月，需操作取消签约后重新提交");
        }
        //自动跳过实名认证
        if (ConfigUtilAdapter.getBoolean(REAL_NAME_JUMP, false)) {
            if (TaskConstant.REAL_NAME_AUTH_A.equals(recordEntity.getEcontractStage())
                    || TaskConstant.REAL_NAME_AUTH_B.equals(recordEntity.getEcontractStage())
                    || TaskConstant.REAL_NAME_AUTH_C.equals(recordEntity.getEcontractStage())
                    || TaskConstant.REAL_NAME_AUTH_D.equals(recordEntity.getEcontractStage())
                    || TaskConstant.REAL_NAME_AUTH_E.equals(recordEntity.getEcontractStage())
                    || TaskConstant.REAL_NAME_AUTH_F.equals(recordEntity.getEcontractStage())
                    || TaskConstant.REAL_NAME_AUTH_G.equals(recordEntity.getEcontractStage())
                    || TaskConstant.REAL_NAME_AUTH_H.equals(recordEntity.getEcontractStage())) {
                autoRealName(recordEntity.getRecordKey());
            }
        }

        if (TaskConstant.CONFIRM_STAMP_A.equals(recordEntity.getEcontractStage())
                || TaskConstant.CONFIRM_STAMP_B.equals(recordEntity.getEcontractStage())
                || TaskConstant.CONFIRM_STAMP_C.equals(recordEntity.getEcontractStage())
                || TaskConstant.CONFIRM_STAMP_D.equals(recordEntity.getEcontractStage())
                || TaskConstant.CONFIRM_STAMP_E.equals(recordEntity.getEcontractStage())
                || TaskConstant.CONFIRM_STAMP_F.equals(recordEntity.getEcontractStage())
                || TaskConstant.CONFIRM_STAMP_G.equals(recordEntity.getEcontractStage())
                || TaskConstant.CONFIRM_STAMP_H.equals(recordEntity.getEcontractStage())) {
            try {
                context.setBatchSendSms(true);
                context.setRealSendSms(realSendSms);
                smsExecutor.sendMessage(context, false);
            } catch (Exception e) {
                LOGGER.error("发送短信失败, recordKey:" + context.getEcontractRecordEntity().getRecordKey(), e);
                EcontractException.SMS_RETRY_NAME_EXCEPTION.newInstance("发送短信失败");
                return Lists.newArrayList();
            }
            return context.getTaskContext().getStageInfoBo().getSignerInfoBo().getMobileList();
        } else {
            EcontractException.SMS_RETRY_NAME_EXCEPTION.newInstance("当前状态不允许发送短信");
            return Lists.newArrayList();
        }
    }

    private List<String> doRetrySmsDealEntity(EcontractSmsDealEntity smsDealEntity, EcontractRecordEntity recordEntity, String stageKeyName) {
        String retry = ConfigUtilAdapter.getString(MccConstant.SIGN_URL_RETRY_TIME, String.valueOf(10 * 60));
        if (smsDealEntity == null || TimeCompareUtils.isMoreThan(smsDealEntity.getCtime(), new Date(), Integer.valueOf(retry))) {
            EcontractContext context = JSON.parseObject(recordEntity.getEcontractRecordContext(), EcontractContext.class);
            EcontractTaskEntity newTaskEntity = econtractTaskService.selectByPrimaryKey(smsDealEntity.getTaskId());
            TaskContext taskContext = new TaskContext();
            taskContext.setTaskId(newTaskEntity.getId());
            taskContext.setStageInfoBo(ContextUtils.getStageInfoBoFromList(context, stageKeyName));
            taskContext.setExecutorType(TaskConstant.SMS_EXECUTOR);
            taskContext.setEcontractStage(stageKeyName);
            taskContext.setState(TaskConstant.TASK_RUNNIG);
            context.setTaskContext(taskContext);
            context.setContextState(ContextConstant.CONTEXT_TO_EXECUTE);
            newTaskEntity.setEcontractTaskContext(JSON.toJSONString(taskContext));
            try {
                smsExecutor.sendMessage(context,true);
                doNotifier(context, taskContext);
            } catch (Exception e) {
                LOGGER.error("重发短信失败, recordKey:" + context.getEcontractRecordEntity().getRecordKey(), e);
                EcontractException.SMS_RETRY_NAME_EXCEPTION.newInstance("发送短信失败");
                return Lists.newArrayList();
            }
//            taskManager.submitTask(context);
            return context.getTaskContext().getStageInfoBo().getSignerInfoBo().getMobileList();
        } else {
            EcontractException.SMS_RETRY_NAME_EXCEPTION.newInstance("发送短信频繁");
            return Lists.newArrayList();
        }
    }

    @Override
    public SignH5InfoBo querySignH5InoByRecordIdAndTaskId(Integer recordId, Integer taskId) {
        SignH5InfoBo signH5InfoBo = new SignH5InfoBo();
        String viewStage = null;
        EcontractRecordEntity recordEntity = econtractRecordService.selectByPrimaryKey(recordId);

        EcontractTaskEntity taskEntity = econtractTaskService.selectByPrimaryKey(taskId);

        EcontractContext context = JSON.parseObject(recordEntity.getEcontractRecordContext(), EcontractContext.class);
        //拼装tab切换数据
        StageInfoBo stageInfoBo = ContextUtils.getStageInfoBoFromList(context, TaskConstant.ECONTRACT_VIEW_CONTENT);
        signH5InfoBo = wrapViewContent(signH5InfoBo, stageInfoBo);
        //拼装外部跳转数据
        StageInfoBo externalJumpStageInfoBo = ContextUtils.getStageInfoBoFromList(context, TaskConstant.ECONTRACT_EXTERNAL_JUMP_CONTENT);
        signH5InfoBo = wrapExternalJumpContent(signH5InfoBo, externalJumpStageInfoBo);

        //拼装上游透传信息
        StageInfoBo upstageInfoBo = ContextUtils.getStageInfoBoFromList(context, TaskConstant.ECONTRACT_UP_STREAM_CONTENT);
        LOGGER.info("上游透传字段信息为: upstageInfoBo{}",JSON.toJSONString(upstageInfoBo));
        signH5InfoBo = wrapExtendDataContent(signH5InfoBo,upstageInfoBo);

        if (TaskConstant.ECONTRACT_FINISH.equals(recordEntity.getEcontractStage())
                && EcontractRecordConstant.SUCCESS.equals(recordEntity.getEcontractState())) {
            // 是否等到上游状态同步结束再返回
            if (MccConfig.isWaitForUpstreamStatusSwitch() && context.getIsWaitForUpstream()) {
                // 上游已同步成功，则返回成功
                if (recordEntity.getUpstreamStatus() == UpstreamStatusEnum.SUCCESS.getCode()) {
                    viewStage = WebViewConstant.STAGE_SUCCESS;
                }
                // 上游同步为失败，则返回失败
                if (recordEntity.getUpstreamStatus() == UpstreamStatusEnum.FAIL.getCode()) {
                    viewStage = WebViewConstant.STAGE_FAIL;
                }
                // 上游未返回，则返回进行中
                if (recordEntity.getUpstreamStatus() == UpstreamStatusEnum.INIT.getCode()) {
                    viewStage = WebViewConstant.STAGE_WAIT_ASY_EXECUTE;
                }
            } else {
                viewStage = WebViewConstant.STAGE_SUCCESS;
            }

            signH5InfoBo.setRecordKey(recordEntity.getRecordKey());

            signH5InfoBo.setViewStage(viewStage);

        } else if (TaskConstant.ECONTRACT_FINISH.equals(recordEntity.getEcontractStage())
                && EcontractRecordConstant.FAIL.equals(recordEntity.getEcontractState())) {
            if (TaskConstant.SERVER_CANCEL.equals(recordEntity.getFailMessage()) || TaskConstant.H5_CANCEL.equals(recordEntity.getFailMessage())) {
                viewStage = WebViewConstant.STAGE_CANCEL_SIGN;
            } else {
                viewStage = WebViewConstant.STAGE_FAIL;
            }
            signH5InfoBo.setRecordKey(recordEntity.getRecordKey());
            signH5InfoBo.setViewStage(viewStage);

        } else if (TaskConstant.SMS_SIGNER_A.equals(taskEntity.getTaskType())) {
            if (context.getSingerStep().charAt(0) - TaskConstant.SIGNER_STEP_A.charAt(0) > 0) {
                viewStage = WebViewConstant.STAGE_SUCCESS;
            } else if (TaskConstant.REAL_NAME_AUTH_A.equals(context.getCurrentTaskNode().getTaskName())) {
                viewStage = WebViewConstant.STAGE_WAIT_REAL_NAME;
            } else if (TaskConstant.CONFIRM_STAMP_A.equals(recordEntity.getEcontractStage())) {
                viewStage = WebViewConstant.STAGE_WAIT_SIGN;
            } else {
                viewStage = WebViewConstant.STAGE_WAIT_ASY_EXECUTE;
            }
            stageInfoBo = ContextUtils.getStageInfoBoFromList(context, TaskConstant.REAL_NAME_AUTH_A);
            signH5InfoBo.setSignerName(stageInfoBo.getSignerInfoBo().getName());
            signH5InfoBo.setRecordKey(recordEntity.getRecordKey());
            signH5InfoBo.setViewStage(viewStage);
            signH5InfoBo.setSignerPhone(stageInfoBo.getSignerInfoBo().getPhone());
            signH5InfoBo.setPageContent(stageInfoBo.getSignerInfoBo().getSignH5InfoMap());

        } else if (TaskConstant.SMS_SIGNER_B.equals(taskEntity.getTaskType())) {
            if (context.getSingerStep().charAt(0) - TaskConstant.SIGNER_STEP_B.charAt(0) > 0) {
                viewStage = WebViewConstant.STAGE_SUCCESS;
            } else if (TaskConstant.REAL_NAME_AUTH_B.equals(context.getCurrentTaskNode().getTaskName())) {
                viewStage = WebViewConstant.STAGE_WAIT_REAL_NAME;
            } else if (TaskConstant.CONFIRM_STAMP_B.equals(recordEntity.getEcontractStage())) {
                viewStage = WebViewConstant.STAGE_WAIT_SIGN;
            } else {
                viewStage = WebViewConstant.STAGE_WAIT_ASY_EXECUTE;
            }
            stageInfoBo = ContextUtils.getStageInfoBoFromList(context, TaskConstant.REAL_NAME_AUTH_B);
            signH5InfoBo.setSignerName(stageInfoBo.getSignerInfoBo().getName());
            signH5InfoBo.setRecordKey(recordEntity.getRecordKey());
            signH5InfoBo.setViewStage(viewStage);
            signH5InfoBo.setSignerPhone(stageInfoBo.getSignerInfoBo().getPhone());
            signH5InfoBo.setPageContent(stageInfoBo.getSignerInfoBo().getSignH5InfoMap());

        } else if (TaskConstant.SMS_SIGNER_C.equals(taskEntity.getTaskType())) {
            if (context.getSingerStep().charAt(0) - TaskConstant.SIGNER_STEP_C.charAt(0) > 0) {
                viewStage = WebViewConstant.STAGE_SUCCESS;
            } else if (TaskConstant.REAL_NAME_AUTH_C.equals(context.getCurrentTaskNode().getTaskName())) {
                viewStage = WebViewConstant.STAGE_WAIT_REAL_NAME;
            } else if (TaskConstant.CONFIRM_STAMP_C.equals(recordEntity.getEcontractStage())) {
                viewStage = WebViewConstant.STAGE_WAIT_SIGN;
            } else {
                viewStage = WebViewConstant.STAGE_WAIT_ASY_EXECUTE;
            }
            stageInfoBo = ContextUtils.getStageInfoBoFromList(context, TaskConstant.REAL_NAME_AUTH_C);
            signH5InfoBo.setSignerName(stageInfoBo.getSignerInfoBo().getName());
            signH5InfoBo.setRecordKey(recordEntity.getRecordKey());
            signH5InfoBo.setViewStage(viewStage);
            signH5InfoBo.setSignerPhone(stageInfoBo.getSignerInfoBo().getPhone());
            signH5InfoBo.setPageContent(stageInfoBo.getSignerInfoBo().getSignH5InfoMap());

        } else if (TaskConstant.SMS_SIGNER_D.equals(taskEntity.getTaskType())) {
            if (TaskConstant.REAL_NAME_AUTH_D.equals(context.getCurrentTaskNode().getTaskName())) {
                viewStage = WebViewConstant.STAGE_WAIT_REAL_NAME;
            } else if (TaskConstant.CONFIRM_STAMP_D.equals(recordEntity.getEcontractStage())) {
                viewStage = WebViewConstant.STAGE_WAIT_SIGN;
            } else {
                viewStage = WebViewConstant.STAGE_WAIT_ASY_EXECUTE;
            }
            stageInfoBo = ContextUtils.getStageInfoBoFromList(context, TaskConstant.REAL_NAME_AUTH_D);
            signH5InfoBo.setSignerName(stageInfoBo.getSignerInfoBo().getName());
            signH5InfoBo.setRecordKey(recordEntity.getRecordKey());
            signH5InfoBo.setViewStage(viewStage);
            signH5InfoBo.setSignerPhone(stageInfoBo.getSignerInfoBo().getPhone());
            signH5InfoBo.setPageContent(stageInfoBo.getSignerInfoBo().getSignH5InfoMap());

        } else if (TaskConstant.SMS_SIGNER_E.equals(taskEntity.getTaskType())) {
            if (TaskConstant.REAL_NAME_AUTH_E.equals(context.getCurrentTaskNode().getTaskName())) {
                viewStage = WebViewConstant.STAGE_WAIT_REAL_NAME;
            } else if (TaskConstant.CONFIRM_STAMP_E.equals(recordEntity.getEcontractStage())) {
                viewStage = WebViewConstant.STAGE_WAIT_SIGN;
            } else {
                viewStage = WebViewConstant.STAGE_WAIT_ASY_EXECUTE;
            }
            stageInfoBo = ContextUtils.getStageInfoBoFromList(context, TaskConstant.REAL_NAME_AUTH_E);
            signH5InfoBo.setSignerName(stageInfoBo.getSignerInfoBo().getName());
            signH5InfoBo.setRecordKey(recordEntity.getRecordKey());
            signH5InfoBo.setViewStage(viewStage);
            signH5InfoBo.setSignerPhone(stageInfoBo.getSignerInfoBo().getPhone());
            signH5InfoBo.setPageContent(stageInfoBo.getSignerInfoBo().getSignH5InfoMap());

        } else if (TaskConstant.SMS_SIGNER_F.equals(taskEntity.getTaskType())) {
            if (TaskConstant.REAL_NAME_AUTH_F.equals(context.getCurrentTaskNode().getTaskName())) {
                viewStage = WebViewConstant.STAGE_WAIT_REAL_NAME;
            } else if (TaskConstant.CONFIRM_STAMP_F.equals(recordEntity.getEcontractStage())) {
                viewStage = WebViewConstant.STAGE_WAIT_SIGN;
            } else {
                viewStage = WebViewConstant.STAGE_WAIT_ASY_EXECUTE;
            }
            stageInfoBo = ContextUtils.getStageInfoBoFromList(context, TaskConstant.REAL_NAME_AUTH_F);
            signH5InfoBo.setSignerName(stageInfoBo.getSignerInfoBo().getName());
            signH5InfoBo.setRecordKey(recordEntity.getRecordKey());
            signH5InfoBo.setViewStage(viewStage);
            signH5InfoBo.setSignerPhone(stageInfoBo.getSignerInfoBo().getPhone());
            signH5InfoBo.setPageContent(stageInfoBo.getSignerInfoBo().getSignH5InfoMap());

        } else if (TaskConstant.SMS_SIGNER_G.equals(taskEntity.getTaskType())) {
            if (TaskConstant.REAL_NAME_AUTH_G.equals(context.getCurrentTaskNode().getTaskName())) {
                viewStage = WebViewConstant.STAGE_WAIT_REAL_NAME;
            } else if (TaskConstant.CONFIRM_STAMP_G.equals(recordEntity.getEcontractStage())) {
                viewStage = WebViewConstant.STAGE_WAIT_SIGN;
            } else {
                viewStage = WebViewConstant.STAGE_WAIT_ASY_EXECUTE;
            }
            stageInfoBo = ContextUtils.getStageInfoBoFromList(context, TaskConstant.REAL_NAME_AUTH_G);
            signH5InfoBo.setSignerName(stageInfoBo.getSignerInfoBo().getName());
            signH5InfoBo.setRecordKey(recordEntity.getRecordKey());
            signH5InfoBo.setViewStage(viewStage);
            signH5InfoBo.setSignerPhone(stageInfoBo.getSignerInfoBo().getPhone());
            signH5InfoBo.setPageContent(stageInfoBo.getSignerInfoBo().getSignH5InfoMap());

        } else if (TaskConstant.SMS_SIGNER_H.equals(taskEntity.getTaskType())) {
            if (TaskConstant.REAL_NAME_AUTH_H.equals(context.getCurrentTaskNode().getTaskName())) {
                viewStage = WebViewConstant.STAGE_WAIT_REAL_NAME;
            } else if (TaskConstant.CONFIRM_STAMP_H.equals(recordEntity.getEcontractStage())) {
                viewStage = WebViewConstant.STAGE_WAIT_SIGN;
            } else {
                viewStage = WebViewConstant.STAGE_WAIT_ASY_EXECUTE;
            }
            stageInfoBo = ContextUtils.getStageInfoBoFromList(context, TaskConstant.REAL_NAME_AUTH_H);
            signH5InfoBo.setSignerName(stageInfoBo.getSignerInfoBo().getName());
            signH5InfoBo.setRecordKey(recordEntity.getRecordKey());
            signH5InfoBo.setViewStage(viewStage);
            signH5InfoBo.setSignerPhone(stageInfoBo.getSignerInfoBo().getPhone());
            signH5InfoBo.setPageContent(stageInfoBo.getSignerInfoBo().getSignH5InfoMap());

        }

        return signH5InfoBo;
    }

    /**
     * 拼装展示文案数据
     */
    private SignH5InfoBo wrapViewContent(SignH5InfoBo signH5InfoBo, StageInfoBo stageInfoBo) {
        LOGGER.info("signH5InfoBo = {}, stageInfoBo = {}", JSON.toJSONString(signH5InfoBo), JSON.toJSONString(stageInfoBo));
        List<String> dataTypeList = Lists.newArrayList(EcontractContentTypeEnum.PDF.getName());
        signH5InfoBo.setDataTypeList(dataTypeList);
        if (stageInfoBo == null || MapUtils.isEmpty(stageInfoBo.getViewContentMap())) {
            return signH5InfoBo;
        }

        signH5InfoBo.getDataTypeList().addAll(Lists.newArrayList(stageInfoBo.getViewContentMap().keySet()));
        return signH5InfoBo;
    }

    /**
     * 拼装外部跳转信息
     */
    private SignH5InfoBo wrapExternalJumpContent(SignH5InfoBo signH5InfoBo, StageInfoBo stageInfoBo) {
        List<String> externalJumpTypeList = Lists.newArrayList();
        signH5InfoBo.setExternalJumpTypeList(externalJumpTypeList);
        if (MapUtils.isEmpty(stageInfoBo.getViewContentMap())) {
            return signH5InfoBo;
        }

        signH5InfoBo.getExternalJumpTypeList().addAll(Lists.newArrayList(stageInfoBo.getViewContentMap().keySet()));
        return signH5InfoBo;
    }


    /**
     * 拼装上游透传的数据
     * @param signH5InfoBo
     * @param stageInfoBo
     * @return
     */
    private SignH5InfoBo wrapExtendDataContent(SignH5InfoBo signH5InfoBo, StageInfoBo stageInfoBo) {
        signH5InfoBo.setExtendDataBo(stageInfoBo.getExtendDataBo());
        return signH5InfoBo;
    }

    /**
     * 自动实名认证
     * @param recordKey
     * @return
     */
    private void autoRealName(String recordKey) {
        EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(recordKey);
        EcontractContext econtractContext = JSON.parseObject(recordEntity.getEcontractRecordContext(), EcontractContext.class);

        EcontractRealNameDealEntity realNameDealEntity = realNameAuthService.queryRealNameDealEntityByRecordId(recordEntity.getId());
        if (realNameDealEntity == null) {//模拟一次假的请求
            this.queryRealNameRedirectUrl(recordKey);
            realNameDealEntity = realNameAuthService.queryRealNameDealEntityByRecordId(recordEntity.getId());
        }
        if (realNameDealEntity == null) {
            return;
        }

        Map<String, String> paramMap = Maps.newHashMap();
        paramMap.put(CallbackConstant.REAL_NAME_AUTH_RESULT, "true");
        paramMap.put(CallbackConstant.REAL_NAME_DEAL_VERSION, realNameDealEntity.getDealVersion());
        callBackCommitTemplateStage(econtractContext, CallbackConstant.REAL_NAME_AUTH_CALL_BACK, paramMap);
    }
}

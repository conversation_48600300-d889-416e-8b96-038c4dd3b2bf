package com.sankuai.meituan.waimai.econtract.server.service.mq;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.google.common.collect.Maps;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.IMessageListener;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.meituan.sankuai.bsi.esign.common.model.dict.NotifyType;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.econtract.server.constants.EcontractAsyncTaskConstant;
import com.sankuai.meituan.waimai.econtract.server.exception.EcontractException;
import com.sankuai.meituan.waimai.econtract.server.service.SpringBeanUtil;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractMetricService;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.AsyncTaskHandler;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.asyncHandler.AsyncCaHandler;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.asyncHandler.AsyncCreatePdfHandler;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.asyncHandler.AsyncEstampHandler;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.asyncHandler.AsyncFilingContractHandler;
import com.sankuai.meituan.waimai.econtract.server.service.engine.template.handler.TaskHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2022-01-17 16:20
 * Email: <EMAIL>
 * Desc:
 */
@Service
@Slf4j
public class EsignAsyncResultListener implements IMessageListener {

    @Autowired
    private EcontractMetricService econtractMetricService;

    public static HashMap<String, AsyncTaskHandler> asyncHandlerRepository = Maps.newHashMap();

    public void init() {
        asyncHandlerRepository.put(NotifyType.APPLY_CERT.toString(),
                (AsyncTaskHandler) SpringBeanUtil.getBean(EcontractAsyncTaskConstant.ASYNC_CA_HANDLER));
        asyncHandlerRepository.put(NotifyType.CREATE_CONTRACT_ASYNC.toString(),
                (AsyncTaskHandler) SpringBeanUtil.getBean(EcontractAsyncTaskConstant.ASYNC_CREATEPDF_HANDLER));
        asyncHandlerRepository.put(NotifyType.SIGN_CONTRACT_ASYNC.toString(),
                (AsyncTaskHandler) SpringBeanUtil.getBean(EcontractAsyncTaskConstant.ASYNC_ESTAMP_HANDLER));
        asyncHandlerRepository.put(NotifyType.ARCHIVE_CONTRACT_ASYNC.toString(),
                (AsyncTaskHandler) SpringBeanUtil.getBean(EcontractAsyncTaskConstant.ASYNC_FILINGCONTRACT_HANDLER));
    }

    @Override
    public ConsumeStatus recvMessage(MafkaMessage mafkaMessage, MessagetContext messagetContext) {
        try {
            log.debug("EsignAsyncResultListener receive message=[" + mafkaMessage.getBody() + "]  partition=" + mafkaMessage.getParttion());
            JSONObject resultObject = JSONObject.parseObject(mafkaMessage.getBody().toString());
            //识别appid，只处理本服务生产的消息
            String appId = resultObject.getString("appId");
            if (StringUtils.isEmpty(appId) || !ConfigUtilAdapter.getString("esign.appId", "232a4c58ca").equals(appId)) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            log.info("EsignAsyncResultListener consume message=[" + mafkaMessage.getBody() + "]  partition=" + mafkaMessage.getParttion());
            AsyncTaskHandler asyncTaskHandler = asyncHandlerRepository.get(resultObject.get("notifyType"));
            if (null == asyncTaskHandler) {
                log.warn("EsignAsyncResultListener无对应处理器，notifyType:{}", resultObject.get("notifyType"));
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            //handler处理
            asyncTaskHandler.handlerAsyncTask(resultObject);
        } catch (Exception e) {
            log.error("EsignAsyncResultListener consume error, msg = [" + mafkaMessage.getBody() + "]  partition=" + mafkaMessage.getParttion());
            econtractMetricService.metricEstampStatus("签章失败","异步签章");
            return ConsumeStatus.RECONSUME_LATER;
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}

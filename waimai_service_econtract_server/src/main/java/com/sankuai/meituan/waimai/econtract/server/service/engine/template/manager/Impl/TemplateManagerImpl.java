package com.sankuai.meituan.waimai.econtract.server.service.engine.template.manager.Impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.squirrel.client.StoreKey;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import com.sankuai.meituan.waimai.econtract.server.constants.EcontractRecordConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractSignBatchEventEntity;
import com.sankuai.meituan.waimai.econtract.server.service.SpringBeanUtil;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractMetricService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractRecordService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractSignRecordBatchEventService;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractSignRecordBatchService;
import com.sankuai.meituan.waimai.econtract.server.service.cache.RedisKvService;
import com.sankuai.meituan.waimai.econtract.server.service.engine.template.manager.TemplateManager;
import com.sankuai.meituan.waimai.econtract.server.service.engine.template.processor.TemplateProcessor;
import com.sankuai.meituan.waimai.econtract.server.utils.JsonUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.BatchEventStatusEnum;
import com.sankuai.meituan.waimai.econtrct.client.constants.BatchSignStatusEnum;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractPdfTypeEnum;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.SignH5InfoBo;
import com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException;
import joptsimple.internal.Strings;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException.ECONTRACT_SERVER_CANCEL_ERROR;

/**
 * <AUTHOR> Hou
 * @date 2017/10/21
 * @time 下午3:50
 */
@Service
public class TemplateManagerImpl implements TemplateManager {

    private static final Logger LOGGER = LoggerFactory.getLogger(TemplateManagerImpl.class);

    public static ConcurrentMap<String, TemplateProcessor> templateProcessorRepository = Maps.newConcurrentMap();

    @Autowired
    private EcontractRecordService econtractRecordService;

    @Autowired
    private EcontractSignRecordBatchEventService econtractSignRecordBatchEventService;

    @Autowired
    private EcontractSignRecordBatchService econtractSignRecordBatchService;

    @Autowired
    private RedisKvService redisKvService;

    @Autowired
    private EcontractMetricService econtractMetricService;

    private static final ImmutableSet<String> CONFIRM_STAMP_TASK = ImmutableSet
            .of(TaskConstant.CONFIRM_STAMP_A, TaskConstant.CONFIRM_STAMP_B, TaskConstant.CONFIRM_STAMP_C, TaskConstant.CONFIRM_STAMP_D, TaskConstant.CONFIRM_STAMP_E, TaskConstant.CONFIRM_STAMP_F, TaskConstant.CONFIRM_STAMP_G);


    @Override
    public void init() {
        templateProcessorRepository.put(TaskConstant.WM_EXCLUSIVE_PROCESSOR, (TemplateProcessor) SpringBeanUtil.getBean(TaskConstant.WM_EXCLUSIVE_PROCESSOR));
        templateProcessorRepository.put(TaskConstant.COMMON_PROCESSOR, (TemplateProcessor) SpringBeanUtil.getBean(TaskConstant.COMMON_PROCESSOR));

    }

    @Override
    public String applyEcontractByTemplate(EcontractContext econtractContext, EcontractBo econtractBo) {
        getTemplateHandlerByContext(econtractContext).applyCheck(econtractContext, econtractBo);
        return getTemplateHandlerByContext(econtractContext).applyEcontractByTemplate(econtractContext);
    }

    @Override
    public String applyEcontractBatchByTemplet(EcontractContext econtractContext, EcontractBatchBo batchBo) {
        return getTemplateHandlerByContext(econtractContext).applyEcontractByTemplate(econtractContext);
    }

    @Override
    public void controlTemplate(EcontractContext econtractContext, String callBcakType) {
        getTemplateHandlerByContext(econtractContext).controlTemplate(econtractContext, callBcakType);
    }

    /**
     * 这里的recordKey其实是签章平台的contractId
     */
    @Override
    public void callBackCommitTemplateStage(String recordKey, String callType, Map<String, String> paramMap) {
        EcontractRecordEntity econtractRecordEntity = econtractRecordService.queryRecordByRecordKey(recordKey);
        if (null != econtractRecordEntity) {
            EcontractContext econtractContext = JSON.parseObject(econtractRecordEntity.getEcontractRecordContext(), EcontractContext.class);
            getTemplateHandlerByContext(econtractContext).callBackCommitTemplateStage(econtractContext, callType, paramMap);
        }

    }

    @Override
    public String queryRealNameRedirectUrl(String recordKey) {
        EcontractRecordEntity econtractRecordEntity = econtractRecordService.queryRecordByRecordKey(recordKey);
        if (null != econtractRecordEntity) {
            EcontractContext econtractContext = JSON.parseObject(econtractRecordEntity.getEcontractRecordContext(), EcontractContext.class);
            return getTemplateHandlerByContext(econtractContext).queryRealNameRedirectUrl(recordKey);
        } else {
            return "";
        }

    }

    @Override
    public boolean sendPhoneCap(String recordKey) throws EcontractException {
        EcontractRecordEntity econtractRecordEntity = econtractRecordService.queryRecordByRecordKey(recordKey);
        if (null != econtractRecordEntity) {
            EcontractContext econtractContext = JSON.parseObject(econtractRecordEntity.getEcontractRecordContext(), EcontractContext.class);
            return getTemplateHandlerByContext(econtractContext).sendPhoneCap(recordKey);
        } else {
            return false;
        }

    }

    @Override
    public boolean verifyPhoneCaptcha(String recordKey, String code) {
        EcontractRecordEntity econtractRecordEntity = econtractRecordService.queryRecordByRecordKey(recordKey);
        if (null != econtractRecordEntity) {
            EcontractContext econtractContext = JSON.parseObject(econtractRecordEntity.getEcontractRecordContext(), EcontractContext.class);
            return getTemplateHandlerByContext(econtractContext).verifyPhoneCaptcha(recordKey, code);
        } else {
            return false;
        }

    }

    @Override
    public void cancelSignEContract(String recordKey, String msg) throws EcontractException {
        //获取分布式锁，防止确认、取消操作并发进行
        EcontractRecordEntity econtractRecordEntity = econtractRecordService.queryRecordByRecordKey(recordKey);
        if (null != econtractRecordEntity) {
            String bizId = recordKey;
            StoreKey storeKey = redisKvService.genConfirmSignKey(bizId);
            if (redisKvService.isLockedByOther(storeKey)) {
                LOGGER.warn("recordKey:{}，正在进行确认签约，不允许进行取消操作", recordKey);
                throw new EcontractException(ECONTRACT_SERVER_CANCEL_ERROR, "正在进行确认签约，不允许进行取消操作");
            } else {
                EcontractContext econtractContext = JSON.parseObject(econtractRecordEntity.getEcontractRecordContext(), EcontractContext.class);
                getTemplateHandlerByContext(econtractContext).cancelSignEContract(recordKey, msg);
            }
        }
        econtractMetricService.metricCancelCount(econtractRecordEntity.getEcontractType());
    }

    @Override
    public void confirmSignEContract(String recordKey) throws EcontractException {
        LOGGER.info("confirmSignEContract,recordKey:{}", recordKey);
        String bizId = recordKey;
        StoreKey storeKey = redisKvService.genConfirmSignKey(bizId);
        try {
            if (redisKvService.tryLock(storeKey, 1L, 60)) {
                EcontractRecordEntity econtractRecordEntity = econtractRecordService.queryRecordByRecordKey(recordKey);
                if (null != econtractRecordEntity) {
                    //todo:测试完成后删除
                    LOGGER.info("confirmSignEContract获取到锁，确认签约:{}", recordKey);
                    Thread.sleep(ConfigUtilAdapter.getInt("confirm_sleep_test", 0));
                    LOGGER.info("recordKey:{}，等待:{}毫秒后进行确认执行", recordKey, ConfigUtilAdapter.getInt("confirm_sleep_test", 0));
                    EcontractContext econtractContext = JSON.parseObject(econtractRecordEntity.getEcontractRecordContext(), EcontractContext.class);
                    getTemplateHandlerByContext(econtractContext).confirmSignEContract(recordKey);
                } else {
                    LOGGER.error("签约任务为空，recordKey:{}", recordKey);
                    throw new EcontractException(EcontractException.STATUS_ERROR, "确认签约失败");
                }
                econtractMetricService.metricConfirmSuccessCount(econtractRecordEntity.getEcontractType());
            } else {
                LOGGER.warn("确认签约获取锁失败，未进行确认，recordKey:{}", recordKey);
                throw new EcontractException(EcontractException.STATUS_ERROR, "当前无法确认签约，请稍后重试");
            }
        } catch (Exception e) {
            LOGGER.warn("确认签约异常，recordKey:{}", recordKey, e);
            Cat.logMetricForCount("confirmsign_single_error");
            throw new EcontractException(EcontractException.SERVER_ERROR, "确认签约失败");
        } finally {
            redisKvService.unLock(storeKey);
        }
    }

    /**
     * 该接口处理批量任务的确认签约
     *
     * @param recordKey
     * @param confirmNums
     * @throws EcontractException
     */
    @Override
    public void confirmSignEContract4BatchRecord(String recordKey, Long confirmNums) throws EcontractException {
        EcontractRecordEntity econtractRecordEntity = econtractRecordService.queryRecordByRecordKey(recordKey);
        if (null != econtractRecordEntity) {
            EcontractContext econtractContext = JSON.parseObject(econtractRecordEntity.getEcontractRecordContext(), EcontractContext.class);
            getTemplateHandlerByContext(econtractContext).confirmSignEContract(recordKey);
        }
        econtractMetricService.metricConfirmSuccessCount(econtractRecordEntity.getEcontractType());
    }

    @Override
    public List<String> doRetrySms(String recordKey) {
        EcontractRecordEntity econtractRecordEntity = econtractRecordService.queryRecordByRecordKey(recordKey);
        if (null != econtractRecordEntity) {
            EcontractContext econtractContext = JSON.parseObject(econtractRecordEntity.getEcontractRecordContext(), EcontractContext.class);
            return getTemplateHandlerByContext(econtractContext).doRetrySms(recordKey);
        }
        return Lists.newArrayList();
    }

    @Override
    public List<String> doRetrySmsForBatch(String recordKey) {
        Integer recordBatchId = econtractRecordService.queryRecordBatchIdByRecordKey(recordKey);
        //允许重发的两种状态：1.从未发过的(NO_EXE、EXE_LATER)；2.定时任务发送成功的(EXE_SUCCESS)
        EcontractSignBatchEventEntity batchEventEntity = econtractSignRecordBatchEventService.getSignBatchEventByBatchId(recordBatchId);
        if (batchEventEntity.getStatus() == BatchEventStatusEnum.NO_EXE.getCode()
                || batchEventEntity.getStatus() == BatchEventStatusEnum.EXE_LATER.getCode()
                || batchEventEntity.getStatus() == BatchEventStatusEnum.EXE_SUCCESS.getCode()) {
            Optional<Pair<String, List<String>>> sendSmsResult = doBatchSms(recordBatchId, recordKey);
            //成功更新event状态，失败不更新状态
            if (sendSmsResult.isPresent()) {
                Pair<String, List<String>> resultPair = sendSmsResult.get();
                //更新event状态，防止定时任务造成重复发送
                batchEventEntity.setStatus(BatchEventStatusEnum.EXE_SUCCESS.getCode());
                batchEventEntity.setExtMsg(BatchEventStatusEnum.EXE_SUCCESS.name() + ":" + batchEventEntity.getBatchId());
                batchEventEntity.setRecordKey(resultPair.getLeft());
                econtractSignRecordBatchEventService.updateByPrimaryKeySelective(batchEventEntity);
                econtractSignRecordBatchService.updateRecordBatchStatusByBatchId(batchEventEntity.getBatchId(), BatchSignStatusEnum.SEND_SMS.getCode());
                LOGGER.info("doRetrySmsForBatch短信发送成功, recordKey:{}，recordBatchId:{}, ", recordKey, batchEventEntity.getBatchId());
                return resultPair.getRight();
            }
        }
        return Lists.newArrayList();
    }

    @Override
    public SignH5InfoBo querySignH5InoByRecordIdAndTaskId(Integer recordId, Integer taskId) {
        EcontractRecordEntity econtractRecordEntity = econtractRecordService.selectByPrimaryKey(recordId);
        LOGGER.info("querySignH5InoByRecordIdAndTaskId econtractRecordEntity = {}, recordId = {}", JSON.toJSONString(econtractRecordEntity), recordId);
        if (null != econtractRecordEntity) {
            EcontractContext econtractContext = JSON.parseObject(econtractRecordEntity.getEcontractRecordContext(), EcontractContext.class);
            SignH5InfoBo signH5InfoBo = getTemplateHandlerByContext(econtractContext).querySignH5InoByRecordIdAndTaskId(econtractRecordEntity.getId(), taskId);
            signH5InfoBo.setPdfUrlMap(buildDownloadPdfUrl(econtractRecordEntity));
            return signH5InfoBo;
        } else {
            return new SignH5InfoBo();
        }
    }

    @Override
    public Map<String, String> buildDownloadPdfUrl(Integer recordId) {
        try {
            EcontractRecordEntity econtractRecordEntity = econtractRecordService.selectByPrimaryKey(recordId);
            if (null != econtractRecordEntity) {
                return buildDownloadPdfUrl(econtractRecordEntity);
            }
        } catch (Exception e) {
            LOGGER.error("buildDownloadPdfUrl error,recordId:{}", recordId, e);
        }
        return null;
    }

    private Map<String, String> buildDownloadPdfUrl(EcontractRecordEntity econtractRecordEntity) {
        try {
            if (StringUtils.isNotBlank(econtractRecordEntity.getSaveUrl())) {
                String saveUrl = econtractRecordEntity.getSaveUrl();
                Map<String, String> pdfUrlMap = new HashMap<>();
                boolean isJson = JsonUtil.isJsonFormat(saveUrl);
                if (isJson) {
                    JSONObject urlJson = JSON.parseObject(econtractRecordEntity.getSaveUrl());
                    for (Map.Entry<String, Object> url : urlJson.entrySet()) {
                        String name = EcontractPdfTypeEnum.getEnumByName(url.getKey()).getDesc();
                        String pdfUrl = url.getValue().toString();
                        pdfUrlMap.put(name, pdfUrl);
                    }
                } else {
                    pdfUrlMap.put("合同",  saveUrl);
                }
                LOGGER.info("buildDownloadPdfUrl, recordKey:{}，pdfUrlMap:{}, ", econtractRecordEntity.getRecordKey(), JSON.toJSONString(pdfUrlMap));
                return pdfUrlMap;
            }
        } catch (Exception e) {
            LOGGER.error("buildDownloadPdfUrl error, econtractRecordEntity:{}, ", JSON.toJSONString(econtractRecordEntity), e);
        }
        return null;
    }

    private TemplateProcessor getTemplateHandlerByContext(EcontractContext econtractContext) {
        return templateProcessorRepository.get(econtractContext.getTemplateProcessorType());
    }

    @Override
    public String getShortLink(String recordKey) {
        EcontractRecordEntity econtractRecordEntity = econtractRecordService.queryRecordByRecordKey(recordKey);
        if (null != econtractRecordEntity) {
            EcontractContext econtractContext = JSON.parseObject(econtractRecordEntity.getEcontractRecordContext(), EcontractContext.class);
            return getTemplateHandlerByContext(econtractContext).getShortLink(recordKey);
        }
        return Strings.EMPTY;
    }

    @Override
    public Optional<Pair<String, List<String>>> doBatchSms(Integer batchId, String recordKey) {
        List<EcontractRecordEntity> recordEntities = econtractRecordService.queryEcontractRecordByBatchIdAsc(batchId);
        if (CollectionUtils.isEmpty(recordEntities)) {
            LOGGER.info("未查询到批次record记录 batchId={}", batchId);
            return Optional.empty();
        }

        //过滤出失败的
        List<EcontractRecordEntity> recordEntityList = recordEntities.stream().filter(record -> !record.getEcontractState().equals(EcontractRecordConstant.FAIL)).collect(Collectors.toList());

        if (!checkCanSendMsg(recordEntityList)) {
            LOGGER.info("该批次对应的record未全部到达发短信状态 batchId={}", batchId);
            return Optional.empty();
        }

        EcontractRecordEntity recordEntity = recordEntities.stream().findFirst().get();
        String sendRecordKey;
        if (StringUtils.isNotEmpty(recordKey)) {
            sendRecordKey = recordKey;
        } else {
            sendRecordKey = recordEntity.getRecordKey();
        }
        EcontractRecordEntity econtractRecordEntity = econtractRecordService.queryRecordByRecordKey(sendRecordKey);
        EcontractContext context = JSON.parseObject(econtractRecordEntity.getEcontractRecordContext(), EcontractContext.class);
        if (null != econtractRecordEntity) {
            //发送短信
            List<String> result = getTemplateHandlerByContext(context).doBatchSms(sendRecordKey);
            if (CollectionUtils.isNotEmpty(result)) {
                Pair<String, List<String>> resultPair = Pair.of(sendRecordKey, result);
                return Optional.of(resultPair);
            }
        }
        return Optional.empty();
    }

    private boolean checkCanSendMsg(List<EcontractRecordEntity> recordEntityList) {
        if (CollectionUtils.isEmpty(recordEntityList)) {
            return false;
        }
        //判断是否都到达签约状态
        return recordEntityList.stream().allMatch(record -> CONFIRM_STAMP_TASK.contains(record.getEcontractStage()));
    }
}

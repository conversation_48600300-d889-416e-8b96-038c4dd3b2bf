package com.sankuai.meituan.waimai.econtract.server.pdfEngine.bo;
import com.itextpdf.awt.geom.Rectangle2D;
import com.itextpdf.text.pdf.parser.ImageRenderInfo;
import com.itextpdf.text.pdf.parser.RenderListener;
import com.itextpdf.text.pdf.parser.TextRenderInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

public class StampStringRenderListener implements RenderListener {

    private static final Logger logger = LoggerFactory.getLogger(StampStringRenderListener.class);

    private static final String MOCK_SIGN_KEY = "模拟盖章预览签章专属";

    //用来存放每一行文字的坐标位置
    private List<Map<String, Rectangle2D.Float>> rowsTextRect = new ArrayList<>();

    public List<Map<String, Rectangle2D.Float>> getRowsTextRect() {
        return rowsTextRect;
    }

    public StampStringRenderListener() {}

    @Override
    public void beginTextBlock() {}

    /**
     * 文字主要处理方法
     */
    @Override
    public void renderText(TextRenderInfo renderInfo) {
        String text = renderInfo.getText();
        if (text.contains(MOCK_SIGN_KEY)) {
            Rectangle2D.Float rectAscent = renderInfo.getAscentLine().getBoundingRectange();
            logger.info("text:{},--x:{},--y:{}", text, rectAscent.x, rectAscent.y);
            Map<String, Rectangle2D.Float> map = new HashMap<>();
            map.put(MOCK_SIGN_KEY, rectAscent);
            rowsTextRect.add(map);
        }
    }

    @Override
    public void endTextBlock() {}

    @Override
    public void renderImage(ImageRenderInfo renderInfo) {}

}

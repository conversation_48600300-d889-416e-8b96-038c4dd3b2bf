package com.sankuai.meituan.waimai.econtract.server.service.engine.task.Impl;

import com.google.common.collect.Maps;

import com.sankuai.meituan.waimai.econtract.server.annotation.EcontractIdempotent;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.constants.ContextConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.TaskExecutor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> <PERSON>u
 * @date 2017/10/25
 * @time 上午10:58
 */
@Service
public class FinishExecutor extends AbstractExecutor implements TaskExecutor {

    private static final Logger LOGGER = LoggerFactory.getLogger(FinishExecutor.class);

    private static final String FINISH_EXECUTOR = "finish_executor";

    @Override
    @EcontractIdempotent
    public void executeTask(EcontractContext context) {
        try {
            LOGGER.info("FinishExecutor executeTask,recordKey:{}",context.getEcontractRecordEntity().getRecordKey());
            String url = context.getEcontractRecordEntity().getSaveUrl();
//            String url = changeEstampUrl(context.getEcontractRecordEntity().getSaveUrl());
            context.getTaskContext().setExecutorResult(Maps.newHashMap());
            context.getTaskContext().getExecutorResult().put(TaskConstant.PDF_URL, url);
            context.setContextState(ContextConstant.CONTEXT_FINISH);
            executeSuccess(context);
            metricExecutorResult(FINISH_EXECUTOR, true);
            LOGGER.info("FinishExecutor success , recordkey : {} , url : {}", context.getEcontractRecordEntity().getRecordKey(), url);
        } catch (Exception e) {
            metricExecutorResult(FINISH_EXECUTOR, false);
            LOGGER.error("{} , fail to FinishExecutor ", context.getEcontractRecordEntity().getRecordKey(), e);
            executeFail(context, e);
        }
    }
}

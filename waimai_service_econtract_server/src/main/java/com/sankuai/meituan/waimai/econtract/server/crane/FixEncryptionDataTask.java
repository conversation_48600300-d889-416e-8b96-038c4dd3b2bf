package com.sankuai.meituan.waimai.econtract.server.crane;

import java.util.Arrays;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.cip.crane.client.spring.annotation.Crane;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractEncryptionRecordMapper;


/**
 * Created by lixuepeng on 2021/12/01
 */

@Component
public class FixEncryptionDataTask {

    private static final Logger LOGGER = LoggerFactory.getLogger(FixEncryptionDataTask.class);
    
    @Autowired
    private EcontractEncryptionRecordMapper econtractEncryptionRecordMapper;

    /**
     *
     * 清理加密脏数据
     */
    @Crane("fix.econtract.encryption.data.task")
    public void handleFixEncryptionData(String recordTypeAndPrimaryKey) {
        try {
            List<String> inputStrList = Arrays.asList(recordTypeAndPrimaryKey.split("#"));
            Integer recordType = Integer.valueOf(inputStrList.get(0));
            Long primaryKey = Long.valueOf(inputStrList.get(1));
            econtractEncryptionRecordMapper.deleteByRecordTypeAndPrimaryKey(recordType, primaryKey);
        } catch (Exception e) {
            LOGGER.error("#handleFixEncryptionData# error recordTypeAndPrimaryKey:{}", recordTypeAndPrimaryKey, e);
        }
    }
}

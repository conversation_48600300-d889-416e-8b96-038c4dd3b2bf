package com.sankuai.meituan.waimai.econtract.server.service.mq;

import com.alibaba.fastjson.JSON;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.constants.MccConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.MessageStatusEnum;
import com.sankuai.meituan.waimai.econtract.server.service.engine.template.manager.TemplateManager;
import com.sankuai.meituan.waimai.econtract.server.service.mq.mafka.handler.MessageConsumerHandler;
import com.sankuai.meituan.waimai.econtrct.client.constants.CallbackConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public abstract class AbstractTaskCommitListener {

    @Autowired
    private TemplateManager templateManager;

    @Resource
    private MessageConsumerHandler messageConsumerHandler;

    public ConsumeStatus recvMessage(MafkaMessage mafkaMessage, MessagetContext messagetContext) {
        log.info("className: {}, AbstractTaskCommitListener#recvMessage, message: {}, partition: {}", getTaskCommitListenerClassName(), mafkaMessage.getBody(), mafkaMessage.getParttion());

        if (null == mafkaMessage.getBody() || StringUtils.isBlank(mafkaMessage.getBody().toString())) {
            log.error("className:{} receive message is null", getTaskCommitListenerClassName());
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        TaskMsg taskMsg = null;
        try {
            String msg = mafkaMessage.getBody().toString();
            taskMsg = JSON.parseObject(msg, TaskMsg.class);
            //以contextId 从数据库获取消息体
            messageConsumerHandler.handler(taskMsg);
            EcontractContext context = JSON.parseObject(taskMsg.getMessageBody(), EcontractContext.class);
            //转给控制器的消息
            Thread.sleep(ConfigUtilAdapter.getInt(MccConstant.COMMIT_TASK_LISTENER_SLEEP_MILLIS));
            templateManager.controlTemplate(context, CallbackConstant.TYPE_MQ);
            // 处理完 若是大消息体更新消息状态
            messageConsumerHandler.updateMessageStatus(taskMsg, MessageStatusEnum.CONSUME_SUCCESS.getStatus());
        } catch (Exception e) {
            log.error("AbstractTaskCommitListener#recvMessage, recorKey: {}, error", taskMsg.getEcontractRecordKey(), e);
            messageConsumerHandler.updateMessageStatus(taskMsg, MessageStatusEnum.CONSUME_FAIL.getStatus());
            return ConsumeStatus.RECONSUME_LATER;
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    abstract String getTaskCommitListenerClassName();

}

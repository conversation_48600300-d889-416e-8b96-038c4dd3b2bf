package com.sankuai.meituan.waimai.econtract.server.service.engine.task.Impl;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.util.MetricHelper;
import com.google.common.collect.Maps;

import com.meituan.sankuai.bsi.esign.common.model.entity.result.AsyncTaskForContractOperateResult;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.constants.EcontractAsyncTaskConstant;
import com.sankuai.meituan.waimai.econtract.server.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractAsyncRecordMapper;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractAsyncRecordEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractTaskEntity;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractUserEntity;
import com.sankuai.meituan.waimai.econtract.server.exception.EcontractException;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractTaskService;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.TaskManager;
import com.sankuai.meituan.waimai.econtract.server.service.idempotent.EcontractIdempotentRedisService;
import com.sankuai.meituan.waimai.econtract.server.service.mq.TaskMsg;
import com.sankuai.meituan.waimai.econtract.server.service.mq.mafka.handler.MessageProducerHandler;
import com.sankuai.meituan.waimai.econtract.server.utils.EstampUtil;

import com.sankuai.meituan.waimai.econtract.server.utils.ServiceEnvUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Map;

import javax.annotation.Resource;

/**
 * <AUTHOR> Hou
 * @date 2017/10/24
 * @time 上午11:07
 */
@Component
@Slf4j
public class AbstractExecutor  {

    @Resource
    private MessageProducerHandler messageProducerHandler;

    private static Map<String, String> map = Maps.newHashMap();

    static {
        map.put("c1contract", "C1");
        map.put("settle", "S");
        map.put("delivery", "D");
    }

    @Autowired
    private TaskManager taskManager;

    @Resource
    private EcontractTaskService econtractTaskService;

    @Resource
    private EcontractAsyncRecordMapper econtractAsyncRecordMapper;

    @Resource
    private EcontractIdempotentRedisService econtractIdempotentRedisService;

    public void executeFail(EcontractContext context, String msg) {
        econtractIdempotentRedisService.setReentrant(context);
        context.getTaskContext().setFailMessage(msg);
        context.getTaskContext().setState(TaskConstant.TASK_FAIL);
        sendMessage(context);
    }

    public void executeFail(EcontractContext context, Exception e){
        econtractIdempotentRedisService.setReentrant(context);
        EcontractException exception = EcontractException.wrap(e);
        context.getTaskContext().setErrorCode(exception.getErrorCode());
        String errorMsg = exception.getMessage();
        if(StringUtils.isEmpty(errorMsg)){
            errorMsg = "系统异常";
        }
        context.getTaskContext().setFailMessage(errorMsg);
        context.getTaskContext().setState(TaskConstant.TASK_FAIL);
        sendMessage(context);
    }


    public void executeSuccess(EcontractContext context){
        context.getTaskContext().setState(TaskConstant.TASK_SUCCESS);
        sendMessage(context);
    }

    public void executeCallback(EcontractContext context) {
        context.getTaskContext().setState(TaskConstant.TASK_SUCCESS);
        sendMessage(context);
    }

    private void sendMessage(EcontractContext context){
        boolean isMafka = taskManager.mafkaMigrateSwitch();
        TaskMsg taskMsg = messageProducerHandler.handler(context,isMafka);
        taskManager.commitTask(taskMsg,isMafka);
    }

    /**
     * 生成签章平台contract_id
     */
    public String genContractId(EcontractContext context, String recordKey) {
        Integer taskId = context.getTaskContext().getTaskId();
        EcontractTaskEntity taskEntity = econtractTaskService.selectByPrimaryKeyMaster(taskId);
        if (!map.keySet().contains(taskEntity.getTaskType())) {
            return recordKey;
        }
        return EstampUtil.genContractId(recordKey, map.get(taskEntity.getTaskType()));
    }

    protected static final String SINGLE_TASK_TYPE = "single_task_type";

    /**
     * 获取子任务类型，根据批量任务的key
     */
    public String getTaskType(EcontractContext context) {
        boolean isBatch = context.getFlowList() != null && CollectionUtils.size(context.getFlowList()) > 1;
        if (!isBatch) {
            return SINGLE_TASK_TYPE;
        }

        Integer taskId = context.getTaskContext().getTaskId();
        EcontractTaskEntity taskEntity = econtractTaskService.selectByPrimaryKeyMaster(taskId);
        log.info("AbstractExecutor#getTaskType, taskEntity: {}", JSON.toJSONString(taskEntity));
        return taskEntity.getTaskType();
    }

    /**
     * 保存异步调用结果与任务的关联关系
     * @param context
     * @param asyncResult
     */
    public EcontractAsyncRecordEntity saveAsyncTaskRel(EcontractContext context, AsyncTaskForContractOperateResult asyncResult, String requestType)
            throws EcontractException {
        if(null == asyncResult || StringUtils.isEmpty(asyncResult.getAsyncTaskId())){
            log.error("recordKey:{}，异步请求签章结果为空", context.getEcontractRecordEntity().getRecordKey());
            throw new EcontractException(EcontractException.ASYNC_CALL_ERROR, "异步签章返回非法流水信息");
        }
        //获取contractid
        String taskType = getTaskType(context);
        String contractId = context.getTaskTypeAndContractIdMap().get(taskType);
        //剔除上下文中的冷数据
        context.setEcontractUserEntity(new EcontractUserEntity());
        context.setFlowList(new ArrayList<>());
        context.setStageInfoBoList(new ArrayList<>());
        context.setStageBatchInfoBoList(new ArrayList<>());
        //封装对象
        EcontractAsyncRecordEntity asyncRecord = EcontractAsyncRecordEntity.builder()
                .asyncId(asyncResult.getAsyncTaskId())
                .taskId((long)context.getTaskContext().getTaskId())
                .contractId(contractId)
                .taskType(requestType)
                .status(EcontractAsyncTaskConstant.COMMIT)
                .taskContext(JSON.toJSONString(context))
                .env(ServiceEnvUtils.queryRecordEnv())
                .build();
        econtractAsyncRecordMapper.insertSelective(asyncRecord);
        return asyncRecord;
    }

    protected void metricExecutorResult(String executorTask, boolean isSuccess) {
        String result = isSuccess ? "success" : "fail";
        MetricHelper.build()
                .name(executorTask)
                .tag("result", result)
                .count();
    }

}

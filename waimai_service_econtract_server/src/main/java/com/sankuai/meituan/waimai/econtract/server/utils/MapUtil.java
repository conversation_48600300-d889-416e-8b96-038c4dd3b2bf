package com.sankuai.meituan.waimai.econtract.server.utils;

import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractRecordEntity;

import java.beans.BeanInfo;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2017/11/8
 * @time 下午2:07
 */
public class MapUtil {
    /**
     * 从map集合中获取属性值
     *
     * @param <E>
     * @param map          map集合
     * @param key          键对
     * @param defaultValue 默认值
     * @return
     * <AUTHOR>
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    public final static <E> E get(Map map, Object key, E defaultValue) {
        Object o = map.get(key);
        if (o == null)
            return defaultValue;
        return (E) o;
    }


    /**
     * Map对象转化成 JavaBean对象
     *
     * @param javaBean JavaBean实例对象
     * @param map      Map对象
     * @return
     * <AUTHOR>
     */
    @SuppressWarnings({"rawtypes", "unchecked", "hiding"})
    public static <T> T map2Java(T javaBean, Map map) throws IntrospectionException, IllegalAccessException, InstantiationException, InvocationTargetException {

        // 获取javaBean属性
        BeanInfo beanInfo = Introspector.getBeanInfo(javaBean.getClass());
        // 创建 JavaBean 对象
        Object obj = javaBean.getClass().newInstance();

        PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
        if (propertyDescriptors != null && propertyDescriptors.length > 0) {
            String propertyName = null; // javaBean属性名
            Object propertyValue = null; // javaBean属性值
            for (PropertyDescriptor pd : propertyDescriptors) {
                propertyName = pd.getName();
                if (map.containsKey(propertyName)) {
                    propertyValue = map.get(propertyName);
                    pd.getWriteMethod().invoke(obj, new Object[]{propertyValue});
                }
            }
            return (T) obj;
        } else {
            return null;
        }
    }

    /**
     * JavaBean对象转化成Map对象
     *
     * @param javaBean
     * @return
     * <AUTHOR>
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    public static Map java2Map(Object javaBean) throws IntrospectionException, InvocationTargetException, IllegalAccessException {
        Map map = Maps.newHashMap();

        // 获取javaBean属性
        BeanInfo beanInfo = Introspector.getBeanInfo(javaBean.getClass());

        PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
        if (propertyDescriptors != null && propertyDescriptors.length > 0) {
            String propertyName = null; // javaBean属性名
            Object propertyValue = null; // javaBean属性值
            for (PropertyDescriptor pd : propertyDescriptors) {
                propertyName = pd.getName();
                if (!propertyName.equals("class")) {
                    Method readMethod = pd.getReadMethod();
                    propertyValue = readMethod.invoke(javaBean, new Object[0]);
                    map.put(propertyName, String.valueOf(propertyValue));
                }
            }
        }
        return map;
    }

    public static void main(String[] args) {
        EcontractRecordEntity recordEntity = new EcontractRecordEntity();
        recordEntity.setId(1);
        recordEntity.setSaveUrl("asd");

    }
}

package com.sankuai.meituan.waimai.econtract.server.service.engine.task.Impl;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.mobileossapi.dto.operate.ShortUrlRequestThrift;
import com.dianping.mobileossapi.dto.operate.ShortUrlResultThrift;
import com.dianping.mobileossapi.service.operate.OperateServiceThrift;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ScheduledThreadPool;
import com.dianping.squirrel.client.StoreKey;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.meituan.mtrace.Tracer;
import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import com.sankuai.inf.octo.mns.util.ProcessInfoUtil;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.econtract.server.annotation.EcontractIdempotent;
import com.sankuai.meituan.waimai.econtract.server.bo.task.EcontractContext;
import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import com.sankuai.meituan.waimai.econtract.server.constants.*;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractSmsDealEntityMapper;
import com.sankuai.meituan.waimai.econtract.server.entity.*;
import com.sankuai.meituan.waimai.econtract.server.service.biz.*;
import com.sankuai.meituan.waimai.econtract.server.service.cache.RedisKvService;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.TaskExecutor;
import com.sankuai.meituan.waimai.econtract.server.service.sms.SendMessageService;
import com.sankuai.meituan.waimai.econtract.server.template.config.service.EcontractTemplateBaseService;
import com.sankuai.meituan.waimai.econtract.server.utils.*;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.SignerInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.base.BoolResult;
import com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException;
import com.sankuai.meituan.waimai.econtrct.client.template.config.domain.EcontractTemplateBaseBo;
import com.sankuai.meituan.waimai.util.DaxiangUtil;
import fr.opensagres.poi.xwpf.converter.core.utils.DxaUtil;
import joptsimple.internal.Strings;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Hou
 * @date 2017/10/25
 * @time 上午10:58
 */
@Service
public class SmsExecutor extends AbstractExecutor implements TaskExecutor {

    private static final Logger LOGGER = LoggerFactory.getLogger(SmsExecutor.class);

    @Autowired
    private EcontractSmsDealEntityMapper smsDealEntityMapper;

    @Autowired
    private EcontractRelService econtractRelService;

    @Autowired
    private EcontractService econtractService;

    @Autowired
    private SendMessageService sendMessageService;

    @Autowired
    private OperateServiceThrift operateServiceThrift;

    @Autowired
    private RedisKvService redisKvService;

    @Autowired
    private EcontractMetricService econtractMetricService;

    @Resource
    private EcontractTemplateBaseService econtractTemplateBaseService;

    @Resource
    private EcontractSignPageTemplateService signPageTemplateService;

    @Resource
    private EcontractRecordService recordService;

    @Resource
    private EcontractSignPageBizService econtractSignPageBizService;

    @Resource
    private EcontractRecordService econtractRecordService;

    private static final String SHORT_MSG_KEY = "switch_to_new_shortmsg";

    private static final Boolean SHORT_MSG_DEFAULT = true;

    private static final String APP_NAME = "com.sankuai.waimai.m.econtractserver";

    private static final String BIZ_TYPE = "shortmsg_biztype";

    private static final String SMS_KEY = "SMS";

    /**
     * 短连接bizType申请地址： http://test.infoplatform.dianping.com/oss/operate/applyShortLinkBizType
     */
    private static final Integer BIZ_TYPE_DEFAULT = 161;

    /**
     * 短连接过期时间
     */
    private static final Integer EXPIRE_TIME_DEFAULT = 1;

    /**
     * 请求短连接 失败状态
     */
    private static final Integer STATUS_FAIL = 0;

    /**
     * 一天对应秒数
     */
    private static final Integer ONE_DAY_SECOND = 24 * 60 * 60;

    private static final String SEND_SMS = "send_sms";
    
    private static final ScheduledThreadPool autoConfirmSignScheduledThreadPool = Rhino.newScheduledThreadPool("autoConfirmSignExecutorService", DefaultThreadPoolProperties.Setter()
            .withThreadFactory(new ThreadFactoryBuilder().setNameFormat("auto_confirm_sign").build()).withRejectHandler(new ThreadPoolExecutor.DiscardPolicy()));
    

    @Override
    @EcontractIdempotent
    public void executeTask(EcontractContext context) {
        try {
            LOGGER.info("SmsExecutor executeTask,recordKey:{}",context.getEcontractRecordEntity().getRecordKey());
            if (sendMessage(context)) {
                context.setContextState(ContextConstant.CONTEXT_FINISH);
                executeSuccess(context);
                econtractMetricService.metricSmsCount("发短信成功");
                LOGGER.info("SmsExecutor success, recordkey : {} ", context.getEcontractRecordEntity().getRecordKey());
            }
        } catch (Exception e) {
            metricExecutorResult(SEND_SMS, false);
            LOGGER.error("fail to SmsExecutor :" + context.getEcontractRecordEntity().getRecordKey() + ",msg:"
                    + e.getMessage(), e);
            executeFail(context, e);
            econtractMetricService.metricSmsCount("发短信失败");
        }
    }

    public boolean sendMessage(EcontractContext context) throws IllegalBlockSizeException, NoSuchPaddingException,
            BadPaddingException, NoSuchAlgorithmException, InvalidKeyException, KmsResultNullException {
        return sendMessage(context, false);
    }

    /**
     * 发送短信
     * @param resendMsg 是否来源于重发短信
     */
    public boolean sendMessage(EcontractContext context, boolean resendMsg)
            throws IllegalBlockSizeException, NoSuchPaddingException, BadPaddingException, NoSuchAlgorithmException,
            InvalidKeyException, KmsResultNullException {
        //record_batch模式(多个并行任务不需要组合成一个record进行签约)
        LOGGER.info("sendMessage context:{}", JacksonUtil.writeAsJsonStr(context));
        if (context.getRecordBatchId() != null && Integer.parseInt(context.getRecordBatchId()) > 0 && !context.getBatchSendSms()){
            return true;
        }

        // 生成短链接
        SignerInfoBo signerInfoBo = context.getTaskContext().getStageInfoBo().getSignerInfoBo();
        boolean isSenselessEffect = signerInfoBo.isSenselessEffect();

        String dealVersion = DealVersionUtils.getSmsVersion(context.getTaskContext().getTaskId());
        Map<String, String> shortUrlMap = parseShortUrl(context, context.getEcontractEntity().getEcontractType(), signerInfoBo, dealVersion);

        // 获取接受短信接收人
        List<String> receiverList = context.getTaskContext().getStageInfoBo().getSignerInfoBo().getMobileList();
        String testUsers = ConfigUtilAdapter.getString(MccConstant.TEST_SMS_USER, StringUtils.EMPTY);
        if (StringUtils.isNotEmpty(testUsers)) {
            receiverList = Lists.newArrayList(testUsers.split(","));
        }

        // 短信内容
        Map<String, String> smsParamMap = Maps.newHashMap();
        smsParamMap.put("shortlink", shortUrlMap.get(SMS_KEY));// 短链接地址
        smsParamMap.putAll(signerInfoBo.getSmsParamMap());

        receiverList.forEach(receiver -> {
            if (StringUtils.isNotEmpty(receiver) && shortUrlMap.get(SMS_KEY) != null && !isSenselessEffect) {
                //首次发起签约 且 配置不发送短信则不重发短信
                if (!resendMsg && Boolean.FALSE.equals(signerInfoBo.getSendSms())) {
                    LOGGER.info("该签约任务不发送短信 record:{},sms to user:{}, url is:{}", context.getEcontractRecordEntity().getRecordKey(), receiver, shortUrlMap.get(SMS_KEY));
                } else {
                    if (context.getRecordBatchId() != null && Integer.parseInt(context.getRecordBatchId()) > 0){
                        if (context.getRealSendSms()){
                            sendMessageService.sms(receiver, signerInfoBo.getSmsTemplateId(), smsParamMap);
                        } else {
                            LOGGER.info("[批量签约]:无需真正发送短信 recordKey:{}",context.getEcontractRecordEntity().getRecordKey());
                        }
                    } else {
                        sendMessageService.sms(receiver, signerInfoBo.getSmsTemplateId(), smsParamMap);
                        LOGGER.info("record  : {},sms to user : {}, url is : {}",
                                context.getEcontractRecordEntity().getRecordKey(), receiver, shortUrlMap.get(SMS_KEY));
                    }
                }
            }
        });

        // 回调通知短链接信息
        context.getTaskContext().setExecutorResult(shortUrlMap);
        if (isSenselessEffect) {
            int size = autoConfirmSignScheduledThreadPool.getWorkQueue().size();
            if (size < MccConfig.getAutoConfirmSignMaxConcurrentSize()) {
                LOGGER.info("SmsExecutor#sendMessage, auto confirm sign, recordKey:{}",context.getEcontractRecordEntity().getRecordKey());
                autoConfirmSignScheduledThreadPool.schedule(() -> autoConfirmSign(context.getEcontractRecordEntity().getRecordKey()), MccConfig.getDelayTimeToAutoConfirmSign(), TimeUnit.SECONDS);
            } else {
                LOGGER.error("SmsExecutor#sendMessage, auto confirm sign error, autoConfirmSignScheduledThreadPool任务数量超过{}, 提交失败, recordKey: {}", MccConfig.getAutoConfirmSignMaxConcurrentSize(), context.getEcontractRecordEntity().getRecordKey());
                DaxiangUtil.push("<EMAIL>", ProcessInfoUtil.getHostEnv() + "环境, 自动确认签约任务提交失败, trace: " + Tracer.id(), MccConfig.getDaXiangAlarmMisList());
            }
        }

        // 以前的短信失效掉
        List<EcontractSmsDealEntity> smsDealEntityList = smsDealEntityMapper
                .queryDealByTaskId(context.getTaskContext().getTaskId());
        if (CollectionUtils.isNotEmpty(smsDealEntityList)) {
            smsDealEntityList.stream().forEach(smsDealEntity -> {
                smsDealEntity.setValid(RealNameConstanst.INVALID);
                smsDealEntityMapper.updateByPrimaryKeySelective(smsDealEntity);
            });
        }

        // 生成新的短信失效掉
        EcontractSmsDealEntity smsDealEntity = new EcontractSmsDealEntity();
        Date nowDate = new Date();
        smsDealEntity.setClientId(signerInfoBo.getClientId());
        smsDealEntity.setMobileList(JSON.toJSONString(signerInfoBo.getMobileList()));
        smsDealEntity.setSmsTemplateId(signerInfoBo.getSmsTemplateId());
        smsDealEntity.setDealVersion(dealVersion);
        smsDealEntity.setCtime(nowDate);
        smsDealEntity.setValid(RealNameConstanst.VALID);
        smsDealEntity.setEcontractRecordId(context.getEcontractRecordEntity().getId());
        smsDealEntity.setTaskId(context.getTaskContext().getTaskId());
        // 设置短信过期时间
        // 默认取MCC设置过期天数
        long expireTime = nowDate.getTime() / 1000
                + Integer.valueOf(ConfigUtilAdapter.getString(MccConstant.SIGN_URL_EFFECTIVE_TIME, String.valueOf(24 * 60 * 60)));
        // 如果业务方动态传入过期天数，则使用业务方传入的天数
        if (context.getSignUrlExpireDays() != null && context.getSignUrlExpireDays() > 0) {
            expireTime = nowDate.getTime() / 1000 + context.getSignUrlExpireDays() * ONE_DAY_SECOND;
        }
        smsDealEntity.setExpireTime(expireTime);
        smsDealEntityMapper.insertSelective(smsDealEntity);
        LOGGER.info("insert smsDealEntity {}", JSON.toJSONString(smsDealEntity));
        return true;

    }

    // --------  替换现有的record_batch模糊发短信的功能  --------
    /**
     * recordbatch模式发送短信
     * 1. 首先查询batch是否已生成redis计数器，若没有生成则生成一个技术器，然后操作减1
     * 2. 若生成了，则直接操作减1
     * 3. 若数据已经减为0则直接发送短信
     *
     */
    private void doRecordBatchSendSMS(Long batchId, String mobile, String templateId, Map<String, String> smsParamMap) {
        if (!redisKvService.isLockedByOther(generateKey(batchId))) {
            //TODO 调用客户查询一共有多少个任务
            Long recordCount = 0L;
            redisKvService.tryLock(generateKey(batchId), recordCount, 24 * 60 * 60);
        }

        Long remainder = redisKvService.decrBy(generateKey(batchId), 1L);
        LOGGER.info("batchId:{},redis减1，剩余任务数:{}", batchId, remainder);
        if (remainder == 0) {
            sendMessageService.sms(mobile, templateId, smsParamMap);
        }
    }

    private StoreKey generateKey(Long batchId) {
        return new StoreKey("record_batch_send_sms_key", batchId);
    }

    // ----------  替换现有的record_batch模糊发短信的功能 结束  --------

    private Map<String, String> parseShortUrl(EcontractContext context, String econtractType, SignerInfoBo signerInfoBo, String smsDealVersion) throws InvalidKeyException, BadPaddingException, NoSuchAlgorithmException, IllegalBlockSizeException, NoSuchPaddingException, KmsResultNullException {
        String longUrl = getLongUrl(econtractType, context, signerInfoBo, smsDealVersion);
        Map<String, String> trackUrlMap = track(longUrl, econtractType, signerInfoBo);
        Map<String, String> shortUrlMap = Maps.newHashMap();
        for (Map.Entry<String, String> trackUrl : trackUrlMap.entrySet()) {
            String midUrl = getMidUrl(econtractType, trackUrl.getValue(), signerInfoBo.getMobileList().get(0));
            String shortUrl = parseShortMsg(midUrl);
            shortUrlMap.put(trackUrl.getKey(), shortUrl);
            LOGGER.info("SmsExecutor, short url = {}, mid url = {}, long url = {}, recordKey = {}", shortUrl, midUrl, longUrl, context.getEcontractRecordEntity().getRecordKey());
        }
        LOGGER.info("SmsExecutor#parseShortUrl, shortUrlMap = {}", JSON.toJSONString(shortUrlMap));
        return shortUrlMap;
    }

    /**
     * 获取长连接
     */
    private String getLongUrl(String econtractType, EcontractContext context, SignerInfoBo signerInfoBo, String smsDealVersion) throws IllegalBlockSizeException, InvalidKeyException, BadPaddingException, NoSuchAlgorithmException, NoSuchPaddingException, KmsResultNullException {
        LOGGER.info("SmsExecutor#getLongUrl, econtractType = {}, context = {}, signerInfoBo = {}, smsDealVersion = {}", econtractType, JSON.toJSONString(context), JSON.toJSONString(signerInfoBo), smsDealVersion);
        String privateKey = Kms.getByName(MccConstant.APP_KEY, KmsConstant.REAL_NAME_PRIVATE_KEY);
        String param = CaSignUtil.encrypt3desc(smsDealVersion, privateKey.substring(0, 24));
        // 因安全问题，此次到餐侧合同不在使用中间层，认证页面和签约页面为同一页面，原返回签约页面url改为返回param
        if (TaskConstant.TYPE_DINE_C1.equals(econtractType) || TaskConstant.TYPE_DINE_C2.equals(econtractType)) {
            return param;
        }
        String host = getHostV2(econtractType, context, signerInfoBo);
        LOGGER.info("getLongUrl host:{}", host);
        return host + "?" + "param=" + param;
    }

    /**
     * 获取中间层链接
     * 
     * @return
     */
    private String getMidUrl(String econtractType, String longUrl, String mobile) throws KmsResultNullException, IllegalBlockSizeException,
            InvalidKeyException, BadPaddingException, NoSuchAlgorithmException, NoSuchPaddingException {
        longUrl = String.format("%s&phone=%s", longUrl, mobile);// param2参数追加手机号
        String host = getMidHost(econtractType);
        String privateKey = Kms.getByName(MccConstant.APP_KEY, KmsConstant.REAL_NAME_PRIVATE_KEY);
        String param = CaSignUtil.encrypt3desc(longUrl, privateKey.substring(0, 24));
        return host + "?" + "param=" + param;
    }

    /**
     * 数据埋点
     */
    private Map<String, String> track(String longUrl, String econtractType, SignerInfoBo signerInfoBo) {
        Map<String, String> maps = Maps.newHashMap();
        if (CollectionUtils.isEmpty(signerInfoBo.getChannelList())) {
            maps.put(SMS_KEY, longUrl);
            return maps;
        }

        EcontractEntity econtractEntity = econtractService.queryEcontractByType(econtractType);
        if (econtractEntity == null) {
            maps.put(SMS_KEY, longUrl);
            return maps;
        }

        for (String channel : signerInfoBo.getChannelList()) {
            String channelUrl = String.format("%s&userId=%s&channel=%s", longUrl, econtractEntity.getEcontractUserId(), channel);
            maps.put(channel, channelUrl);
        }
        return maps;
    }

    /**
     * 获取短链链接
     * @param econtractType
     * @param signerInfoBo
     * @return
     */
    private String getHost(String econtractType, SignerInfoBo signerInfoBo) {
        EcontractRelEntity relEntity = econtractRelService.selectBytemplateName(econtractType);
        // 不为空代表已关联签约模板
        if (relEntity != null && StringUtils.isNotBlank(relEntity.getSignPageName())) {
            Set<String> phfTypeSet = Sets.newHashSet(Splitter.on(",").trimResults().splitToList(MccConfig.getPHFSignTempletTypeList()));
            if (phfTypeSet.contains(econtractType)) {
                return ConfigUtilAdapter.getString(MccConstant.SIGN_H5_URL_PHF, MccConstant.SIGN_H5_URL_PHF_DEFAULT);
            }
            return ConfigUtilAdapter.getString(MccConstant.SIGN_H5_URL_V3, MccConstant.SIGN_H5_URL_V3_DEFAULT);
        }
        if (signerInfoBo.getSmsTempletVersion() != null && signerInfoBo.getSmsTempletVersion() == 3) {
            return ConfigUtilAdapter.getString(MccConstant.SIGN_H5_URL_V3, MccConstant.SIGN_H5_URL_V3_DEFAULT);
        } else if (signerInfoBo.getSmsTempletVersion() != null && signerInfoBo.getSmsTempletVersion() == 2) {
            return ConfigUtilAdapter.getString(MccConstant.SIGN_H5_URL_V2, MccConstant.SIGN_H5_URL_V2_DEFAULT);
        } else {
            return ConfigUtilAdapter.getString(MccConstant.SIGN_H5_URL);
        }
    }

    /**
     * 获取短链链接V2
     * @param econtractType
     * @param context
     * @param signerInfoBo
     * @return
     */
    private String getHostV2(String econtractType, EcontractContext context, SignerInfoBo signerInfoBo){
        //签约任务类型
        Integer pdfNumber = 0;
        List<StageBatchInfoBo> stageBatchInfoBoList = context.getStageBatchInfoBoList();
        if(null != stageBatchInfoBoList){
            List<StageBatchInfoBo> pdfStageInfoBoList = stageBatchInfoBoList.stream()
                    .filter(stage -> stage.getStageName().equals("create_pdf")).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(pdfStageInfoBoList)) {
                StageBatchInfoBo pdfStageInfoBo = pdfStageInfoBoList.get(0);
                Map<String, List<PdfContentInfoBo>> pdfContentInfoBoMap = pdfStageInfoBo.getPdfContentInfoBoMap();
                pdfNumber = pdfContentInfoBoMap.size();
            }
            econtractType = context.getEcontractEntity().getEcontractType();
        }
        List<StageInfoBo> stageInfoBoList = context.getStageInfoBoList();
        if(null != stageInfoBoList){
            List<StageInfoBo> pdfStageInfoBoList = stageInfoBoList.stream()
                    .filter(stage -> stage.getStageName().equals("create_pdf")).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(pdfStageInfoBoList)) {
                StageInfoBo pdfStageInfoBo = pdfStageInfoBoList.get(0);
                List<PdfContentInfoBo> pdfContentInfoBoList = pdfStageInfoBo.getPdfContentInfoBoList();
                pdfNumber = pdfContentInfoBoList.size();
            }
        }

        EcontractRelEntity relEntity = econtractRelService.selectBytemplateName(econtractType);
        Set<String> phfTypeSet = Sets.newHashSet(Splitter.on(",").trimResults().splitToList(MccConfig.getPHFSignTempletTypeList()));

        Integer contentTemplateId = extractContentTemplateId(context.getEcontractRecordEntity().getRecordKey());
        LOGGER.info("SmsExecutor#getHostV2, econtractType: {}, contentTemplateId: {}, recordKey: {}, relEntity: {}",
                econtractType, contentTemplateId, context.getEcontractRecordEntity().getRecordKey(), JacksonUtil.writeAsJsonStr(relEntity));
        if (relEntity != null && StringUtils.isNotBlank(relEntity.getSignPageName())) {
            // PDF数大于1 or 拼好饭相关的合同
            if (pdfNumber > 1 || phfTypeSet.contains(econtractType)) {
                Cat.logMetricForCount("sign_url_phf_default");
                return ConfigUtilAdapter.getString(MccConstant.SIGN_H5_URL_PHF, MccConstant.SIGN_H5_URL_PHF_DEFAULT);
            }
            // 批量发起打包的合同
            if (StringUtils.isNotEmpty(context.getRecordBatchId()) && Long.parseLong(context.getRecordBatchId()) > 0) {
                Cat.logMetricForCount("sign_url_phf_default");
                return ConfigUtilAdapter.getString(MccConstant.SIGN_H5_URL_PHF, MccConstant.SIGN_H5_URL_PHF_DEFAULT);
            }
            Cat.logMetricForCount("sign_url_v3_default");
            return ConfigUtilAdapter.getString(MccConstant.SIGN_H5_URL_V3, MccConstant.SIGN_H5_URL_V3_DEFAULT);
        }
        String host = getHostV2BasedOnContentTemplateId(contentTemplateId, context, pdfNumber);
        if (!Strings.isNullOrEmpty(host)) {
            return host;
        }
        if (signerInfoBo.getSmsTempletVersion() != null && signerInfoBo.getSmsTempletVersion() == 3) {
            Cat.logMetricForCount("sign_url_v3_default");
            return ConfigUtilAdapter.getString(MccConstant.SIGN_H5_URL_V3, MccConstant.SIGN_H5_URL_V3_DEFAULT);
        } else if (signerInfoBo.getSmsTempletVersion() != null && signerInfoBo.getSmsTempletVersion() == 2) {
            Cat.logMetricForCount("sign_url_v2_default");
            return ConfigUtilAdapter.getString(MccConstant.SIGN_H5_URL_V2, MccConstant.SIGN_H5_URL_V2_DEFAULT);
        } else {
            Cat.logMetricForCount("sign_url_v1_default");
            return ConfigUtilAdapter.getString(MccConstant.SIGN_H5_URL);
        }
    }

    private Integer extractContentTemplateId(String recordKey) {
        try {
            EcontractRecordEntity recordEntity = recordService.queryRecordByRecordKey(recordKey);
            EcontractContext econtractContext = JacksonUtil.readValue(recordEntity.getEcontractRecordContext(), EcontractContext.class);
            return ContractContentTemplateUtil.extractTemplateIdFromEcontractContext(econtractContext);
        } catch (Exception e) {
            LOGGER.warn("SmsExecutor#extractContentTemplateId, warn, e", e);
            return 0;
        }
    }

    private String getHostV2BasedOnContentTemplateId(Integer contentTemplateId,  EcontractContext context, Integer pdfNumber) {
        try {
            if (contentTemplateId == null || contentTemplateId <= 0) {
                return null;
            }
            EcontractTemplateBaseBo econtractTemplate = econtractTemplateBaseService.getValidEcontractTemplate(contentTemplateId);
            EcontractSignPageTemplateEntity signPageTemplate = signPageTemplateService.selectByPrimaryKey(econtractTemplate.getSignPageTemplateId());
            if (signPageTemplate == null) {
                return null;
            }
            // 批量发起打包的合同
            if (StringUtils.isNotEmpty(context.getRecordBatchId()) && Long.parseLong(context.getRecordBatchId()) > 0) {
                Cat.logMetricForCount("sign_url_phf_default");
                return ConfigUtilAdapter.getString(MccConstant.SIGN_H5_URL_PHF, MccConstant.SIGN_H5_URL_PHF_DEFAULT);
            } else {
                // 直接发起签约且包含多个合同
                if (Objects.nonNull(pdfNumber) && pdfNumber > 1) {
                    Cat.logMetricForCount("sign_url_phf_default");
                    return ConfigUtilAdapter.getString(MccConstant.SIGN_H5_URL_PHF, MccConstant.SIGN_H5_URL_PHF_DEFAULT);
                }
                Cat.logMetricForCount("sign_url_v3_default");
                return ConfigUtilAdapter.getString(MccConstant.SIGN_H5_URL_V3, MccConstant.SIGN_H5_URL_V3_DEFAULT);
            }
        } catch (Exception e) {
            LOGGER.warn("SmsExecutor@querySignPageInfo, warn, contentTemplateId: {}", contentTemplateId, e);
            return null;
        }
    }

    /**
     * 获取中间层host
     * 
     * @return
     */
    private String getMidHost(String econtractType) {
        // 因安全问题，此次到餐侧合同不在使用中间层，认证页面和签约页面为同一页面，原返回签约页面url改为返回param
        if (TaskConstant.TYPE_DINE_C1.equals(econtractType) || TaskConstant.TYPE_DINE_C2.equals(econtractType)) {
            return ConfigUtilAdapter.getString(MccConstant.SIGN_H5_DINE_URL, MccConstant.SIGN_H5_DINE_URL_DEFAULT);
        }
        return ConfigUtilAdapter.getString(MccConstant.SIGN_MID_H5_URL);
    }

    /**
     * 长连接生成短连接
     */
    public String parseShortMsg(String longUrl) {
        if (com.sankuai.meituan.waimai.util.wmzkconfig.ConfigUtilAdapter.getBoolean(SHORT_MSG_KEY, SHORT_MSG_DEFAULT)) {
            ShortUrlResultThrift result = requestShortMsg(longUrl);
            handleException(longUrl, result);
            return UrlConvetUtil.getValidShortUrl(result.getShortUrl());
        }
        return UrlConvetUtil.getValidShortUrl(operateServiceThrift.genShortLink(longUrl));
    }

    /**
     * 请求短连接
     */
    private ShortUrlResultThrift requestShortMsg(String longUrl) {
        ShortUrlRequestThrift request = new ShortUrlRequestThrift();
        request.setAppName(APP_NAME);
        request.setBizType(com.sankuai.meituan.waimai.util.wmzkconfig.ConfigUtilAdapter.getInt(BIZ_TYPE, BIZ_TYPE_DEFAULT));
        request.setExpireDays(MccConfig.getShortUrlExpireDays());
        request.setOriginUrl(longUrl);
        return operateServiceThrift.genShortLinkWithParam(request);
    }

    /**
     * 短连接请求失败报警
     */
    private void handleException(String longUrl, ShortUrlResultThrift result) {
        if (result == null) {
            LOGGER.error("请求短连接返回为空, longurl = {}", longUrl);
            return;
        }

        if (STATUS_FAIL.equals(result.getStatus())) {
            LOGGER.error("请求短连接服务失败, 失败原因:" + result.getMsg() + ", longUrl:" + longUrl);
        }
    }

    /**
     * 获取短链接
     * 
     * @param context
     * @return
     */
    public String getShortLink(EcontractContext context) throws BadPaddingException, NoSuchAlgorithmException,
            IllegalBlockSizeException, KmsResultNullException, NoSuchPaddingException, InvalidKeyException {
        SignerInfoBo signerInfoBo = context.getTaskContext().getStageInfoBo().getSignerInfoBo();
        String dealVersion = DealVersionUtils.getSmsVersion(context.getTaskContext().getTaskId());
        LOGGER.info("获取签约短链接：dealVersion={},recordKey:{}", dealVersion, context.getEcontractRecordEntity().getRecordKey());
        Map<String, String> shortUrlMap = parseShortUrl(context, context.getEcontractEntity().getEcontractType(), signerInfoBo, dealVersion);
        LOGGER.info("获取签约短链接：shortUrlMap={},recordKey:{}", JSON.toJSONString(shortUrlMap), context.getEcontractRecordEntity().getRecordKey());
        if (shortUrlMap != null && !shortUrlMap.isEmpty()){
            //生成短信流水
            EcontractSmsDealEntity smsDealEntity = new EcontractSmsDealEntity();
            Date nowDate = new Date();
            smsDealEntity.setClientId(signerInfoBo.getClientId());
            smsDealEntity.setMobileList(JSON.toJSONString(signerInfoBo.getMobileList()));
            smsDealEntity.setSmsTemplateId(signerInfoBo.getSmsTemplateId());
            smsDealEntity.setDealVersion(dealVersion);
            smsDealEntity.setCtime(nowDate);
            smsDealEntity.setValid(RealNameConstanst.VALID);
            smsDealEntity.setEcontractRecordId(context.getEcontractRecordEntity().getId());
            smsDealEntity.setTaskId(context.getTaskContext().getTaskId());
            // 设置短信过期时间
            // 默认取MCC设置过期天数
            long expireTime = nowDate.getTime() / 1000
                    + Integer.valueOf(ConfigUtilAdapter.getString(MccConstant.SIGN_URL_EFFECTIVE_TIME, String.valueOf(24 * 60 * 60)));
            // 如果业务方动态传入过期天数，则使用业务方传入的天数
            if (context.getSignUrlExpireDays() != null && context.getSignUrlExpireDays() > 0) {
                expireTime = nowDate.getTime() / 1000 + context.getSignUrlExpireDays() * ONE_DAY_SECOND;
            }
            smsDealEntity.setExpireTime(expireTime);
            smsDealEntityMapper.insertSelective(smsDealEntity);
            LOGGER.info("insert smsDealEntity {}, recordKey:{}", JSON.toJSONString(smsDealEntity), context.getEcontractRecordEntity().getRecordKey());
            return shortUrlMap.get(SMS_KEY);
        }
        return Strings.EMPTY;
    }

    private void autoConfirmSign(String recordKey) {
        try {
            LOGGER.info("SmsExecutor#autoConfirmSign, recordKey: {}", recordKey);
            int retryTimes = MccConfig.getRetryTimesToSenselessEffect();
            if (!waitForRecordKeyStatus(recordKey, retryTimes)) {
                LOGGER.warn("SmsExecutor#autoConfirmSign, 重试{}次后仍不满足状态要求, recordKey: {}", MccConfig.getRetryTimesToSenselessEffect(), recordKey);
                sendAlarm("自动生效异常");
                return;
            }
            if (!confirmSignEContract(recordKey)) {
                sendAlarm("确认签约失败");
            }
            econtractMetricService.metricSenselessEffect("无感生效成功");
        } catch (Exception e) {
            LOGGER.error("SmsExecutor#autoConfirmSign, error", e);
            sendAlarm("自动生效异常");
        }
    }

    private boolean checkRecordKeyStatusToConfirmSign(EcontractRecordEntity recordEntity, EcontractContext context) {
        return TaskConstant.CONFIRM_STAMP_TASK.equals(context.getCurrentTaskNode().getTaskType())
                && EcontractRecordConstant.WAIT_CALL_BACK.equals(recordEntity.getEcontractState());
    }

    private boolean waitForRecordKeyStatus(String recordKey, int retryTimes) throws InterruptedException {
        for (int attempt = 0; attempt < retryTimes; ++attempt) {
            Thread.sleep(1000);
            EcontractRecordEntity recordEntity = econtractRecordService.queryRecordByRecordKey(recordKey);
            EcontractContext context = JSON.parseObject(recordEntity.getEcontractRecordContext(), EcontractContext.class);
            if (checkRecordKeyStatusToConfirmSign(recordEntity, context)) {
                return true;
            }
        }
        return false;
    }

    private boolean confirmSignEContract(String recordKey) throws TException, EcontractException {
        BoolResult confirmResult = econtractSignPageBizService.confirmSignEContract(recordKey);
        return confirmResult != null && confirmResult.isValue();
    }

    private void sendAlarm(String message) {
        econtractMetricService.metricSenselessEffect("无感生效失败");
        DaxiangUtil.push("<EMAIL>", ProcessInfoUtil.getHostEnv() + "环境, " + message + ", trace: " + Tracer.id(), MccConfig.getDaXiangAlarmMisList());
    }

}

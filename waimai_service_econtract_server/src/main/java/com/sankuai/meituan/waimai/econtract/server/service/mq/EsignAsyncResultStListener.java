package com.sankuai.meituan.waimai.econtract.server.service.mq;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.google.common.collect.Maps;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.IMessageListener;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.meituan.sankuai.bsi.esign.common.model.dict.NotifyType;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.econtract.server.constants.EcontractAsyncTaskConstant;
import com.sankuai.meituan.waimai.econtract.server.service.SpringBeanUtil;
import com.sankuai.meituan.waimai.econtract.server.service.biz.EcontractMetricService;
import com.sankuai.meituan.waimai.econtract.server.service.engine.task.AsyncTaskHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2022-03-09 20:27
 * Email: <EMAIL>
 * Desc:
 */
@Slf4j
public class EsignAsyncResultStListener implements IMessageListener {

    @Autowired
    private EcontractMetricService econtractMetricService;

    public static HashMap<String, AsyncTaskHandler> asyncHandlerStRepository = Maps.newHashMap();

    public void init() {
        asyncHandlerStRepository.put(NotifyType.SIGN_CONTRACT_ASYNC.toString(),
                (AsyncTaskHandler) SpringBeanUtil.getBean(EcontractAsyncTaskConstant.ASYNC_ESTAMP_ST_HANDLER));
        asyncHandlerStRepository.put(NotifyType.ARCHIVE_CONTRACT_ASYNC.toString(),
                (AsyncTaskHandler) SpringBeanUtil.getBean(EcontractAsyncTaskConstant.ASYNC_FILINGCONTRACT_ST_HANDLER));
    }

    @Override
    public ConsumeStatus recvMessage(MafkaMessage mafkaMessage, MessagetContext context) {
        try {
            log.debug("EsignAsyncResultStListener receive message=[" + mafkaMessage.getBody() + "]  partition=" + mafkaMessage.getParttion());
            JSONObject resultObject = JSONObject.parseObject(mafkaMessage.getBody().toString());
            //识别appid，只处理本服务生产的消息
            String appId = resultObject.getString("appId");
            if (StringUtils.isEmpty(appId) || !ConfigUtilAdapter.getString("esign.appId", "232a4c58ca").equals(appId)) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            log.info("EsignAsyncResultStListener consume message=[" + mafkaMessage.getBody() + "]  partition=" + mafkaMessage.getParttion());
            AsyncTaskHandler asyncTaskHandler = asyncHandlerStRepository.get(resultObject.get("notifyType"));
            if (null == asyncTaskHandler) {
                log.warn("EsignAsyncResultStListener无对应处理器，notifyType:{}", resultObject.get("notifyType"));
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            //handler处理
            asyncTaskHandler.handlerAsyncTask(resultObject);
        } catch (Exception e) {
            log.error("EsignAsyncResultStListener consume error, msg = [" + mafkaMessage.getBody() + "]  partition=" + mafkaMessage.getParttion());
            econtractMetricService.metricEstampStatus("签章失败","异步签章");
            return ConsumeStatus.RECONSUME_LATER;
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }

}

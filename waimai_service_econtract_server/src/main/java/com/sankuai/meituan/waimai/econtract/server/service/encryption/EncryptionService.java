package com.sankuai.meituan.waimai.econtract.server.service.encryption;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.commons.collections.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.google.common.util.concurrent.*;
import com.sankuai.meituan.waimai.econtract.server.config.MccConfig;
import com.sankuai.meituan.waimai.econtract.server.dao.EcontractEncryptionRecordMapper;
import com.sankuai.meituan.waimai.econtract.server.bo.EcontractSubContextBo;
import com.sankuai.meituan.waimai.econtract.server.entity.EcontractEncryptionRecordEntity;
import com.sankuai.meituan.waimai.econtract.server.utils.SubContextUtil;
import com.sankuai.meituan.waimai.econtract.server.exception.EcontractException;
import com.sankuai.meituan.waimai.econtract.server.utils.TairLocker;
import javax.annotation.Nullable;
import javax.annotation.Resource;

/**
 * Created by lixuepeng on 2021/8/17
 */

@Component("EncryptionService")
public class EncryptionService {

    private static final Logger LOGGER = LoggerFactory.getLogger(EncryptionService.class);

    private static String PREFIX = "encryption#doSave#";

    private static final ListeningExecutorService encryptionExecutorService;

    private static final ListeningExecutorService decryptionExecutorService;

    static {
        encryptionExecutorService = MoreExecutors.listeningDecorator(Executors.newFixedThreadPool(MccConfig.encryptionThreadPoolSize()));
        decryptionExecutorService = MoreExecutors.listeningDecorator(Executors.newFixedThreadPool(MccConfig.encryptionThreadPoolSize()));
    }

    @Autowired
    private EcontractEncryptionRecordMapper econtractEncryptionRecordMapper;
    @Resource(name = "econtractTairLocker")
    private TairLocker                      tairLocker;

    public void deleteEncryptionRecord(Integer recordType, Long recordId, String recordColumn) {
        econtractEncryptionRecordMapper.deleteByRecordTypeAndIdAndColumn(recordType, recordId, recordColumn);
    }

    public List<EcontractEncryptionRecordEntity> queryEncrypttionRecordList(String toEncryptField, String toEncryptValue) {
        LOGGER.info("EncryptionService#queryEncryptedValueList toEncryptField:{}, toEncryptValue:{}", toEncryptField, toEncryptValue);
        try {
            String toEncryptValueTmp = toEncryptValue;
            //结果
            List<EcontractEncryptionRecordEntity> encryptionRecordEntityList = Collections.synchronizedList(new ArrayList<>());
            //原始待加密列表
            List<EcontractSubContextBo> subContextBoList = SubContextUtil.assemblySubContext(toEncryptValueTmp);
            //加密后列表
            List<EcontractSubContextBo> encryptedSubContextBoList = Collections.synchronizedList(new ArrayList<>());
            EcontractException econtractException = new EcontractException(EcontractException.SUCCESS_CODE);
            if (CollectionUtils.isNotEmpty(subContextBoList)) {
                CountDownLatch latch = new CountDownLatch(subContextBoList.size());
                for (EcontractSubContextBo subContextBo : subContextBoList) {
                    ListenableFuture<EcontractSubContextBo> future = encryptionExecutorService.submit(new Callable() {
                        @Override
                        public EcontractSubContextBo call() throws Exception {
                            EcontractSubContextBo result = new EcontractSubContextBo();
                            result.setSort(subContextBo.getSort());
                            result.setSubContext(EncryptionUtil.doEncrypt(JSON.toJSONString(subContextBo)));
                            return result;
                        }
                    });
                    Futures.addCallback(future, new FutureCallback<EcontractSubContextBo>() {
                        @Override
                        public void onSuccess(@Nullable EcontractSubContextBo result) {
                            encryptedSubContextBoList.add(result);
                            latch.countDown();
                        }
                        @Override
                        public void onFailure(Throwable t) {
                            econtractException.setErrorCode(EcontractException.SERVER_ERROR);
                            econtractException.setMessage("KMS加密失败 subContextBo=" + JSON.toJSONString(subContextBo));
                            latch.countDown();
                        }
                    });
                }
                latch.await();
            }

            //如果存在加密异常
            if (econtractException.getErrorCode() != EcontractException.SUCCESS_CODE || CollectionUtils.isEmpty(encryptedSubContextBoList)) {
                throw econtractException;
            }
            //进行排序
            Collections.sort(encryptedSubContextBoList, new Comparator<EcontractSubContextBo>() {
                public int compare(EcontractSubContextBo o1, EcontractSubContextBo o2) {
                    return o1.getSort() - o2.getSort();
                }
            });
            //添加结果集
            for (EcontractSubContextBo subContextBo : encryptedSubContextBoList) {
                EcontractEncryptionRecordEntity encryptionRecordEntity = new EcontractEncryptionRecordEntity();
                encryptionRecordEntity.setRecordColumn(toEncryptField);
                encryptionRecordEntity.setRecordEncryption(subContextBo.getSubContext());
                encryptionRecordEntity.setValid((byte) 1);
                encryptionRecordEntityList.add(encryptionRecordEntity);
            }
            return encryptionRecordEntityList;
        } catch (Exception e) {
            LOGGER.error("EncryptionService#queryEncryptedValueList toEncryptField:{}, toEncryptValue:{}", toEncryptField, toEncryptValue, e);
        }
        return new ArrayList<>();
    }
    
    public void doSaveEncryptionRecords(int recordType, long recordId, List<EcontractEncryptionRecordEntity> encryptionRecordEntityList) throws EcontractException {
        LOGGER.info("EncryptionService#doSaveEncryptionRecords recordType:{}, recordId:{}", recordType, recordId);
        for (EcontractEncryptionRecordEntity encryptionRecordEntity : encryptionRecordEntityList) {
            encryptionRecordEntity.setRecordType(recordType);
            encryptionRecordEntity.setRecordId(recordId);
        }
        String lockKey = PREFIX + recordType + "#" + recordId;
        try {
            int retryTime = 0;
            //获取锁三次失败,则跳过加锁过程
            while (!tairLocker.tryLock(lockKey, MccConfig.encryptionTairLockExpireTime()) && retryTime <= 5) {
                retryTime++;
                Thread.sleep(MccConfig.encryptionThreadSleepMillis());
            }
            //存在则删除
            Integer cnt = econtractEncryptionRecordMapper.selectCntByRecordTypeAndId(recordType, recordId);
            if (cnt != null && cnt > 0) {
                econtractEncryptionRecordMapper.deleteByRecordTypeAndId(recordType, recordId);
            }
            //加密后的数据不为空说明重新进行了加密
            if (CollectionUtils.isNotEmpty(encryptionRecordEntityList)) {
                econtractEncryptionRecordMapper.batchInsert(encryptionRecordEntityList);
            }
        } catch (Exception e) {
            LOGGER.error("EncryptionService#doSaveEncryptionRecords recordType:{}, recordId:{}", recordType, recordId, e);
            throw new EcontractException(EcontractException.SERVER_ERROR, "插入密文信息失败");
        } finally {
            tairLocker.unLock(lockKey);
        }
    }

    public String doHandleDecrypt(int recordType, long recordId, String recordColumn) {
        LOGGER.info("EncryptionService#doHandleDecrypt recordType:{}, recordId:{}, recordColumn:{}", recordType, recordId, recordColumn);
        try {
            List<EcontractEncryptionRecordEntity> recordEntityList = econtractEncryptionRecordMapper.selectByRecordTypeAndIdAndColumn(recordType, recordId, recordColumn);
            if (CollectionUtils.isEmpty(recordEntityList)) {
                return "";
            }
            List<EcontractSubContextBo> subContextBoList = Collections.synchronizedList(new ArrayList<>());
            CountDownLatch latch = new CountDownLatch(recordEntityList.size());
            EcontractException econtractException = new EcontractException(EcontractException.SUCCESS_CODE);
            for (EcontractEncryptionRecordEntity recordEntity : recordEntityList) {
                ListenableFuture<String> future = decryptionExecutorService.submit(new Callable() {
                    @Override
                    public String call() throws Exception {
                        return EncryptionUtil.doDecrypt(recordEntity.getRecordEncryption());
                    }
                });
                Futures.addCallback(future, new FutureCallback<String>() {
                    @Override
                    public void onSuccess(@Nullable String result) {
                        subContextBoList.add(JSON.parseObject(result, EcontractSubContextBo.class));
                        latch.countDown();
                    }
                    @Override
                    public void onFailure(Throwable t) {
                        econtractException.setErrorCode(EcontractException.SERVER_ERROR);
                        econtractException.setMessage("KMS解密失败 subContextBo=" + recordEntity.getRecordEncryption());
                        latch.countDown();
                    }
                });
            }
            latch.await();

            //如果存在解密失败的场景
            if (econtractException.getErrorCode() != EcontractException.SUCCESS_CODE) {
                throw econtractException;
            }

            if (subContextBoList.size() != recordEntityList.size()) {
                LOGGER.error("EncryptionService#doHandleDecrypt add操作存在并发问题 recordEntityList:{}, subContextBoList:{}",
                        JSON.toJSONString(recordEntityList), JSON.toJSONString(subContextBoList));
            }

            //开始拼接解密后子文本
            Collections.sort(subContextBoList, new Comparator<EcontractSubContextBo>() {
                public int compare(EcontractSubContextBo o1, EcontractSubContextBo o2) {
                    return o1.getSort() - o2.getSort();
                }
            });

            if (MccConfig.isHandleConcurrentRecord() && isHasDuplicateSortRerod(subContextBoList)) {
                LOGGER.error("EncryptionService#doHandleDecrypt 存在重复的密文记录 subContextBoList:{}", JSON.toJSONString(subContextBoList));
                // 对于存在重复记录的数据进行修复
                List<EcontractSubContextBo> finalSubContextBoList = handleConcurrentRecord(subContextBoList);
                LOGGER.info("EncryptionService#doHandleDecrypt finalSubContextBoList:{}", JSON.toJSONString(finalSubContextBoList));
                StringBuilder resultBuilder = new StringBuilder();
                for (EcontractSubContextBo subContextBo : finalSubContextBoList) {
                    resultBuilder.append(subContextBo.getSubContext());
                }
                return resultBuilder.toString();
            } else {
                StringBuilder resultBuilder = new StringBuilder();
                for (EcontractSubContextBo subContextBo : subContextBoList) {
                    resultBuilder.append(subContextBo.getSubContext());
                }
                return resultBuilder.toString();
            }
        } catch (Exception e) {
            LOGGER.error("EncryptionService#doHandleDecrypt recordType:{}, recordId:{}, recordColumn:{}", recordType, recordId, recordColumn, e);
        }
        return "";
    }

    //是否存在重复的sort的记录
    private boolean isHasDuplicateSortRerod(List<EcontractSubContextBo> subContextBoList) {
        // 获取对象的sort列表
        List<Integer> sortList = subContextBoList.stream().map(EcontractSubContextBo::getSort).collect(Collectors.toList());
        // 获取distinct后的数量
        long count = sortList.stream().distinct().count();
        // 不相等则代表存在重复的sort记录
        return sortList.size() != count;
    }

    private List<EcontractSubContextBo> handleConcurrentRecord(List<EcontractSubContextBo> subContextBoList) {

        List<Integer> exitSortList = new ArrayList<>();
        List<EcontractSubContextBo> finalSubContextBoList = new ArrayList<>();

        for (EcontractSubContextBo subContextBo : subContextBoList) {
            if (!exitSortList.contains(subContextBo.getSort())) {
                exitSortList.add(subContextBo.getSort());
                finalSubContextBoList.add(subContextBo);
            }
        }

        Collections.sort(finalSubContextBoList, new Comparator<EcontractSubContextBo>() {
            public int compare(EcontractSubContextBo o1, EcontractSubContextBo o2) {
                return o1.getSort() - o2.getSort();
            }
        });
        LOGGER.info("EncryptionService#handleConcurrentRecord finalSubContextBoList:{}", JSON.toJSONString(finalSubContextBoList));
        return finalSubContextBoList;
    }
}

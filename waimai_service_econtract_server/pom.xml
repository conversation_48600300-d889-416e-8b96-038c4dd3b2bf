<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>waimai_m_econtract</artifactId>
        <groupId>com.sankuai.meituan.waimai</groupId>
        <version>1.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>waimai_service_econtract_server</artifactId>


    <dependencies>

        <dependency>
            <groupId>org.springframework.amqp</groupId>
            <artifactId>spring-amqp</artifactId>
        </dependency>

        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-all</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
        </dependency>

        <!--pigeon-->
        <dependency>
            <groupId>com.dianping.dpsf</groupId>
            <artifactId>dpsf-net</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>commons-httpclient</groupId>
                    <artifactId>commons-httpclient</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.service.inf</groupId>
                    <artifactId>kms-tls-sdk</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.dianping.squirrel</groupId>
                    <artifactId>squirrel-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javassist</groupId>
                    <artifactId>javassist</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.dianping.squirrel</groupId>
            <artifactId>squirrel-client</artifactId>
        </dependency>

        <!--美团第三方业务服务-->
        <dependency>
            <groupId>com.sankuai.meituan.waimai.kv</groupId>
            <artifactId>group-m</artifactId>
            <version>2.1.0</version>
        </dependency>
        <!--MCC配置读取-->
        <dependency>
            <groupId>com.sankuai.meituan</groupId>
            <artifactId>mtconfig-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.lion</groupId>
            <artifactId>lion-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan</groupId>
            <artifactId>mt-config-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.waimai</groupId>
            <artifactId>waimai_util</artifactId>
            <version>1.0.5-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.elasticsearch</groupId>
                    <artifactId>waimai_util</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>scribe-log4j</artifactId>
                    <groupId>com.meituan.scribe</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.waimai</groupId>
            <artifactId>waimai_db_security</artifactId>
            <version>1.0.0.3</version>
        </dependency>


        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>3.7.7</version>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <version>3.7.7</version>
            <exclusions>
                <exclusion>
                    <groupId>org.mockito</groupId>
                    <artifactId>mockito-core</artifactId>
                </exclusion>
            </exclusions>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy</artifactId>
            <version>1.10.19</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
        </dependency>
        <!-- Spring AOP dependency -->
        <dependency>
            <groupId>cglib</groupId>
            <artifactId>cglib</artifactId>
            <version>2.2</version>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
            <version>1.7.4</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore</artifactId>
            <version>4.4.5</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
            <version>4.5.2</version>
        </dependency>


        <!-- 添加zebra依赖 -->
        <dependency>
            <groupId>com.dianping.zebra</groupId>
            <artifactId>zebra-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.zebra</groupId>
            <artifactId>zebra-ds-monitor-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.zebra</groupId>
            <artifactId>zebra-dao</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-spring</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mchange</groupId>
            <artifactId>c3p0</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-dbcp</groupId>
            <artifactId>commons-dbcp</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-pool</groupId>
            <artifactId>commons-pool</artifactId>
        </dependency>
        <!--日志-->
        <dependency>
            <groupId>com.meituan.inf</groupId>
            <artifactId>xmd-log4j2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.inf</groupId>
            <artifactId>xmd-common-log4j2</artifactId>
        </dependency>
        <!--订阅日志服务-->
        <dependency>
            <groupId>com.meituan.log</groupId>
            <artifactId>scribe-log4j2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>20.0</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <!--pdf工具-->
        <dependency>
            <groupId>com.itextpdf.tool</groupId>
            <artifactId>xmlworker</artifactId>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
        </dependency>


        <!--电子合同客户端-->
        <dependency>
            <groupId>com.sankuai.meituan.waimai</groupId>
            <artifactId>waimai_service_econtract_client</artifactId>
            <version>1.6.28</version>
        </dependency>
        <!--签章平台-->
        <dependency>
            <groupId>com.sankuai.meituan</groupId>
            <artifactId>bsi-esign-sdk</artifactId>
            <version>1.7.8</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>cms-api</artifactId>
            <version>1.7.3</version>
            <exclusions>
                <exclusion>
                    <groupId>com.facebook.swift</groupId>
                    <artifactId>swift-annotations</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.servicecatalog</groupId>
                    <artifactId>api-annotations</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.conch.certify</groupId>
            <artifactId>tokenAccessSdk</artifactId>
            <version>1.0.7-jdk7-RELEASE</version>
            <exclusions>
                <exclusion>
                    <artifactId>lombok</artifactId>
                    <groupId>org.projectlombok</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 生成短连接服务 -->
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>mobile-oss-api</artifactId>
            <version>1.1.2.10</version>
            <exclusions>
                <exclusion>
                    <groupId>javassist</groupId>
                    <artifactId>javassist</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>gson</artifactId>
                    <groupId>com.google.code.gson</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.waimai</groupId>
            <artifactId>waimai_lib_m_util</artifactId>
            <version>1.0.4-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>mt-service-http</artifactId>
                    <groupId>com.sankuai.meituan</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mt-upm-auth</artifactId>
                    <groupId>com.sankuai.meituan</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mtorg-remote-service</artifactId>
                    <groupId>com.sankuai.meituan</groupId>
                </exclusion>
                <exclusion>
                    <groupId>commons-httpclient</groupId>
                    <artifactId>commons-httpclient</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-aspects</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.waimai</groupId>
            <artifactId>bizsso-client</artifactId>
            <version>1.7.5</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan</groupId>
            <artifactId>mt-service-http</artifactId>
            <version>1.3.22</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.waimai.contract</groupId>
            <artifactId>waimai_service_contract_client</artifactId>
            <version>1.3.3</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.mafka</groupId>
            <artifactId>mafka-client_2.10</artifactId>
        </dependency>
        <!-- 分布式调度 crane-->
        <dependency>
            <groupId>com.cip.crane</groupId>
            <artifactId>crane-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.waimai.customer</groupId>
            <artifactId>waimai_service_customer_client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meituan.service.inf</groupId>
            <artifactId>kms-pangolin-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.inf</groupId>
            <artifactId>idl-kms</artifactId>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
            <version>1.60</version>
        </dependency>
        <dependency>
            <groupId>csc-pirate</groupId>
            <artifactId>csc-pirate-client</artifactId>
            <version>2.1.8</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-collections</artifactId>
                    <groupId>commons-collections</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>groovy-all</artifactId>
                    <groupId>org.codehaus.groovy</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>squirrel-client</artifactId>
                    <groupId>com.dianping.squirrel</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>gson</artifactId>
                    <groupId>com.google.code.gson</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-core</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.github.rholder</groupId>
            <artifactId>guava-retrying</artifactId>
            <version>2.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.meituan.mtrace</groupId>
            <artifactId>mtrace</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mtrace</groupId>
            <artifactId>mtrace-agent</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mtrace</groupId>
            <artifactId>idl-mtrace</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mtrace</groupId>
            <artifactId>mtrace-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mtrace</groupId>
            <artifactId>mtrace-http</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mtrace</groupId>
            <artifactId>mtrace-zebra</artifactId>
        </dependency>
        <!--配送履约服务-->
        <dependency>
            <groupId>com.sankuai.meituan.banma.deliverycontract</groupId>
            <artifactId>delivery-contract-platform-process-sdk</artifactId>
            <version>1.0.38</version>
        </dependency>
        <!--配送范围服务-->
        <dependency>
            <groupId>com.sankuai.meituan.banma.business</groupId>
            <artifactId>banma_service_poi_sp_area_manage_client</artifactId>
            <version>1.0.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.eclipse.jetty.orbit</groupId>
                    <artifactId>javax.annotation</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--配送范围服务-->
        <!--新配送范围服务-->
        <dependency>
            <groupId>com.sankuai.meituan.banma.business</groupId>
            <artifactId>banma_service_poi_sparea_client</artifactId>
            <version>1.0.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.eclipse.jetty.orbit</groupId>
                    <artifactId>javax.annotation</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-web</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-actuator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-jetty</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>jetty-servlet</artifactId>
                    <groupId>org.eclipse.jetty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>validation-api</artifactId>
                    <groupId>javax.validation</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>hibernate-validator</artifactId>
                    <groupId>org.hibernate.validator</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--新配送范围服务-->

        <dependency>
            <groupId>com.sankuai.meituan.banma.deliveryproduct</groupId>
            <artifactId>banma_service_deliveryproduct_client</artifactId>
            <version>1.0.14</version><!-- 以上线前发布的正式版为准 -->
        </dependency>


        <dependency>
            <groupId>com.meituan.xframe</groupId>
            <artifactId>thrift-xframe-boot-starter</artifactId>
            <version>2.6.5</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.waimai</groupId>
            <artifactId>async-framework</artifactId>
            <version>2.1.4</version>
        </dependency>
        <!-- sso-sdk新版本 -->
        <dependency>
            <groupId>com.sankuai.it.sso</groupId>
            <artifactId>sso-java-sdk</artifactId>
            <version>2.5.5</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.waimai.infra</groupId>
            <artifactId>waimai_service_infra_client</artifactId>
            <version>1.1.58</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan</groupId>
            <artifactId>uac-common-sdk</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>mt-service-http</artifactId>
                    <groupId>com.sankuai.meituan</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-core</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-slf4j-impl</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-1.2-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.tsp</groupId>
            <artifactId>pecker-trace</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.waimai</groupId>
            <artifactId>waimai_digger_client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan</groupId>
            <artifactId>mtcoop-thrift-client</artifactId>
            <version>2.1.16</version>
        </dependency>

        <!--pdf转图片-->
        <dependency>
            <groupId>org.jpedal</groupId>
            <artifactId>jpedal-lgpl</artifactId>
            <version>4.74b27</version>
            <exclusions>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcmail-jdk15</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcprov-jdk15</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--  到家到店ST环境路由转发 -->
        <dependency>
            <groupId>com.sankuai.order.route</groupId>
            <artifactId>stenv-routing</artifactId>
        </dependency>

        <!--  海螺模板管理 -->
        <dependency>
            <groupId>com.meituan.it</groupId>
            <artifactId>contract-platform-api</artifactId>
        </dependency>

        <!--  海螺合同管理 -->
        <dependency>
            <groupId>com.meituan.it</groupId>
            <artifactId>contract-application-api</artifactId>
        </dependency>

        <!-- Apache POI - 用于Word文档处理 -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-scratchpad</artifactId>
            <version>3.17</version>
        </dependency>

        <!-- XDocReport - 用于HTML到Word的转换 -->
        <dependency>
            <groupId>fr.opensagres.xdocreport</groupId>
            <artifactId>fr.opensagres.xdocreport.document.docx</artifactId>
        </dependency>
        <dependency>
            <groupId>fr.opensagres.xdocreport</groupId>
            <artifactId>fr.opensagres.xdocreport.converter.docx.xwpf</artifactId>
        </dependency>

        <!-- Jsoup - 用于HTML解析 -->
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.3.2</version>
                <configuration>
                    <verbose>true</verbose>
                    <overwrite>true</overwrite>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>2.1.1</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>install</id>
                        <phase>install</phase>
                        <goals>
                            <goal>sources</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.1</version>
                <configuration>
                    <skipTests>false</skipTests>
                    <junitArtifactName>junit:junit</junitArtifactName>
                    <argLine>-Xmx2048m</argLine>
                    <excludes>
                        <exclude>**/*_Roo_*</exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <version>1.5</version>
                <executions>

                </executions>
            </plugin>
            <plugin>
                <groupId>org.mortbay.jetty</groupId>
                <artifactId>maven-jetty-plugin</artifactId>
                <version>6.1.10</version>
                <configuration>
                    <connectors>
                        <connector implementation="org.mortbay.jetty.nio.SelectChannelConnector">
                            <port>9000</port>
                        </connector>
                    </connectors>
                    <stopPort>9090</stopPort>
                    <stopKey>a</stopKey>
                    <!-- <scanIntervalSeconds>10</scanIntervalSeconds> -->
                    <contextPath>/</contextPath>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>tomcat-maven-plugin</artifactId>
                <version>1.1</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>2.9.1</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>2.4</version>
                <configuration>
                    <archive>
                        <manifest>
                            <mainClass>com.sankuai.meituan.waimai.econtract.server.boot.EcontractServer</mainClass>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>

            <plugin>
                <groupId>com.sankuai.inf</groupId>
                <artifactId>xmdlog-maven-plugin</artifactId>
                <version>1.1.3-SNAPSHOT</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>check</goal>
                        </goals>
                        <phase>compile</phase>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <finalName>waimai_service_econtract_server</finalName>
    </build>
    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <package.environment>develop</package.environment>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <resources>
                    <resource>
                        <directory>src/main/resources-dev</directory>
                    </resource>
                    <resource>
                        <directory>src/main/resources</directory>
                    </resource>
                </resources>
            </build>
        </profile>
        <profile>
            <id>local</id>
            <properties>
                <package.environment>develop</package.environment>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>

        <profile>
            <id>stage</id>
            <properties>
                <package.environment>stage</package.environment>
            </properties>
            <build>
                <resources>
                    <resource>
                        <directory>src/main/resources-stage</directory>
                    </resource>
                    <resource>
                        <directory>src/main/resources</directory>
                    </resource>
                </resources>
            </build>
        </profile>


        <profile>
            <id>prod</id>
            <properties>
                <package.environment>product</package.environment>
            </properties>
            <build>
                <resources>
                    <resource>
                        <directory>src/main/resources-prod</directory>
                    </resource>
                    <resource>
                        <directory>src/main/resources</directory>
                    </resource>
                </resources>
            </build>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <package.environment>test</package.environment>
            </properties>
            <build>
                <resources>
                    <resource>
                        <directory>src/main/resources-test</directory>
                    </resource>
                    <resource>
                        <directory>src/main/resources</directory>
                    </resource>
                </resources>
            </build>
        </profile>
        <profile>
            <id>beta</id>
            <properties>
                <package.environment>beta</package.environment>
            </properties>
            <build>
                <resources>
                    <resource>
                        <directory>src/main/resources-beta</directory>
                    </resource>
                    <resource>
                        <directory>src/main/resources</directory>
                    </resource>
                </resources>
            </build>
        </profile>
    </profiles>


</project>

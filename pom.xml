<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.sankuai.meituan.waimai</groupId>
    <artifactId>waimai_m_econtract</artifactId>
    <packaging>pom</packaging>
    <version>1.1-SNAPSHOT</version>
    <modules>
        <module>waimai_service_econtract_server</module>
        <module>waimai_service_econtract_client</module>
        <module>waimai_m_econtract_web</module>
    </modules>


    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.sankuai</groupId>
                <artifactId>inf-bom</artifactId>
                <version>1.13.3</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.sankuai.waimai.contract</groupId>
                <artifactId>info-bom</artifactId>
                <version>1.0.1</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan</groupId>
                <artifactId>mt-config-api</artifactId>
                <version>1.3.7</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>2.10.2</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>2.10.2</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>2.10.2</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.module</groupId>
                <artifactId>jackson-module-jsonSchema</artifactId>
                <version>2.10.2</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.servicecatalog</groupId>
                <artifactId>api-annotations</artifactId>
                <version>1.0.7</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.83_noneautotype</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.rhino</groupId>
                <artifactId>rhino-extend</artifactId>
                <version>1.7.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.zebra</groupId>
                <artifactId>zebra-calcite</artifactId>
                <version>3.3.1</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.zebra</groupId>
                <artifactId>zebra-tool</artifactId>
                <version>3.3.1</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.amqp</groupId>
                <artifactId>spring-amqp</artifactId>
                <version>1.7.10.RELEASE</version>
            </dependency>

            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-all</artifactId>
                <version>2.4.8</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch</artifactId>
                <version>5.6.3</version>
            </dependency>

            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>1.3.3</version>
            </dependency>

            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>3.2.2</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan.waimai.poi</groupId>
                <artifactId>waimai_service_poi_client</artifactId>
                <version>1.9.45</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.waimai.customer</groupId>
                <artifactId>waimai_service_customer_client</artifactId>
                <version>2.1.16</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan</groupId>
                <artifactId>uac-common-sdk</artifactId>
                <version>1.1.32</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.tsp</groupId>
                <artifactId>pecker-trace</artifactId>
                <version>1.3.20</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.waimai</groupId>
                <artifactId>waimai_digger_client</artifactId>
                <version>1.9.7</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan.waimai</groupId>
                <artifactId>waimai_util</artifactId>
                <version>1.0.5-SNAPSHOT</version>
            </dependency>

            <!--  到家到店ST环境路由转发 -->
            <dependency>
                <groupId>com.sankuai.order.route</groupId>
                <artifactId>stenv-routing</artifactId>
                <version>1.0.3</version>
            </dependency>

            <!--  海螺模板管理 -->
            <dependency>
                <groupId>com.meituan.it</groupId>
                <artifactId>contract-platform-api</artifactId>
                <version>2.7.8</version>
            </dependency>

            <!--  海螺合同管理 -->
            <dependency>
                <groupId>com.meituan.it</groupId>
                <artifactId>contract-application-api</artifactId>
                <version>1.7.18</version>
            </dependency>
            <!-- Apache POI - 用于Word文档处理 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>3.17</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>3.17</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-scratchpad</artifactId>
                <version>3.17</version>
            </dependency>

            <!-- XDocReport - 用于HTML到Word的转换 -->
            <dependency>
                <groupId>fr.opensagres.xdocreport</groupId>
                <artifactId>fr.opensagres.xdocreport.document.docx</artifactId>
                <version>2.0.2</version>
            </dependency>
            <dependency>
                <groupId>fr.opensagres.xdocreport</groupId>
                <artifactId>fr.opensagres.xdocreport.converter.docx.xwpf</artifactId>
                <version>2.0.2</version>
            </dependency>

            <!-- Jsoup - 用于HTML解析 -->
            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>1.14.3</version>
            </dependency>

            <dependency>
                <groupId>com.meituan.mafka</groupId>
                <artifactId>mafka-client_2.10</artifactId>
                <version>3.10.0-RC6</version>
            </dependency>

            <!-- rhino线程池 -->
            <dependency>
                <groupId>com.dianping.rhino</groupId>
                <artifactId>rhino-client</artifactId>
                <version>1.6.2</version>
            </dependency>
        </dependencies>
    </dependencyManagement>


    <dependencies>
        <dependency>
            <groupId>com.sankuai.waimai.contract</groupId>
            <artifactId>info-bom</artifactId>
            <version>1.0.1</version>
            <type>pom</type>
        </dependency>

    </dependencies>



    <distributionManagement>
        <repository>
            <id>meituan-nexus-releases</id>
            <name>Meituan Nexus Repository</name>
            <url>http://maven.sankuai.com/nexus/content/repositories/releases/</url>
        </repository>
        <snapshotRepository>
            <id>meituan-nexus-snapshots</id>
            <name>Meituan Nexus Repository</name>
            <url>http://maven.sankuai.com/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>

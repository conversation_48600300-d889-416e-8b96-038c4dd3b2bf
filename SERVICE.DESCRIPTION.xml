<serviceCatalog
        xmlns="http://service.sankuai.com/1.0.1"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://service.sankuai.com/1.0.1
            http://pixel.sankuai.com/repository/releases/com/meituan/apidoc/servicecatalog/1.0.1/servicecatalog-1.0.1.xsd">
    <serviceDescs>

        <!--
            该示例描述的是使用 Restful 服务框架的情况下如何进行文档编写。该类型的编写方式和普通 java 的一样，都是通过注解的方式在
            代码中进行相关文档的描述。接入文档可以参考：https://km.sankuai.com/page/60715770
            可以将 <serviceDesc> 中 name, description, scenarios 等信息以 @ServiceDoc 的注解在代码中标注，也可以直接
            按下面这种方式进行编写。
        -->
        <serviceDesc>
            <appkey>com.sankuai.waimai.m.econtractserver</appkey>
            <name>电子合同平台接入接口文档</name>
            <description>Thrift 注解服务文档编写示例，直接在使用了 Thrift 注解的代码中进行文档的编写</description>
            <scenarios>服务文档示例</scenarios>
            <interfaceDescs>
                <interfaceDesc>
                    <type>octo.thrift.annotation</type>
                    <class>com.sankuai.meituan.waimai.econtrct.client.service.EcontractAPIService</class>
                </interfaceDesc>
                <interfaceDesc>
                    <type>octo.thrift.annotation</type>
                    <class>com.sankuai.meituan.waimai.econtrct.client.service.EcontractUserManagerService</class>
                </interfaceDesc>
                <interfaceDesc>
                    <type>octo.thrift.annotation</type>
                    <class>
                        com.sankuai.meituan.waimai.econtrct.client.template.config.service.EcontractTemplateConfigThriftService
                    </class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>octo.thrift.annotation</type>
                    <class>com.sankuai.meituan.waimai.econtrct.client.service.EcontractManagerService</class>
                </interfaceDesc>
            </interfaceDescs>
        </serviceDesc>

        <serviceDesc>
            <appkey>com.sankuai.waimai.m.econtractweb</appkey>
            <name>电子合同web服务</name>
            <description>电子合同web服务接口文档</description>
            <scenarios>电子合同相关</scenarios>
            <interfaceDescs>
                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.waimai.econtract.web.controller.EcontractSignController</class>
                </interfaceDesc>
                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.waimai.econtract.web.controller.DownLoadController</class>
                </interfaceDesc>
                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.waimai.econtract.web.controller.template.config.EcontractConfigTemplateController</class>
                </interfaceDesc>
                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.waimai.econtract.web.controller.template.config.EcontractConfigVersionController</class>
                </interfaceDesc>
                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.waimai.econtract.web.controller.template.config.EcontractConfigCommonController</class>
                </interfaceDesc>
                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.waimai.econtract.web.controller.UiComponentController</class>
                </interfaceDesc>
            </interfaceDescs>
        </serviceDesc>

    </serviceDescs>

</serviceCatalog>
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>waimai_m_econtract</artifactId>
        <groupId>com.sankuai.meituan.waimai</groupId>
        <version>1.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.sankuai.meituan.waimai</groupId>
    <artifactId>waimai_service_econtract_client</artifactId>
    <version>1.6.28</version>
    <dependencies>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.thrift</groupId>
            <artifactId>libthrift</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.facebook.swift</groupId>
            <artifactId>swift-annotations</artifactId>
            <version>0.16.0-mt-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>20.0</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.servicecatalog</groupId>
            <artifactId>api-annotations</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.mobile</groupId>
            <artifactId>mtthrift</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>javassist</groupId>
                    <artifactId>javassist</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.meituan.mtrace</groupId>
            <artifactId>mtrace</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mtrace</groupId>
            <artifactId>mtrace-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mtrace</groupId>
            <artifactId>mtrace-agent</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mtrace</groupId>
            <artifactId>idl-mtrace</artifactId>
        </dependency>
        <!--工具类-->
        <dependency>
            <groupId>com.sankuai.meituan.waimai</groupId>
            <artifactId>waimai_util</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.elasticsearch</groupId>
                    <artifactId>elasticsearch</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.dianping.dpsf</groupId>
                    <artifactId>dpsf-net</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan</groupId>
            <artifactId>mtconfig-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping.lion</groupId>
                    <artifactId>lion-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.lion</groupId>
            <artifactId>lion-client</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>2.3.2</version>
                <configuration>
                    <source>1.7</source>
                    <target>1.7</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>dev</id> <!-- 这里的 id 需要配置到部署模板中 -->
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <version.subfix>-SNAPSHOT</version.subfix>
            </properties>
            <distributionManagement>
                <repository>
                    <id>meituan-offline-releases</id>
                    <name>Meituan Nexus Repository</name>
                    <url>http://maven.offline.sankuai.com/nexus/content/repositories/releases/</url>
                </repository>
                <snapshotRepository>
                    <id>meituan-offline-snapshots</id>
                    <name>Meituan Nexus Repository</name>
                    <url>http://maven.offline.sankuai.com/nexus/content/repositories/snapshots/</url>
                </snapshotRepository>
            </distributionManagement>
        </profile>
        <profile>
            <id>test</id> <!-- 这里的 id 需要配置到部署模板中 -->
            <properties>
                <version.subfix>-SNAPSHOT</version.subfix>
            </properties>
            <distributionManagement>
                <repository>
                    <id>meituan-offline-releases</id>
                    <name>Meituan Nexus Repository</name>
                    <url>http://maven.offline.sankuai.com/nexus/content/repositories/releases/</url>
                </repository>
                <snapshotRepository>
                    <id>meituan-offline-snapshots</id>
                    <name>Meituan Nexus Repository</name>
                    <url>http://maven.offline.sankuai.com/nexus/content/repositories/snapshots/</url>
                </snapshotRepository>
            </distributionManagement>
        </profile>
        <profile>
            <id>prod</id> <!-- 这里的 id 需要配置到部署模板中 -->
            <properties>
                <version.subfix></version.subfix>
            </properties>
            <distributionManagement>
                <repository>
                    <id>meituan-nexus-releases</id>
                    <name>Meituan Nexus Repository</name>
                    <url>http://pixel.sankuai.com/repository/releases</url>
                </repository>
                <snapshotRepository>
                    <id>meituan-nexus-snapshots</id>
                    <name>Meituan Nexus Repository</name>
                    <url>http://pixel.sankuai.com/repository/snapshots</url>
                </snapshotRepository>
            </distributionManagement>
        </profile>
    </profiles>

</project>

package com.sankuai.meituan.waimai.econtrct.client.domain.applybiz;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.sankuai.meituan.waimai.econtrct.client.domain.biz.EcontractContentBo;

import java.util.List;

import lombok.NoArgsConstructor;

/**
 * 复杂对象
 * 配送范围展示信息
 */
@ThriftStruct
@NoArgsConstructor
public class WmPoiSpAreaContentBo extends EcontractContentBo {

    /**
     * 门店id
     */
    private Integer wmPoiId;

    /**
     * 门店名称
     */
    private String wmPoiName;

    /**
     * 描述
     */
    private String desc;

    /**
     * 门店坐标
     */
    private WmPoiLocation centerPoint;

    /**
     * 配送区域列表(兼容旧结构)
     */
    @Deprecated
    private List<WmPoiAreaBo> areaBoList;

    /**
     * SLA说明
     */
    private String slaText;

    /**
     * 配送方案（时段）列表
     */
    private List<WmPoiDeliveryPlanBo> deliveryPlanBoList;


    private WmPoiSpAreaContentBo(Builder builder) {
        setId(builder.id);
        setType(builder.type);
        setContent(builder.content);
        setWmPoiId(builder.wmPoiId);
        setWmPoiName(builder.wmPoiName);
        setDesc(builder.desc);
        setCenterPoint(builder.centerPoint);
        setAreaBoList(builder.areaBoList);
        setSlaText(builder.slaText);
        setDeliveryPlanBoList(builder.deliveryPlanBoList);
    }


    @ThriftField(4)
    public Integer getWmPoiId() {
        return this.wmPoiId;
    }

    @ThriftField
    public void setWmPoiId(Integer wmPoiId) {
        this.wmPoiId = wmPoiId;
    }

    @ThriftField(5)
    public String getWmPoiName() {
        return this.wmPoiName;
    }

    @ThriftField
    public void setWmPoiName(String wmPoiName) {
        this.wmPoiName = wmPoiName;
    }

    @ThriftField(6)
    public String getDesc() {
        return this.desc;
    }

    @ThriftField
    public void setDesc(String desc) {
        this.desc = desc;
    }

    @ThriftField(7)
    public WmPoiLocation getCenterPoint() {
        return this.centerPoint;
    }

    @ThriftField
    public void setCenterPoint(WmPoiLocation centerPoint) {
        this.centerPoint = centerPoint;
    }

    @ThriftField(8)
    public List<WmPoiAreaBo> getAreaBoList() {
        return this.areaBoList;
    }

    @ThriftField
    @Deprecated
    public void setAreaBoList(List<WmPoiAreaBo> areaBoList) {
        this.areaBoList = areaBoList;
    }

    @ThriftField(9)
    public String getSlaText() {
        return slaText;
    }

    @ThriftField
    public void setSlaText(String slaText) {
        this.slaText = slaText;
    }

    @ThriftField(10)
    public List<WmPoiDeliveryPlanBo> getDeliveryPlanBoList() {
        return deliveryPlanBoList;
    }

    @ThriftField
    public void setDeliveryPlanBoList(List<WmPoiDeliveryPlanBo> deliveryPlanBoList) {
        this.deliveryPlanBoList = deliveryPlanBoList;
    }


    public static final class Builder {

        private String id;
        private String type;
        private String content;
        private Integer wmPoiId;
        private String wmPoiName;
        private String desc;
        private WmPoiLocation centerPoint;
        private List<WmPoiAreaBo> areaBoList;
        private String slaText;
        private List<WmPoiDeliveryPlanBo> deliveryPlanBoList;

        public Builder() {
        }

        public Builder id(String val) {
            id = val;
            return this;
        }

        public Builder type(String val) {
            type = val;
            return this;
        }

        public Builder content(String val) {
            content = val;
            return this;
        }

        public Builder wmPoiId(Integer val) {
            wmPoiId = val;
            return this;
        }

        public Builder wmPoiName(String val) {
            wmPoiName = val;
            return this;
        }

        public Builder desc(String val) {
            desc = val;
            return this;
        }

        public Builder centerPoint(WmPoiLocation val) {
            centerPoint = val;
            return this;
        }

        @Deprecated
        public Builder areaBoList(List<WmPoiAreaBo> val) {
            areaBoList = val;
            return this;
        }

        public Builder slaText(String val) {
            slaText = val;
            return this;
        }

        public Builder deliveryPlanBoList(List<WmPoiDeliveryPlanBo> val) {
            deliveryPlanBoList = val;
            return this;
        }

        public WmPoiSpAreaContentBo build() {
            return new WmPoiSpAreaContentBo(this);
        }
    }
}

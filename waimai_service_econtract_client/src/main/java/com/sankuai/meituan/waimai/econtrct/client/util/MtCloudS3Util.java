package com.sankuai.meituan.waimai.econtrct.client.util;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.S3ClientOptions;
import com.amazonaws.services.s3.model.GetObjectRequest;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import com.sankuai.meituan.util.ConfigUtilAdapter;

import com.sankuai.meituan.util.HttpUtil;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

/**
 * copy from MtCloudFileUtil
 * 用于集群迁移
 */
public class MtCloudS3Util {

    //老的美团云集群
    private static final String SWIFT_MT_PUBLIC_DOMAIN = "http://mss.sankuai.com";
    private static final String SWIFT_MT_INTERNAL_DOMAIN = "http://mss.vip.sankuai.com";

    //新的美团云集群
    private static final String S3PLUS_MT_PUBLIC_DOMAIN = "https://s3plus.sankuai.com";
    private static final String S3PLUS_MT_INTERNAL_DOMAIN = "http://s3plus.vip.sankuai.com";

    private static final String READ_ROUTE_TOS3PLUST_KEY = "read_route_s3plus";
    private static final String WRITE_ROUTE_TOS3PLUST_KEY = "write_route_s3plus";

    private static final String MT_CLOUD_TENANT = "mt_tenant_2230562";
    private static final String MT_CLOUD_STORE = "wm_imgstore_intern";
    private static final String MT_CLOUD_TKEY = "f9NAUp2N9gVn";
    private static final String MID_PATH = "/download/mos/";
    private static final Integer RETRY_TIMES = 5;
    public static final int BUFFER = 1024;
    private static Logger log = LoggerFactory.getLogger(MtCloudS3Util.class);
    private static final String[] hexDigits = new String[]{"0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "a", "b", "c", "d", "e", "f"};

    /**
     * 临时文件下载并上传至美团云，生成目标文件
     * @param tempUrl 临时文件地址
     * @param fileName 目标文件地址
     * @return
     */
    public static String retryTransTempUrl2MtCloudUrl(String tempUrl, String fileName) {
        byte[] bytes = retryDownloadByTempUrl(tempUrl);
        return retryUploadFileFromBytes(bytes, fileName);
    }

    /**
     * 多次重试上传文件到美团云
     * @param myByteArray 二进制数据
     * @param filename 目标文件地址
     * @return
     */
    public static String retryUploadFileFromBytes(byte[] myByteArray, String filename) {
        for (int i = 0; i < RETRY_TIMES; i++) {
            try {
                return uploadFileFromBytes(myByteArray, filename);
            } catch (Exception e) {
                log.error("上传文件到美团云失败，filename:{}", filename, e);
            }
        }
        throw new RuntimeException("重试" + RETRY_TIMES + "次上传文件到美团云失败，filename:" + filename);
    }

    /**
     * 通过二进制数组上传文件
     * @param myByteArray 二进制数组
     * @param filename 文件名
     * @return 外卖download路径
     */
    public static String uploadFileFromBytes(byte[] myByteArray, String filename) {
        return uploadFileFromBytes(myByteArray, filename, (String)null);
    }

    /**
     * 通过二进制数组上传文件
     * @param myByteArray 二进制数组
     * @param filename 文件名
     * @param expectName 期望名称
     * @return 外卖download路径
     */
    public static String uploadFileFromBytes(byte[] myByteArray, String filename, String expectName) {
        String dir = ConfigUtilAdapter.getString("mos.upload.dir", "/tmp/mos/");
        File dirFile = new File(dir);
        if (!dirFile.exists()) {
            dirFile.mkdir();
        }

        File mos = new File(dir + filename);
        FileOutputStream fos = null;

        try {
            fos = new FileOutputStream(mos);
            fos.write(myByteArray);
        } catch (Exception e) {
            log.error("请检查硬盘情况，路径: " + dir, e);
        } finally {
            try {
                fos.close();
            } catch (IOException e) {
                log.error("请检查硬盘情况[2]，路径: " + dir, e);
            }
        }

        return uploadFileFromFile(mos, expectName);
    }

    /**
     * 通过本地文件上传到美团云
     * @param file 文件
     * @return 外卖download路径
     */
    public static String uploadFileFromFile(File file) {
        return uploadFileFromFile(file, (String)null);
    }

    /**
     * 通过本地文件上传到美团云
     * @param file 文件
     * @param expectName 期望名称
     * @return 外卖download路径
     */
    public static String uploadFileFromFile(File file, String expectName) {
        String md5Name = getMD5code(file.getName());
        int pos = file.getName().lastIndexOf(".");
        if (pos != -1) {
            md5Name = md5Name + file.getName().substring(pos);
        }

        AWSCredentials
            credentials = new BasicAWSCredentials("8d75743d3eef4021a09a7fa3f7ee2035", "3e897d63d5ce40d2acb6b19197db517c");
        ClientConfiguration clientConfig = new ClientConfiguration();
        clientConfig.setProtocol(Protocol.HTTP);
        clientConfig.setSocketTimeout(6000000);
        clientConfig.setConnectionTimeout(6000000);
        S3ClientOptions s3ClientOptions = new S3ClientOptions();
        s3ClientOptions.setPathStyleAccess(true);
        AmazonS3 conn = new AmazonS3Client(credentials, clientConfig);
        conn.setS3ClientOptions(s3ClientOptions);
        conn.setEndpoint(getInnerHost(writeRouteToS3Plus()));
        PutObjectRequest putObjectRequest = new PutObjectRequest("wm_imgstore_intern", md5Name, file);

        try {
            if (StringUtils.isNotBlank(expectName)) {
                if (expectName.indexOf(",") > -1) {
                    throw new RuntimeException("expectName含有非法字符将会导致下载异常！");
                }
                ObjectMetadata objectMetadata = new ObjectMetadata();

                try {
                    objectMetadata.setContentDisposition("attachment;filename=" + new String(expectName.getBytes("utf-8"), "iso-8859-1"));
                } catch (UnsupportedEncodingException e) {
                    log.error("expect文件名太奇怪了:" + expectName, e);
                }
                putObjectRequest.setMetadata(objectMetadata);
            }

            conn.putObject(putObjectRequest);
        } finally {
            try {
                file.delete();
            } catch (Exception e) {
                log.error("美团云图片上传完后本地无法清除", e);
            }

        }

        String url = "/download/mos/" + file.getName();
        return url;
    }

    /**
     * 通过临时URL下载文件
     * @param tempUrl 临时URL
     * @return
     */
    public static byte[] retryDownloadByTempUrl(String tempUrl) {
        for (int i = 0; i < RETRY_TIMES; i++) {
            try {
                byte[] result = HttpUtil.getByteResult(tempUrl);
                if(result != null){
                    log.info("retryDownloadByTempUrl result length:{},tempUrl:{}",result.length,tempUrl);
                } else {
                    log.info("retryDownloadByTempUrl result is null,tempUrl:{}",tempUrl);
                }
                return result;
            } catch (Exception e) {
                log.error("从美团云下载文件失败, tempUrl:{}", tempUrl, e);
            }
        }
        throw new RuntimeException("重试" + RETRY_TIMES + "次从美团云下载文件失败，tempUrl:" + tempUrl);
    }

    /**
     * 从美团云获取文件
     * @param fileName 文件名
     * @return 流
     */
    public static InputStream downloadFromMos(String fileName) {
        return downloadFromMos(fileName, -1L, -1L);
    }

    /**
     * 从美团云获取文件
     * @param fileName 文件名
     * @param start 开始字节数
     * @param end 结束字节数
     * @return 流
     */
    public static InputStream downloadFromMos(String fileName, long start, long end) {
        String md5Name = getMD5code(fileName);
        int pos = fileName.lastIndexOf(".");
        if (pos != -1) {
            md5Name = md5Name + fileName.substring(pos);
            BasicAWSCredentials credentials = new BasicAWSCredentials("8d75743d3eef4021a09a7fa3f7ee2035", "3e897d63d5ce40d2acb6b19197db517c");
            ClientConfiguration clientConfig = new ClientConfiguration();
            clientConfig.setProtocol(Protocol.HTTP);
            clientConfig.setSocketTimeout(6000000);
            clientConfig.setConnectionTimeout(6000000);
            S3ClientOptions s3ClientOptions = new S3ClientOptions();
            s3ClientOptions.setPathStyleAccess(true);
            AmazonS3Client conn = new AmazonS3Client(credentials, clientConfig);
            conn.setS3ClientOptions(s3ClientOptions);
            conn.setEndpoint(getInnerHost(readRouteToS3Plus()));
            S3ObjectInputStream input = null;

            try {
                GetObjectRequest getObjectRequest = new GetObjectRequest("wm_imgstore_intern", md5Name);
                if (start <= end && start >= 0L) {
                    getObjectRequest.setRange(start, end);
                }

                S3Object s3Object = conn.getObject(getObjectRequest);
                input = s3Object.getObjectContent();
            } catch (Exception e) {
                log.error("快叫剑哥！！！ md5Name:" + md5Name, e);
            }

            return input;
        } else {
            throw new RuntimeException("不科学啊，下的图片没后缀" + fileName);
        }
    }

    /**
     * 生成外网临时URL
     * @param fileName 文件名
     * @return 内网临时URL
     */
    public static String genTempUrl4Mos(String fileName) {
        String md5Name = getMD5code(fileName);
        int pos = fileName.lastIndexOf(".");
        if (pos != -1) {
            md5Name = md5Name + fileName.substring(pos);
        }

        int MT_CLOUD_EXPIRE = ConfigUtilAdapter.getInt("mt_cloud_expire", 300);
        String url = tempurl("GET", "mt_tenant_2230562", "wm_imgstore_intern", md5Name, (long)MT_CLOUD_EXPIRE, "f9NAUp2N9gVn");
        return getPublicHost(readRouteToS3Plus()) + url;
    }

    /**
     * 生成内网临时URL
     * @param fileName 文件名
     * @return 内网临时URL
     */
    public static String genInternalTempUrl4Mos(String fileName) {
        String md5Name = getMD5code(fileName);
        int pos = fileName.lastIndexOf(".");
        if (pos != -1) {
            md5Name = md5Name + fileName.substring(pos);
        }

        int MT_CLOUD_EXPIRE = ConfigUtilAdapter.getInt("mt_cloud_expire", 300);
        String url = tempurl("GET", "mt_tenant_2230562", "wm_imgstore_intern", md5Name, (long)MT_CLOUD_EXPIRE, "f9NAUp2N9gVn");
        return getInnerHost(readRouteToS3Plus()) + url;
    }

    /**
     * 临时URL
     */
    public static String tempurl(String method, String tenantId, String container, String object, long expire, String key) {
        String path = "/v1/mss_" + tenantId + "/" + container + "/" + object;
        return tempurl(method, path, expire, key);
    }

    /**
     * 临时URL
     */
    public static String tempurl(String method, String path, long expire, String key) {
        expire += System.currentTimeMillis() / 1000L;
        String hmac_body = method + "\n" + expire + "\n" + path;
        String sig = hmacsha1(hmac_body, key);
        return path + "?temp_url_sig=" + sig + "&temp_url_expires=" + expire;
    }

    /**
     * HMACSHA1加密,生成160位的哈希值
     * @param value 要加密的内容
     * @param key 加密的key
     * @return 密文
     */
    private static String hmacsha1(String value, String key) {
        try {
            Mac mac = Mac.getInstance("HmacSHA1");
            SecretKeySpec secret = new SecretKeySpec(key.getBytes(), "HmacSHA1");
            mac.init(secret);
            byte[] digest = mac.doFinal(value.getBytes());
            return digest == null ? null : String.valueOf(Hex.encodeHex(digest));
        } catch (Exception var5) {
            System.out.println("hmacsha1 error " + var5);
            return null;
        }
    }

    /**
     * 获取MD5值
     * @param m string
     * @return MD5
     */
    public static String getMD5code(String m) {
        MessageDigest md5 = null;

        try {
            md5 = MessageDigest.getInstance("MD5");
            md5.update(m.getBytes("utf-8"));
        } catch (Exception var3) {
            log.error("MD5找不到啊，用的是1.几?", var3);
        }

        byte[] array = md5.digest();
        return byteArrayToHexString(array);
    }

    /**
     * byte数组转16进制字符串
     * @param b byte[]
     * @return 十六进制
     */
    private static String byteArrayToHexString(byte[] b) {
        StringBuffer resultSb = new StringBuffer();

        for(int i = 0; i < b.length; ++i) {
            resultSb.append(byteToHexString(b[i]));
        }

        return resultSb.toString();
    }

    /**
     * byte转16进制字符串
     * @param b byte
     * @return 十六进制
     */
    private static String byteToHexString(byte b) {

        int n = b;
        if (b < 0) {
            n = 256 + b;
        }

        int d1 = n / 16;
        int d2 = n % 16;
        return hexDigits[d1] + hexDigits[d2];
    }

    /**
     * 读流量新老集群切换开关
     */
    private static boolean readRouteToS3Plus() {
        return ConfigUtilAdapter.getBoolean(READ_ROUTE_TOS3PLUST_KEY, false);
    }

    /**
     * 写流量新老集群切换开关
     */
    private static boolean writeRouteToS3Plus() {return ConfigUtilAdapter.getBoolean(WRITE_ROUTE_TOS3PLUST_KEY, false);}

    /**
     * 获取美团云公网域名
     */
    private static String getPublicHost(boolean routeToS3Plus) {
        if (routeToS3Plus) {
            return S3PLUS_MT_PUBLIC_DOMAIN;
        } else {
            return SWIFT_MT_PUBLIC_DOMAIN;
        }
    }

    /**
     * 获取美团云内网域名
     */
    private static String getInnerHost(boolean routeToS3Plus) {
       if (routeToS3Plus) {
           return S3PLUS_MT_INTERNAL_DOMAIN;
       } else {
           return SWIFT_MT_INTERNAL_DOMAIN;
       }
    }
}

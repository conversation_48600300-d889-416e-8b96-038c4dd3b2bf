<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>waimai_m_econtract</artifactId>
        <groupId>com.sankuai.meituan.waimai</groupId>
        <version>1.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>waimai_m_econtract_web</artifactId>

    <packaging>war</packaging>


    <dependencies>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore</artifactId>
            <version>4.4.5</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
            <version>4.5.2</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.amqp</groupId>
            <artifactId>spring-amqp</artifactId>
        </dependency>
        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-all</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.waimai.poi</groupId>
            <artifactId>waimai_service_poi_client</artifactId>
        </dependency>

        <!--服务治理-->
        <dependency>
            <groupId>com.sankuai.octo</groupId>
            <artifactId>mns-invoker</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.octo</groupId>
            <artifactId>idl-common</artifactId>
        </dependency>
        <!--密码存取-->
        <dependency>
            <groupId>com.meituan.service.inf</groupId>
            <artifactId>kms-java-client</artifactId>
        </dependency>
        <!--MCC配置-->
        <dependency>
            <groupId>com.sankuai.meituan</groupId>
            <artifactId>mt-common</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.httpcomponents</groupId>
                    <artifactId>httpclient</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan</groupId>
            <artifactId>mt-config-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan</groupId>
            <artifactId>mt-config-default</artifactId>
        </dependency>
        <!--订阅日志服务-->
        <dependency>
            <groupId>com.meituan.log</groupId>
            <artifactId>scribe-log4j2</artifactId>
        </dependency>

        <!--jetty启动组件-->
        <dependency>
            <groupId>com.sankuai.mms</groupId>
            <artifactId>mms-boot</artifactId>
            <version>1.2.2</version>
        </dependency>
        <!--Oceanus接入-->
        <dependency>
            <groupId>com.meituan.mtrace</groupId>
            <artifactId>mtrace</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mtrace</groupId>
            <artifactId>mtrace-http</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mtrace</groupId>
            <artifactId>mtrace-agent</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mtrace</groupId>
            <artifactId>idl-mtrace</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mtrace</groupId>
            <artifactId>mtrace-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mtrace</groupId>
            <artifactId>mtrace-zebra</artifactId>
        </dependency>

        <dependency>
            <groupId>org.elasticsearch</groupId>
            <artifactId>elasticsearch</artifactId>
        </dependency>
        <!--外卖工具包-->
        <dependency>
            <groupId>com.sankuai.meituan.waimai</groupId>
            <artifactId>waimai_util</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.elasticsearch</groupId>
                    <artifactId>elasticsearch</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.dianping.dpsf</groupId>
                    <artifactId>dpsf-net</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan</groupId>
            <artifactId>mt-basic-webapp</artifactId>
            <version>1.2.4</version>
        </dependency>

        <!--美团点评业务服务-->
        <!--第三方工具包-->
        <!--javax-->
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.el</groupId>
            <artifactId>javax.el-api</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>servlet-api</artifactId>
        </dependency>

        <!--第三方工具-->
        <!--spring-->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>20.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-orm</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-expression</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-beans</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
        </dependency>

        <!--序列化-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.httpcomponents</groupId>
                    <artifactId>httpclient</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--日志-->
        <dependency>
            <groupId>com.meituan.inf</groupId>
            <artifactId>xmd-log4j2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.inf</groupId>
            <artifactId>xmd-common-log4j2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.waimai.oplog</groupId>
            <artifactId>waimai_service_oplog_client</artifactId>
            <version>1.0.16</version>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.mafka</groupId>
                    <artifactId>mafka-client_2.10</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-text</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--美团点评业务服务-->
        <!--组织结构-->
        <dependency>
            <groupId>com.sankuai.meituan.waimai</groupId>
            <artifactId>waimai_service_econtract_client</artifactId>
            <version>1.6.28</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.waimai.infra</groupId>
            <artifactId>waimai_service_infra_client</artifactId>
            <version>1.1.58</version>
        </dependency>
        <!-- sso-sdk新版本 -->
        <dependency>
            <groupId>com.sankuai.it.sso</groupId>
            <artifactId>sso-java-sdk</artifactId>
            <version>2.5.5</version>
        </dependency>
        <!-- mt-upm-auth 版本升级 -->
        <dependency>
            <groupId>com.sankuai.meituan</groupId>
            <artifactId>mt-upm-auth</artifactId>
            <version>2.0.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>sso-java-sdk</artifactId>
                    <groupId>com.sankuai.it.sso</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.dianping.dpsf</groupId>
                    <artifactId>dpsf-net</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--组织结构-->
        <dependency>
            <groupId>com.sankuai.meituan.waimai</groupId>
            <artifactId>waimai_lib_mtauth</artifactId>
            <version>2.0.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.sankuai.meituan.banma</groupId>
                    <artifactId>banma_open_client</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>mtorg-http-api</artifactId>
                    <groupId>com.sankuai.meituan</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- waimai_lib_m_util, 升级版本 -->
        <dependency>
            <groupId>com.sankuai.meituan.waimai</groupId>
            <artifactId>waimai_lib_m_util</artifactId>
            <version>1.0.3-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>mtorg-remote-service</artifactId>
                    <groupId>com.sankuai.meituan</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--pdf转图片-->
        <dependency>
            <groupId>org.jpedal</groupId>
            <artifactId>jpedal-lgpl</artifactId>
            <version>4.74b27</version>
            <exclusions>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcmail-jdk15</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcprov-jdk15</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.meituan.servicecatalog</groupId>
            <artifactId>api-annotations</artifactId>
        </dependency>

        <!--aviator执行-->
        <dependency>
            <groupId>com.googlecode.aviator</groupId>
            <artifactId>aviator</artifactId>
            <version>5.2.7</version>
        </dependency>

        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.8.8</version>
        </dependency>
        <!--MCC配置读取-->
        <dependency>
            <groupId>com.sankuai.meituan</groupId>
            <artifactId>mtconfig-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.lion</groupId>
            <artifactId>lion-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.tsp</groupId>
            <artifactId>pecker-trace</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.waimai</groupId>
            <artifactId>waimai_digger_client</artifactId>
        </dependency>
    </dependencies>


    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>2.3.2</version>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>2.1.1</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>install</id>
                        <phase>install</phase>
                        <goals>
                            <goal>sources</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.8.1</version>
                <configuration>
                    <skipTests>true</skipTests>
                    <junitArtifactName>junit:junit</junitArtifactName>
                    <excludes>
                        <exclude>**/*_Roo_*</exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <version>1.5</version>
                <executions>

                </executions>
            </plugin>
            <plugin>
                <groupId>org.mortbay.jetty</groupId>
                <artifactId>jetty-maven-plugin</artifactId>
                <version>8.1.16.v20140903</version>
                <configuration>
                    <jvmArgs>-XX:+CMSClassUnloadingEnabled -XX:PermSize=512M
                        -XX:MaxPermSize=512M
                    </jvmArgs>
                    <connectors>
                        <connector implementation="org.eclipse.jetty.server.nio.SelectChannelConnector">
                            <port>9000</port>
                        </connector>
                    </connectors>
                    <stopPort>9090</stopPort>
                    <stopKey>a</stopKey>
                    <scanIntervalSeconds>0</scanIntervalSeconds>
                    <webApp>
                        <contextPath>/</contextPath>
                    </webApp>
                    <jettyXml>src/main/webapp/WEB-INF/jetty8.xml</jettyXml>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>tomcat-maven-plugin</artifactId>
                <version>1.1</version>
            </plugin>

            <plugin>
                <groupId>com.sankuai.inf</groupId>
                <artifactId>xmdlog-maven-plugin</artifactId>
                <version>1.1.3-SNAPSHOT</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>check</goal>
                        </goals>
                        <phase>compile</phase>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <finalName>waimai_m_econtract_web</finalName>
    </build>


    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <package.environment>develop</package.environment>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <resources>
                    <resource>
                        <directory>src/main/resources-dev</directory>
                    </resource>
                    <resource>
                        <directory>src/main/resources</directory>
                    </resource>
                </resources>
            </build>
        </profile>
        <profile>
            <id>local</id>
            <properties>
                <package.environment>develop</package.environment>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <resources>
                    <resource>
                        <directory>src/main/resources-local</directory>
                    </resource>
                    <resource>
                        <directory>src/main/resources</directory>
                    </resource>
                </resources>
            </build>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <package.environment>product</package.environment>
            </properties>
            <build>
                <resources>
                    <resource>
                        <directory>src/main/resources-prod</directory>
                    </resource>
                    <resource>
                        <directory>src/main/resources</directory>
                    </resource>
                </resources>
            </build>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <package.environment>test</package.environment>
            </properties>
            <build>
                <resources>
                    <resource>
                        <directory>src/main/resources-test</directory>
                    </resource>
                    <resource>
                        <directory>src/main/resources</directory>
                    </resource>
                </resources>
            </build>
        </profile>
        <profile>
            <id>beta</id>
            <properties>
                <package.environment>beta</package.environment>
            </properties>
            <build>
                <resources>
                    <resource>
                        <directory>src/main/resources-beta</directory>
                    </resource>
                    <resource>
                        <directory>src/main/resources</directory>
                    </resource>
                </resources>
            </build>
        </profile>
    </profiles>

</project>

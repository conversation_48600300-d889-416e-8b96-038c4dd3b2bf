package com.sankuai.meituan.waimai.econtract.web.utils;

import com.sankuai.meituan.waimai.econtrct.client.domain.api.CertifyH5InfoBo;
import org.junit.Assert;
import org.junit.Test;

import static org.junit.Assert.*;

/**
 * 加密工具类单元测试
 *
 * @Author: wangyongfang
 * @Date: 2024-03-12
 */
public class EncryptUtilTest {


    @Test
    public void testEncryptCertifyH5InfoBo() {
        CertifyH5InfoBo certifyH5InfoBo = new CertifyH5InfoBo();
        certifyH5InfoBo.setCertType(1);
        certifyH5InfoBo.setCompanyName("合肥经济技术开发区涂涂餐饮店");
        certifyH5InfoBo.setCompanyNum("9****************H");
        certifyH5InfoBo.setSignerName("涂**");
        certifyH5InfoBo.setSignerCardNum("3****************X");
        certifyH5InfoBo.setCertPhone("193****1868");
        certifyH5InfoBo.setSignerCardType(1);
        certifyH5InfoBo = EncryptUtil.encryptCertifyH5InfoBo(certifyH5InfoBo);
        Assert.assertEquals("9****************H", certifyH5InfoBo.getCompanyNum());
        Assert.assertEquals("3****************X", certifyH5InfoBo.getSignerCardNum());
        Assert.assertEquals("193****1868", certifyH5InfoBo.getCertPhone());
        Assert.assertEquals("合肥经济技术开发区涂涂餐饮店", certifyH5InfoBo.getCompanyName());
        Assert.assertEquals("涂**", certifyH5InfoBo.getSignerName());
    }

    @Test
    public void testEncryptCertifyH5InfoBoWithNullField() {
        CertifyH5InfoBo certifyH5InfoBo = new CertifyH5InfoBo();
        certifyH5InfoBo.setCertType(1);
        certifyH5InfoBo.setCompanyName(null);
        certifyH5InfoBo.setCompanyNum(null);
        certifyH5InfoBo.setSignerName("涂**");
        certifyH5InfoBo.setSignerCardNum("3****************X");
        certifyH5InfoBo.setCertPhone("193****1868");
        certifyH5InfoBo.setSignerCardType(2);
        certifyH5InfoBo = EncryptUtil.encryptCertifyH5InfoBo(certifyH5InfoBo);
        Assert.assertNull(certifyH5InfoBo.getCompanyNum());
        Assert.assertEquals("3****************X", certifyH5InfoBo.getSignerCardNum());
        Assert.assertEquals("193****1868", certifyH5InfoBo.getCertPhone());
        Assert.assertNull(certifyH5InfoBo.getCompanyName());
        Assert.assertEquals("涂**", certifyH5InfoBo.getSignerName());
    }
}
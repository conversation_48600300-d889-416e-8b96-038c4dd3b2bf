import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.econtrct.client.constants.PdfConstant;
import com.sankuai.meituan.waimai.econtrct.client.constants.TaskConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.*;

import java.util.Arrays;
import java.util.Map;

/**
 * <AUTHOR> <PERSON>
 * @date 2018/1/16
 * @time 上午10:53
 */
public class ApiBoBuilderTest {


    public static void main(String[] args) {
        Map<String, String> metaMap = Maps.newHashMap();
        metaMap.put("testVal", "testVal");
        PdfContentInfoBo pdfContentInfoBo = new PdfContentInfoBo.Builder()
                .setPdfMetaContent(metaMap)
                .setPdfTemplateName("test-contract.ftl")
                .build();
        StageInfoBo createStage = new StageInfoBo.Builder()
                .setStageName(TaskConstant.CREATE_PDF)
                .setPdfContentInfoBoList((Lists.newArrayList(pdfContentInfoBo)))
                .build();

        //实名认证模块
        Map<String, String> smsParamMap = Maps.newHashMap();
        smsParamMap.put("number", "number");
        smsParamMap.put("name", "name");
        smsParamMap.put("phone", "phone");

        SignerInfoBo signerInfoBo = new SignerInfoBo.Builder()
                .setName("侯璐")
                .setIdCardNo("610582199106210513")
                .setPhone("***********")
                .setBankName("中国银行")
                .setBankCardNo("6216615300013001992")
                .setClientId("sms-contract_kefu")
                .setClientSecret("AA81E263E10F5D64B7FCDAC71D11AB8D")
                .setSmsTemplateId("6183")
                .setSmsParamMap(smsParamMap)
                .setMobileList(Arrays.asList("***********"))
                .build();
        StageInfoBo realNameStage = new StageInfoBo.Builder()
                .setSignerInfoBo(signerInfoBo)
                .setStageName(TaskConstant.REAL_NAME_AUTH_A)
                .build();

        //CA认证模块
        CertifyInfoBo certifyInfoBo = new CertifyInfoBo.Builder()
                .setCustomerName("侯璐")
                .setEmail("<EMAIL>")
                .setMobile("***********")
                .setQuaNum("610582199106210513")
                .setCaType(CAType.PERSON)
                .build();
        StageInfoBo caStage = new StageInfoBo.Builder()
                .setStageName(TaskConstant.CA_CERTIFY_PART_A)
                .setCertifyInfoBo(certifyInfoBo)
                .build();


        //ESTAMP模块
        Map<String, String> estampParamPam = Maps.newHashMap();
        estampParamPam.put(TaskConstant.PDF_ESTAMP_SIGN_KEY, PdfConstant.WM_EXCLUSIVE_SIGNKEY);
        EstampInfoBo estampInfoBo = new EstampInfoBo.Builder()
                .setEstampMap(estampParamPam)
                .build();
        StageInfoBo estampStage = new StageInfoBo.Builder()
                .setStageName(TaskConstant.ECONTRACT_STAMP_A)
                .setEstampInfoBo(estampInfoBo)
                .build();

        String callback = "http://ceshi.call.back";


        //新建一份合同参数
        EcontractBo econtractBo = new EcontractBo.Builder().setToken("USR_waimai_contract_7377f61f-11af-42")
                .setEcontractType(TaskConstant.ECONTRACT_TYPE_WM_EXCLUSIVE)
                .setStageInfoBoList(Lists.newArrayList(createStage, realNameStage, caStage,estampStage))
                .setCallBackUrl(callback)
                .setEcontractName("testContract")
                .build();
        System.out.println(JSON.toJSONString(econtractBo));
    }
}

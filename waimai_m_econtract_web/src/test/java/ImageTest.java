import com.sankuai.meituan.config.configuration.MccConfiguration;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.econtract.web.constant.MccConstant;
import com.sankuai.meituan.waimai.econtract.web.utils.ImageUtil;
import com.sankuai.meituan.waimai.econtract.web.utils.PdfConvertImageUtil;
import org.apache.commons.codec.binary.Base64;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2017/11/16
 * @time 下午8:53
 */
public class ImageTest {

    public static void main(String[] args) throws IOException {

        ConfigUtilAdapter.addConfiguration(new MccConfiguration(MccConstant.APP_KEY, "", ""));
        ConfigUtilAdapter.init();

        byte[] bytesOfPdf = ImageUtil.getCloudPicBytes("/download/mos/30644f00e6d2cb605fb9c06677b495da.pdf");
        byte[] bytesOfImage = PdfConvertImageUtil.transfer(bytesOfPdf, 1);
        System.out.println(Base64.encodeBase64String(bytesOfImage));
    }
}

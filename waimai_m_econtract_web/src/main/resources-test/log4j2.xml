<?xml version="1.0" encoding="UTF-8"?>
<configuration status="info">
	<appenders>
		<Console name="Console" target="SYSTEM_OUT" follow="true">
			<PatternLayout pattern="%d{yyyy/MM/dd HH:mm:ss.SSS} %XMDT %t [%p] %c{1} (%F:%L) %msg%n%ex"/>
		</Console>

		<!--XMDFile异步磁盘日志配置示例-->
		<!--默认按天&按512M文件大小切分日志，默认最多保留30个日志文件。-->
		<!--目录为/opt/logs/${appkey} 或 /data/applogs/${appkey}，优先选用磁盘挂载目录-->
		<!--注意：fileName前会自动增加文件路径，只配置文件名即可-->
		<!--可选配置：ERROR日志、WARN日志单独输出到一个文件-->
		<XMDFile name="ERROR-LOG" fileName="${sys:app.key}.err.log" xmdFilePath="/var/sankuai/logs"
				 addAppkeyToFilePath="false">
			<ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
		</XMDFile>

		<XMDFile name="file" fileName="${sys:app.key}.log"
				 xmdFilePath="/var/sankuai/logs" includeLocation="true" sizeBasedTriggeringSize="10G">
			<PatternLayout charset="UTF-8">
				<Pattern>%d %t %-5p (%F:%L) %XMDT - %m%n %ex</Pattern>
			</PatternLayout>
			<Policies>
				<TimeBasedTriggeringPolicy interval="24" modulate="true"/>
			</Policies>
		</XMDFile>

		<!-- jmonitor相关日志，打印到单独的文件中，并使用异步日志 -->
		<XMDFile name="jmonitorappender" fileName="${sys:app.key}.jmonitor.log" xmdFilePath="/var/sankuai/logs"
				 timeBasedTriggeringInterval="1" addAppkeyToFilePath="false">
			<ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
		</XMDFile>
		<XMDFile name="jmonitorlogstoreappender" fileName="${sys:app.key}.jmonitor.logstore.log"
				 xmdFilePath="/var/sankuai/logs" timeBasedTriggeringInterval="1" addAppkeyToFilePath="false">
			<ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
		</XMDFile>

        <!--异步日志上报远程配置示例-->
        <!-- 如果期望将上报日志从 异步 改为 同步，容忍同步时延，避免丢失，请修改blocking=true参数 -->
        <AsyncScribe name="AsyncScribeAppender" blocking="false">
            <!-- 在指定日志名方面，scribeCategory 和 appkey 两者至少存在一种，且 scribeCategory 高于 appkey。-->
            <!-- 大陆内地的日志和非内地的日志需要区分开，请不要混用 -->
            <Property name="scribeCategory">com.sankuai.waimai.m.econtractweb</Property>
            <!--下面checkLoss标记表示是否开启丢失率检测，true为开启，false为不开启，默认为false -->
            <Property name="checkLoss">false</Property>
            <!-- LcLayout对应NEW_LOG格式，如向已有Topic写入数据，请确保新的日志的格式与原Topic日志格式保持一致，否则会对下游实时数仓或离线数仓生产造成影响 -->
            <LcLayout/>
        </AsyncScribe>

    </appenders>

    <loggers>
        <!-- 单独打印jmonitor -->
        <logger name="jmonitor" additivity="false" level="info">
            <appender-ref ref="jmonitorappender"/>
        </logger>

        <logger name="jmonitorlogstore" additivity="false" level="info">
            <appender-ref ref="jmonitorlogstoreappender"/>
        </logger>

        <root level="info">
            <appender-ref ref="Console"/>                  <!--控制台输出推荐只在本地调试时使用，线上将该配置去掉-->
            <appender-ref ref="XMDFileAppender"/>            <!--写入request.log文件-->
            <appender-ref ref="ERROR-LOG"/>                <!--ERROR日志单独输出到一个文件-->
            <appender-ref ref="WARN-LOG"/>                 <!--ERROR&WARN日志单独输出到一个文件-->
            <appender-ref ref="AsyncScribeAppender"/>      <!--日志传入远程日志中心 -->
        </root>

    </loggers>
</configuration>
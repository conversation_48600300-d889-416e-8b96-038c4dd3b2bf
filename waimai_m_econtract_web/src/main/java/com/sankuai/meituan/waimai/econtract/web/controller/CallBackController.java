package com.sankuai.meituan.waimai.econtract.web.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;

import com.sankuai.meituan.waimai.econtract.web.utils.EstampUtil;
import com.sankuai.meituan.waimai.econtract.web.utils.WmRestReturnUtil;
import com.sankuai.meituan.waimai.econtract.web.vo.real.name.RealNameCallBackVo;
import com.sankuai.meituan.waimai.econtract.web.vo.stamp.StampCallBackVo;
import com.sankuai.meituan.waimai.econtrct.client.constants.CallbackConstant;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractAPIResponseConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractNotifyInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.service.EcontractAPIResponse;
import com.sankuai.meituan.waimai.econtrct.client.service.EcontractAPIService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR> Hou
 * @date 2017/10/26
 * @time 下午2:08
 */
@Controller
@RequestMapping("/econtract/restful/api/v1/call_back")
public class CallBackController {

    private static final Logger LOGGER = LoggerFactory.getLogger(CallBackController.class);

    @Autowired
    private EcontractAPIService econtractAPIService;


    /**
     * 上传PDF回调
     *
     * @param input
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/ca/upload_pdf", method = {RequestMethod.POST})
    public Object caUploadPdf(@RequestBody String input) {
        LOGGER.info("upload_pdf call back , input={}", input);
        try {
            StampCallBackVo stampCallBackVo = JSON.parseObject(input, StampCallBackVo.class);
            Map<String, String> paramMap = Maps.newHashMap();
            if (StringUtils.isNotEmpty(stampCallBackVo.getRequest().getTransaction_id())) {
                //thrift的map不能有空value值
                paramMap.put(CallbackConstant.ESTAMP_TRANSCATION_ID, stampCallBackVo.getRequest().getTransaction_id());
            }
            paramMap.put(CallbackConstant.UPLOAD_RESULT_CODE, String.valueOf(stampCallBackVo.getResult().getCode()));
            LOGGER.info("caUploadPdf recordKey = {}, paramMap = {}", EstampUtil.parseRecordKey(stampCallBackVo.getRequest().getContract_id()), JSON.toJSONString(paramMap));
            EcontractAPIResponse response = econtractAPIService.callBackEcontractByRecordKey(EstampUtil.parseRecordKey(stampCallBackVo.getRequest().getContract_id()), CallbackConstant.UPLOAD_PDF_CALL_BACK, paramMap);
            if (EcontractAPIResponseConstant.SUCCESS_CODE == response.getCode()) {
                return WmRestReturnUtil.success("success");
            } else {
                return WmRestReturnUtil.fail(response.getMsg());
            }
        } catch (Exception e) {
            LOGGER.error("fail to receive upload_pdf callback ", e);
            return WmRestReturnUtil.fail("传入数据对象格式错误");
        }
    }


    /**
     * @param input
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/ca/estamp", method = {RequestMethod.POST})
    public Object caEstamp(@RequestBody String input) {
        LOGGER.info("estamp call back,input={}", input);
        try {
            StampCallBackVo stampCallBackVo = JSON.parseObject(input, StampCallBackVo.class);
            Map<String, String> paramMap = Maps.newHashMap();
            paramMap.put(CallbackConstant.ESTAMP_RESULT_CODE, String.valueOf(stampCallBackVo.getResult().getCode()));
            if (StringUtils.isNotEmpty(stampCallBackVo.getResult().getDownload_url())) {
                //thrift的map不能有空value值
                paramMap.put(CallbackConstant.ESTAMP_DOWNLOAD_URL, stampCallBackVo.getResult().getDownload_url());
            }
            if (StringUtils.isNotEmpty(stampCallBackVo.getResult().getMsg())) {
                //thrift的map不能有空value值
                paramMap.put(CallbackConstant.ESTAMP_RESULT_MSG, stampCallBackVo.getResult().getMsg());
            }
            if (StringUtils.isNotEmpty(stampCallBackVo.getRequest().getTransaction_id())) {
                //thrift的map不能有空value值
                paramMap.put(CallbackConstant.ESTAMP_TRANSCATION_ID, stampCallBackVo.getRequest().getTransaction_id());
            }
            EcontractAPIResponse response = econtractAPIService.callBackEcontractByRecordKey(EstampUtil.parseRecordKey(stampCallBackVo.getRequest().getContract_id()), CallbackConstant.ESTAMP_CALL_BACK, paramMap);
            if (EcontractAPIResponseConstant.SUCCESS_CODE == response.getCode()) {
                return WmRestReturnUtil.success("success");
            } else {
                return WmRestReturnUtil.fail(response.getMsg());
            }

        } catch (Exception e) {
            LOGGER.error("fail to receive estamp callback ", e);
            return WmRestReturnUtil.fail("传入数据对象格式错误");
        }
    }


    @ResponseBody
    @RequestMapping(value = "/real_name/notify")
    public Object realNameNotify(RealNameCallBackVo realNameCallBackVo) {
        LOGGER.info("real name notify {}", JSON.toJSONString(realNameCallBackVo));
        try {
            String dealVersion = realNameCallBackVo.getBizCertifyId();
            Map<String, String> paramMap = Maps.newHashMap();
            paramMap.put(CallbackConstant.REAL_NAME_AUTH_RESULT, String.valueOf(realNameCallBackVo.isResult()));
            paramMap.put(CallbackConstant.REAL_NAME_AUTH_FAIL_CODE, String.valueOf(realNameCallBackVo.getFailCode()));
            paramMap.put(CallbackConstant.REAL_NAME_DEAL_VERSION, dealVersion);


            EcontractAPIResponse response = econtractAPIService.callBackEcontractByRealNamDealVersion(dealVersion, CallbackConstant.REAL_NAME_AUTH_CALL_BACK, paramMap);
            if (EcontractAPIResponseConstant.SUCCESS_CODE == response.getCode()) {
                return WmRestReturnUtil.success("success");
            } else {
                return WmRestReturnUtil.fail(response.getMsg());
            }
        } catch (Exception e) {
            return WmRestReturnUtil.fail("传入数据对象格式错误");
        }
    }


    @ResponseBody
    @RequestMapping(value = "/test/stage/call")
    public Object testStageCall(@RequestParam(value = "recordKey") String recordKey, @RequestParam(value = "callType") String callType) {
        LOGGER.info("testStageCall , recordKey  {}", recordKey);
        try {
            EcontractAPIResponse response = econtractAPIService.callBackEcontractByRecordKey(recordKey, callType, null);
            if (EcontractAPIResponseConstant.SUCCESS_CODE == response.getCode()) {
                return WmRestReturnUtil.success("success");
            } else {
                return WmRestReturnUtil.fail(response.getMsg());
            }
        } catch (Exception e) {
            return WmRestReturnUtil.fail("传入数据对象格式错误");
        }
    }


    @ResponseBody
    @RequestMapping(value = "/test/notifier_call_back")
    public Object testStageCall(EcontractNotifyInfoBo notifyInfoBo) {
        LOGGER.info("notifyInfoBo is : {}", JSON.toJSONString(notifyInfoBo));
        return WmRestReturnUtil.success("success");
    }


}

package com.sankuai.meituan.waimai.econtract.web.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.econtract.web.constant.MccConstant;
import com.sankuai.meituan.waimai.econtract.web.service.EmployService;
import com.sankuai.meituan.waimai.econtract.web.utils.WmRestReturnUtil;
import com.sankuai.meituan.waimai.econtrct.client.domain.biz.EcontractTemplateBo;
import com.sankuai.meituan.waimai.econtrct.client.service.EcontractManagerService;
import com.sankuai.meituan.waimai.econtrct.client.service.EcontractUserManagerService;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.io.InputStream;

/**
 * 类名称: UserController <br>
 * 类描述: <br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 16/10/6 上午10:37
 */
@Controller
@RequestMapping("/econtract/restful/api/v1/activiti")
public class ActivitiController {

    private static final org.slf4j.Logger LOGGER = LoggerFactory.getLogger(ActivitiController.class);

    @Autowired
    private EcontractManagerService econtractManagerService;

    @Autowired
    private EcontractUserManagerService userManagerService;

    @Autowired
    private EmployService employService;


    final String MODEL_ID = "modelId";
    final String MODEL_NAME = "name";
    final String MODE = "model";
    final String MODEL_DESCRIPTION = "description";

    final String EMPTY_TEMPLATE = "{\"resourceId\":0,\"properties\":{\"name\":\"\",\"taskname\":\"\",\"nexttaskname\":\"\",\"tocallbacktaskname\":\"\",\"stageinfobokey\":\"\",\"canjump\":\"\",\"neednotify\":\"process\",\"process_id\":\"process\",\"hasandjump\":\"\",\"process_namespace\":\"http://www.activiti.org/processdef\",\"executionlisteners\":\"\",\"eventlisteners\":\"\",\"documentation\":\"\",\"process_version\":\"\",\"process_author\":\"\",\"signaldefinitions\":\"\",\"messagedefinitions\":\"\",\"jumpnexttaskname\":\"\"},\"stencil\":{\"id\":\"BPMNDiagram\"},\"childShapes\":[],\"bounds\":{\"lowerRight\":{\"x\":1200,\"y\":1050},\"upperLeft\":{\"x\":0,\"y\":0}},\"stencilset\":{\"url\":\"stencilsets/bpmn2.0/bpmn2.0.json\",\"namespace\":\"http://b3mn.org/stencilset/bpmn2.0#\"},\"ssextensions\":[]}";


    @RequestMapping("/model/{modelId}/json")
    @ResponseBody
    public Object json(@PathVariable("modelId") Integer id) {
        User user = UserUtils.getUser();
        EcontractTemplateBo templateBo = econtractManagerService.querySignFlowTemplateIdByIdAndUid(id, user.getId());
        if (templateBo == null) {
            return "查询失败";
        }
        JSONObject object = new JSONObject();
        object.put(MODEL_ID, templateBo.getId());
        object.put(MODEL_NAME, templateBo.getName());
        if (StringUtils.isEmpty(templateBo.getProcedureTemplate())) {
            object.put(MODE, JSON.parseObject(EMPTY_TEMPLATE));
        } else {
            object.put(MODE, JSON.parseObject(templateBo.getProcedureTemplate()));
        }
        object.put(MODEL_DESCRIPTION, templateBo.getName());
        return object;
    }

    @RequestMapping("/editor/stencilset")
    @ResponseBody
    public Object stencilset() {
        User user = UserUtils.getUser();
        if (!MccConstant.deleteRdsAuthControl()) {
            try {
                String econtractUser = userManagerService.getEcontractUserByUserId(user.getId());
                if (StringUtils.isBlank(econtractUser)) {
                    return WmRestReturnUtil.fail("无操作权限");
                }
            } catch (Exception e) {
                LOGGER.error("校验用户失败", e);
                return "";
            }
        }
        InputStream stencilsetStream = this.getClass().getClassLoader().getResourceAsStream("/activiti/stencilset-change.json");
        String str = "";
        try {
            str = IOUtils.toString(stencilsetStream, "utf-8");
        } catch (IOException e) {
            e.printStackTrace();
        }
        return str;
    }

    @RequestMapping(value = "/model/{modelId}/save", method = RequestMethod.POST)
    @ResponseBody
    public Object saveModel(@PathVariable String modelId, @RequestBody MultiValueMap<String, String> values) {
        WmEmploy wmEmploy;
        try {
            User user = UserUtils.getUser();
            wmEmploy = employService.getEmployInfoById(user.getId());
        } catch (Exception e) {
            LOGGER.error("校验用户失败", e);
            return "fail";
        }
        EcontractTemplateBo templateBo = econtractManagerService.querySignFlowTemplateIdByIdAndUid(Integer.valueOf(modelId), wmEmploy.getUid());
        if (templateBo == null) {
            return "没有权限操作";
        }
        templateBo.setProcedureTemplate(values.getFirst("json_xml"));
        if (null == wmEmploy) {
            return "获取雇员信息失败";
        }
        String errorMsg = econtractManagerService.updateEcontracTemplate(templateBo,wmEmploy.getMisId());
        if(StringUtils.isNotBlank(errorMsg)){
            return errorMsg;
        }
        return "success";
    }

    @RequestMapping(value = "/model/update_authority_mis_id")
    @ResponseBody
    public Object updateAuthorityMisId(Integer id, String authorityMisId) {
        if(id==null || StringUtils.isBlank(authorityMisId)){
            return WmRestReturnUtil.fail("参数不能为空");
        }
        try{
            User user = UserUtils.getUser();
            WmEmploy wmEmploy = employService.getEmployInfoById(user.getId());
            if(wmEmploy==null){
                return WmRestReturnUtil.fail("获取当前登录人misId失败");
            }
            String result = econtractManagerService.updateContractAuthorityById(id,authorityMisId,wmEmploy.getMisId());
            if(StringUtils.isNotBlank(result)){
                return WmRestReturnUtil.fail(result);
            }
        }catch (Exception e){
            LOGGER.error("更新合同模板权限所有人失败，id:{},authorityMisId:{}",id,authorityMisId,e);
            return WmRestReturnUtil.fail("更新失败，系统异常");
        }
        return WmRestReturnUtil.success("success");
    }

}

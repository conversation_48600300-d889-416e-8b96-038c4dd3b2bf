package com.sankuai.meituan.waimai.econtract.web.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.econtract.web.constant.MccConstant;
import com.sankuai.meituan.waimai.econtract.web.service.EmployService;
import com.sankuai.meituan.waimai.econtract.web.utils.SplitUtil;
import com.sankuai.meituan.waimai.econtract.web.utils.WmRestReturnUtil;
import com.sankuai.meituan.waimai.econtract.web.vo.h5signpage.SignPageVo;
import com.sankuai.meituan.waimai.econtract.web.vo.pdf.PdfContext;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.PdfContentInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.SignPageInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.biz.*;
import com.sankuai.meituan.waimai.econtrct.client.domain.config.econtractuser.EcontractUserQueryRequestDTO;
import com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException;
import com.sankuai.meituan.waimai.econtrct.client.service.EcontractManagerService;
import com.sankuai.meituan.waimai.econtrct.client.service.EcontractUserManagerService;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Hou
 * @date 2017/10/26
 * @time 下午2:08
 */
@Controller
@RequestMapping("/econtract/restful/api/v1/manager")
public class EcontractManagerController {

    private static final Logger LOGGER = LoggerFactory.getLogger(EcontractManagerController.class);

    private static final Integer MAX_PAGESIZE = 100;

    private static final Integer MAX_LENGTH = 64;

    private static final String UNDEFINED = "undefined";

    private static final String WHITE_LIST_KEY = "MANAGER_WHITE_LIST_KEY";

    private static final String WHITE_LIST_DEFAULT = "lihaowei,fangcaili";

    private static final Integer USER_MAX_LENGTH = 50;


    @Autowired
    private EcontractUserManagerService userManagerService;
    @Autowired
    private EcontractManagerService econtractManagerService;
    @Autowired
    private EmployService employService;


    @ResponseBody
    @RequestMapping(value = "/query_all_user")
    public Object queryAllUser() {
        try {
            int userId = UserUtils.getUser().getId();
            EcontractUserQueryRequestDTO requestDTO = new EcontractUserQueryRequestDTO();
            requestDTO.setUid(userId);
            return userManagerService.queryEcontractUserList(requestDTO);
        } catch (Exception e) {
            LOGGER.error("query_all_user ", e);
            return WmRestReturnUtil.fail("fail");
        }
    }

    @ResponseBody
    @RequestMapping(value = "/is_user_exist")
    public Object isUserExist(String userName) {
        if (StringUtils.isEmpty(userName) || UNDEFINED.equals(userName)) {
            return WmRestReturnUtil.fail("fail");
        }
        boolean isExist;
        try {
            String econtractUser = userManagerService.getEcontractUserByUserId(UserUtils.getUser().getId());
            if (StringUtils.isBlank(econtractUser)) {
                return WmRestReturnUtil.fail("无操作权限");
            }
            isExist = userManagerService.isUserExist(userName);
            if (isExist) {
                return WmRestReturnUtil.success("fail");
            } else {
                return WmRestReturnUtil.success("success");
            }
        } catch (Exception e) {
            LOGGER.error("is_user_exist ", e);
            return WmRestReturnUtil.fail("fail");
        }
    }


    @ResponseBody
    @RequestMapping(value = "/create_user", method = {RequestMethod.POST})
    public Object createUser(@RequestBody EcontractUserBo userBo) {
        LOGGER.info("EcontractManagerController#createUser, userBo: {}", JSON.toJSONString(userBo));
        if (StringUtils.isEmpty(userBo.getUserName()) || UNDEFINED.equals(userBo.getUserName())) {
            return WmRestReturnUtil.fail("fail");
        }
        if ((userBo.getUserName().length() > USER_MAX_LENGTH) || (userBo.getDescription().length() > USER_MAX_LENGTH)) {
            return WmRestReturnUtil.fail("长度限制" + USER_MAX_LENGTH);
        }

        int userId = UserUtils.getUser().getId();
        WmEmploy wmEmploy = employService.getEmployInfoById(userId);
        if (wmEmploy == null) {
            return WmRestReturnUtil.fail("查询用户信息异常");
        }

        Integer maxNum = MccConstant.getMaxNumOfUserMemberMis();
        if (userBo.getMemberMisList().size() > maxNum) {
            return WmRestReturnUtil.fail("接入方人员限制" + maxNum);
        }
        try {
            userManagerService.createEcontractUser4Config(userBo, userId);
            return WmRestReturnUtil.success("success");
        } catch (EcontractException e) {
            LOGGER.warn("EcontractManagerController#createUser, error", e);
            return WmRestReturnUtil.fail(e.getMsg());
        } catch (Exception e) {
            LOGGER.error("EcontractManagerController#createUser, error", e);
            return WmRestReturnUtil.fail("fail");
        }
    }

    @ResponseBody
    @RequestMapping(value = "/query_all_econtract_template")
    public Object queryAllTemplate() {
        try {
            String econtractUser = userManagerService.getEcontractUserByUserId(UserUtils.getUser().getId());
            if (StringUtils.isBlank(econtractUser)) {
                return WmRestReturnUtil.fail("无操作权限");
            }
            return econtractManagerService.queryEcontractTemplateBoList();
        } catch (Exception e) {
            LOGGER.error("query_all_econtract_template ", e);
            return WmRestReturnUtil.fail("fail");
        }
    }


    @ResponseBody
    @RequestMapping(value = "/is_econtract_type_exist")
    public Object isEcontractTypeExist(String typeName) {
        if (StringUtils.isEmpty(typeName)) {
            return WmRestReturnUtil.fail("fail");
        }
        boolean isExist;
        try {
            String econtractUser = userManagerService.getEcontractUserByUserId(UserUtils.getUser().getId());
            if (StringUtils.isBlank(econtractUser)) {
                return WmRestReturnUtil.fail("无操作权限");
            }
            isExist = econtractManagerService.isEcontractTemplateExist(typeName);
            if (isExist) {
                return WmRestReturnUtil.success("fail");
            } else {
                return WmRestReturnUtil.success("success");
            }
        } catch (Exception e) {
            LOGGER.error("is_econtract_type_exist ", e);
            return WmRestReturnUtil.fail("fail");
        }
    }

    @ResponseBody
    @RequestMapping(value = "/create_econtract_template", method = {RequestMethod.POST})
    public Object createEcontractTemplate(@RequestBody EcontractTemplateBo econtractTemplateBo) {
        LOGGER.info("EcontractManagerController#EcontractTemplateBo: {}", JSON.toJSONString(econtractTemplateBo));
        if (StringUtils.isEmpty(econtractTemplateBo.getUserName()) ||
                StringUtils.isEmpty(econtractTemplateBo.getEcontractType())
                || StringUtils.isEmpty(econtractTemplateBo.getName())) {
            return WmRestReturnUtil.fail("必填项为空");
        }
        boolean isExist = econtractManagerService.isEcontractTemplateExist(econtractTemplateBo.getEcontractType());
        if (isExist) {
            return WmRestReturnUtil.fail("当前code重复，需修改后提交");
        }
        try {
            int userId = UserUtils.getUser().getId();
            WmEmploy wmEmploy = employService.getEmployInfoById(userId);
            if (null != wmEmploy) {
                econtractTemplateBo.setAuthorityMisId(wmEmploy.getMisId());
            }
            boolean createResult = econtractManagerService.createSignFlowTemplate(econtractTemplateBo, userId);
            if (!createResult) {
                return WmRestReturnUtil.fail("创建失败");
            }
            return WmRestReturnUtil.success("success");
        } catch (Exception e) {
            LOGGER.error("create_econtract_template ", e);
            return WmRestReturnUtil.fail("fail");
        }
    }

    @ResponseBody
    @RequestMapping(value = "/del_econtract_template", method = {RequestMethod.POST})
    public Object delEcontractTemplate(Integer econtractId) {
        try {
            String econtractUser = userManagerService.getEcontractUserByUserId(UserUtils.getUser().getId());
            if (StringUtils.isBlank(econtractUser)) {
                return WmRestReturnUtil.fail("无操作权限");
            }
            econtractManagerService.delEcontractTemplate(econtractId);
            return WmRestReturnUtil.success("success");
        } catch (Exception e) {
            LOGGER.error("del_econtract_template ", e);
            return WmRestReturnUtil.fail("fail");
        }
    }

    @ResponseBody
    @RequestMapping(value = "/query_econtract_page", method = {RequestMethod.POST})
    public Object queryEcontractRecordByPage(EcontractRecordWebQueryBo queryBo) {
        try {
            String econtractUser = userManagerService.getEcontractUserByUserId(UserUtils.getUser().getId());
            if (StringUtils.isBlank(econtractUser)) {
                return WmRestReturnUtil.fail("无操作权限");
            }
            List<EcontractRecordBo> econtractRecordBoList = econtractManagerService.queryEcontractRecordByPage(queryBo);
            PageInfoBo pageInfoBo = econtractManagerService.queryEcontractRecordPageInfo(queryBo);
            Map<String, Object> returnData = Maps.newHashMap();
            returnData.put("econtractRecordBoList", econtractRecordBoList);
            returnData.put("pageInfoBo", pageInfoBo);
            return WmRestReturnUtil.success(returnData);
        } catch (Exception e) {
            LOGGER.error("query_econtract_page ", e);
            return WmRestReturnUtil.fail("fail");
        }
    }

    @ResponseBody
    @RequestMapping(value = "/create_pdf_v2", method = {RequestMethod.GET, RequestMethod.POST})
    public Object createPdfV2(String ftlInfo, String optionsInfo, String jsonInfo, Boolean vertical) {
        LOGGER.info("createPdf ftlInfo = {}, optionsInfo = {}, jsonInfo = {}, vertical = {}", ftlInfo, optionsInfo, jsonInfo, vertical);
        if (StringUtils.isEmpty(ftlInfo)) {
            return WmRestReturnUtil.fail("参数不完整!");
        }

        try {
            String econtractUser = userManagerService.getEcontractUserByUserId(UserUtils.getUser().getId());
            if (StringUtils.isBlank(econtractUser)) {
                return WmRestReturnUtil.fail("无操作权限");
            }
            PdfContext pdfBo = new PdfContext(0L, StringUtils.EMPTY, ftlInfo, optionsInfo, jsonInfo, vertical);
            PdfContentInfoBo pdfContent = new PdfContentInfoBo();
            pdfContent.setFtlInfo(pdfBo.getFtlInfo());
            pdfContent.setPdfBizContent(pdfBo.getBizContent());
            pdfContent.setPdfMetaContent(pdfBo.getMetaContent());
            pdfContent.setVertical(pdfBo.getVertical());
            String pdfUrl = econtractManagerService.createPdf(pdfContent);

            if (StringUtils.isNotEmpty(pdfUrl)) {
                JSONObject jo = new JSONObject();
                jo.put("pdfurl", pdfUrl);
                return WmRestReturnUtil.success(jo);
            }
        } catch (Exception e) {
            LOGGER.error("createPdf err!", e);
            return WmRestReturnUtil.fail("创建pdf失败! 失败原因:" + e.getMessage());
        }
        return WmRestReturnUtil.fail("创建pdf失败!");
    }

    /**
     * 新建PDF
     */
    @ResponseBody
    @RequestMapping(value = "/create_pdf", method = {RequestMethod.POST})
    public Object createPdf(@RequestBody PdfContext pdfBo) {
        LOGGER.info("createPdf pdfBo = {}", JSON.toJSONString(pdfBo));
        if (StringUtils.isEmpty(pdfBo.getFtlInfo())) {
            return WmRestReturnUtil.fail("参数不完整!");
        }

        try {
            String econtractUser = userManagerService.getEcontractUserByUserId(UserUtils.getUser().getId());
            if (StringUtils.isBlank(econtractUser)) {
                return WmRestReturnUtil.fail("无操作权限");
            }
            PdfContentInfoBo pdfContent = new PdfContentInfoBo();
            pdfContent.setFtlInfo(pdfBo.getFtlInfo());
            pdfContent.setPdfBizContent(pdfBo.getBizContent());
            pdfContent.setPdfMetaContent(pdfBo.getMetaContent());
            pdfContent.setVertical(pdfBo.getVertical());
            String pdfUrl = econtractManagerService.createPdf(pdfContent);

            if (StringUtils.isNotEmpty(pdfUrl)) {
                JSONObject jo = new JSONObject();
                jo.put("pdfurl", pdfUrl);
                return WmRestReturnUtil.success(jo);
            }
        } catch (Exception e) {
            LOGGER.error("createPdf err!", e);
            return WmRestReturnUtil.fail("创建pdf失败! 失败原因:" + e.getMessage());
        }
        return WmRestReturnUtil.fail("创建pdf失败!");
    }

    @ResponseBody
    @RequestMapping(value = "/query_all_pdf_template", method = {RequestMethod.GET, RequestMethod.POST})
    public Object listPdfTemplate(Integer pageNum, Integer pageSize) {
        LOGGER.info("listPdf pageNum = {}, pageSize", pageNum, pageSize);
        if (pageNum <= 0 || pageSize > MAX_PAGESIZE) {
            return WmRestReturnUtil.fail("查询不合规");
        }
        User user = UserUtils.getUser();
        if (user == null) {
            return WmRestReturnUtil.fail("登录信息不完整");
        }

        try {
            String econtractUser = userManagerService.getEcontractUserByUserId(user.getId());
            if (StringUtils.isBlank(econtractUser)) {
                return WmRestReturnUtil.fail("无操作权限");
            }
            PageAndListInfoBo pdfInfoBoList = econtractManagerService.listPdfTemplate(econtractUser, pageNum, pageSize);
            return WmRestReturnUtil.success(pdfInfoBoList);
        } catch (Exception e) {
            LOGGER.error("查询PDF模板列表, pageNum = " + pageNum + "pageSize = " + pageSize, e);
            return WmRestReturnUtil.fail("系统异常,保存失败");
        }
    }

    /**
     * 保存PDF模板配置
     */
    @ResponseBody
    @RequestMapping(value = "/save_pdf_template", method = {RequestMethod.POST})
    public Object savePdfTemplate(@RequestBody PdfContext pdfBo) {
        LOGGER.info("savePdfTemplate pdfBo = {}", JSON.toJSONString(pdfBo));
        if (pdfBo == null
            || StringUtils.isBlank(pdfBo.getFtlInfo())
            || StringUtils.isBlank(pdfBo.getOptionsInfo())
            || StringUtils.isBlank(pdfBo.getName())) {
            return WmRestReturnUtil.fail("参数不完整!");
        }
        if (pdfBo.getName().length() > MAX_LENGTH) {
            return WmRestReturnUtil.fail("名称不允许大于" + MAX_LENGTH);
        }
        User user = UserUtils.getUser();
        if (user == null) {
            return WmRestReturnUtil.fail("登录信息不完整");
        }
        LOGGER.info("user = {}", JSON.toJSONString(user));
        WmEmploy wmEmploy = employService.getEmployInfoById(user.getId());
        String misId = "";
        if(null!=wmEmploy){
            misId = wmEmploy.getMisId();
        }
        try {
            String econtractUser = userManagerService.getEcontractUserByUserId(user.getId());
            if (StringUtils.isBlank(econtractUser)) {
                return WmRestReturnUtil.fail("无操作权限");
            }
            LOGGER.info("savePdfTemplate econtractUser = {}", JSON.toJSONString(econtractUser));
            PdfContentInfoBo pdfInfoBo = new PdfContentInfoBo.Builder()
                .setId(pdfBo.getId())
                .setPdfTemplateName(pdfBo.getName())
                .setFtlInfo(pdfBo.getFtlInfo())
                .setOptionsInfo(pdfBo.getOptionsInfo())
                .setJsonInfo(pdfBo.getJsonInfo())
                .setVertical(pdfBo.getVertical())
                .setAuthorityMisId(misId)
                .build();
            String result = econtractManagerService.savePdfTemplate(econtractUser, pdfInfoBo);
            if(StringUtils.isNotBlank(result)){
                return WmRestReturnUtil.fail("非权限所有人,更新失败");
            }
        } catch (EcontractException e) {
            LOGGER.warn("保存PDF模板失败, msg = " + e.getMessage(), e);
            return WmRestReturnUtil.fail(e.getMessage());
        } catch (Exception e) {
            LOGGER.error("保存PDF模板失败, msg = " + e.getMessage(), e);
            return WmRestReturnUtil.fail("系统异常,保存失败");
        }
        return WmRestReturnUtil.success("success");
    }

    @ResponseBody
    @RequestMapping(value = "/sync_pdf_template", method = {RequestMethod.GET, RequestMethod.POST})
    public Object batchSyncPdfTemplate(String ids, String toEnv) {
        if (StringUtils.isBlank(ids)
            || StringUtils.isBlank(toEnv)) {
            return WmRestReturnUtil.fail("参数不完整!");
        }
        User user = UserUtils.getUser();
        if (user == null) {
            return WmRestReturnUtil.fail("登录信息不完整");
        }
        LOGGER.info("({})batchSyncPdfTemplate ids = {}, toEnv = {}", user.getName(), ids, toEnv);

        try {
            String econtractUser = userManagerService.getEcontractUserByUserId(user.getId());
            if (StringUtils.isBlank(econtractUser)) {
                return WmRestReturnUtil.fail("无操作权限");
            }

            List<Integer> idList = SplitUtil.splitToIntList(ids);
            for (Integer id:idList) {
                econtractManagerService.syncPdfTemplate(id, toEnv);
            }
        } catch (EcontractException e) {
            LOGGER.warn("同步PDF模板失败, msg = " + e.getMessage(), e);
            return WmRestReturnUtil.fail(e.getMessage());
        } catch (Exception e) {
            LOGGER.error("同步PDF模板失败, msg = " + e.getMessage(), e);
            return WmRestReturnUtil.fail(e.getMessage());
        }
        return WmRestReturnUtil.success("操作成功");
    }

    /**
     * 查询已有的PDF模板
     */
    @ResponseBody
    @RequestMapping(value = "/query_pdf_template_by_name", method = {RequestMethod.GET, RequestMethod.POST})
    public Object getPdfTemplate(String name) {
        LOGGER.info("getPdfTemplate name = {}", name);
        if (StringUtils.isBlank(name)) {
            return WmRestReturnUtil.fail("参数不完整!");
        }
        User user = UserUtils.getUser();
        if (user == null) {
            return WmRestReturnUtil.fail("登录信息不完整");
        }

        try {
            String econtractUser = userManagerService.getEcontractUserByUserId(user.getId());
            if (StringUtils.isBlank(econtractUser)) {
                return WmRestReturnUtil.fail("无操作权限");
            }
            PdfContentInfoBo pdfContentInfoBo = econtractManagerService.getPdfTemplate(econtractUser, name);
            PdfContext context = new PdfContext.Builder()
                .id(pdfContentInfoBo.getId())
                .name(pdfContentInfoBo.getPdfTemplateName())
                .ftlInfo(pdfContentInfoBo.getFtlInfo())
                .jsonInfo(pdfContentInfoBo.getJsonInfo())
                .optionsInfo(pdfContentInfoBo.getOptionsInfo())
                .build();
            return WmRestReturnUtil.success(context);
        } catch (Exception e) {
            LOGGER.error("获取PDF配置模板失败, msg = " + e.getMessage(), e);
            return WmRestReturnUtil.fail("系统异常,保存失败");
        }
    }

    /**
     * 删除的PDF模板
     */
    @ResponseBody
    @RequestMapping(value = "/delete_pdf_template_by_name", method = {RequestMethod.GET, RequestMethod.POST})
    public Object delPdfTemplate(String name) {
        LOGGER.info("delPdfTemplate name = {}", name);
        if (StringUtils.isBlank(name)) {
            return WmRestReturnUtil.fail("参数不完整!");
        }
        User user = UserUtils.getUser();
        if (user == null) {
            return WmRestReturnUtil.fail("登录信息不完整");
        }
        int userId = user.getId();
        WmEmploy wmEmploy = employService.getEmployInfoById(userId);
        if (wmEmploy == null) {
            return WmRestReturnUtil.fail("获取员工信息失败");
        }
        try {
            String econtractUser = userManagerService.getEcontractUserByUserId(user.getId());
            if (StringUtils.isBlank(econtractUser)) {
                return WmRestReturnUtil.fail("无操作权限");
            }
            String errorMsg = econtractManagerService.deletePdfTemplate(econtractUser, name,wmEmploy.getMisId());
            if(StringUtils.isNotBlank(errorMsg)){
                return WmRestReturnUtil.fail(errorMsg);
            }
            return WmRestReturnUtil.success("success");
        } catch (Exception e) {
            LOGGER.error("删除PDF配置模板失败, msg = " + e.getMessage(), e);
            return WmRestReturnUtil.fail("系统异常,保存失败");
        }
    }

    /**
     * 查询全部配置的签约页面
     */
    @ResponseBody
    @RequestMapping(value = "/query_all_signpage_template", method = {RequestMethod.GET, RequestMethod.POST})
    public Object listSignPage(Integer pageNum, Integer pageSize) {
        LOGGER.info("listSignPage pageNum = {}, pageSize", pageNum, pageSize);
        if (pageNum <= 0) {
            return WmRestReturnUtil.fail("查询不合规");
        }
        User user = UserUtils.getUser();
        if (user == null) {
            return WmRestReturnUtil.fail("登录信息不完整");
        }

        try {
            String econtractUser = userManagerService.getEcontractUserByUserId(user.getId());
            if (StringUtils.isBlank(econtractUser)) {
                return WmRestReturnUtil.fail("无操作权限");
            }
            PageAndListInfoBo pdfInfoBoList = econtractManagerService.listSignPageTemplate(econtractUser, pageNum, pageSize);
            return WmRestReturnUtil.success(pdfInfoBoList);
        } catch (Exception e) {
            LOGGER.error("获取H5签约模板失败, msg = " + e.getMessage(), e);
            return WmRestReturnUtil.fail("系统异常,保存失败");
        }
    }

    /**
     * 保存合同签约页
     */
    @ResponseBody
    @RequestMapping(value = "/save_signpage_template", method = {RequestMethod.POST})
    public Object saveSignPage(@RequestBody SignPageVo signPageVo) {
        LOGGER.info("savePdfTemplate signPageVo = {}", JSON.toJSONString(signPageVo));
        if (signPageVo == null
            || StringUtils.isBlank(signPageVo.getName())
            || signPageVo.getSignPageInfo() == null) {
            return WmRestReturnUtil.fail("参数不完整!");
        }
        if (signPageVo.getName().length() > MAX_LENGTH) {
            return WmRestReturnUtil.fail("名称不允许大于" + MAX_LENGTH);
        }
        User user = UserUtils.getUser();
        if (user == null) {
            return WmRestReturnUtil.fail("登录信息不完整");
        }
        String misId = "";
        WmEmploy wmEmploy = employService.getEmployInfoById(user.getId());
        if(wmEmploy!=null){
            misId = wmEmploy.getMisId();
        }
        try {
            String econtractUser = userManagerService.getEcontractUserByUserId(user.getId());
            if (StringUtils.isBlank(econtractUser)) {
                return WmRestReturnUtil.fail("无操作权限");
            }
            SignPageInfoBo signPageInfoBo = signPageVo.getSignPageInfo();
            signPageInfoBo.setTemplateName(signPageVo.getName());
            signPageInfoBo.setId(signPageVo.getId());
            signPageInfoBo.setAuthorityMisId(misId);
            String errorInfo = econtractManagerService.saveSignPageTemplate(econtractUser, signPageInfoBo);
            if(StringUtils.isNotBlank(errorInfo)){
                return WmRestReturnUtil.fail(errorInfo);
            }
        } catch (EcontractException e) {
            LOGGER.warn("保存签约信息页面, msg = " + e.getMessage(), e);
            return WmRestReturnUtil.fail(e.getMessage());
        } catch (Exception e) {
            LOGGER.error("保存签约信息页面, msg = " + e.getMessage(), e);
            return WmRestReturnUtil.fail("系统异常,保存失败");
        }
        return WmRestReturnUtil.success("success");
    }

    @ResponseBody
    @RequestMapping(value = "/sync_signpage_template", method = {RequestMethod.GET, RequestMethod.POST})
    public Object batchSyncSignPage(String ids, String toEnv) {
        if (StringUtils.isBlank(ids)
            || StringUtils.isBlank(toEnv)) {
            return WmRestReturnUtil.fail("参数不完整!");
        }
        User user = UserUtils.getUser();
        if (user == null) {
            return WmRestReturnUtil.fail("登录信息不完整");
        }
        LOGGER.info("({})batchSyncSignPage ids = {}, toEnv = {}", user.getName(), ids, toEnv);

        try {
            String econtractUser = userManagerService.getEcontractUserByUserId(user.getId());
            if (StringUtils.isBlank(econtractUser)) {
                return WmRestReturnUtil.fail("无操作权限");
            }

            List<Integer> idList = SplitUtil.splitToIntList(ids);
            for (Integer id:idList) {
                econtractManagerService.syncSignPageTemplate(id, toEnv);
            }
        } catch (EcontractException e) {
            LOGGER.warn("同步签约模板失败, msg = " + e.getMessage(), e);
            return WmRestReturnUtil.fail(e.getMessage());
        } catch (Exception e) {
            LOGGER.error("同步签约模板失败, msg = " + e.getMessage(), e);
            return WmRestReturnUtil.fail(e.getMessage());
        }
        return WmRestReturnUtil.success("操作成功");
    }

    @ResponseBody
    @RequestMapping(value = "/query_signpage_template_by_name", method = {RequestMethod.GET, RequestMethod.POST})
    public Object getSignPageInfo(String name) {
        LOGGER.info("getSingPageInfo name = {}", name);
        if (StringUtils.isBlank(name)) {
            return WmRestReturnUtil.fail("参数不完整!");
        }
        User user = UserUtils.getUser();
        if (user == null) {
            return WmRestReturnUtil.fail("登录信息不完整");
        }

        try {
            String econtractUser = userManagerService.getEcontractUserByUserId(user.getId());
            if (StringUtils.isBlank(econtractUser)) {
                return WmRestReturnUtil.fail("无操作权限");
            }
            SignPageInfoBo signInfoBo = econtractManagerService.getSignPageTemplate(econtractUser, name);
            SignPageVo signPageVo = new SignPageVo.Builder()
                .id(signInfoBo.getId())
                .name(name)
                .signPageInfo(signInfoBo)
                .build();
            return WmRestReturnUtil.success(signPageVo);
        } catch (Exception e) {
            LOGGER.error("获取签约页面配置模板失败, msg = " + e.getMessage(), e);
            return WmRestReturnUtil.fail("系统异常,保存失败");
        }
    }

    @ResponseBody
    @RequestMapping(value = "/delete_signpage_template_by_name", method = {RequestMethod.GET, RequestMethod.POST})
    public Object delSignPageInfo(String name) {
        LOGGER.info("delSignPageInfo name = {}", name);
        if (StringUtils.isBlank(name)) {
            return WmRestReturnUtil.fail("参数不完整!");
        }
        User user = UserUtils.getUser();
        if (user == null) {
            return WmRestReturnUtil.fail("登录信息不完整");
        }
        int userId = user.getId();
        WmEmploy wmEmploy = employService.getEmployInfoById(userId);
        if (wmEmploy == null) {
            return WmRestReturnUtil.fail("获取员工信息失败");
        }
        try {
            String econtractUser = userManagerService.getEcontractUserByUserId(user.getId());
            if (StringUtils.isBlank(econtractUser)) {
                return WmRestReturnUtil.fail("无操作权限");
            }
            String errorMsg = econtractManagerService.deleteSignPageTemplate(econtractUser, name,wmEmploy.getMisId());
            if(StringUtils.isNotBlank(errorMsg)){
                return WmRestReturnUtil.fail(errorMsg);
            }
            return WmRestReturnUtil.success("success");
        } catch (Exception e) {
            LOGGER.error("获取签约页面配置模板失败, msg = " + e.getMessage(), e);
            return WmRestReturnUtil.fail("系统异常,保存失败");
        }
    }

    @ResponseBody
    @RequestMapping(value = "/bind_template_and_signpage", method = {RequestMethod.GET, RequestMethod.POST})
    public Object bindEcontract(String econtractType, String signPageName) {
        if (StringUtils.isBlank(econtractType)) {
            return WmRestReturnUtil.fail("参数不完整");
        }
        if (StringUtils.isBlank(signPageName)) {
            return WmRestReturnUtil.fail("绑定签约页不允许为空");
        }

        try {
            String econtractUser = userManagerService.getEcontractUserByUserId(UserUtils.getUser().getId());
            if (StringUtils.isBlank(econtractUser)) {
                return WmRestReturnUtil.fail("无操作权限");
            }
            econtractManagerService.bindSignPageTemplate(econtractType, signPageName);
            return WmRestReturnUtil.success("success");
        } catch (Exception e) {
            LOGGER.error("绑定电子合同模板与签约页失败, msg = " + e.getMessage(), e);
            return WmRestReturnUtil.fail("系统异常,保存失败");
        }
    }

    @ResponseBody
    @RequestMapping(value = "/sync_econtract_rel", method = {RequestMethod.GET, RequestMethod.POST})
    public Object batchSyncEcontractRel(String ids, String toEnv) {
        if (StringUtils.isBlank(ids)
            || StringUtils.isBlank(toEnv)) {
            return WmRestReturnUtil.fail("参数不完整!");
        }
        User user = UserUtils.getUser();
        if (user == null) {
            return WmRestReturnUtil.fail("登录信息不完整");
        }
        LOGGER.info("({})batchSyncEcontractRel ids = {}, toEnv = {}", user.getName(), ids, toEnv);

        try {
            String econtractUser = userManagerService.getEcontractUserByUserId(user.getId());
            if (StringUtils.isBlank(econtractUser)) {
                return WmRestReturnUtil.fail("无操作权限");
            }

            List<Integer> idList = SplitUtil.splitToIntList(ids);
            for (Integer id:idList) {
                econtractManagerService.syncEcontractBindRel(id, toEnv);
            }
        } catch (EcontractException e) {
            LOGGER.warn("同步签约模板失败, msg = " + e.getMessage(), e);
            return WmRestReturnUtil.fail(e.getMessage());
        } catch (Exception e) {
            LOGGER.error("同步签约模板失败, msg = " + e.getMessage(), e);
            return WmRestReturnUtil.fail(e.getMessage());
        }
        return WmRestReturnUtil.success("操作成功");
    }

    @ResponseBody
    @RequestMapping(value = "/bind_user", method = {RequestMethod.GET, RequestMethod.POST})
    public Object bindUser(String econtractUser, Integer user) {
        if (StringUtils.isBlank(econtractUser)) {
            return WmRestReturnUtil.fail("参数不完整");
        }
        if (user == null || user <= 0) {
            user = UserUtils.getUser().getId();
        }
        LOGGER.info("user = " + JSON.toJSONString(UserUtils.getUser()));
        if (!ConfigUtilAdapter.getString(WHITE_LIST_KEY, WHITE_LIST_DEFAULT).contains(UserUtils.getUser().getLogin())) {
            return WmRestReturnUtil.fail("无操作权限, 请联系:" + ConfigUtilAdapter.getString(WHITE_LIST_KEY, WHITE_LIST_DEFAULT));
        }

        try {
            userManagerService.bindUser(econtractUser, user);
            return WmRestReturnUtil.success("success");
        } catch (Exception e) {
            LOGGER.error("绑定用户关系失败, msg = " + e.getMessage(), e);
            return WmRestReturnUtil.fail("系统异常,保存失败");
        }
    }

    @RequestMapping(value = "/pdf_template/update_authority_mis_id")
    @ResponseBody
    public Object updatePdfAuthorityMisId(Integer id, String authorityMisId) {
        if(id==null || StringUtils.isBlank(authorityMisId)){
            return WmRestReturnUtil.fail("参数不能为空");
        }
        User user = UserUtils.getUser();
        WmEmploy wmEmploy = employService.getEmployInfoById(user.getId());
        if(wmEmploy==null){
            return WmRestReturnUtil.fail("获取当前登录人misId失败");
        }
        try{
            String errorInfo = econtractManagerService.updateContractFtlAuthorityById(id,authorityMisId,wmEmploy.getMisId());
            if(StringUtils.isNotBlank(errorInfo)){
                return WmRestReturnUtil.fail(errorInfo);
            }
        }catch (Exception e){
            LOGGER.error("更新PDF模板权限所有人失败，id:{},authorityMisId:{}",id,authorityMisId,e);
            return WmRestReturnUtil.fail("更新失败，系统异常");
        }
        return WmRestReturnUtil.success("success");
    }

    @RequestMapping(value = "/sign_template/update_authority_mis_id")
    @ResponseBody
    public Object updateSignAuthorityMisId(Integer id, String authorityMisId) {
        if(id==null || StringUtils.isBlank(authorityMisId)){
            return WmRestReturnUtil.fail("参数不能为空");
        }
        User user = UserUtils.getUser();
        WmEmploy wmEmploy = employService.getEmployInfoById(user.getId());
        if(wmEmploy==null){
            return WmRestReturnUtil.fail("获取当前登录人misId失败");
        }
        try{
            String errorInfo = econtractManagerService.updateSignFtlAuthorityById(id,authorityMisId,wmEmploy.getMisId());
            if(StringUtils.isNotBlank(errorInfo)){
                return WmRestReturnUtil.fail(errorInfo);
            }
        }catch (Exception e){
            LOGGER.error("更新签约模板权限所有人失败，id:{},authorityMisId:{}",id,authorityMisId,e);
            return WmRestReturnUtil.success("更新失败，系统异常");
        }
        return WmRestReturnUtil.success("success");
    }
}

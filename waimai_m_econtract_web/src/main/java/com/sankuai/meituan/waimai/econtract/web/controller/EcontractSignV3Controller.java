package com.sankuai.meituan.waimai.econtract.web.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListeningExecutorService;
import com.google.common.util.concurrent.MoreExecutors;
import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.econtract.web.constant.MccConstant;
import com.sankuai.meituan.waimai.econtract.web.service.contentwrapper.WmEcontractContentWrapperFactory;
import com.sankuai.meituan.waimai.econtract.web.utils.EncryptUtil;
import com.sankuai.meituan.waimai.econtract.web.utils.ImageUtil;
import com.sankuai.meituan.waimai.econtract.web.utils.PdfConvertImageUtil;
import com.sankuai.meituan.waimai.econtract.web.utils.WmRestReturnUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.WebViewConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.CertifyH5InfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.SignH5InfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.applybiz.SignAdditionInfoMeta;
import com.sankuai.meituan.waimai.econtrct.client.domain.applybiz.WmContractContentAggreBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.base.BoolResult;
import com.sankuai.meituan.waimai.econtrct.client.domain.biz.EcontractRecordBo;
import com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException;
import com.sankuai.meituan.waimai.econtrct.client.service.EcontractBizService;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.elasticsearch.common.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Nullable;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * 针对拼好饭新费率模式-新交互方式
 * <AUTHOR>
 * @date 2021/4/30
 */
@InterfaceDoc(
        type = "restful",
        displayName = "电子合同签约相关接口",
        description = "电子合同签约相关接口",
        scenarios = "电子合同签约及页面相关",
        host = ""
)
@Controller
@RequestMapping("/econtract/restful/api/v3/sign")
public class EcontractSignV3Controller {

    private static final Logger LOGGER = LoggerFactory.getLogger(EcontractSignV3Controller.class);

    @Autowired
    private EcontractBizService econtractBizService;

    @Autowired
    private WmEcontractContentWrapperFactory wmEcontractContentWrapperFactory;

    private static ListeningExecutorService executorService;

    static {
        //初始化线程池
        executorService = MoreExecutors.listeningDecorator(Executors.newFixedThreadPool(ConfigUtilAdapter.getInt(
                "contract_pdf_thread_num", 10)));
    }


    @MethodDoc(
            displayName = "合同信息-聚合后返回",
            description = "合同信息-聚合后返回（获取协议PDF等数据）",
            parameters = {
                    @ParamDoc(
                            name = "param",
                            description = "param",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "Object", description = "调用返回")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "示例restExampleUrl",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "内部回调函数，无需鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @ResponseBody
    @RequestMapping(value = "/get_econtract_data_info")
    public Object getEcontractDataInfo(String param) {
        try {
            List<WmContractContentAggreBo> aggreBos = new ArrayList<>();
            List<WmContractContentAggreBo> aggreBoList = Lists.newArrayList();

            String recordKey = econtractBizService.queryRecordKeyBySecretParam(param);
            LOGGER.info("getEcontractDataInfo#param:{},recordKey:{}", param, recordKey);
            EcontractRecordBo recordBo = econtractBizService.queryEcontractRecordByRecordKey(recordKey);
            if (recordBo == null) {
                return WmRestReturnUtil.fail("查询数据失败,参数错误");
            }
            boolean isBatch = false;
            boolean isForceAllOp = false;
            if (recordBo.getRecordBatchId() != null && recordBo.getRecordBatchId() != 0) {
                List<EcontractRecordBo> recordBoList = econtractBizService.queryEcontractRecordByBatchId(recordBo.getRecordBatchId());
                if (CollectionUtils.isNotEmpty((recordBoList))) {
                    List<String> recordKeyList = recordBoList.stream().map(EcontractRecordBo::getRecordKey).collect(Collectors.toList());
                    List<WmContractContentAggreBo> aggreBoFuture = Lists.newArrayList();
                    CountDownLatch latch = new CountDownLatch(recordKeyList.size());
                    for (String record : recordKeyList) {
                        ListenableFuture<List<WmContractContentAggreBo>> future = executorService.submit(new Callable() {
                            @Override
                            public List<WmContractContentAggreBo> call() throws Exception {
                                return econtractBizService.queryEcontractContentAggreList(record);
                            }
                        });
                        Futures.addCallback(future, new FutureCallback<List<WmContractContentAggreBo>>() {
                            @Override
                            public void onSuccess(@Nullable List<WmContractContentAggreBo> result) {
                                aggreBoFuture.addAll(result);
                                latch.countDown();
                            }
                            @Override
                            public void onFailure(Throwable t) {
                                LOGGER.error("queryEcontractContentAggreList 查询合同列表数据异常record:{}", record);
                                latch.countDown();
                            }
                        });
                    }
                    latch.await();
                    isBatch = true;
                    isForceAllOp = econtractBizService.isForceAllOp(recordBo.getRecordBatchId());
                    aggreBoList.addAll(aggreBoFuture);
                }
            } else {
                //获取合同基本聚合信息
                aggreBoList = econtractBizService.queryEcontractContentAggreList(recordKey);
                isBatch = false;
            }

            if (CollectionUtils.isNotEmpty(aggreBoList)) {
                for (WmContractContentAggreBo aggreBo : aggreBoList) {
                    if (aggreBo == null){
                        continue;
                    }
                    analysisData(aggreBo,isBatch);
                    List<SignAdditionInfoMeta> extraInfoList = econtractBizService.queryEcontractListExtraInfo(aggreBo.getRecordKey(), aggreBo);
                    aggreBo.setSignAdditionInfo(extraInfoList);
                    LOGGER.info("getEcontractDataInfo aggreBo = {}", JSON.toJSONString(aggreBo));
                    aggreBos.add(aggreBo);
                }
            }
            String totalViewStage = queryTotalViewStage(aggreBos);
            JSONObject result = new JSONObject();
            result.put("totalViewStage",totalViewStage);
            result.put("isBatch", isBatch);
            result.put("isForceAllOp", isForceAllOp);

            List<WmContractContentAggreBo> contentAggreBos = Lists.newArrayList();
            List<WmContractContentAggreBo> successAggreBos = aggreBos.stream().filter(bo -> WebViewConstant.STAGE_SUCCESS.equals(bo.getViewStage())).collect(Collectors.toList());

            List<WmContractContentAggreBo> otherAggreBos = aggreBos.stream().filter(bo -> WebViewConstant.STAGE_WAIT_SIGN.equals(bo.getViewStage())
                    || WebViewConstant.STAGE_WAIT_ASY_EXECUTE.equals(bo.getViewStage())
                    || WebViewConstant.STAGE_WAIT_REAL_NAME.equals(bo.getViewStage())).collect(Collectors.toList());

            contentAggreBos.addAll(otherAggreBos);
            contentAggreBos.addAll(successAggreBos);

            if (recordBo.getRecordBatchId() != null && recordBo.getRecordBatchId() > 0){
                result.put("totalViewStage", contentAggreBos.size() > 0 ? WebViewConstant.STAGE_WAIT_SIGN : WebViewConstant.STAGE_CANCEL_SIGN);
            }
            result.put("aggreBos", contentAggreBos);

            LOGGER.info("getEcontractDataInfo#result:{}", JSON.toJSONString(result));
            return WmRestReturnUtil.success(result);
        } catch (EcontractException e) {
            LOGGER.warn("查询电子合同页面展示数据失败, msg:" + e.getMsg(), e);
            return WmRestReturnUtil.fail("查询数据失败, 错误码:" + e.getCode());
        } catch (TException | IOException e) {
            LOGGER.warn("查询电子合同页面展示数据失败, param:" + param + "msg:" + e.getMessage(), e);
            return WmRestReturnUtil.fail("查询数据失败");
        } catch (InterruptedException e) {
            LOGGER.warn("查询电子合同页面展示数据失败", e);
            return WmRestReturnUtil.fail("查询数据失败");
        } catch (Exception e) {
            LOGGER.error("EcontractSignV3Controller#getEcontractDataInfo, error", e);
            return WmRestReturnUtil.fail("查询数据失败");
        }
    }

    /**
     * 分页获取PDF图片
     * @param param
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/get_econtract_pdf_info")
    public Object getEcontractPDFInfo(String param,String recordKey,String pdfName, boolean isBatch,int pageOffset, int pageSize) {
        LOGGER.info("getEcontractPDFInfo param = {}, recordKey = {}, pdfName = {}, isBatch = {}, pageOffset = {}, pageSize = {}",
                param, recordKey, pdfName, isBatch, pageOffset, pageSize);
        try {
            String recordKeyUse = "";
            if (isBatch) {
                recordKeyUse = recordKey;
            } else {
                recordKeyUse = econtractBizService.queryRecordKeyBySecretParam(param);
            }

            //获取合同基本聚合信息
            WmContractContentAggreBo aggreBo = new WmContractContentAggreBo();
            List<WmContractContentAggreBo> aggreBoList = econtractBizService.queryEcontractContentAggreList(recordKeyUse);
            if (CollectionUtils.isNotEmpty(aggreBoList)) {
                List<WmContractContentAggreBo> tempAggreBo = aggreBoList.stream().filter(aggre -> {
                    // 协议名称变更，发版过程问题兼容
                    String thisPdfName = "";
                    String deliveryThisPdfName = "";
                    if(aggre.getContractName().equals("佣金") || aggre.getContractName().equals("平台服务费")){
                        thisPdfName = "佣金&" + aggre.getContractDesc();
                        deliveryThisPdfName = "平台服务费&" + aggre.getContractDesc();
                    }else if(aggre.getContractName().equals("配送服务费") || aggre.getContractName().equals("履约服务费")){
                        thisPdfName = "配送服务费&" + aggre.getContractDesc();
                        deliveryThisPdfName = "履约服务费&" + aggre.getContractDesc();
                    }else{
                        thisPdfName = aggre.getContractName() + "&" + aggre.getContractDesc();
                    }
                    return pdfName.equals(thisPdfName) || pdfName.equals(deliveryThisPdfName);
                }).collect(Collectors.toList());

                if (CollectionUtils.isEmpty(aggreBoList)) {
                    return WmRestReturnUtil.fail("无对应PDF：" + pdfName);
                }
                aggreBo = tempAggreBo.get(0);
                SignH5InfoBo signH5InfoBo = econtractBizService.querySignH5InoByRecordKey(recordKeyUse);
                aggreBo.setViewStage(signH5InfoBo.getViewStage());
                analysisDataWithPage(aggreBo, pageOffset, pageSize);
                LOGGER.info("getEcontractPDFInfo aggreBo = {}", JSON.toJSONString(aggreBo));
            }
            return WmRestReturnUtil.success(aggreBo);
        } catch (EcontractException e) {
            LOGGER.warn("查询电子合同页面展示数据失败, msg:" + e.getMsg(), e);
            return WmRestReturnUtil.fail("查询数据失败, 错误码:" + e.getCode());
        } catch (TException | IOException e) {
            LOGGER.warn("查询电子合同页面展示数据失败, param:" + param + "msg:" + e.getMessage(), e);
            return WmRestReturnUtil.fail("查询数据失败");
        }
    }


     /**
     * 签约页面-实名认证新页面
     *
     * @param param 中间层
     * @return 实名认证信息
     */
    @ResponseBody
    @RequestMapping(value = "/get_cert_info")
    public Object getCertInfo(String param) {
        LOGGER.info("getCertInfo param = {}", param);
        if (StringUtils.isEmpty(param) || "undefined".equals(param)) {
            return WmRestReturnUtil.fail("fail");
        }
        try {
            String longUrl = econtractBizService.getLongUrl(param);
            String innerParam = queryParamByLongUrl(longUrl);
            CertifyH5InfoBo certifyH5InfoBo = econtractBizService.queryH5CertifyInfoBySecretParam(innerParam);
            if (certifyH5InfoBo != null) {//查询不为空
                return WmRestReturnUtil.success(EncryptUtil.encryptCertifyH5InfoBo(certifyH5InfoBo));
            }
            //查询结果为空或查询异常
            CertifyH5InfoBo.Builder builder = new CertifyH5InfoBo.Builder().setCertType(0);
            String certPhone = econtractBizService.getCertPhone(param);
            if (StringUtils.isNotEmpty(certPhone)) {
                //电话打星
                StringBuilder sbCertPhone = new StringBuilder(certPhone);
                sbCertPhone.replace(3, 7, "****");
                LOGGER.info("{} certPhone is = {}", param, sbCertPhone.toString());
                builder.setCertPhone(sbCertPhone.toString());
            } else {
                builder.setCertPhone(certPhone);
            }
            return WmRestReturnUtil.success(builder.build());
        } catch (EcontractException e) {
            LOGGER.warn("fail to get getCertInfo ", e);
            return WmRestReturnUtil.fail("连接已失效,错误码:" + e.getCode());
        } catch (TException e) {
            LOGGER.error("fail to get getCertInfo ", e);
            return WmRestReturnUtil.fail("连接已失效");
        }
    }

    /**
     * 签约页面- 批量签约判断链接是否超时
     *
     * @param param 中间层
     * @return 链接状态
     */
    @ResponseBody
    @RequestMapping(value = "/check_effective_link")
    public Object getEffectiveLinkStatus(String param) {
        LOGGER.info("getEffectiveLinkStatus param = {}", param);
        if (StringUtils.isEmpty(param)) {
            return WmRestReturnUtil.fail("fail");
        }
        try {
            String longUrl = econtractBizService.getLongUrl(param);
            String innerParam = queryParamByLongUrl(longUrl);
            SignH5InfoBo signH5InfoBo = econtractBizService.querySignH5InoBySecretParamV2(innerParam);
            JSONObject result = new JSONObject();
            result.put("totalViewStage", signH5InfoBo.getViewStage());
            return WmRestReturnUtil.success(result);
        } catch (EcontractException e) {
            LOGGER.warn("fail to getEffectiveLinkStatus ", e);
            return WmRestReturnUtil.fail("查询签约链接状态异常,错误码:" + e.getCode());
        } catch (TException e) {
            LOGGER.error("fail to getEffectiveLinkStatus ", e);
            return WmRestReturnUtil.fail("查询签约链接状态异常");
        }
    }

    /**
     * 签约页面- 判断是否有可签约的合同
     *
     * @param param 中间层
     * @return 链接状态
     */
    @ResponseBody
    @RequestMapping(value = "/check_has_sign_contract")
    public Object getCanSignContract(String param) {
        LOGGER.info("getCanSignContract param = {}", param);
        if (StringUtils.isEmpty(param)) {
            return WmRestReturnUtil.fail("fail");
        }
        try {
            String longUrl = econtractBizService.getLongUrl(param);
            String innerParam = queryParamByLongUrl(longUrl);
            String recordKey = econtractBizService.queryRecordKeyBySecretParam(innerParam);
            String host = longUrl.substring(0, longUrl.lastIndexOf("?"));
            JSONObject result = new JSONObject();
            BoolResult boolResult = econtractBizService.queryHasEcontractData(recordKey,host);
            result.put("totalViewStage", boolResult.isValue());
            return WmRestReturnUtil.success(result);
        } catch (EcontractException e) {
            LOGGER.warn("fail to getCanSignContract ", e);
            return WmRestReturnUtil.fail("查询是否有可签约合同异常,错误码:" + e.getCode());
        } catch (Exception e) {
            LOGGER.error("fail to getCanSignContract ", e);
            return WmRestReturnUtil.fail("查询是否有可签约合同异常");
        }
    }

    private List<Object> getContractListV2(String recordKey) throws EcontractException, TException, IOException {
        String data = econtractBizService.queryEcontractData(recordKey, "pdf");
        LOGGER.info("getEcontractDataInfo data = {}", data);
        List<Object> result = wmEcontractContentWrapperFactory.analysisData(data, "pdf");
        return result;
    }

    private List<WmContractContentAggreBo> getContractListV3(String recordKey) throws EcontractException, TException {
        EcontractRecordBo recordBo = econtractBizService.queryEcontractRecordByRecordKey(recordKey);
        if (recordBo == null) {
            return Lists.newArrayList();
        }
        List<WmContractContentAggreBo> aggreBos = Lists.newArrayList();
        List<WmContractContentAggreBo> aggreBoList = Lists.newArrayList();
        if (recordBo.getRecordBatchId() == null || recordBo.getRecordBatchId() == 0) {
            //单合同
            aggreBoList = econtractBizService.queryEcontractContentAggreList(recordKey);
        } else {
            List<EcontractRecordBo> recordBoList = econtractBizService.queryEcontractRecordByBatchId(recordBo.getRecordBatchId());
            if (CollectionUtils.isNotEmpty((recordBoList))) {
                List<String> recordKeyList = recordBoList.stream().map(EcontractRecordBo::getRecordKey).collect(Collectors.toList());
                for (String record : recordKeyList) {
                    aggreBoList.addAll(econtractBizService.queryEcontractContentAggreList(record));
                }
            }
        }
        if (CollectionUtils.isNotEmpty(aggreBoList)) {
            for (WmContractContentAggreBo aggreBo : aggreBoList) {
                SignH5InfoBo signH5InfoBo = econtractBizService.querySignH5InoByRecordKey(aggreBo.getRecordKey());
                aggreBo.setViewStage(signH5InfoBo.getViewStage());
                aggreBos.add(aggreBo);
            }
        }
        List<WmContractContentAggreBo> filterList = aggreBos.stream().filter(bo -> WebViewConstant.STAGE_SUCCESS.equals(bo.getViewStage())
                || WebViewConstant.STAGE_WAIT_SIGN.equals(bo.getViewStage())
                || WebViewConstant.STAGE_WAIT_ASY_EXECUTE.equals(bo.getViewStage())
                || WebViewConstant.STAGE_WAIT_REAL_NAME.equals(bo.getViewStage())).collect(Collectors.toList());

        return filterList;
    }

    /**
     * 将PDF链接转化为图片
     *
     * @param aggreBo
     * @throws IOException
     */
    private void analysisData(WmContractContentAggreBo aggreBo,boolean isBatch) throws IOException {
        if (Strings.isEmpty(aggreBo.getPdfUrl())) {
            LOGGER.warn("EcontractSignV3Controller#analysisData, aggreBo: {}", JSON.toJSONString(aggreBo));
            return;
        }
        LOGGER.info("analysisData aggreBo = {}", JSON.toJSONString(aggreBo));
        byte[] bytesOfImage;
        byte[] bytesOfPdf = ImageUtil.getCloudPicBytes(aggreBo.getPdfUrl());
        List<String> pdfImageList = new ArrayList<>();
        int totalPages = PdfConvertImageUtil.getTotalPages(bytesOfPdf);
        LOGGER.info("[MCC开关清理]:已删除key=is.loading.pdf.pic.by.partition，value=true");
        if (!MccConstant.isBatchContractPDFLoadImage()){
            isBatch = false;
        }
        if (!isBatch){
            // 非批量转换图片，批量不转换图片
            int firstTimeCnt = Math.min(MccConstant.loadingPDFPicCntFirstTime(), totalPages);
            for (int i = 1; i < firstTimeCnt + 1; i++) {
                bytesOfImage = PdfConvertImageUtil.transfer(bytesOfPdf, i);
                pdfImageList.add(Base64.encodeBase64String(bytesOfImage));
            }
        }
        aggreBo.setTotalPage(totalPages);
        aggreBo.setPdfImageList(pdfImageList);
    }

    /**
     * 分页获取PDF图片
     * @param aggreBo
     * @param pageOffset
     * @param pageSize
     * @throws IOException
     */
    private void analysisDataWithPage(WmContractContentAggreBo aggreBo, int pageOffset, int pageSize) throws IOException {
        LOGGER.info("analysisDataWithPage aggreBo = {}", JSON.toJSONString(aggreBo));
        byte[] bytesOfImage;
        byte[] bytesOfPdf = ImageUtil.getCloudPicBytes(aggreBo.getPdfUrl());
        List<String> pdfImageList = new ArrayList<>();
        int totalPages = PdfConvertImageUtil.getTotalPages(bytesOfPdf);

        //校验，如果起始页大于总页码
        if (pageOffset > totalPages) {
            return;
        }

        int endCnt = Math.min(pageOffset + pageSize, totalPages + 1);

        //进行转换PDF->base64
        for (int i = pageOffset; i < endCnt; i++) {
            bytesOfImage = PdfConvertImageUtil.transfer(bytesOfPdf, i);
            pdfImageList.add(Base64.encodeBase64String(bytesOfImage));
        }

        aggreBo.setTotalPage(totalPages);
        aggreBo.setPdfImageList(pdfImageList);
    }

    /**
     * 获取合同列表总状态
     *
     * @param aggreBos
     * @return
     */
    private String queryTotalViewStage(List<WmContractContentAggreBo> aggreBos) {
        if (CollectionUtils.isNotEmpty(aggreBos)) {
            return aggreBos.get(0).getViewStage();//本期取第一个
        }
        return WebViewConstant.STAGE_FAIL;
    }

    /**
     * 根据完整长连接获取参数
     */
    private String queryParamByLongUrl(String longUrl) {
        //参数信息
        String params = longUrl.substring(longUrl.lastIndexOf("?") + 1);
        List<String> paramList = Arrays.asList(params.split("&"));
        for (String param : paramList) {
            if (param.startsWith("param=")) {
                return param.substring(param.lastIndexOf("=") + 1);
            }
        }
        return "";
    }
}

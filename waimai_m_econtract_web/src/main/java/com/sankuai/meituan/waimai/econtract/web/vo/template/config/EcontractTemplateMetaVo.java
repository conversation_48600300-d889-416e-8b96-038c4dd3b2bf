package com.sankuai.meituan.waimai.econtract.web.vo.template.config;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * 电子合同模版版本动态元素视图类
 * <AUTHOR>
 * @date 2020/12/27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EcontractTemplateMetaVo {

    /**
     * 自增主键
     */
    private Integer id;

    /**
     * 元素名称
     */
    private String name;

    /**
     * 唯一标识码（前端用）
     */
    private String code;

    /**
     * 元素描述
     */
    private String description;

    /**
     * 对应业务后端字段名
     */
    private String valueName;

    /**
     * 动态元素类型
     */
    private Integer metaTag;

    /**
     * 对应业务后端字段名
     */
    private String defaultValue;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        EcontractTemplateMetaVo that = (EcontractTemplateMetaVo) o;
        return Objects.equals(name, that.name) &&
                Objects.equals(code, that.code) &&
                Objects.equals(description, that.description) &&
                Objects.equals(valueName, that.valueName) &&
                Objects.equals(metaTag, that.metaTag) &&
                Objects.equals(defaultValue, that.defaultValue);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, code, description, valueName, metaTag, defaultValue);
    }
}

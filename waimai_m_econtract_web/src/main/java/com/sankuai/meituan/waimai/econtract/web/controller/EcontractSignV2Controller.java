package com.sankuai.meituan.waimai.econtract.web.controller;

import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.sankuai.meituan.waimai.econtract.web.service.contentwrapper.WmEcontractContentWrapperFactory;
import com.sankuai.meituan.waimai.econtract.web.utils.ImageUtil;
import com.sankuai.meituan.waimai.econtract.web.utils.PdfConvertImageUtil;
import com.sankuai.meituan.waimai.econtract.web.utils.WmRestReturnUtil;
import com.sankuai.meituan.waimai.econtract.web.vo.spArea.SpAreaDataVo;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractContentTypeEnum;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractPdfTypeEnum;
import com.sankuai.meituan.waimai.econtrct.client.domain.applybiz.WmPdfContentBo;
import com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException;
import com.sankuai.meituan.waimai.econtrct.client.service.EcontractBizService;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020/01/14
 * @time 下午4:36
 */
@InterfaceDoc(
        type = "restful",
        displayName = "电子合同签约相关接口",
        description = "电子合同签约相关接口",
        scenarios = "电子合同签约及页面相关",
        authors = {"yinzhangfu"},
        host = ""
)
@Controller
@RequestMapping("/econtract/restful/api/v2/sign")
public class EcontractSignV2Controller {

    private static final Logger LOGGER = LoggerFactory.getLogger(EcontractSignV2Controller.class);

    @Autowired
    private EcontractBizService econtractBizService;

    @Autowired
    private WmEcontractContentWrapperFactory wmEcontractContentWrapperFactory;

    @MethodDoc(
            displayName = "合同信息-聚合后返回",
            description = "合同信息-聚合后返回（获取协议PDF等数据）",
            parameters = {
                    @ParamDoc(
                            name = "param",
                            description = "param",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            responseParams = {
                    @ParamDoc(name = "Object", description = "调用返回")
            },
            returnValueDescription = "返回returnValueDescription",
            restExampleUrl = "示例restExampleUrl",
            restExampleResponseData = "输出restExampleResponseData",
            restExamplePostData = "示例restExamplePostData",
            example = "举例example",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "内部回调函数，无需鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @ResponseBody
    @RequestMapping(value = "/get_econtract_data_info")
    public Object getEcontractDataInfo(String param, String dataType) {
        try {
            String recordKey = econtractBizService.queryRecordKeyBySecretParam(param);

            if (EcontractContentTypeEnum.SP_AREA.getName().equals(dataType)) {
                //spArea类型单独处理
                Map<String, String> spAreaMap = econtractBizService.queryEcontractSpAreaData(recordKey);
                LOGGER.info("spAreaMap data = {}", spAreaMap);
                return WmRestReturnUtil.success(analysisSpAreaData(spAreaMap));
            }
            String data = econtractBizService.queryEcontractData(recordKey, dataType);
            LOGGER.info("getEcontractDataInfo data = {}", data);
            List<Object> result = wmEcontractContentWrapperFactory.analysisData(data, dataType);
            return WmRestReturnUtil.success(result);
        } catch (EcontractException e) {
            LOGGER.warn("查询电子合同页面展示数据失败, msg:" + e.getMsg(), e);
            return WmRestReturnUtil.fail("查询数据失败, 错误码:" + e.getCode());
        } catch (TException | IOException e) {
            LOGGER.warn("查询电子合同页面展示数据失败, param:" + param + "msg:" + e.getMessage(), e);
            return WmRestReturnUtil.fail("查询数据失败");
        }
    }

    @ResponseBody
    @RequestMapping(value = "/get_econtract_pdf_info")
    public Object getEcontractPDFInfo(String param, String pdfName, int pageOffset, int pageSize) {
        try {
            String recordKey = econtractBizService.queryRecordKeyBySecretParam(param);
            //获取PDF数据
            String data = econtractBizService.queryEcontractData(recordKey, EcontractContentTypeEnum.PDF.getName());
            LOGGER.info("getEcontractDataInfo data = {}", data);

            //分页获取PDF
            List<Object> result = analysisDataWithPage(data, pdfName, pageOffset, pageSize);

            return WmRestReturnUtil.success(result);
        } catch (EcontractException e) {
            LOGGER.warn("查询电子合同页面展示数据失败, msg:" + e.getMsg(), e);
            return WmRestReturnUtil.fail("查询数据失败, 错误码:" + e.getCode());
        } catch (TException | IOException e) {
            LOGGER.warn("查询电子合同页面展示数据失败, param:" + param + "msg:" + e.getMessage(), e);
            return WmRestReturnUtil.fail("查询数据失败");
        }
    }


    /**
     * 分页获取PDF图片信息
     * @param data
     * @param pdfTypeName
     * @param pageOffset
     * @param pageSize
     * @return
     * @throws IOException
     */
    public List<Object> analysisDataWithPage(String data, String pdfTypeName, int pageOffset, int pageSize) throws IOException {
        byte[] bytesOfImage;
        Map<String, WmPdfContentBo> boMap = ImageUtil.urlFormatToMap(data);
        List<Object> contentBoList = com.google.common.collect.Lists.newArrayList();

        //获取对应对象
        WmPdfContentBo targetBo = boMap.get(pdfTypeName);

        //获取PDF总页数
        byte[] bytesOfPdf = ImageUtil.getCloudPicBytes(targetBo.getPdf());
        int totalPages = PdfConvertImageUtil.getTotalPages(bytesOfPdf);

        //校验，如果起始页大于总页码
        if (pageOffset > totalPages) {
            return contentBoList;
        }

        int endCnt = Math.min(pageOffset + pageSize, totalPages + 1);

        //进行转换PDF->base64
        for (int i = pageOffset; i < endCnt; i++) {
            bytesOfImage = PdfConvertImageUtil.transfer(bytesOfPdf, i);
            WmPdfContentBo pdfContentBo = new WmPdfContentBo();
            pdfContentBo.setPdf(Base64.encodeBase64String(bytesOfImage));
            pdfContentBo.setPdfTypeName(targetBo.getPdfTypeName());
            pdfContentBo.setTotalPage(totalPages);
            CollectionUtils.addIgnoreNull(contentBoList, pdfContentBo);
        }

        return contentBoList;
    }

    /**
     * 解析配送范围信息spArea
     *
     * @param spAreaMap
     * @return
     * @throws IOException
     */
    private List<SpAreaDataVo> analysisSpAreaData(Map<String, String> spAreaMap) throws IOException {
        if (MapUtils.isEmpty(spAreaMap)) {
            Lists.newArrayList();
        }
        List<SpAreaDataVo> result = new ArrayList<>();
        Set<Map.Entry<String, String>> set = spAreaMap.entrySet();
        Iterator<Map.Entry<String, String>> iterator = set.iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, String> entry = iterator.next();
            String value = entry.getValue();
            String key = entry.getKey();
            if (StringUtils.isEmpty(value)) {
                continue;
            }
            SpAreaDataVo vo = new SpAreaDataVo();
            //历史数据中默认只有配送协议，所以只要是历史数据默认都是delivery
            vo.setKey("default".equals(key) ? EcontractPdfTypeEnum.DELIVERY.getName():key);
            vo.setKeyName("default".equals(key) ? EcontractPdfTypeEnum.DELIVERY.getDesc() : getEcontractContentTypeEnumDesc(key));
            vo.setKeyData(wmEcontractContentWrapperFactory.analysisData(value, EcontractContentTypeEnum.SP_AREA.getName()));
            result.add(vo);
        }
        return result;
    }

    /**
     * 获取EcontractPdfTypeEnum对应的desc
     * @param name
     * @return
     */
    private String getEcontractContentTypeEnumDesc(String name) {
        if (StringUtils.isEmpty(name)) {
            return Strings.EMPTY;
        }
        for (EcontractPdfTypeEnum item : EcontractPdfTypeEnum.values()) {
            if (item.getName().equals(name)) {
                return item.getDesc();
            }
        }
        return Strings.EMPTY;
    }

}

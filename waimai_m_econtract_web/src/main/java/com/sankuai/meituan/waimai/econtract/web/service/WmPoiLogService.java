package com.sankuai.meituan.waimai.econtract.web.service;

import com.sankuai.meituan.waimai.oplog.thrift.domain.WmPoiOplog;
import com.sankuai.meituan.waimai.oplog.thrift.service.WmPoiOplogThriftService;
import com.sankuai.meituan.waimai.poi.domain.WmPoiOpLog;
import org.apache.commons.lang.StringUtils;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class WmPoiLogService {

    private static final org.slf4j.Logger LOGGER = LoggerFactory.getLogger(WmPoiLogService.class);

    @Autowired
    private WmPoiOplogThriftService.Iface wmPoiOplogThriftService;

    public void insertPoiOpLogForPoi(Long wmPoiId, Integer accId, String accName, String recordKey) {
        try {
            WmPoiOplog wmPoiOplog = new WmPoiOplog();
            wmPoiOplog.setWm_poi_id(wmPoiId);
            wmPoiOplog.setOp_type(WmPoiOpLog.OpType.LOGISTICS_CONTRACT_EFFECT.getId());
            wmPoiOplog.setDiff(StringUtils.defaultString("C1合同到期，费率自动更新，商家确认完成"));
            wmPoiOplog.setOp_uid(accId);
            wmPoiOplog.setOp_uname(accName);
            wmPoiOplogThriftService.insertWithoutMQByAsync(wmPoiOplog);
        } catch (Exception e) {
            LOGGER.error("插入门店日志异常 wmPoiId={},accId={},accName:{},recordKey={}", wmPoiId, accId, accName, recordKey, e);
        }
    }
}

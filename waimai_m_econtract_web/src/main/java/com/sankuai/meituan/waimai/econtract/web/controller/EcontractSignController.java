package com.sankuai.meituan.waimai.econtract.web.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.google.common.base.Splitter;
import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.econtract.web.constant.MccConstant;
import com.sankuai.meituan.waimai.econtract.web.service.WmPoiLogService;
import com.sankuai.meituan.waimai.econtract.web.service.contentwrapper.WmEcontractContentWrapperFactory;
import com.sankuai.meituan.waimai.econtract.web.service.contentwrapper.WmEcontractExternalJumpWrapperFactory;
import com.sankuai.meituan.waimai.econtract.web.utils.ImageUtil;
import com.sankuai.meituan.waimai.econtract.web.utils.PageUtil;
import com.sankuai.meituan.waimai.econtract.web.utils.PdfConvertImageUtil;
import com.sankuai.meituan.waimai.econtract.web.utils.WmRestReturnUtil;
import com.sankuai.meituan.waimai.econtract.web.vo.trace.EcontractTraceVo;
import com.sankuai.meituan.waimai.econtrct.client.constants.*;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.SignH5InfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.SignPageInfoBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.biz.EcontractRecordBo;
import com.sankuai.meituan.waimai.econtrct.client.exception.EcontractException;
import com.sankuai.meituan.waimai.econtrct.client.service.EcontractAPIResponse;
import com.sankuai.meituan.waimai.econtrct.client.service.EcontractBizService;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Hou
 * @date 2017/10/26
 * @time 下午2:08
 */
@InterfaceDoc(
        type = "restful",
        displayName = "电子合同签约相关接口",
        description = "电子合同签约相关接口",
        scenarios = "电子合同签约及页面相关",
        authors = {"LuHou"},
        host = ""
)
@Controller
@RequestMapping("/econtract/restful/api/v1/sign")
public class EcontractSignController {

    private static final Logger LOGGER = LoggerFactory.getLogger(EcontractSignController.class);

    @Autowired
    private EcontractBizService econtractBizService;

    @Autowired
    private WmEcontractContentWrapperFactory wmEcontractContentWrapperFactory;

    @Autowired
    private WmEcontractExternalJumpWrapperFactory wmEcontractExternalJumpWrapperFactory;

    @Autowired
    private WmPoiLogService wmPoiLogService;


    /**
     * 前往签约人信息页面
     *
     * @param param 手机加密短连接
     * @return 合同记录编号
     */
    @ResponseBody
    @RequestMapping(value = "/get_signer_page_type")
    public Object getSignerPageType(String param) {
        LOGGER.info("get_signer_page_type param = {}", param);
        if (StringUtils.isEmpty(param) || "undefined".equals(param)) {
            return WmRestReturnUtil.fail("fail");
        }
        try {
            String h5Type = econtractBizService.querySignerPageTypeBySecretParamV2(param);
            if (StringUtils.isNotEmpty(h5Type)) {
                LOGGER.info("{} return h5Type is = {}", param, h5Type);
                return WmRestReturnUtil.success(h5Type);
            }
            return WmRestReturnUtil.fail("连接已失效");
        } catch (EcontractException e) {
            LOGGER.warn("fail to get recordkey ", e);
            return WmRestReturnUtil.fail("连接已失效,错误码:" + e.getCode());
        } catch (TException e) {
            LOGGER.error("fail to get recordkey ", e);
            return WmRestReturnUtil.fail("连接已失效");
        }
    }


    /**
     * 前往签约人信息页面
     *
     * @param param 手机加密短连接
     * @return 合同记录编号
     */
    @ResponseBody
    @RequestMapping(value = "/get_signer_info")
    public Object getSignerInfo(String param) {
        LOGGER.info("get_signer_info param = {}", param);
        if (StringUtils.isEmpty(param) || "undefined".equals(param)) {
            return WmRestReturnUtil.fail("fail");
        }
        try {
            SignH5InfoBo signH5InfoBo = econtractBizService.querySignH5InoBySecretParamV2(param);
            if (StringUtils.isNotEmpty(signH5InfoBo.getSignerPhone())) {
                //电话打星
                StringBuilder sbSignerPhone = new StringBuilder(signH5InfoBo.getSignerPhone());
                sbSignerPhone.replace(3,7, "****");
                signH5InfoBo.setSignerPhone(sbSignerPhone.toString());
            }
            LOGGER.info(param + "signer_info is = {}", JSON.toJSONString(signH5InfoBo));
            return WmRestReturnUtil.success(signH5InfoBo);
        } catch (EcontractException e) {
            LOGGER.warn("fail to get getSignerInfo ", e);
            return WmRestReturnUtil.fail("连接已失效,错误码:" + e.getCode());
        } catch (TException e) {
            LOGGER.error("fail to get getSignerInfo ", e);
            return WmRestReturnUtil.fail("连接已失效");
        }
    }

    /**
     * 根据param2查询验证码手机号
     *
     * @param param 手机加密短连接
     * @return 合同记录编号
     */
    @ResponseBody
    @RequestMapping(value = "/get_cert_phone")
    public Object getCertPhone(String param) {
        LOGGER.info("getCertPhone param = {}", param);
        if (StringUtils.isEmpty(param) || "undefined".equals(param)) {
            return WmRestReturnUtil.fail("fail");
        }
        try {
            String certPhone = econtractBizService.getCertPhoneV2(param);
            if (StringUtils.isNotEmpty(certPhone)) {
                //电话打星
                StringBuilder sbCertPhone = new StringBuilder(certPhone);
                sbCertPhone.replace(3, 7, "****");
                LOGGER.info("{} certPhone is = {}", param, sbCertPhone.toString());
                return WmRestReturnUtil.success(sbCertPhone.toString());
            }
            LOGGER.info("{} certPhone is = {}", param, certPhone);
            return WmRestReturnUtil.success(certPhone);
        } catch (EcontractException e) {
            LOGGER.warn("fail to get getCertPhone ", e);
            return WmRestReturnUtil.fail("连接已失效,错误码:" + e.getCode());
        } catch (TException e) {
            LOGGER.error("fail to get getCertPhone ", e);
            return WmRestReturnUtil.fail("连接已失效");
        }
    }



    /**
     * 查看pdf信息
     */
    @ResponseBody
    @RequestMapping(value = "/get_econtract_page_info")
    public Object getEcontractPageInfo(String recordKey) {
        LOGGER.info("getEcontractPageInfo recordKey = {} , pageNum = {}", recordKey);
        byte[] bytesOfImage = null;
        if (StringUtils.isEmpty(recordKey)) {
            return WmRestReturnUtil.fail("fail");
        }
        try {
            String urls = econtractBizService.queryEcontractSaveUrlV2(recordKey);
            List<String> urlList = ImageUtil.urlFormat(urls);
            List<String> picBase64StringList = new ArrayList<>();
            for (String url:urlList) {
                LOGGER.info("url : " + url);
                byte[] bytesOfPdf = ImageUtil.getCloudPicBytes(url);
                int totalPages = PdfConvertImageUtil.getTotalPages(bytesOfPdf);
                for (int i = 1; i < totalPages + 1; i++) {
                    bytesOfImage = PdfConvertImageUtil.transfer(bytesOfPdf, i);
                    picBase64StringList.add(Base64.encodeBase64String(bytesOfImage));
                }
            }
            return WmRestReturnUtil.success(picBase64StringList);
        } catch (EcontractException e) {
            LOGGER.warn("fail to get pdfPage ", e);
            return WmRestReturnUtil.fail("查询失败,错误码:" + e.getCode());
        } catch (TException | IOException e) {
            LOGGER.error("fail to get pdfPage ", e);
            return WmRestReturnUtil.fail("查询失败");
        }
    }

    /**
     * 查看pdf信息
     */
    @ResponseBody
    @RequestMapping(value = "/get_econtract_page_info_param")
    public Object getEcontractPageInfoByParam(String param) {
        try {
            String recordKey = econtractBizService.queryRecordKeyBySecretParam(param);
            return getEcontractPageInfo(recordKey);
        } catch (EcontractException e) {
            LOGGER.warn("fail to getEcontractPageInfoByParam param = " + param, e);
            return WmRestReturnUtil.fail("查询失败,错误码:" + e.getCode());
        } catch (TException e) {
            LOGGER.error("fail to getEcontractPageInfoByParam param = " + param, e);
            return WmRestReturnUtil.fail("查询失败");
        }
    }

    @ResponseBody
    @RequestMapping(value = "/get_econtract_data_info")
    public Object getEcontractDataInfo(String param, String dataType) {
        try {
            String recordKey = econtractBizService.queryRecordKeyBySecretParam(param);
            String data = econtractBizService.queryEcontractData(recordKey, dataType);
            LOGGER.info("getEcontractDataInfo data = {}", data);
            List<Object> result = wmEcontractContentWrapperFactory.analysisData(data, dataType);
            return WmRestReturnUtil.success(result);
        } catch (EcontractException e) {
            LOGGER.warn("查询电子合同页面展示数据失败, msg:" + e.getMsg(), e);
            return WmRestReturnUtil.fail("查询数据失败, 错误码:" + e.getCode());
        } catch (TException | IOException e) {
            LOGGER.warn("查询电子合同页面展示数据失败, param:" + param +"msg:" + e.getMessage() , e);
            return WmRestReturnUtil.fail("查询数据失败");
        }
    }

    @ResponseBody
    @RequestMapping(value = "/get_econtract_change_info")
    public Object getEcontractExternalJumpInfo(String param, Integer pageNum, Integer pageSize) {
        try {
            if (Boolean.TRUE.equals(ConfigUtilAdapter.getBoolean(MccConstant.CLOSE_GET_ECONTRACT_CHANGE_INFO, false))) {
                LOGGER.info("getEcontractExternalJumpInfo 功能关闭，直接返回空，param:{}", param);
                return WmRestReturnUtil.fail("查询数据失败");
            }
            String recordKey = econtractBizService.queryRecordKeyBySecretParam(param);
            String data = econtractBizService.queryExternalJumpData(recordKey, EcontractExternalJumpContentTypeEnum.CHANGE_INFO.getName());
            LOGGER.info("getEcontractExternalJumpInfo data = {}", data);
            List<Object> result = wmEcontractExternalJumpWrapperFactory.analysisData(data, EcontractExternalJumpContentTypeEnum.CHANGE_INFO.getName());
            return WmRestReturnUtil.success(PageUtil.toPageVo(result, pageNum, pageSize));
        } catch (EcontractException e) {
            LOGGER.warn("查询电子合同页面跳转信息数据失败, msg:" + e.getMsg(), e);
            return WmRestReturnUtil.fail("查询数据失败, 错误码:" + e.getCode());
        } catch (TException | IOException e) {
            LOGGER.warn("查询电子合同页面跳转信息数据失败, param:" + param +"msg:" + e.getMessage() , e);
            return WmRestReturnUtil.fail("查询数据失败");
        }
    }

    /**
     * 跳转
     *
     * @param recordKey
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/turn_real_name_auth")
    public Object turnRealNameAuth(String recordKey) {
        LOGGER.info("turnRealNameAuth recordKey = {}", recordKey);
        if (StringUtils.isEmpty(recordKey)) {
            return WmRestReturnUtil.fail("fail");
        }
        String url = "";
        try {
            url = econtractBizService.queryRealNameRedirectUrlV2(recordKey);
        } catch (EcontractException e) {
            LOGGER.warn("fail turnRealNameAuth ", e);
            return WmRestReturnUtil.fail("跳转实名认证失败,错误码:" + e.getCode());
        } catch (TException e) {
            LOGGER.error("fail turnRealNameAuth ", e);
            return WmRestReturnUtil.fail("跳转实名认证失败");
        }
        return WmRestReturnUtil.success(url);
    }

    /**
     * 跳转
     */
    @ResponseBody
    @RequestMapping(value = "/turn_real_name_auth_param")
    public Object turnRealNameAuthByParam(String param) {
        try {
            String recordKey = econtractBizService.queryRecordKeyBySecretParam(param);
            return turnRealNameAuth(recordKey);
        } catch (EcontractException e) {
            LOGGER.warn("fail turnRealNameAuthByParam ", e);
            return WmRestReturnUtil.fail("跳转实名认证失败,错误码:" + e.getCode());
        } catch (TException e) {
            LOGGER.error("fail turnRealNameAuthByParam ", e);
            return WmRestReturnUtil.fail("跳转实名认证失败");
        }
    }

    /**
     * 发送验证码
     */
    @ResponseBody
    @RequestMapping(value = "/send_phone_captcha")
    public Object sendPhoneCap(String recordKey) {
        LOGGER.info("sendPhoneCap recordKey = {}", recordKey);
        if (StringUtils.isEmpty(recordKey)) {
            return WmRestReturnUtil.fail("发送验证码失败,参数异常");
        }
        try {
            if (!econtractBizService.sendPhoneCapV2(recordKey).isValue()) {
                return WmRestReturnUtil.failWithCode(WmRestReturnUtil.CAPTCHA_FAILED_CODE, "发送验证码失败");
            }
        } catch (EcontractException e) {
            LOGGER.warn("fail sendPhoneCap ", e);
            // SERVER_ERROR为系统异常
            if (e.getCode() == EcontractException.SERVER_ERROR) {
                return WmRestReturnUtil.fail("发送验证码失败,错误码:" + e.getCode());
            }
            // 否则为业务异常
            return WmRestReturnUtil.failWithCode(WmRestReturnUtil.CAPTCHA_FAILED_CODE, "发送验证码失败,错误码:" + e.getCode());
        } catch (TException e) {
            LOGGER.error("fail sendPhoneCap ", e);
            return WmRestReturnUtil.fail("发送验证码失败");
        }
        return WmRestReturnUtil.success("success");
    }

    /**
     * 发送验证码 at 中间层
     *
     * @param param 2阶段加密参数
     * @return
     */
    @MethodDoc(
            displayName = "发送验证码",
            description = "发送验证码 at 中间层",
            parameters = {
                    @ParamDoc(name = "param", description = "2阶段加密参数", paramType =ParamType.REQUEST_BODY, example = {})
            },
            returnValueDescription = "标准返回参数，code=1为成功标识",
            requestMethods = {HttpMethod.POST,HttpMethod.GET},
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无数据，无需鉴权")
            }
    )
    @ResponseBody
    @RequestMapping(value = "/send_phone_captcha_at_mid")
    public Object sendPhoneCapAtMid(String param) {
        LOGGER.info("sendPhoneCapAtMid param = {}", param);
        if (StringUtils.isEmpty(param)) {
            return WmRestReturnUtil.fail("发送验证码失败");
        }
        try {
            String certPhone = econtractBizService.getCertPhone(param);

            if (!econtractBizService.sendPhoneCapAtMidV2(certPhone).isValue()) {
                return WmRestReturnUtil.failWithCode(WmRestReturnUtil.CAPTCHA_FAILED_CODE, "发送验证码失败");
            }
        } catch (EcontractException e) {
            LOGGER.warn("fail sendCertPhoneCap ", e);
            return WmRestReturnUtil.fail("发送验证码失败,错误码:" + e.getCode());
        } catch (TException e) {
            LOGGER.error("fail sendCertPhoneCap ", e);
            return WmRestReturnUtil.fail("发送验证码失败");
        }
        return WmRestReturnUtil.success("success");
    }

    /**
     * 发送验证码
     */
    @MethodDoc(
            displayName = "发送短信验证码",
            description = "发送验证码",
            parameters = {
                    @ParamDoc(name = "param", description = "手机号、合同等加密信息", paramType = ParamType.REQUEST_BODY, example = {})
            },
            returnValueDescription = "是否成功",
            requestMethods = {HttpMethod.POST,HttpMethod.GET},
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：无数据，无需鉴权")
            }
    )
    @ResponseBody
    @RequestMapping(value = "/send_phone_captcha_param")
    public Object sendPhoneCapByParam(String param) {
        try {
            String recordKey = econtractBizService.queryRecordKeyBySecretParam(param);
            return sendPhoneCap(recordKey);
        } catch (EcontractException e) {
            LOGGER.error("fail sendPhoneCapByParam ", e);
            return WmRestReturnUtil.fail("发送验证码失败,错误码:" + e.getCode());
        } catch (TException e) {
            LOGGER.warn("fail sendPhoneCapByParam ", e);
            return WmRestReturnUtil.fail("发送验证码失败");
        }
    }

    /**
     * 验证码
     */
    @ResponseBody
    @RequestMapping(value = "/verify_phone_captcha")
    public Object verifyPhoneCaptcha(String recordKey, String code) {
        LOGGER.info("verifyPhoneCaptcha recordKey = {} , code = {}", recordKey, code);
        if (StringUtils.isEmpty(recordKey) || StringUtils.isEmpty(code)) {
            return WmRestReturnUtil.fail("验证失败");
        }
        try {
            //测试开关，直接通过验证
            if ("true".equals(ConfigUtilAdapter.getString(MccConstant.TEST_VAERIFY_CAPTCHA, "false"))) {
                return WmRestReturnUtil.success("success");
            }
            if (!econtractBizService.verifyPhoneCaptchaV2(recordKey, code).isValue()) {
                return WmRestReturnUtil.failWithCode(WmRestReturnUtil.CAPTCHA_FAILED_CODE,"验证失败");
            }
        } catch (EcontractException e) {
            LOGGER.warn("fail verifyPhoneCaptcha ", e);
            return WmRestReturnUtil.fail("验证失败,错误码:" + e.getCode());
        } catch (TException e) {
            LOGGER.error("fail verifyPhoneCaptcha ", e);
            return WmRestReturnUtil.fail("验证失败");
        }
        return WmRestReturnUtil.success("success");
    }

    /**
     * 校验验证码 at 中间层
     */
    @ResponseBody
    @RequestMapping(value = "/verify_phone_captcha_at_mid")
    public Object verifyPhoneCaptchaAtMid(String param, String code) {
        LOGGER.info("verifyPhoneCaptcha param = {} , code = {}", param, code);
        if (StringUtils.isEmpty(param) || StringUtils.isEmpty(code)) {
            return WmRestReturnUtil.fail("验证失败");
        }
        try {
            String longUrl = econtractBizService.getLongUrl(param);
            //测试开关，直接通过验证
            if ("true".equals(ConfigUtilAdapter.getString(MccConstant.TEST_VAERIFY_CAPTCHA, "false"))) {
                return WmRestReturnUtil.success(longUrl);
            }
            String certPhone = econtractBizService.getCertPhone(param);
            if (econtractBizService.verifyPhoneCaptchaAtMidV2(certPhone, code).isValue()) {
                return WmRestReturnUtil.success(longUrl);
            }
        } catch (EcontractException e) {
            LOGGER.warn("EcontractSignController#verifyPhoneCaptchaAtMid, 验证失败", e);
            return WmRestReturnUtil.fail(e.getMessage());
        } catch (TException e) {
            LOGGER.error("fail verifyCertPhoneCaptcha ", e);
            return WmRestReturnUtil.fail("验证失败");
        }
        return WmRestReturnUtil.failWithCode(WmRestReturnUtil.CAPTCHA_FAILED_CODE, "验证失败");
    }

    /**
     * 验证码
     */
    @ResponseBody
    @RequestMapping(value = "/verify_phone_captcha_param")
    public Object verifyPhoneCaptchaByParam(String param, String code) {
        try {
            String recordKey = econtractBizService.queryRecordKeyBySecretParam(param);
            return verifyPhoneCaptcha(recordKey, code);
        } catch (EcontractException e) {
            LOGGER.warn("fail verifyPhoneCaptchaByParam ", e);
            return WmRestReturnUtil.fail("验证失败,错误码:" + e.getCode());
        } catch (TException e) {
            LOGGER.error("fail verifyPhoneCaptchaByParam ", e);
            return WmRestReturnUtil.fail("验证失败");
        }
    }

    /**
     * 确认签约 recordKey
     */
    @ResponseBody
    @RequestMapping(value = "/confirm_sign_econtract")
    public Object confirmSignEContract(String recordKey) {
        LOGGER.info("confirmSignEContract recordKey = {}", recordKey);
        if (StringUtils.isEmpty(recordKey)) {
            return WmRestReturnUtil.fail("fail");
        }
        try {
            econtractBizService.confirmSignEContractV2(recordKey);
        } catch (EcontractException e) {
            LOGGER.warn("fail confirmSignEContract ", e);
            return WmRestReturnUtil.fail("请刷新页面,错误码:" + e.getCode());
        } catch (TException e) {
            LOGGER.error("fail confirmSignEContract ", e);
            return WmRestReturnUtil.fail("请刷新页面");
        }
        return WmRestReturnUtil.success("success");
    }

    /**
     * 批量确认签约 recordKeys
     */
    @ResponseBody
    @RequestMapping(value = "/batch_confirm_sign_econtract")
    public Object batchConfirmSignEContract(String recordKeys) {
        LOGGER.info("batchConfirmSignEContract recordKeys = {}", recordKeys);
        if (StringUtils.isEmpty(recordKeys)) {
            return WmRestReturnUtil.fail("fail");
        }
        try {
            List<String> recordKeyList = Splitter.on(",").trimResults().splitToList(recordKeys);
            EcontractAPIResponse response = econtractBizService.batchOpByRecordKeys(recordKeyList, EcontractBatchOpTypeConstant.CONFIRM);
            if (response.getCode() == EcontractAPIResponseConstant.SUCCESS_CODE
                    && EcontractAPIResponseConstant.SUCCESS.equals(response.getStatus())) {
                Map<String, String> returnData = response.getReturnData();
                JSONObject result = new JSONObject();
                result.put("batchConfirmId", returnData.get("batchOpId"));
                return WmRestReturnUtil.success(result);
            }
        } catch (EcontractException e) {
            LOGGER.warn("fail batchConfirmSignEContract ", e);
            return WmRestReturnUtil.fail("请刷新页面,错误码:" + e.getCode());
        } catch (TException e) {
            LOGGER.error("fail batchConfirmSignEContract ", e);
            return WmRestReturnUtil.fail("请刷新页面");
        }
        return WmRestReturnUtil.success("success");
    }

    /**
     * 确认签约 recordKey
     */
    @ResponseBody
    @RequestMapping(value = "/confirm_sign_econtract_param")
    public Object confirmSignEContractByParam(String param) {
        try {
            String recordKey = econtractBizService.queryRecordKeyBySecretParam(param);
            return confirmSignEContract(recordKey);
        } catch (EcontractException e) {
            LOGGER.warn("fail confirmSignEContractByParam ", e);
            return WmRestReturnUtil.fail("请刷新页面,错误码:" + e.getCode());
        } catch (TException e) {
            LOGGER.error("fail confirmSignEContract ", e);
            return WmRestReturnUtil.fail("请刷新页面");
        }
    }

    /**
     * 商家端确认签约 recordKey,accId,wmPoiId
     */
    @ResponseBody
    @RequestMapping(value = "/confirm_sign_econtract_for_poi", method ={RequestMethod.POST,RequestMethod.GET})
    public Object confirmSignEContractForPoi(@RequestParam(value = "recordKey")String recordKey,
                                             @RequestParam(value = "wmPoiId")Long wmPoiId,
                                             @RequestParam(value = "accId")Integer accId,
                                             @RequestParam(value = "accName")String accName,
                                             HttpServletResponse response) {
        response.setHeader("Access-Control-Allow-Origin", "*");
        LOGGER.info("confirmSignEContractForPoi recordKey = {}", recordKey);
        if (StringUtils.isEmpty(recordKey)) {
            LOGGER.info("confirmSignEContractForPoi recordKey为空");
            return WmRestReturnUtil.failWithCode(400,"合同签约失败，可前往合同协议中心重新签约或联系业务经理");
        }
        try {
            EcontractRecordBo econtractRecordBo = econtractBizService.queryEcontractRecordByRecordKey(recordKey);
            if(econtractRecordBo == null){
                LOGGER.info("confirmSignEContractForPoi econtractRecordBo为空 recordKey = {}", recordKey);
                return WmRestReturnUtil.failWithCode(400,"合同签约失败，可前往合同协议中心重新签约或联系业务经理");
            }
            if(StringUtils.equals(econtractRecordBo.getEcontractState(),"state_success")
                    && StringUtils.equals(econtractRecordBo.getEcontractStage(),"econtract_finish")){
                LOGGER.info("confirmSignEContractForPoi econtractRecordBo已经签约成功 recordKey = {}", recordKey);
                return WmRestReturnUtil.failWithCode(400,"合同状态已变更，此操作无效，可前往合同协议中心重新签约或联系业务经理");
            }
            if(StringUtils.equals(econtractRecordBo.getEcontractState(),"state_fail")
                    && StringUtils.equals(econtractRecordBo.getEcontractStage(),"econtract_finish")){
                LOGGER.info("confirmSignEContractForPoi econtractRecordBo已经取消签约 recordKey = {}", recordKey);
                return WmRestReturnUtil.failWithCode(400,"合同已被取消，此操作无效，可前往合同协议中心查看或联系业务经理");
            }
            if (econtractRecordBo.getRecordBatchId() != null && econtractRecordBo.getRecordBatchId() > 0) {
                List<EcontractRecordBo> recordBoList = econtractBizService.queryEcontractRecordByBatchId(econtractRecordBo.getRecordBatchId());
                List<String> recordKeyList = recordBoList.stream().map(EcontractRecordBo::getRecordKey).filter(Objects::nonNull).collect(Collectors.toList());
                econtractBizService.batchOpByRecordKeys(recordKeyList, EcontractBatchOpTypeConstant.CONFIRM);
                wmPoiLogService.insertPoiOpLogForPoi(wmPoiId, accId, accName, recordKey);
            } else {
                econtractBizService.confirmSignEContractV2(recordKey);
                wmPoiLogService.insertPoiOpLogForPoi(wmPoiId,accId,accName,recordKey);
            }
        } catch (EcontractException e) {
            LOGGER.warn("fail confirmSignEContractForPoi ", e);
            return WmRestReturnUtil.failWithCode(400,"合同签约失败，可前往合同协议中心重新签约或联系业务经理" + e.getCode());
        } catch (TException e) {
            LOGGER.error("fail confirmSignEContractForPoi ", e);
            return WmRestReturnUtil.failWithCode(400,"合同签约失败，可前往合同协议中心重新签约或联系业务经理");
        }
        return WmRestReturnUtil.successWithCode(200,"合同已完成签约，可前往合同协议中心查看","合同已完成签约，可前往合同协议中心查看");
    }

    /**
     * 取消签约 recordKey
     */
    @ResponseBody
    @RequestMapping(value = "/cancel_sign_econtract")
    public Object cancelSignEContract(String recordKey) {
        LOGGER.info("cancelSignEContract recordKey = {}", recordKey);
        if (StringUtils.isEmpty(recordKey)) {
            return WmRestReturnUtil.fail("fail");
        }
        try {
            econtractBizService.cancelSignEContractV2(recordKey, TaskConstant.H5_CANCEL);
        } catch (EcontractException e) {
            LOGGER.warn("fail cancelSignEContract ", e);
            return WmRestReturnUtil.fail("取消签约失败,错误码:" + e.getCode());
        } catch (TException e) {
            LOGGER.error("fail cancelSignEContract ", e);
            return WmRestReturnUtil.fail("取消签约失败");
        }
        return WmRestReturnUtil.success("success");
    }

    /**
     * 批量取消签约 recordKeys
     */
    @ResponseBody
    @RequestMapping(value = "/batch_cancel_sign_econtract")
    public Object batchCancelSignEContract(String recordKeys) {
        LOGGER.info("batchCancelSignEContract recordKeys = {}", recordKeys);
        if (StringUtils.isEmpty(recordKeys)) {
            return WmRestReturnUtil.fail("fail");
        }
        try {
            List<String> recordKeyList = Splitter.on(",").trimResults().splitToList(recordKeys);
            EcontractAPIResponse response = econtractBizService.batchOpByRecordKeys(recordKeyList, EcontractBatchOpTypeConstant.CANCEL);
            if (response.getCode() == EcontractAPIResponseConstant.SUCCESS_CODE
                    && EcontractAPIResponseConstant.SUCCESS.equals(response.getStatus())) {
                Map<String, String> returnData = response.getReturnData();
                JSONObject result = new JSONObject();
                result.put("batchCancelId", returnData.get("batchOpId"));
                return WmRestReturnUtil.success(result);
            }
        } catch (EcontractException e) {
            LOGGER.warn("fail batchCancelSignEContract ", e);
            return WmRestReturnUtil.fail("批量取消签约失败,错误码:" + e.getCode());
        } catch (TException e) {
            LOGGER.error("fail batchCancelSignEContract ", e);
            return WmRestReturnUtil.fail("批量取消签约失败");
        }
        return WmRestReturnUtil.success("success");
    }

    /**
     * 取消签约
     */
    @ResponseBody
    @RequestMapping(value = "/cancel_sign_econtract_param")
    public Object cancelSignEContractByParam(String param) {
        try {
            String recordKey = econtractBizService.queryRecordKeyBySecretParam(param);
            return cancelSignEContract(recordKey);
        } catch (EcontractException e) {
            LOGGER.warn("fail cancelSignEContractByParam ", e);
            return WmRestReturnUtil.fail("取消签约失败,错误码:" + e.getCode());
        } catch (TException e) {
            LOGGER.error("fail cancelSignEContractByParam ", e);
            return WmRestReturnUtil.fail("取消签约失败");
        }
    }

    /**
     * 查询签约页面文案信息
     */
    @ResponseBody
    @RequestMapping(value = "/get_pagetype_and_signpageinfo", method = {RequestMethod.GET, RequestMethod.POST})
    public Object getSignPageInfo(String param) {
        LOGGER.info("getSignPageInfo param = {}", param);
        if (StringUtils.isBlank(param) || EcontractBaseConstant.UNDEFINED.equals(param)) {
            return WmRestReturnUtil.fail("参数不能为空");
        }

        try {
            SignPageInfoBo signPageInfoBo = econtractBizService.querySignPageInfoBySecretParamV2(param);
            JSONObject jo = new JSONObject();
            jo.put("type", signPageInfoBo.getTemplateName());
            jo.put("signPageInfo", signPageInfoBo);
            return WmRestReturnUtil.success(jo);
        } catch (EcontractException e) {
            LOGGER.warn("fail getSignPageInfo ", e);
            return WmRestReturnUtil.fail("获取签约页失败,错误码:" + e.getCode());
        } catch (TException e) {
            LOGGER.error("fail getSignPageInfo ", e);
            return WmRestReturnUtil.fail("获取签约页失败");
        }
    }

    /**
     * 电子合同签约埋点
     */
    @ResponseBody
    @RequestMapping(value = "/trace", method = {RequestMethod.GET, RequestMethod.POST})
    public Object trace(EcontractTraceVo traceVo) {
        LOGGER.info("trace userId = {}, channel = {}", traceVo.getUserId(), traceVo.getChannel());
        if (traceVo.getUserId() == null || traceVo.getChannel() == null) {
            return WmRestReturnUtil.fail("埋点失败");
        }

        Cat.logMetricForCount("econtract_trace_" + traceVo.getUserId() + "_" + traceVo.getChannel());
        return WmRestReturnUtil.success("success");
    }

    @ResponseBody
    @RequestMapping(value = "/batchSignStatus", method = RequestMethod.GET)
    public Object queryBatchSignStatus(long batchConfirmId)  {
        LOGGER.info("#queryBatchSignStatus, batchConfirmId={}", batchConfirmId);
        try {
            if (batchConfirmId <= 0) {
                return WmRestReturnUtil.fail("参数不合法");
            }
            EcontractAPIResponse response = econtractBizService.queryBatchOpStatus(batchConfirmId);
            if (response.getCode() == EcontractAPIResponseConstant.SUCCESS_CODE
                    && EcontractAPIResponseConstant.SUCCESS.equals(response.getStatus())) {
                return WmRestReturnUtil.success(response.getReturnData());
            }
            return WmRestReturnUtil.fail("获取批量签约状态异常");
        } catch (Exception e) {
            LOGGER.error("#queryBatchSignStatus, batchConfirmId={}", batchConfirmId, e);
            return WmRestReturnUtil.fail("服务异常");
        }
    }

}

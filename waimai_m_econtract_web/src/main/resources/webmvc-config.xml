<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:mvc="http://www.springframework.org/schema/mvc" xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc-3.0.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">

    <context:component-scan base-package="com.sankuai.meituan.waimai.econtract.web.controller,com.sankuai.meituan.waimai.econtract.web.controller.template.config"/>

    <mvc:resources mapping="/econtract/frontend/**" location="/static/dist/"/>
    <mvc:resources mapping="/process-editor/**" location="/process-editor/"/>


    <bean id="exceptionResolver" class="com.sankuai.meituan.waimai.econtract.web.controller.ErrorLogMappingExceptionResolver">
        <property name="exceptionMappings">
            <props>
                <prop key="org.springframework.web.multipart.MultipartException">error/uploadFileFail</prop>
                <prop key="java.lang.Exception">error/uncaughtException</prop>
                <prop key="java.lang.Throwable">error/uncaughtException</prop>
                <prop key=".NoURIFoundException">error/pageNotFound</prop>
                <prop key=".WmNoDataPermissionException">error/accessDenied</prop>
            </props>
        </property>
        <property name="statusCodes">
            <props>
                <prop key="error/uploadFileFail">200</prop>
                <prop key="error/error">500</prop>
                <prop key="error/pageNotFound">404</prop>
            </props>
        </property>
        <!-- 设置日志输出级别，不定义则默认不输出警告等错误日志信息 -->
        <property name="warnLogCategory" value="WARN"></property>
        <!-- 默认错误页面，当找不到上面mappings中指定的异常对应视图时，使用本默认配置 -->
        <property name="defaultErrorView" value="errors/error"></property>
        <!-- 默认HTTP状态码 -->
        <property name="defaultStatusCode" value="500"></property>
    </bean>

    <bean id="freemarkerConfig" class="com.sankuai.meituan.basic.webapp.HtmlFreeMarkerConfigurer">
        <property name="templateLoaderPath" value="/WEB-INF/views/"/>
        <property name="freemarkerSettings">
            <props>
                <prop key="template_update_delay">0</prop>
                <prop key="default_encoding">UTF-8</prop>
                <prop key="locale">zh_CN</prop>
                <prop key="url_escaping_charset">UTF-8</prop>
                <prop key="whitespace_stripping">true</prop>
                <prop key="number_format">0.##</prop>
            </props>
        </property>
        <property name="freemarkerVariables">
            <map>
                <entry key="timestamp" value-ref="timestampLabel"/>
            </map>
        </property>
    </bean>

    <bean id="viewResolver" class="org.springframework.web.servlet.view.freemarker.FreeMarkerViewResolver">
        <property name="contentType" value="text/html; charset=UTF-8"/>
        <property name="cache" value="true"/>
        <property name="prefix" value=""/>
        <property name="suffix" value=".ftl"/>
        <property name="exposeSpringMacroHelpers" value="true"/>
        <property name="exposeRequestAttributes" value="true"/>
        <property name="exposeSessionAttributes" value="true"/>
        <property name="allowSessionOverride" value="true"/>
        <property name="requestContextAttribute" value="request"/>
    </bean>

    <bean id="multipartResolver" class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
        <property name="maxInMemorySize">
            <value>10240000</value>
        </property>
    </bean>


    <mvc:annotation-driven/>
    <mvc:interceptors>
        <!-- 加入TraceHandlerInterceptor -->
        <mvc:interceptor>
            <mvc:mapping path="/**"/>    <!-- 想加入mtrace监控的url -->
            <bean class="com.meituan.mtrace.http.TraceHandlerInterceptor"/>
        </mvc:interceptor>
        <!-- 业务异常行为监控 -->
        <mvc:interceptor>
            <mvc:mapping path="/**"/>
            <bean class="com.sankuai.meituan.waimai.mtauth.interceptor.OwlMonitorInterceptor" />
        </mvc:interceptor>
    </mvc:interceptors>

</beans>

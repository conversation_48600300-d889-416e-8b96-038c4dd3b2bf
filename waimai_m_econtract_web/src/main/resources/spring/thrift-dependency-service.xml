<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">


    <!--外部-->
    <import resource="classpath:waimaiConfig/waimai_lib_mtauth_sso_degrade.xml"/>
    <import resource="classpath:waimaiConfig/waimai_service_infra_client.xml"/>
    <import resource="classpath:waimai_service_poiquery_client.xml"/>
    <import resource="classpath:waimai_service_poi_flowline_client.xml"/>


    <bean id="mtThriftPoolConfig" class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig">
        <property name="maxActive" value="50"/>
        <property name="maxIdle" value="8"/>
        <property name="minIdle" value="2"/>
        <property name="maxWait" value="3000"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="false"/>
    </bean>


    <!-- api系统回调 -->
    <bean id="econtractAPIService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.econtrct.client.service.EcontractAPIService"/> <!-- 接口名 -->
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="appKey" value="com.sankuai.waimai.m.econtractweb"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.m.econtractserver"/> <!-- 目标 Server Appkey -->
        <property name="remoteServerPort" value="9001" />
        <property name="timeout" value="1000"/><!--超时时间ms-->
    </bean>
    <!-- 电子合同系统H5页面信息 -->
    <bean id="econtractBizService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.econtrct.client.service.EcontractBizService"/> <!-- 接口名 -->
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="appKey" value="com.sankuai.waimai.m.econtractweb"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.m.econtractserver"/> <!-- 目标 Server Appkey -->
        <property name="maxResponseMessageBytes" value="32768000"/>
        <property name="remoteServerPort" value="9002" />
        <property name="timeout" value="10000"/><!--超时时间ms-->
    </bean>

    <!-- 电子合同用户管理 -->
    <bean id="econtractUserManagerService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.econtrct.client.service.EcontractUserManagerService"/> <!-- 接口名 -->
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="appKey" value="com.sankuai.waimai.m.econtractweb"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.m.econtractserver"/> <!-- 目标 Server Appkey -->
        <property name="remoteServerPort" value="9003" />
        <property name="timeout" value="10000"/><!--超时时间ms-->
    </bean>

    <!-- 电子合同管理信息 -->
    <bean id="econtractManagerService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.econtrct.client.service.EcontractManagerService"/> <!-- 接口名 -->
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="appKey" value="com.sankuai.waimai.m.econtractweb"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.m.econtractserver"/> <!-- 目标 Server Appkey -->
        <property name="remoteServerPort" value="9004" />
        <property name="timeout" value="10000"/><!--超时时间ms-->
    </bean>

    <!-- 电子合同模版配置 -->
    <bean id="econtractTemplateConfigThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.econtrct.client.template.config.service.EcontractTemplateConfigThriftService"/> <!-- 接口名 -->
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="appKey" value="com.sankuai.waimai.m.econtractweb"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.m.econtractserver"/> <!-- 目标 Server Appkey -->
        <property name="remoteServerPort" value="9006" />
        <property name="timeout" value="10000"/><!--超时时间ms-->
    </bean>

    <bean id="wmPoiOplogThriftServicePublisher" class="com.sankuai.meituan.waimai.oplog.thrift.service.WmPoiOplogClientproxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.oplog.thrift.service.WmPoiOplogThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.service.oplog"/>
        <property name="remoteServerPort" value="12345"/>
    </bean>

    <bean id="wmPoiOplogThriftServiceAsync" class="com.sankuai.meituan.waimai.oplog.thrift.service.WmPoiOplogClientAsyncProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.waimai.oplog.thrift.service.WmPoiOplogThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('octo.appkey', T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('app.key'))}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.service.oplog"/>
        <property name="remoteServerPort" value="12345"/>
        <property name="async" value="true" />
        <property name="nettyIO" value="true"/>
    </bean>




</beans>


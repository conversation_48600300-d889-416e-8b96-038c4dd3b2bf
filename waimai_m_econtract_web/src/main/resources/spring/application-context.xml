<?xml version="1.0" encoding="UTF-8"?>
<beans
        xmlns="http://www.springframework.org/schema/beans"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns:context="http://www.springframework.org/schema/context"
        xmlns:mvc="http://www.springframework.org/schema/mvc" xmlns:aop="http://www.springframework.org/schema/aop"
        xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">


    <import resource="thrift-dependency-service.xml"/>

    <bean id="mtSSOFilter" class="com.sankuai.it.sso.sdk.spring.FilterFactoryBean">
        <!--必须配置，以下二者需先到开放平台(http://open.sankuai.com)申请接入SSO后颁发，对应企平开放平台的AppKey和AppSecret-->
        <property name="clientId" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('mtsso_econtract_clientid', 'db61133e5c')}"/>
        <property name="secret" value="#{T(com.meituan.service.inf.kms.client.Kms).getByName('com.sankuai.waimai.m.econtractweb', 'mtsso_econtract_secret')}"/>

        <!--必须配置，表示需要经过SSO过滤的uri，多个uri以','分割。exlucdedUriList与includedUriList二者仅一个有效，请只配置一项。如果两项都配置，includedUriList优先级更高 -->
        <!--<property name="excludedUriList" value="/static/**,/econtract/restful/api/v1/sign/**,/econtract/restful/api/v1/call_back/**"/>-->
        <!--或-->
        <property name="includedUriList" value="/econtract/restful/api/v1/manager/**,/econtract/frontend/contractManager/**,/econtract/restful/api/v1/activiti/**,/econtract/backdoor/**,/process-editor/**,/econtract/restful/api/v2/manager/**,/econtract/restful/api/uicomponent/**"/>
    </bean>

    <!--<bean id="mtFilter" class="com.sankuai.meituan.auth.filters.upm2.UpmFilterFactoryBean">-->
        <!---->
    <!--</bean>-->
    <bean id="peckerTraceBeanAutoConfiguration" class="com.sankuai.tsp.pecker.trace.infrastructure.config.PeckerTraceBeanAutoConfiguration">

    </bean>

</beans>

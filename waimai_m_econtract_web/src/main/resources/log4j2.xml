<?xml version="1.0" encoding="UTF-8"?>
<configuration status="info">

    <!--trace-log4j2  start -->

    <Scribe name="waimai_lib_metric_trace_wm_poi_log">
        <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
        <Property name="hostname">${sys:app.key}</Property>
        <Property name="scribeHost">127.0.0.1</Property>
        <Property name="scribePort">4252</Property>
        <Property name="scribeCategory">wm_poi_log</Property>
        <Property name="printExceptionStack">false</Property>
        <Property name="addStackTraceToMessage">false</Property>
        <Property name="timeToWaitBeforeRetry">6000</Property>
        <Property name="sizeOfInMemoryStoreForward">100</Property>
        <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} %XMDT %p $${sys:app.host} $${sys:app.ip} %c %m%n%ex  "/>
    </Scribe>

    <Scribe name="waimai_lib_metric_trace_metrics_wm_poitrace">
        <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
        <Property name="hostname">${sys:app.key}</Property>
        <Property name="scribeHost">127.0.0.1</Property>
        <Property name="scribePort">4252</Property>
        <Property name="scribeCategory">metrics_wm_poitrace</Property>
        <Property name="printExceptionStack">false</Property>
        <Property name="addStackTraceToMessage">false</Property>
        <Property name="timeToWaitBeforeRetry">6000</Property>
        <Property name="sizeOfInMemoryStoreForward">100</Property>
        <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} %XMDT %p $${sys:app.host} $${sys:app.ip} %c %m%n%ex  "/>
    </Scribe>

    <Logger name="metrics_wm_poitrace"  level="INFO" includeLocation="true" additivity="false">
        <AppenderRef ref="console"/>
        <AppenderRef ref="waimai_lib_metric_trace_metrics_wm_poitrace"/>
    </Logger>

    <!--trace-log4j2  end -->

    <appenders>
        <Console name="Console" target="SYSTEM_OUT" follow="true">
            <PatternLayout pattern="%d{yyyy/MM/dd HH:mm:ss.SSS} %XMDT %t [%p] %c{1} (%F:%L) - %msg%n%ex"/>
        </Console>

        <!--XMDFile异步磁盘日志配置示例-->
        <!--默认按天&按512M文件大小切分日志，默认最多保留30个日志文件。-->
        <!--目录为/opt/logs/${appkey} 或 /data/applogs/${appkey}，优先选用磁盘挂载目录-->
        <!--注意：fileName前会自动增加文件路径，只配置文件名即可-->
        <!--可选配置：ERROR日志、WARN日志单独输出到一个文件-->
        <XMDFile name="ERROR-LOG" fileName="${sys:app.key}.err.log" xmdFilePath="/var/sankuai/logs"
                 addAppkeyToFilePath="false">
            <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>

        </XMDFile>

        <!-- jmonitor相关日志，打印到单独的文件中，并使用异步日志 -->
        <XMDFile name="jmonitorappender" fileName="${sys:app.key}.jmonitor.log" xmdFilePath="/var/sankuai/logs"
                 timeBasedTriggeringInterval="1" addAppkeyToFilePath="false">
            <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
        </XMDFile>
        <XMDFile name="jmonitorlogstoreappender" fileName="${sys:app.key}.jmonitor.logstore.log"
                 xmdFilePath="/var/sankuai/logs" timeBasedTriggeringInterval="1" addAppkeyToFilePath="false">
            <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
        </XMDFile>


        <!--异常日志远程上报-->
        <Async name="AsyncErrorLog" blocking="false">  <!-- 当 blocking 为 false 时，超过最高 qps 时，不阻塞，直接抛弃数据。-->
            <AppenderRef ref="remoteErrorLog"/>
        </Async>
        <Scribe name="remoteErrorLog">
            <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
            <LcLayout/>
        </Scribe>


        <Scribe name="ScribeAppender">
            <!-- 远程日志默认使用appkey作为日志名(app.properties文件中的app.name字段)，也可以选择用如下方式自定义scribeCategory。scribeCategory优先级高于appkey-->
            <!-- <Property name="scribeCategory">waimai_m_contract_job</Property> -->
            <LcLayout/>
        </Scribe>
        <Scribe name="wmPoiLogAppender">
            <Property name="scribeCategory">wm_poi_log</Property>
            <ScribeLayout/>
        </Scribe>
        <Scribe name="metricsWmPoitraceAppender">
            <Property name="scribeCategory">metrics_wm_poitrace</Property>
            <ScribeLayout/>
        </Scribe>
        <Async name="ScribeAsyncAppender" blocking="false">
            <AppenderRef ref="ScribeAppender"/>
        </Async>
        <Async name="wmPoiLogAsyncAppender" blocking="false">
            <AppenderRef ref="wmPoiLogAppender"/>
        </Async>
        <Async name="metricsWmPoitraceAsyncAppender" blocking="false">
            <AppenderRef ref="metricsWmPoitraceAppender"/>
        </Async>
    </appenders>

    <loggers>
        <!--定制化配置示例：可以对某个group单独设置logger-->
        <logger name="com.sankuai.octo.HttpRegist.controller" level="info">
            <appender-ref ref="SERVICE-LOG"/>   <!--若希望日志落地本地文件，需要配置本appender-ref-->
        </logger>

        <!-- 单独打印jmonitor -->
        <logger name="jmonitor" additivity="false" level="info">
            <appender-ref ref="jmonitorappender" />
        </logger>

        <logger name="jmonitorlogstore" additivity="false" level="info">
            <appender-ref ref="jmonitorlogstoreappender" />
        </logger>

        <logger name="com.sankuai.meituan.web" additivity="false" level="info">
            <appender-ref ref="ERROR-LOG"/>
        </logger>

        <logger name="com.sankuai.meituan" level="info">
        </logger>

        <!-- 3rdparty Loggers -->
        <logger name="org.springframework.core" level="info">
        </logger>

        <logger name="org.springframework.beans" level="info">
        </logger>

        <logger name="org.springframework.context" level="info">
        </logger>

        <logger name="org.springframework.web" level="info">
        </logger>

        <!-- Application Loggers -->
        <logger name="wm_poi_log" additivity="false">
            <level value="info"/>
            <appender-ref ref="wmPoiLogAsyncAppender"/>
        </logger>
        <logger name="metrics_wm_poitrace" additivity="false">
            <level value="info"/>
            <appender-ref ref="metricsWmPoitraceAsyncAppender"/>
        </logger>


        <root level="info">
            <appender-ref ref="ERROR-LOG" />                <!--ERROR日志单独输出到一个文件-->
            <appender-ref ref="ScribeAsyncAppender" />          <!--日志传入远程日志中心 -->

            <appender-ref ref="AsyncErrorLog" />      <!--异常日志上报要添加到root-->
        </root>
    </loggers>
</configuration>
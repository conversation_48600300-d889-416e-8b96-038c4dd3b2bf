<?xml version="1.0"?>
<!DOCTYPE Configure PUBLIC "-//Mort Bay Consulting//DTD Configure//EN" "http://jetty.eclipse.org/configure.dtd">
<Configure id="Server" class="org.eclipse.jetty.server.Server">

    <New id="ServerLog" class="java.io.PrintStream">
        <Arg>
            <New class="com.sankuai.mms.util.MtRolloverFileOutputStream">
                <Arg><SystemProperty name="jetty.logs" default="./logs"/>/<SystemProperty name="jetty.appkey" default="jetty"/>.log.yyyy_mm_dd</Arg>
                <Arg type="boolean">true</Arg>
                <Arg type="int"><SystemProperty name="jetty.log.retainDays" default="30"/></Arg>
                <Arg type="int"><SystemProperty name="jetty.log.nonCompressDays" default="10"/></Arg>
                <Arg><Call class="java.util.TimeZone" name="getTimeZone"><Arg>GMT+8</Arg></Call></Arg>
                <Arg type="string">yyyy-MM-dd</Arg>
                <Arg type="string"></Arg>
                <Get id="ServerLogName" name="datedFilename"/>
            </New>
        </Arg>
    </New>
    <Call class="org.eclipse.jetty.util.log.Log" name="info"><Arg>Redirecting stderr/stdout to <Ref id="ServerLogName"/></Arg></Call>
    <Call class="java.lang.System" name="setErr"><Arg><Ref id="ServerLog"/></Arg></Call>
    <Call class="java.lang.System" name="setOut"><Arg><Ref id="ServerLog"/></Arg></Call>

    <Set name="ThreadPool">
        <New class="org.eclipse.jetty.util.thread.QueuedThreadPool">
            <Set name="minThreads">20</Set>
            <Set name="maxThreads">200</Set>
            <Set name="name">jetty-worker</Set>
        </New>
    </Set>

    <Call name="addConnector">
        <Arg>
            <New class="org.eclipse.jetty.server.nio.SelectChannelConnector">
                <Set name="host"><!--<SystemProperty name="jetty.host"/>--></Set>
                <Set name="port"><SystemProperty name="jetty.port" default="8080"/></Set>
                <Set name="maxIdleTime">30000</Set>
                <Set name="Acceptors">2</Set>
                <Set name="statsOn">false</Set>
                <!-- Set name="confidentialPort">8063</Set -->
                <Set name="lowResourcesConnections">5000</Set>
                <Set name="lowResourcesMaxIdleTime">5000</Set>
                <Set name="requestHeaderSize">32768</Set>
            </New>
        </Arg>
    </Call>

    <Array id="plusConfig" type="java.lang.String">
        <!-- <Item>org.eclipse.jetty.webapp.WebInfConfiguration</Item> -->
        <Item>org.eclipse.jetty.webapp.WebXmlConfiguration</Item>
        <!-- <Item>org.eclipse.jetty.webapp.MetaInfConfiguration</Item> -->
        <Item>org.eclipse.jetty.webapp.FragmentConfiguration</Item>
        <Item>org.eclipse.jetty.plus.webapp.EnvConfiguration</Item>
        <Item>org.eclipse.jetty.plus.webapp.PlusConfiguration</Item>
        <!-- <Item>org.eclipse.jetty.annotations.AnnotationConfiguration</Item> -->
        <Item>org.eclipse.jetty.webapp.JettyWebXmlConfiguration</Item>
        <Item>org.eclipse.jetty.webapp.TagLibConfiguration</Item>
        <Item>org.eclipse.jetty.webapp.WebInfConfiguration</Item>
        <Item>org.eclipse.jetty.webapp.MetaInfConfiguration</Item>
        <Item>org.eclipse.jetty.annotations.AnnotationConfiguration</Item>
    </Array>

    <Set name="handler">
        <New id="Handlers" class="org.eclipse.jetty.server.handler.HandlerCollection">
            <Set name="handlers">
                <Array type="org.eclipse.jetty.server.Handler">
                    <Item>
                        <New class="org.eclipse.jetty.webapp.WebAppContext">
                            <Set name="resourceBase"><SystemProperty name="jetty.webroot"/></Set>
                            <Set name="contextPath"><SystemProperty name="jetty.context" default="/"/></Set>
                            <Set name="parentLoaderPriority">false</Set>
                            <!-- <Set name="defaultsDescriptor"><SystemProperty name="jetty.home"/>/etc/webdefault.xml</Set> -->
                            <Set name="configurationClasses"><Ref id="plusConfig"/></Set>
                            <Set name="maxFormContentSize" type="int">100000000</Set>
                        </New>
                    </Item>
                    <Item>
                        <New class="org.eclipse.jetty.server.handler.RequestLogHandler">
                            <Set name="requestLog">
                                <New id="RequestLogImpl" class="org.eclipse.jetty.server.NCSARequestLog">
                                    <Set name="filename"><SystemProperty name="jetty.logs" default="./logs"/>/<SystemProperty name="jetty.appkey" default="jetty"/>.request.log.yyyy_mm_dd</Set>
                                    <Set name="filenameDateFormat">yyyy-MM-dd</Set>
                                    <Set name="retainDays"><SystemProperty name="jetty.request.log.retainDays" default="90"/></Set>
                                    <Set name="append">true</Set>
                                    <Set name="extended">false</Set>
                                    <Set name="logCookies">false</Set>
                                    <Set name="LogTimeZone">GMT+8</Set>
                                    <Set name="logLatency">true</Set>
                                    <Set name="preferProxiedForAddress">true</Set>
                                </New>
                            </Set>
                        </New>
                    </Item>
                </Array>
            </Set>
        </New>
    </Set>

    <Set name="stopAtShutdown">true</Set>
    <Set name="sendServerVersion">true</Set>
    <Set name="sendDateHeader">true</Set>
    <Set name="gracefulShutdown">1000</Set>
    <!--如果打开了会打非常恐怖的日志,导致启动日志没法看了-->
    <Set name="dumpAfterStart">false</Set>
    <Set name="dumpBeforeStop">false</Set>

</Configure>
<!DOCTYP<PERSON> web-app PUBLIC
        "-//Sun Microsystems, Inc.//DTD Web Application 2.3//EN"
        "http://java.sun.com/dtd/web-app_2_3.dtd" >

<web-app>
	<display-name>waimai_econtract_web</display-name>


	<!--context-param-->
	<context-param>
		<param-name>webAppRootKey</param-name>
		<param-value>waimai_m_econtract_web</param-value>
	</context-param>

	<context-param>
		<param-name>octoAppkey</param-name>
		<param-value>com.sankuai.waimai.m.econtractweb</param-value>
	</context-param>

	<context-param>
		<param-name>contextConfigLocation</param-name>
		<param-value>
			classpath*:spring/application-context.xml
		</param-value>
	</context-param>


	<!--filter -->
	<filter>
		<filter-name>SetCharacterEncoding</filter-name>
		<filter-class>org.springframework.web.filter.CharacterEncodingFilter</filter-class>
		<init-param>
			<param-name>encoding</param-name>
			<param-value>UTF-8</param-value>
		</init-param>
	</filter>
	<filter-mapping>
		<filter-name>SetCharacterEncoding</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>

	<filter>
		<filter-name>cat-filter</filter-name>
		<filter-class>com.dianping.cat.servlet.CatFilter</filter-class>
	</filter>
	<filter-mapping>
		<filter-name>cat-filter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>

	<!-- 配置需要鉴权的url-pattern -->
	<filter>
		<filter-name>mtSSOFilter</filter-name>
		<filter-class>org.springframework.web.filter.DelegatingFilterProxy</filter-class>
		<init-param>
			<param-name>targetFilterLifecycle</param-name>
			<param-value>true</param-value>
		</init-param>
	</filter>
	<filter-mapping>
		<filter-name>mtSSOFilter</filter-name>
		<!--将所有URI交给SSO过滤器管理-->
		<url-pattern>/*</url-pattern>
	</filter-mapping>
	<!--<filter>-->
		<!--<filter-name>mtFilter</filter-name>-->
		<!--<filter-class>org.springframework.web.filter.DelegatingFilterProxy</filter-class>-->
		<!--<init-param>-->
			<!--<param-name>targetFilterLifecycle</param-name>-->
			<!--<param-value>true</param-value>-->
		<!--</init-param>-->
	<!--</filter>-->
	<!--<filter-mapping>-->
		<!--<filter-name>mtFilter</filter-name>-->
		<!--<url-pattern>/econtract/restful/api/v1/manager/*</url-pattern>-->
	<!--</filter-mapping>-->
	<!--<filter-mapping>-->
		<!--<filter-name>mtFilter</filter-name>-->
		<!--<url-pattern>/upload/*</url-pattern>-->
	<!--</filter-mapping>-->
	<!--filter END-->



	<!--listener-->
	<listener>
		<listener-class>org.springframework.web.util.Log4jConfigListener</listener-class>
	</listener>

	<listener>
		<listener-class>com.sankuai.meituan.basic.webapp.MtDefaultContextListener</listener-class>
	</listener>

	<listener>
		<listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
	</listener>
	<!--listener END-->

	<!-- servlet -->
	<servlet>
		<servlet-name>appServlet</servlet-name>
		<servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
		<init-param>
			<param-name>contextConfigLocation</param-name>
			<param-value>
				classpath:webmvc-basic-webapp.xml,
				classpath:webmvc-config.xml
			</param-value>
		</init-param>
		<load-on-startup>2</load-on-startup>
	</servlet>
	<servlet-mapping>
		<servlet-name>appServlet</servlet-name>
		<url-pattern>/</url-pattern>
	</servlet-mapping>


</web-app>

#===========================================================
# Jetty start.jar arguments
#===========================================================



#===========================================================
# If the arguements in this file include JVM arguments
# (eg -Xmx512m) or JVM System properties (eg com.sun.???),
# then these will not take affect unless the --exec
# parameter is included or if the output from --dry-run
# is executed like:
#   eval $(java -jar start.jar --dry-run)
#
# Below are some recommended options for Sun's JRE
#-----------------------------------------------------------
--exec

-Dfile.encoding=UTF-8
-Djava.io.tmpdir=/tmp
-Djava.net.preferIPv6Addresses=false
-Duser.timezone=GMT+08

# è¿ä¸ªåæ°å¾éè¦ï¼å¼å®¹èä»£ç 
-Dcore.step=app

# è®¾ç½®jconsoleè¿ç¨è¿æ¥
#-Dcom.sun.management.jmxremote=true
#-Djava.rmi.server.hostname=************
#-Dcom.sun.management.jmxremote.port=50013
#-Dcom.sun.management.jmxremote.authenticate=false

-server
-Xmx4g
-Xms4g
#-Xmn1g
#-XX:SurvivorRatio=8
-XX:PermSize=128m
-XX:MaxPermSize=384m

-XX:+HeapDumpOnOutOfMemoryError
#-XX:ReservedCodeCacheSize=128m
#-XX:InitialCodeCacheSize=128m

-XX:+DisableExplicitGC
-XX:+UseParallelGC
#-XX:ParallelGCThreads=4
-XX:+UseParallelOldGC
-XX:+UseAdaptiveSizePolicy

# -Dorg.apache.jasper.compiler.disablejsr199=true
# -Dcom.sun.management.jmxremote
# -Dorg.eclipse.jetty.util.log.IGNORED=true
# -Dorg.eclipse.jetty.LEVEL=DEBUG
# -Dorg.eclipse.jetty.util.log.stderr.SOURCE=true
# -verbose:gc

#-XX:+PrintGC
-XX:+PrintGCDetails
#-XX:+PrintGCDateStamps
-XX:+PrintGCTimeStamps
#-XX:+PrintGCApplicationConcurrentTime
#-XX:+PrintGCApplicationStoppedTime
#-XX:+PrintHeapAtGC
#-XX:+PrintTenuringDistribution
-XX:+PrintCommandLineFlags

-XX:+UseConcMarkSweepGC
-XX:+UseParNewGC
-XX:ParallelCMSThreads=4
-XX:+CMSClassUnloadingEnabled
-XX:+UseCMSCompactAtFullCollection
-XX:CMSFullGCsBeforeCompaction=1
-XX:CMSInitiatingOccupancyFraction=50
-XX:+UseCMSInitiatingOccupancyOnly
#-----------------------------------------------------------


#===========================================================
# Start classpath OPTIONS.
#-----------------------------------------------------------
OPTIONS=Server,plus,annotations
#-----------------------------------------------------------
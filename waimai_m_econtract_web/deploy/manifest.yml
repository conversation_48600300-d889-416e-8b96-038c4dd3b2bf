version: v1
common:
  os: centos7
  tools:
    mt_oraclejdk: 8
build:
  tools:
    maven: 3.3.3
  run:
    workDir: ./     # workDir是代码仓库的相对目录
    cmd:
      - sh waimai_m_econtract_web/deploy/compile.sh
  target:
    distDir: ./waimai_m_econtract_web/target   # distDir是代码仓库的相对目录
    files:              # files中的文件描述是相对distDir的
      - ./*.war
      - ../deploy
autodeploy:
    targetDir: /opt/meituan/apps/waimai_m_econtract_web/wars
    run: sh deploy/run.sh
    check: sh deploy/check.sh
    checkRetry: 10 #后面下掉，加默认值为1
    checkInterval: 10s #后面下掉